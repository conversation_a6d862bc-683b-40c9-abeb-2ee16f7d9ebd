---
apiVersion: v1
kind: Service
metadata:
  name: imes-tim-service
  labels:
    app: imes-tim-service
spec:
  ports:
  - port: 8199
    protocol: TCP
    targetPort: 8199
    nodePort: 31199
    name: imes-tim-port
  selector:
    app: mom-test-tim
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-system-service
  labels:
    app: imes-system-service
spec:
  ports:
  - port: 8150
    protocol: TCP
    targetPort: 8150
    nodePort: 31150
    name: imes-sys-port
  selector:
    app: mom-test-sys
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-ppc-service
  labels:
    app: imes-ppc-service
spec:
  ports:
  - port: 1100
    protocol: TCP
    targetPort: 1100
    nodePort: 31100
    name: imes-ppc-port
  selector:
    app: mom-test-ppc
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-file-service
  labels:
    app: imes-file-service
spec:
  ports:
  - port: 8104
    protocol: TCP
    targetPort: 8104
    nodePort: 31104
    name: imes-file-port
  selector:
    app: mom-test-file
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-fts-service
  labels:
    app: imes-fts-service
spec:
  ports:
  - port: 8190
    protocol: TCP
    targetPort: 8190
    nodePort: 31190
    name: imes-fts-port
  selector:
    app: mom-test-fts
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-wms-service
  labels:
    app: imes-wms-service
spec:
  ports:
  - port: 8155
    protocol: TCP
    targetPort: 8155
    nodePort: 31155
    name: imes-wms-port
  selector:
    app: mom-test-wms
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-pg-service
  labels:
    app: imes-pg-service
spec:
  ports:
  - port: 8160
    protocol: TCP
    targetPort: 8160
    nodePort: 31160
    name: imes-pg-port
  selector:
    app: mom-test-pag
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-activiti-service
  labels:
    app: imes-activiti-service
spec:
  ports:
  - port: 8113
    protocol: TCP
    targetPort: 8113
    nodePort: 31113
    name: imes-activiti-port
  selector:
    app: mom-test-activiti
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-dev-service
  labels:
    app: imes-dev-service
spec:
  ports:
  - port: 2100
    protocol: TCP
    targetPort: 2100
    nodePort: 31102
    name: imes-dev-port
  selector:
    app: mom-test-dev
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-performanc-service
  labels:
    app: imes-performanc-service
spec:
  ports:
  - port: 10086
    protocol: TCP
    targetPort: 10086
    nodePort: 31086
    name: imes-pref-port
  selector:
    app: mom-test-pref
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-ene-service
  labels:
    app: imes-ene-service
spec:
  ports:
  - port: 3100
    protocol: TCP
    targetPort: 3100
    nodePort: 31000
    name: imes-ene-port
  selector:
    app: mom-test-ene
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-lims-service
  labels:
    app: imes-lims-service
spec:
  ports:
  - port: 6100
    protocol: TCP
    targetPort: 6100
    nodePort: 31610
    name: imes-lims-port
  selector:
    app: mom-test-lims
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-tenant-service
  labels:
    app: imes-tenant-service
spec:
  ports:
  - port: 10087
    protocol: TCP 
    targetPort: 10087
    nodePort: 30087
    name: imes-ten-port
  selector:
    app: mom-test-tenant
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-food-service
  labels:
    app: imes-food-service
spec:
  ports:
  - port: 11081
    protocol: TCP 
    targetPort: 11081
    nodePort: 31081
    name: imes-food-port
  selector:
    app: mom-test-food
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-qms-service
  labels:
    app: imes-qms-service
spec:
  ports:
  - port: 6001
    protocol: TCP 
    targetPort: 6001
    nodePort: 31886
    name: imes-qms-port
  selector:
    app: mom-test-qms
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-crm-service
  labels:
    app: imes-crm-service
spec:
  ports:
  - port: 10088
    protocol: TCP 
    targetPort: 10088
    nodePort: 31888
    name: imes-crm-port
  selector:
    app: mom-test-crm
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-hse-service
  labels:
    app: imes-hse-service
spec:
  ports:
  - port: 9953
    protocol: TCP
    targetPort: 9953
    nodePort: 30053
    name: imes-hse-port
  selector:
    app: mom-test-hse
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-flowable-service
  labels:
    app: imes-flowable-service
spec:
  ports:
  - port: 1587
    protocol: TCP
    targetPort: 1587
    nodePort: 30055
    name: imes-flowable-port
  selector:
    app: mom-test-flowable
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-po-service
  labels:
    app: imes-po-service
spec:
  ports:
  - port: 1200
    protocol: TCP
    targetPort: 1200
    nodePort: 30056
    name: imes-po-port
  selector:
    app: mom-test-po
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-scs-service
  labels:
    app: imes-scs-service
spec:
  ports:
  - port: 1300
    protocol: TCP
    targetPort: 1300
    nodePort: 30057
    name: imes-scs-port
  selector:
    app: mom-test-scs
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-fes-service
  labels:
    app: imes-fes-service
spec:
  ports:
  - port: 8390
    protocol: TCP
    targetPort: 8390
    nodePort: 30058
    name: imes-fes-port
  selector:
    app: mom-test-fes
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-hr-service
  labels:
    app: imes-hr-service
spec:
  ports:
  - port: 10090
    protocol: TCP
    targetPort: 10090
    nodePort: 30059
    name: imes-hr-port
  selector:
    app: mom-test-hr
  type: NodePort
---
apiVersion: v1
kind: Service
metadata:
  name: imes-plugins-service
  labels:
    app: imes-plugins-service
spec:
  ports:
  - port: 9956
    protocol: TCP
    targetPort: 9956
    nodePort: 30060
    name: imes-plugins-port
  selector:
    app: mom-test-plugins
  type: NodePort