package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购订单回复记录")	
@QueryModel(	
        name = "1201",	
        remark = "采购订单回复记录",	
        searchApi = "/api/po/poPurchaseChange/queryList",	
        alias = {"po_purchase_change", "po_purchase_detail_change"}	
)	
public class PoPurchaseChangeQueryVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
	@ApiModelProperty("子订单id")	
    @QueryField(name = "子订单id", show = false, alias = "po_purchase_detail_change.id")	
    private String detailId;	
	
	@ApiModelProperty("mainId")	
    @QueryField(name = "mainId", show = false, alias = "po_purchase_detail_change")	
    private String mainId;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, type = Type.Date)	
    private String createOn;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号")	
    private String purchaseNo;	
	
	@ApiModelProperty("需求版本号")	
    @QueryField(name = "需求版本号")	
    private String versionNo;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号",alias = "po_purchase_detail_change")	
    private String sdNo;	
	@ApiModelProperty("采购单名称")	
    @QueryField(name = "采购单名称")	
    private String purchaseName;	
	
	@ApiModelProperty("业务类型")	
    @QueryField(name = "业务类型",type = Type.MultiSelect,option = {"普通采购","普通采购","委外加工","委外加工","代管采购","代管采购"})	
    private String docType;	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_TYPE")	
    private String purchaseType;	
	
	@ApiModelProperty("制单人")	
    @QueryField(name = "制单人")	
    private String operatorName;	
	
	@ApiModelProperty("下单日期")	
    @QueryField(name = "下单日期", type = Type.Date, format = "yyyy-MM-dd", order = OrderBy.DESC)	
    private String orderDate;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("供应商联系人")	
    @QueryField(name = "供应商联系人")	
    private String supplierLinkMan;	
	
	@ApiModelProperty("供应商联系方式")	
    @QueryField(name = "供应商联系方式")	
    private String supplierLinkPhone;	
	
	@ApiModelProperty("采购部门名称")	
    @QueryField(name = "采购部门名称")	
    private String demandDepName;	
	
	@ApiModelProperty("业务员编号")	
    @QueryField(name = "业务员编号")	
    private String demandUserCode;	
	
	@ApiModelProperty("业务员名称")	
    @QueryField(name = "业务员名称")	
    private String demandUserName;	
	
/*	
	@ApiModelProperty("产品阶段")	
    @QueryField(name = "产品阶段", type = Type.MultiSelect, dictOption = "PO_PRODUCT_STAGE")	
    private String productStage;	
*/	
	
	@ApiModelProperty("已阅状态")	
    @QueryField(name = "已阅状态", type = Type.MultiSelect, option = {"0", "未阅", "1", "已阅"})	
    private String readStatus;	
	
/*    @QueryField(name = "数量状态", type = Type.MultiSelect, dictOption = "PO_QTY_STATUS", alias = ".(if(ifnull(po_purchase_detail.reply_qty, '4') != '4', if((po_purchase_detail.reply_qty - po_purchase_detail.purchase_qty) > 0, '2',if((po_purchase_detail.reply_qty - po_purchase_detail.purchase_qty) >= 0, '1', '3')),'4'))")	
    private String qtyStatus;*/	
	
	@ApiModelProperty("时间状态")	
    @QueryField(name = "时间状态", type = Type.MultiSelect, dictOption = "PO_TIME_STATUS", alias = ".(if(ifnull(po_purchase_detail_change.requested_delivery_date, '4') != '4', if((TIMESTAMPDIFF(SECOND, po_purchase_detail_change.requested_delivery_date, ifnull(po_purchase_detail_change.estimated_delivery_date, now()))) > 0, '3',if((TIMESTAMPDIFF(SECOND, po_purchase_detail_change.requested_delivery_date,ifnull(po_purchase_detail_change.estimated_delivery_date, now()))) >= 0, '1', '2')),'4'))")	
    private String timeStatus;	
	
	@ApiModelProperty("采购单备注")	
    @QueryField(name = "采购单备注")	
    private String remarks;	
	
	@ApiModelProperty("供应商提交备注")	
    @QueryField(name = "供应商提交备注")	
    private String supplierRemarks;	
	
	@ApiModelProperty("供应商拒绝原因")	
    @QueryField(name = "供应商拒绝原因")	
    private String refuseRemarks;	
	
	@ApiModelProperty("驳回原因")	
    @QueryField(name = "驳回原因")	
    private String erpRemarks;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "POPURCHASE_STATSU")	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE")	
    private String businessStatus;	
	
	@ApiModelProperty("供应商推送状态")	
    @QueryField(name = "供应商推送状态", type = Type.MultiSelect, dictOption = "PO_PUSH_STATUS")	
    private String pushStatus;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "po_purchase_detail_change")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "po_purchase_detail_change")	
    private String materialName;	
	
	@ApiModelProperty("物料规格型号")	
    @QueryField(name = "物料规格型号", alias = "po_purchase_detail_change")	
    private String specification;	
	
/*    @QueryField(name = "物料型号", alias = "po_purchase_detail_change")	
    private String materialMarker;*/	
	
	@ApiModelProperty("物料图号")	
    @QueryField(name = "物料图号", alias = "po_purchase_detail_change")	
    private String dwgNo;	
	
	@ApiModelProperty("物料旧图号")	
    @QueryField(name = "物料旧图号", alias = "po_purchase_detail_change")	
    private String oldDwgNo;	
	
	@ApiModelProperty("品牌")	
    @QueryField(name = "品牌", alias = "po_purchase_detail_change")	
    private String brand;	
	
	@ApiModelProperty("标准件/非标件")	
    @QueryField(name = "标准件/非标件", alias = "po_purchase_detail_change")	
    private String standardPart;	
	
	@ApiModelProperty("物料刻印号")	
    @QueryField(name = "物料刻印号", alias = "po_purchase_detail_change")	
    private String engravingNumber;	
	
	@ApiModelProperty("行版本")	
    @QueryField(name = "行版本", alias = "po_purchase_detail_change")	
    private String detailVer;	
	
   /* @QueryField(name = "行单据状态",alias = "po_purchase_detail_change", type = Type.MultiSelect, option = {"10","采购已提交","20","采购已生效","30","供应商拒绝"})	
    private String detailStatus;*/	
	
	@ApiModelProperty("行锁定状态")	
    @QueryField(name = "行锁定状态",alias = "po_purchase_detail_change", type = Type.MultiSelect, option = {"10","未锁定","20","已锁定"})	
    private String lockupStatus;	
	
/*    @QueryField(name = "推送供应商状态",alias = "po_purchase_detail_change", type = Type.MultiSelect, dictOption = "PO_PUSH_STATUS")	
    private String supplierStatus;*/	
	
	@ApiModelProperty("行业务状态")	
    @QueryField(name = "行业务状态", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE", alias = "po_purchase_detail_change.business_status")	
    private String detailBusinessStatus;	
	
	@ApiModelProperty("产品类型")	
    @QueryField(name = "产品类型", type = Type.Select, alias = "po_purchase_detail_change", treeKey = {"typeCode", "parentCode", "0"}, sqlOption = "select type_code as value, type_name as label, type_code as typeCode, parent_type_code as parentCode from sys_material_type order by type_code")	
    private String materialTypeCode;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量", alias = "po_purchase_detail_change")	
    private String purchaseQty;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", alias = "po_purchase_detail_change")	
    private String arrivalQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "po_purchase_detail_change")	
    private String unit;	
	
	@ApiModelProperty("折扣率(%)")	
    @QueryField(name = "折扣率(%)", alias = "po_purchase_detail_change")	
    private String discountRate;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "po_purchase_detail_change", option = {"0", "是", "1", "否"}, type = Type.MultiSelect)	
    private String includeTax;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(po_purchase_detail_change.tax_rate, 2), 0) * 100)", format = "{}%")	
    private String taxRate;	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", alias = "po_purchase_detail_change")	
    private String singlePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", alias = "po_purchase_detail_change", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", alias = "po_purchase_detail_change", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", alias = "po_purchase_detail_change", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", alias = "po_purchase_detail_change", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税总额")	
    @QueryField(name = "价税总额", alias = "po_purchase_detail_change", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("是否发运拆分")	
    @QueryField(name = "是否发运拆分", sort = false, option = {"1", "拆分", "0", "正常"}, type = Type.Select, alias = ".(if(ifnull((select count(1) from po_purchase_detail_item_change g where g.main_id = po_purchase_detail_change.id group by version order by version desc limit 1),0)>1,'1','0'))")	
    private String forwardingSplit;	
	
	@ApiModelProperty("U8计划到货时间")	
    @QueryField(name = "U8计划到货时间", type = Type.Date, alias = "po_purchase_detail_change", format = "yyyy-MM-dd")	
    private String estimatedDeliveryDate;	
	
	@ApiModelProperty("需求回复数量")	
    @QueryField(name = "需求回复数量", alias = "po_purchase_detail_change")	
    private String replyQty;	
	
	@ApiModelProperty("需求回复时间")	
    @QueryField(name = "需求回复时间", type = Type.Date, alias = "po_purchase_detail_change", format = "yyyy-MM-dd")	
    private String requestedDeliveryDate;	
	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "po_purchase_detail_change.remarks")	
    private String detailRemarks;	
}	
