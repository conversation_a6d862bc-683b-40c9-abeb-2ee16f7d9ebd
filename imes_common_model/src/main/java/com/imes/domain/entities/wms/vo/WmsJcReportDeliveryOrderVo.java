package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportDeliveryOrderVo {	
	
	
    /**	
     * 出库单号	
     */	
    @ApiModelProperty("出库单号")	
    private String doCode;	
	
    /**	
     * 客户	
     */	
    @ApiModelProperty("客户")	
    private String customerName;	
	
    /**	
     * 制单时间	
     */	
    @ApiModelProperty("制单时间")	
    private String createOn;	
	
    /**	
     * 制单人	
     */	
    @ApiModelProperty("制单人")	
    private String userName;	
	
}	
