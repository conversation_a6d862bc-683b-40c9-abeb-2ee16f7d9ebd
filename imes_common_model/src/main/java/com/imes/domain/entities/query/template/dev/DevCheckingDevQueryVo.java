package com.imes.domain.entities.query.template.dev;

import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@ApiModel(value = "点检设备高级查询模型")
@Data
@QueryModel(
        name = "0034_1",
        remark = "点检设备",
        auth = Auth.PC,
        resume = "id",
        searchApi = "/dev/checking/recordDev/queryAll"
)
public class DevCheckingDevQueryVo extends BaseModel {

    @QueryField(name = "id", show = false)
    private String id;

    @QueryField(name = "点检单号")
    private String checkingRecordNo;

    @QueryField(name = "设备编码")
    private String devCode;

    @QueryField(name = "设备名称")
    private String devName;

    @QueryField(name = "状态")
    private String status;

    @QueryField(name = "主修人", flowableApprovalUser = true)
    private String receiveUserCode;

    @QueryField(name = "辅修人", flowableApprovalUser = true)
    private String minorUserCode;

    @QueryField(name = "领取时间", type = Type.DateTime)
    private String receiveTime;

    @QueryField(name = "执行部门负责人", show = false, flowableApprovalUser = true)
    private List<String> approvalUserCodeList;
}
