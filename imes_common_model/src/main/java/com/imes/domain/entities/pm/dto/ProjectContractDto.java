package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (ProjectContract)实体类
 *
 * <AUTHOR>
 * @since 2021-04-29 15:35:42
 */
@ApiModel("项目合同实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectContractDto implements Serializable {
    private static final long serialVersionUID = -40537976378943342L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    @ApiModelProperty("id")
    @NotBlank(message = "合同id不能为空", groups = UpdateGroup.class)
    @Null(message = "合同id必须为空", groups = AddGroup.class)
    private String id;

    @ApiModelProperty("项目id")
    @NotBlank(message = "项目id不能为空")
    private String projectId;

    @ApiModelProperty("合同号")
    @NotBlank(message = "合同号不能为空")
    private String contractId;

    @ApiModelProperty("客户合同号")
    private String customerContractId;

    @ApiModelProperty("合同金额")
    private BigDecimal contractAmount;

    @ApiModelProperty("开票金额")
    private BigDecimal invoicedAmount;

    @ApiModelProperty("到款金额")
    private BigDecimal amountReceived;

    @ApiModelProperty("合同来源")
    private String source;

    @ApiModelProperty("创建人")
    private String createdBy;

    @ApiModelProperty("修改人")
    private String updatedBy;

    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
}