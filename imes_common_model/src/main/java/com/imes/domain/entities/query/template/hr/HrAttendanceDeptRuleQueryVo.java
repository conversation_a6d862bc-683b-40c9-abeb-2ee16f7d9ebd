package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr组织考勤规则(HrAttendanceDeptRule)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-24 13:30:17	
 */	
@Data	
@ApiModel("组织考勤管理")	
@QueryModel(name = "0782",	
        remark = "组织考勤管理",	
        alias = "hr_attendance_dept_rule",	
        searchApi = "/api/hr/attendance/rule/user/query")	
public class HrAttendanceDeptRuleQueryVo  extends BaseModel {	
	
    /**	
     * 组织编码	
     */	
	@ApiModelProperty("部门编码")	
    @QueryField(name = "部门编码" )	
    private String deptCode;	
    /**	
     * 假日计划名称	
     */	
	@ApiModelProperty("假日计划名称")	
    @QueryField(name = "假日计划名称" )	
    private String holidayPlanName;	
    /**	
     * 班次编码	
     */	
	@ApiModelProperty("班次名称")	
    @QueryField(name = "班次名称" )	
    private String shiftName;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人编码")	
    @QueryField(name = "创建人编码", show = false)	
    private String createBy;	
	
    /**	
     * 部门名称	
     */	
	@ApiModelProperty("部门名称")	
    @QueryField(name = "部门名称")	
    private String deptName;	
	
}	
	
