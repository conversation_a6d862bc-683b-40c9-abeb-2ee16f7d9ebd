package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.ppc.po.PpcProducePlanSchedulSn;
import com.imes.domain.entities.scs.po.ScsSaleDetail;
import com.imes.domain.entities.scs.po.ScsSaleDetailItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ScsSaleDetailVo extends ScsSaleDetail {
    //明细
    private List<ScsSaleDetailItem> poPurchaseDetailItemVoList;

    private String uniqueCode;

    private String soNo;

    private String soName;

    private String customerCode;

    private String customerName;

    private Date receiveDate;

    // 待发货数量
    private BigDecimal waitForSendNum;

    private List<PpcProducePlanSchedulSn> snList;

    //商品类型名称
    private String productTypeName;

    //用于判断子单是否需要提示联动关闭子项
    private String checkIsNeed;

    //用于判断累计和订单的查
    private BigDecimal displayNum;
    //更新标志 add update delete
    private String updateFlg;
    //交货前置期天
    private Integer beforeShipDate;
    //在途时间
    private Integer shipDeliverDate;

    private int indexSort;

    private String memonicCode;

    private String supplyModelNumber;

    private String supplyDwgNo;
}
