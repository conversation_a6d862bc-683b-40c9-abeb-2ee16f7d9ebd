package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.OutBillInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;	
import com.imes.domain.entities.qms.po.ReturnBillInspection;	
import com.imes.domain.entities.qms.po.ReturnBillInspectionDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class ReturnBillInspectionInfoVo implements Serializable {	
	
    /**	
     * 主信息	
     */	
	@ApiModelProperty("主信息")	
    ReturnBillInspection main;	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    Map<String,List<ReturnBillInspectionDetail>> detail;	
	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 发货信息	
     */	
	@ApiModelProperty("发货信息")	
    List<MaterialBuyOrderVo> info;	
	
    /**	
     * 合并条目	
     */	
	@ApiModelProperty("合并条目")	
    List<String> orderIds;	
	
	
    private static final long serialVersionUID = 1L;	
}	
