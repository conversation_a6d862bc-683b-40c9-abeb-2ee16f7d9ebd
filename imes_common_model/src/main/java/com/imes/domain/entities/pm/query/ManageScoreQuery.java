package com.imes.domain.entities.pm.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《管理考评》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-06-03 10:14
 */
@ApiModel("《管理考评》查询条件")
@Builder
@Getter
@Setter
public class ManageScoreQuery {
    @ApiModelProperty("人员工号")
    private String employeeCode;
    @ApiModelProperty("人员姓名")
    private String employeeName;
    @ApiModelProperty("考评周期")
    private Integer cycle;
    @ApiModelProperty("考评状态")
    private Integer state;
    @ApiModelProperty("审批状态")
    private Integer approvalStatus;
    @ApiModelProperty("时间范围")
    private List<LocalDate> dates;
}
