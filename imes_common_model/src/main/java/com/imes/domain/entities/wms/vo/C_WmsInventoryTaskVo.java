package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import io.swagger.annotations.ApiModelProperty;	
import io.swagger.annotations.ApiParam;	
import lombok.Data;	
import lombok.EqualsAndHashCode;	
import org.springframework.format.annotation.DateTimeFormat;	
	
import java.util.Date;	
	
/**	
 * @author: zhangbo	
 * @email: <EMAIL>	
 * @description:	
 * @date: 2023/6/26 15:51	
 */	
@Data	
@EqualsAndHashCode(callSuper = false)	
public class C_WmsInventoryTaskVo extends BaseModel {	
	
    /**	
     * 盘点单号	
     */	
	@ApiModelProperty("盘点单号")	
    @ApiParam("盘点单号")	
    private String inventoryCode;	
	
	
    @ApiParam("仓库名称")	
    private String whName;	
	
	
    /**	
     * 盘点类型	
     */	
	@ApiModelProperty("盘点类型")	
    @ApiParam("盘点类型")	
    private String checkType;	
	
    /**	
     * 盘点状态	
     */	
	@ApiModelProperty("盘点状态")	
    @ApiParam("盘点状态")	
    private String checkStatus;	
	
    @ApiParam("操作员名称")	
    private String checkByName;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @ApiParam("盘点时间")	
    @DateTimeFormat(pattern = "yyyy-MM-dd")	
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")	
    private Date createOn;
	
    private Date startDate;	
	
    private Date endDate;	
	
	
}	
