package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《项目》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-28 14:49
 */
@ApiModel("《项目》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpProjectQuery extends BaseQuery {
    @ApiModelProperty("项目编号")
    private String bid;
    @ApiModelProperty("项目名称")
    private String vXmMc;
    @ApiModelProperty("所属企业")
    private String gCName;
    @ApiModelProperty("客户名称")
    private String cidName;
}
