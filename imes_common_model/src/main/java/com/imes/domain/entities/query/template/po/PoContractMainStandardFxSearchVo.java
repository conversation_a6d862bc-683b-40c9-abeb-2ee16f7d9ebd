package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.Logic;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryField;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import com.imes.domain.entities.query.template.ppc.PpcContractMainStandardSearchVo;	
import lombok.Data;	
	
	
@Data	
@ApiModel("采购合同管理(反选使用)")	
@QueryModel(	
        name = "1363-fx",	
        remark = "采购合同管理(反选使用)",	
        searchApi = "/api/po/poContractMainStandard/queryList",	
        alias = {"po_contract_main_standard", "po_contract_detail_standard"},	
        customBind = "1363")	
public class PoContractMainStandardFxSearchVo extends PoContractMainStandardSearchVo {	
	@ApiModelProperty("主单单据状态")	
    @QueryField(name = "主单单据状态", type = Type.MultiSelect,alias = "po_contract_main_standard",dictOption = "SALE_BILL_CODE", value = "30", query = false,required = true)	
    private String status;	
	
	@ApiModelProperty("主单业务状态")	
    @QueryField(name = "主单业务状态", type = Type.MultiSelect,alias = "po_contract_main_standard", dictOption = "SALE_BUSINESS_CODE", value = "10", query = false,required = true)	
    private String businessStatus;	
	
	@ApiModelProperty("子单业务状态")	
    @QueryField(name = "子单业务状态", type = Type.MultiSelect, alias = "po_contract_detail_standard.business_status", dictOption = "SALE_BUSINESS_CODE",value = "10",query = false,required = true)	
    private String detailBusinessStatus;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码",alias = "po_contract_main_standard",query = false , logic = Logic.Eq,required = true)	
    private String supplierCode;	
	
	
}	
	
