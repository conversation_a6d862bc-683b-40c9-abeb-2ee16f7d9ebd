package com.imes.domain.entities.query.template.crm;

import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import io.swagger.annotations.ApiModel;
import lombok.Data;


@Data
@ApiModel("crm-客户管理")
@QueryModel(name = "0455",
        remark = "crm-客户管理",
        searchApi = "/crm/customer/queryCustomerQueryVoList")
public class CrmHighSeaQueryVo extends BaseModel {
    /**
     * id
     */

    private String id;
    /**
     * 客户编号
     */
    @QueryField(name = "客户编号")
    private String code;
    /**
     * 客户名称
     */
    @QueryField(name = "客户名称")
    private String name;
    /**
     * 客户类型
     */
    @QueryField(name = "客户类型")
    private String type;
    /**
     * 客户来源
     */
    @QueryField(name = "客户来源")
    private String clueSource;
    /**
     * 客户所有人
     */
    @QueryField(name = "客户所有人")
    private String userCode;
    /**
     * 客户可变通性
     */
    @QueryField(name = "客户可变通性")
    private String flexibility;
    /**
     * 客户关系
     */
    @QueryField(name = "客户关系")
    private String relationship;
    /**
     * 信息化
     */
    @QueryField(name = "信息化")
    private String informatization;

    /**
     * 等级
     */
    @QueryField(name = "等级")
    private String grade;
    /**
     * 主营产品
     */
    @QueryField(name = "主营产品")
    private String mainProduct;
    /**
     * 币种
     */
    @QueryField(name = "币种")
    private String currency;
    /**
     * 电话
     */
    @QueryField(name = "电话")
    private String phone;

    /**
     * 详细地址
     */
    @QueryField(name = "详细地址")
    private String detailAddress;
    /**
     * 经纬度
     */
    @QueryField(name = "经纬度")
    private String latlng;
    /**
     * 传真
     */
    @QueryField(name = "传真")
    private String fax;
    /**
     * 网站
     */
    @QueryField(name = "网站")
    private String website;
    /**
     * 股票代码
     */
    @QueryField(name = "股票代码")
    private String stockCode;
    /**
     * 公司所有权
     */
    @QueryField(name = "公司所有权")
    private String ownership;
    /**
     * 行业
     */
    @QueryField(name = "行业")
    private String industry;
    /**
     * 行业分类
     */
    @QueryField(name = "行业分类")
    private String industryClassify;
    /**
     * 行业代码
     */
    @QueryField(name = "行业代码")
    private String industryCode;
    /**
     * 员工人数
     */
    @QueryField(name = "员工人数")
    private String employeesNumber;
    /**
     * 年收入
     */
    @QueryField(name = "年收入")
    private String annualIncome;
    /**
     * sic代码
     */
    @QueryField(name = "sic代码")
    private String sicCode;
    /**
     * 开票地址
     */
    @QueryField(name = "开票地址")
    private String billingAddress;
    /**
     * 发货地址
     */
    @QueryField(name = "发货地址")
    private String shippingAddress;
    /**
     * 备注
     */
    @QueryField(name = "备注")
    private String remarks;
}
