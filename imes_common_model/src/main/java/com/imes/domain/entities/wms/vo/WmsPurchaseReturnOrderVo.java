package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPurchaseReturnOrder;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsPurchaseReturnOrderItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPurchaseReturnOrderVo extends WmsPurchaseReturnOrder {

    @ApiModelProperty(value = "关联明细单号")
    private String receiptItemCode;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;
	
    List<WmsPurchaseReturnOrderItem> itemList;	
	
    List<WmsPurchaseReturnItemVo> itemVoList;	
	
}	
