package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
	
/**	
 * hr加班申请表(HrOvertimeOrder)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-28 10:44:11	
 */	
@Data	
@ApiModel("加班申请表管理")	
@QueryModel(name = "0787",	
        remark = "加班申请表管理",	
        alias = "hr_overtime_order",	
        searchApi = "/api/hr/attendance/query/leave")	
public class HrOvertimeOrderQueryVo extends BaseModel {	
	
    /**	
     * 加班单号	
     */	
	@ApiModelProperty("加班单号")	
    @QueryField(name = "加班单号" )	
    private String overtimeNo;	
    /**	
     * 用户编码	
     */	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码" )	
    private String userCode;	
    /**	
     * 加班日期	
     */	
	@ApiModelProperty("加班日期")	
    @QueryField(name = "加班日期", type = Type.Date)	
    private String overtimeDay;	
    /**	
     * 审核状态	
     */	
	@ApiModelProperty("审核状态")	
    @QueryField(name = "审核状态" )	
    private String auditStatus;	
    /**	
     * 用户编码	
     */	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码" )	
    private String userName;	
}	
	
