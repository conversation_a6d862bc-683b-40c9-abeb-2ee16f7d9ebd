package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class DevActualTimeData implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 设备编码
     */
    @ApiModelProperty(value = "设备编码", dataType = "String")
    private String devCode;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String devName;

    /**
     * 点位
     */
    @ApiModelProperty(value = "参数点位")
    private String searchPoint;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建日期
     */
    private Date createOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新日期
     */
    private Date updateOn;

    /**
     * 点位实时值
     */
    private BigDecimal value;

    /**
     * 点位值类型：根据点位参数类型有温度、电压、电流
     */
    private String valueType;

    /**
     * 点位值上限
     */
    private BigDecimal valueMax;

    /**
     * 点位值下限
     */
    private BigDecimal valueMin;

    /**
     * 状态：0异常，1正常
     */
    private Integer status;

    /**
     * 点位值名称
     */
    private String valueTypeName;

    /**
     * 对应参数的id
     */
    private String paramId;

    private static final long serialVersionUID = 1L;
}