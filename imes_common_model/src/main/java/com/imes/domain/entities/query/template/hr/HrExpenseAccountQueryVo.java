package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * hr费用报销表(HrExpenseAccount)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-03-09 09:53:43	
 */	
@Data	
@ApiModel("费用报销管理")	
@QueryModel(name = "0679",	
        remark = "费用报销管理",	
        alias = "expense",	
        searchApi = "/api/hr/expense/query")	
public class HrExpenseAccountQueryVo extends BaseModel {	
	
    /**	
     * 费用明细	
     */	
	@ApiModelProperty("费用明细")	
    @QueryField(name = "费用明细" )	
    private String expenseCode;	
    /**	
     * 凭证单号	
     */	
	@ApiModelProperty("凭证单号")	
    @QueryField(name = "凭证单号" )	
    private String voucherNumber;	
    /**	
     * 支付时间	
     */	
	@ApiModelProperty("支付时间")	
    @QueryField(name = "支付时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private Date payTime;	
    /**	
     * 人员编码	
     */	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称" )	
    private String userCode;	
    /**	
     * 销售合同单号	
     */	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称" )	
    private String salesContract;	
    /**	
     * 备注	
     */	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称" )	
    private String remarks;	
    /**	
     * 是否提供发票	
     */	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称", type = Type.Select , option = {"是", "否"} )	
    private String provideInvoice;	
	
	
}	
	
