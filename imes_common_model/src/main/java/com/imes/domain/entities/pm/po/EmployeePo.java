package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-01-11 9:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pe_user", resultMap = "BaseResultMap")
public class EmployeePo implements Serializable {
    private static final long serialVersionUID = 513509437906183060L;
    private String id;
    private String userCode;
    private String userName;
    private String isHaveAccount;
}
