package com.imes.domain.entities.po.po;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "采购价目表主单")
public class PoPurchasePrice implements Serializable {

    private static final long serialVersionUID = 555902706411728428L;

    private String id;

    @ApiModelProperty(value = "定价单号")
    private String priceNo;

    @ApiModelProperty(value = "定价单名称")
    private String priceName;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "价目表类型")
    private String priceType;

    @ApiModelProperty(value = "定价员工号")
    private String madeUserCode;

    @ApiModelProperty(value = "定价员名称")
    private String madeUserName;

    @ApiModelProperty(value = "状态10录入20审核中30审核通过")
    private String status;

    @ApiModelProperty(value = "是否启用1启用0禁用")
    private String isEnable;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String custom;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "update-更新,add-新增,delete-删除")
    private String updateFlag;

    @ApiModelProperty(value = "采购价目子单List")
    private List<PoPurchasePriceDetail> detailList;
}