package com.imes.domain.entities.scs.po;
import java.io.Serializable;
import lombok.Data;
import java.math.BigDecimal;
import java.util.Date;
import io.swagger.annotations.ApiModelProperty;

/**
 * 锁铜对账价目表(ScsStPrice)实体类
 *
 * <AUTHOR>
 * @since 2023-07-14 14:20:04
 */
@Data
public class ScsStPrice implements Serializable {
     private static final long serialVersionUID = -78029043521935918L;
   
private String id;    
   
     @ApiModelProperty(value = "物料编码")
     private String materialCode;    
   
     @ApiModelProperty(value = "物料名称")
     private String materialName;    
   
     @ApiModelProperty(value = "规格")
     private String specification;    
   
     @ApiModelProperty(value = "图号")
     private String dwgNo;    
   
     @ApiModelProperty(value = "旧图号")
     private String oldDwgNo;    
   
     @ApiModelProperty(value = "型号")
     private String modelNumber;    
   
     @ApiModelProperty(value = "单位")
     private String unit;    
   
     @ApiModelProperty(value = "是否大宗物料")
     private String isLargeMaterial;    
   
     @ApiModelProperty(value = "是否含税 0是 1否")
     private String includeTax;    
   
     @ApiModelProperty(value = "税率")
     private BigDecimal taxRate;    
   
     @ApiModelProperty(value = "未税单价")
     private BigDecimal unIncludePrice;    
   
     @ApiModelProperty(value = "市铜未税单价")
     private BigDecimal oldUnIncludePrice;    
   
     @ApiModelProperty(value = "含税单价")
     private BigDecimal includePrice;    
   
     @ApiModelProperty(value = "税额")
     private BigDecimal taxRatePrice;    
   
     @ApiModelProperty(value = "未税额金额")
     private BigDecimal unTaxRatePrice;    
   
     @ApiModelProperty(value = "价税合计(总金额)")
     private BigDecimal allPrice;    
   
     @ApiModelProperty(value = "实际结算系数")
     private BigDecimal settlementRatio;    
   
     @ApiModelProperty(value = "批号")
     private String cbatch;    
   
     @ApiModelProperty(value = "批号日期")
     private Date cbatchDate;    
   
     @ApiModelProperty(value = "创建时间")
     private Date createOn;    
   
     @ApiModelProperty(value = "创建人")
     private String createBy;    
   
     @ApiModelProperty(value = "更新时间")
     private Date updateOn;    
   
     @ApiModelProperty(value = "更新人")
     private String updateBy;    
   
     @ApiModelProperty(value = "备注")
     private String remarks;    
   
     @ApiModelProperty(value = "损益定价时间")
     private Date settlementDate;    

}

