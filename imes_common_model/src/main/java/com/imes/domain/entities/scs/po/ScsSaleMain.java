package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "供应链销售订单")
public class ScsSaleMain implements Serializable {

    private static final long serialVersionUID = 323717891992061444L;

    private String id;

    @ApiModelProperty(value = "总订单号")
    private String soNo;

    @ApiModelProperty(value = "需求版本")
    private String versionNo;

    @ApiModelProperty(value = "总订单名")
    private String soName;

    @ApiModelProperty(value = "部门编码")
    private String departCode;

    @ApiModelProperty(value = "部门名称")
    private String departName;

    @ApiModelProperty(value = "销售业务员")
    private String salesPersonCode;

    @ApiModelProperty(value = "业务员名称")
    private String salesPersonName;

    @ApiModelProperty(value = "接单日期")
    private Date receiveDate;

    @ApiModelProperty(value = "交货日期")
    private Date deliveryDate;

    @ApiModelProperty(value = "采购商机构id")
    private String customerCode;

    @ApiModelProperty(value = "采购商名称")
    private String customerName;

    @ApiModelProperty(value = "采购商联系人id")
    private String customerLinkPerson;

    @ApiModelProperty(value = "采购商联系人名称")
    private String customerLinkPersonName;

    @ApiModelProperty(value = "采购商联系方式")
    private String customerLinkPhone;

    @ApiModelProperty(value = "公司组织编码")
    private String companyOrgCode;

    @ApiModelProperty(value = "公司组织名称")
    private String companyOrgName;

    @ApiModelProperty(value = "erp订单号")
    private String addrArea;

    @ApiModelProperty(value = "送货详细地址")
    private String addrDetail;

    @ApiModelProperty(value = "10-已录入,20-审核中,30-已审核")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "是否支付1是0否")
    private String payStatus;

    @ApiModelProperty(value = "交付方式0配送1自取")
    private String deliveryMode;

    @ApiModelProperty(value = "订单总价")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "支付时间")
    private Date payOn;

    @ApiModelProperty(value = "付款条件")
    private String termOfPayment;

    @ApiModelProperty(value = "发货类型")
    private String documentType;

    @ApiModelProperty(value = "订单单据类型")
    private String planType;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "结算公司")
    private String clearingCompany;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    private String deleteStatus;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    @ApiModelProperty(value = "当前提交版本号")
    private int version;

    //采购商备注
    private String poRemarks;
    //是否已阅
    private String readStatus;
    //回复客户备注
    private String supplierRemarks;
    //拒绝备注
    private String refuseRemarks;
    //业务类型
    private String docType;

    private String purchaseType;
}