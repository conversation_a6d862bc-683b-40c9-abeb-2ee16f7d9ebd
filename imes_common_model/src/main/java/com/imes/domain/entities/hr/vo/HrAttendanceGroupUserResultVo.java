package com.imes.domain.entities.hr.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 考勤组人员表(HrAttendanceGroupUserResultVo)实体类
 *
 * <AUTHOR> z
 * @since 2023-04-25 10:20:55
 */
@Data
public class HrAttendanceGroupUserResultVo implements Serializable {

    private static final long serialVersionUID = 2434558925415837031L;
    private String id;
    /**
     * 考勤组编码
     */
    private String groupCode;
    /**
     * 人员编码
     */
    private String userCode;
    /**
     * 日历计划编码
     */
    private String holidayPlanCode;
    /**
     * 班次编码
     */
    private String shiftCode;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createOn;
    /**
     * 创建人
     */
    private String createBy;

    private String userName;


}

