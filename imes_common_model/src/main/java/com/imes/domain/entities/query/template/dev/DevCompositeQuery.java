package com.imes.domain.entities.query.template.dev;		
		
		
import com.imes.domain.entities.query.model.base.*;		
import io.swagger.annotations.ApiModel;		
import io.swagger.annotations.ApiModelProperty;		
import lombok.Data;		
		
@ApiModel(value = "点润巡保综合查询高级查询模型")		
@Data		
@QueryModel(		
        name = "1188",		
        remark = "点润巡保综合查询",		
        searchApi = "/dev/report/compositeQuery")		
public class DevCompositeQuery extends BaseModel {		
		
    @ApiModelProperty(value = "任务分类：1-点检；2-巡检；3-润滑；4-保养")		
    @QueryField(name = "任务分类", order = OrderBy.ASC, type = Type.Select, option = {"1", "点检", "2", "巡检", "3", "润滑", "4", "保养"})		
    private String type;		
		
    @ApiModelProperty(value = "任务名称")		
    @QueryField(name = "任务名称")		
    private String planName;		
		
    @ApiModelProperty(value = "任务单号")		
    @QueryField(name = "任务单号")		
    private String recordNo;		
		
    @ApiModelProperty(value = "执行人")		
    @QueryField(name = "执行人")		
    private String executorName;		
		
    @ApiModelProperty(value = "执行部门")		
    @QueryField(name = "执行部门", alias = "co_department")		
    private String departName;		
		
    @ApiModelProperty(value = "任务状态：10-执行中；20-已完成；30-强制完成")		
    @QueryField(name = "任务状态", type = Type.Select, option = {"10", "执行中", "20", "已完成", "30", "强制完成"})		
    private String status;		
		
    @ApiModelProperty(value = "计划开始")		
    @QueryField(name = "计划开始", type = Type.Date, order = OrderBy.DESC)		
    private String planStartTime;		
		
    @ApiModelProperty(value = "计划结束")		
    @QueryField(name = "计划结束", type = Type.Date)		
    private String planEndTime;		
		
    @ApiModelProperty(value = "实际开始")		
    @QueryField(name = "实际开始", type = Type.Date)		
    private String startTime;		
		
    @ApiModelProperty(value = "实际完成")		
    @QueryField(name = "实际完成", type = Type.Date)		
    private String endTime;		
		
    @ApiModelProperty(value = "确认状态：0-未确认；1-已确认")		
    @QueryField(name = "确认状态", type = Type.Select, option = {"0", "未确认", "1", "已确认"})		
    private String confirmed;		
		
    @ApiModelProperty(value = "确认人")		
    @QueryField(name = "确认人")		
    private String confirmUsername;		
		
		
}		
