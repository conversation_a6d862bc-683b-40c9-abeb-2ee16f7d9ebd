package com.imes.domain.entities.pm.vo.gantt;

import com.imes.domain.entities.pm.po.LinkTask;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<甘特图数据>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 15:18
 */
@ApiModel("甘特图数据VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GanttCharTasksVo {
    @ApiModelProperty("任务数据")
    private List<GanttCharDataVo> data;
    @ApiModelProperty("任务数据链接")
    private List<LinkTask> links;
    @ApiModelProperty("参与人")
    private List<GanttCharParticiVo> participants;
}