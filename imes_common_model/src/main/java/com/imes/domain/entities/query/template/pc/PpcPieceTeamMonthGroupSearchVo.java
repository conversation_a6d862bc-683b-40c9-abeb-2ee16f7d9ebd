package com.imes.domain.entities.query.template.pc;

import com.imes.domain.entities.query.model.base.*;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@Data
@QueryModel(
        name = "",
        remark = "班组计件分组表",
        searchApi = "/api/pc/ppcPieceTeamMonthGroup/queryList")
public class PpcPieceTeamMonthGroupSearchVo extends BaseModel {

    @ApiModelProperty(value = "班组计件表id")
    @QueryField(name = "班组计件表id")
    private String mainId;

    @ApiModelProperty(value = "开始日期")
    @QueryField(name = "开始日期", type = Type.Date, format = "yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty(value = "截止日期")
    @QueryField(name = "截止日期", type = Type.Date, format = "yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty(value = "生产班组编码")
    @QueryField(name = "生产班组编码")
    private String teamCode;

    @ApiModelProperty(value = "班组名称")
    @QueryField(name = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "计件工段")
    @QueryField(name = "计件工段")
    private String bigProcessCode;

    @ApiModelProperty(value = "工段名称")
    @QueryField(name = "工段名称")
    private String bigProcessName;

    @ApiModelProperty(value = "小工序编码")
    @QueryField(name = "小工序编码")
    private String smallProcessCode;

    @ApiModelProperty(value = "小工序名称")
    @QueryField(name = "小工序名称")
    private String smallProcessName;

    @ApiModelProperty(value = "计件明细数量")
    @QueryField(name = "计件明细数量")
    private String detailCount;

    @ApiModelProperty(value = "总金额")
    @QueryField(name = "总金额", type = Type.Number)
    private String totalMoney;

    @ApiModelProperty(value = "个人计件金额")
    @QueryField(name = "个人计件金额", type = Type.Number)
    private String personalMoney;

    @ApiModelProperty(value = "状态")
    @QueryField(name = "状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    @QueryField(name = "创建时间", type = Type.DateTime)
    private String createOn;

    @ApiModelProperty(value = "创建人")
    @QueryField(name = "创建人", type = Type.Select, sqlOption = "select user_code as value,user_name as label from pe_user ")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    @QueryField(name = "更新时间", type = Type.Date, format = "yyyy-MM-dd")
    private String updateOn;

    @ApiModelProperty(value = "更新人")
    @QueryField(name = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    @QueryField(name = "备注")
    private String remarks;


}

