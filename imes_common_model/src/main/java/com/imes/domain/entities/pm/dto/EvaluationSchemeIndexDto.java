package com.imes.domain.entities.pm.dto;

import com.imes.domain.entities.pm.po.EvaluationSchemeAssessor;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (PerEvaluationSchemeIndex)实体类
 *
 * <AUTHOR>
 * @since 2022-01-06 17:26:58
 */
@ApiModel("绩效考评方案指标")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationSchemeIndexDto implements Serializable {
    private static final long serialVersionUID = -96778996494978376L;
    /**
    * id
    */
    private String id;
    /**
    * 方案id
    */
    private String schemeId;
    /**
    * 指标名称
    */
    private String name;
    /**
    * 指标类型
    */
    private Integer type;
    /**
    * 权重
    */
    private Integer weight;
    /**
     * 最高分
     */
    private BigDecimal highestScore;
    /**
    * 考核说明
    */
    private String rules;
    /**
    * 考核内容
    */
    private String target;
    /**
     * 指标考评人
     */
    private List<EvaluationSchemeAssessor> assessors;
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 修改人
     */
    private String updatedBy;

}