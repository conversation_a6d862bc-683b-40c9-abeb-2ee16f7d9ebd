package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import com.fasterxml.jackson.annotation.JsonFormat;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsBatchMsg implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 工厂编码	
 	
	@ApiModelProperty("工厂编码")	
     */	
    private String ftyCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 生产批次	
     */	
	@ApiModelProperty("生产批次")	
    private String batchProduction;	
	
    /**	
     * 质检批次	
     */	
	@ApiModelProperty("质检批次")	
    private String batchInspection;	
	
    /**	
     * 供应商批次	
     */	
	@ApiModelProperty("供应商批次")	
    private String batchSupplier;	
	
    /**	
     * 入库时间	
     */	
	@ApiModelProperty("入库时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date putAwayDate;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date productionDate;	
    /**	
     * 失效日期	
     */	
	@ApiModelProperty("失效日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date failureDate;	
	
    /**	
     * 质保期	
     */	
	@ApiModelProperty("质保期")	
    private BigDecimal qcPeriod;	
	
    /**	
     * 单价	
     */	
	@ApiModelProperty("单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 逾期状态（0、未逾期;1、已逾期）	
     */	
	@ApiModelProperty("逾期状态（0、未逾期;1、已逾期）")	
    private Integer dueState;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 核检状态（10：未核检；20：部分检验；30：全部检验）	
     */	
	@ApiModelProperty("核检状态（10：未核检；20：部分检验；30：全部检验）")	
    private String qualityStatus;	
	
    /**	
     * 核检结果（10：合格；20：让步接收；30：不合格）	
     */	
	@ApiModelProperty("核检结果（10：合格；20：让步接收；30：不合格）")	
    private String qualityResult;	
	
    /**	
     * 发送消息通知次数	
     */	
	@ApiModelProperty("发送消息通知次数")	
    private Integer sendTimes;	
	
    /**	
     * 批次接收数量	
     */	
	@ApiModelProperty("批次接收数量")	
    private BigDecimal receiveQty;	
	
    /**	
     * 批次入库数量	
     */	
	@ApiModelProperty("批次入库数量")	
    private BigDecimal inboundQty;	
	
    /**	
     * 质检日期	
     */	
	@ApiModelProperty("质检日期")	
    private Date qualityDate;	
	
	
    private static final long serialVersionUID = 1L;	
}	
