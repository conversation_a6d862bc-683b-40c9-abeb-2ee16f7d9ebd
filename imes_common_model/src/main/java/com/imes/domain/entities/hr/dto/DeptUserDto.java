package com.imes.domain.entities.hr.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 组织与员工关系表(DeptUser)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-29 14:57:51
 */
@ApiModel("组织与员工关系表")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeptUserDto implements Serializable {
    private static final long serialVersionUID = -39117795070622506L;
    
    private String id;
    /**
     * 替换目标部门编码
     */
    private String departmantCode;
    /**
     * 替换目标部门编码
     */
    private String deptName;
    /**
     * 岗位编码
     */
    private String jobsId;
    /**
     * 替换目标部门编码
     */
    private String jobsName;
    /**
     * 员工工号
     */
    private String userCode;
    /**
     * 主要负责人1是，0否
     */
    private String isMainLeader;
    /**
     * 原本的编码
     */
    private String originalDeptCode;
    /**
     * 修改类型状态
     */
    private Integer processType;
    private String processJson;


}

