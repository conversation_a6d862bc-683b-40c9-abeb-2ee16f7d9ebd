package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * 人才库表(HrTalentPool)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-14 13:39:27	
 */	
@Data	
@ApiModel("人才库")	
@QueryModel(name = "0705",	
        remark = "人才库",	
        alias = "hr_talent_pool",	
        searchApi = "/api/hr/talent/pool/query")	
public class HrTalentPoolQueryVo extends BaseModel {	
    /**	
     * 编码	
     */	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
    /**	
     * 姓名	
     */	
	@ApiModelProperty("姓名")	
    @QueryField(name = "姓名")	
    private String name;	
    /**	
     * 人才性别	
     */	
	@ApiModelProperty("人才性别")	
    @QueryField(name = "人才性别")	
    private Integer gender;	
    /**	
     * 人才年龄	
     */	
	@ApiModelProperty("人才年龄")	
    @QueryField(name = "人才年龄")	
    private Integer age;	
    /**	
     * 人才联系方式	
     */	
	@ApiModelProperty("人才联系方式")	
    @QueryField(name = "人才联系方式")	
    private String phone;	
    /**	
     * 人才电子邮件	
     */	
	@ApiModelProperty("人才电子邮件")	
    @QueryField(name = "人才电子邮件")	
    private String email;	
    /**	
     * 人才地址	
     */	
	@ApiModelProperty("人才地址")	
    @QueryField(name = "人才地址")	
    private String address;	
    /**	
     * 人才学历	
     */	
	@ApiModelProperty("人才学历")	
    @QueryField(name = "人才学历")	
    private String education;	
    /**	
     * 人才工作经历	
     */	
	@ApiModelProperty("人才工作经历")	
    @QueryField(name = "人才工作经历")	
    private String workExperience;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private Date createOn;	
	
	
	
	
}	
	
