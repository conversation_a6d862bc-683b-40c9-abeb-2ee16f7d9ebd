package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "延期送货申请单明细")
public class PoPurchaseChangeDetail implements Serializable {

    private static final long serialVersionUID = 218174895344053364L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "变更单号")
    private String changeCode;

    @ApiModelProperty(value = "变更版本号")
    private String changeVersion;

    @ApiModelProperty(value = "变更后交货日期")
    private Date changeDate;

    @ApiModelProperty(value = "变更前交货日期")
    private Date beginChangeDate;

    @ApiModelProperty(value = "变更状态 10待审核 20通过 30不通过")
    private String changeStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;
}