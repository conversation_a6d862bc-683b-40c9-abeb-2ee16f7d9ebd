package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr考勤假期规则(HrVacationRule)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-05-10 10:21:32	
 */	
@Data	
@ApiModel("员工信息")	
@QueryModel(name = "0655",	
        remark = "员工信息",	
        alias = "hr_user",	
        searchApi = "/api/hr/user/query")	
public class HrVacationRuleQueryVo extends BaseModel {	
	
    /**	
     * 规则年度	
     */	
	@ApiModelProperty("规则年度")	
    @QueryField(name = "规则年度")	
    private String year;	
    /**	
     * 公司	
     */	
	@ApiModelProperty("公司编号")	
    @QueryField(name = "公司编号")	
    private String company;	
    /**	
     * 休假规则类型	
     */	
	@ApiModelProperty("休假规则类型")	
    @QueryField(name = "休假规则类型")	
    private String type;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
	
	@ApiModelProperty("部门名称")	
    @QueryField(name = "部门名称")	
    private String deptName;	
	
}	
	
