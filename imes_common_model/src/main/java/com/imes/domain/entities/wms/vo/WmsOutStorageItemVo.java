package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsOutStorageItemVo {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 出库单编码	
     */	
	@ApiModelProperty("出库单编码")	
    private String storageOutCode;	
	
    /**	
     * 出库明细单编号	
     */	
	@ApiModelProperty("出库明细单编号")	
    private String storageOutItemCode;	
	
    /**	
     * 关联单据明细单号	
     */	
	@ApiModelProperty("关联单据明细单号")	
    private String receiptItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料描述	
     */	
	@ApiModelProperty("物料描述")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")	
    private String ftyCode;	
	
    /**	
     * 采购组织编码	
     */	
	@ApiModelProperty("采购组织编码")	
    private String departCode;	
	
    /**	
     * 采购组织描述	
     */	
	@ApiModelProperty("采购组织描述")	
    private String departName;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商描述	
     */	
	@ApiModelProperty("供应商描述")	
    private String supplierName;	
	
    /**	
     * 入库单物料数量	
     */	
	@ApiModelProperty("入库单物料数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 已出库数量	
     */	
	@ApiModelProperty("已出库数量")	
    private BigDecimal pickedQty;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 材质	
     */	
	@ApiModelProperty("材质")	
    private String quality;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 颜色	
     */	
	@ApiModelProperty("颜色")	
    private String color;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 批次库存	
     */	
	@ApiModelProperty("批次库存")	
    private List<WmsStockBinVo> stockBins;	
	
    /**	
     * 预计出库存储区编码	
     */	
	@ApiModelProperty("预计出库存储区编码")	
    private String areaCode;	
	
    /**	
     * 预计出库存储区名称	
     */	
	@ApiModelProperty("预计出库存储区名称")	
    private String areaName;	
	
    /**	
     * 预计出库仓位	
     */	
	@ApiModelProperty("预计出库仓位")	
    private String binCode;	
	
    private String whCode;	
	
    private String category;	
	
    private String applyBy;	
	
    private String customerName;	
	
    private String customerCode;	
	
    private String receiptType;	
	
    private String status;	
	
    private String whName;	
	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date approveOn;	
	
    private String receiptCode;	
	
    private String binName;	
	
    private String applyName;	
	
    private String purpose;	
	
    private BigDecimal theInventoryQty;	
	
    private String applyUserName;	
	
    private String pickUserName;	
	
    private String fromWhCode;	
	
    private String fromWhName;	
	
    private String fromAreaCode;	
	
    private String fromAreaName;	
	
    private String fromBinCode;	
	
    /**	
     * 可退数量	
     */	
	@ApiModelProperty("可退数量")	
    private BigDecimal returnQty;	
	
    /**	
     * 辅助属性字典编码拼接	
     */	
	@ApiModelProperty("辅助属性字典编码拼接")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	

    @ApiModelProperty(value = "包装申请数量")	
    private BigDecimal packApplyQty;	

    @ApiModelProperty(value = "包装件单位")	
    private String packCodeUnit;	
}	
