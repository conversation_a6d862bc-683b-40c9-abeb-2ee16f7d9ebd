package com.imes.domain.entities.hr.enmus;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.ibatis.type.Alias;

import java.util.Arrays;

/**
 * 用户性别
 *
 * <AUTHOR> z
 * @since 2022-06-20 15:44:39
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Alias("userSexEnum")
public enum UserSexEnum {

    MALE(0, "男"),
    FEMALE(1, "女");


    @EnumValue
    private final Integer status;
    // @JsonValue
    private final String statusName;

    UserSexEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }

    public static UserSexEnum match(Integer status) {
        return Arrays.stream(UserSexEnum.values())
                .filter(e -> e.getStatus().equals(status))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("审批状态错误"));
    }

    public static UserSexEnum getStatus(String values) {
        return Arrays.stream(UserSexEnum.values())
                .filter(e -> e.getStatusName().equals(values))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("考勤状态错误"));
    }

}
