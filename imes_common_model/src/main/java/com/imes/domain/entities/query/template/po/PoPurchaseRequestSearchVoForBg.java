package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import com.imes.domain.entities.query.template.ppc.PpcSaleMainSearchVo;	
import lombok.Data;	
	
@Data	
@ApiModel("采购申请单(变更单反选)")	
@QueryModel(	
        name = "0633-forBg",	
        remark = "采购申请单(变更单反选)",	
        searchApi = "/api/po/poPurchaseRequest/queryList",	
        alias = {"po_purchase_request", "po_purchase_request_detail"},	
        customBind = "0633"	
)	
	
public class PoPurchaseRequestSearchVoForBg extends PoPurchaseRequestQueryVo {	
	
	@ApiModelProperty("主单单据状态")	
    @QueryField(name = "主单单据状态", alias = "po_purchase_request", type = Type.MultiSelect, dictOption = "SALE_BILL_CODE", value = "30", query = false, required = true)	
    private String status;	
	
	@ApiModelProperty("主单业务状态")	
    @QueryField(name = "主单业务状态", alias = "po_purchase_request", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE", value = "10", query = false, required = true)	
    private String businessStatus;	
	
	@ApiModelProperty("子订单业务状态")	
    @QueryField(name = "子订单业务状态", type = Type.MultiSelect, alias = "po_purchase_request_detail.business_status", dictOption = "SALE_BUSINESS_CODE", value = "10", query = false, required = true)	
    private String detailBusinessStatus;	
	
	
}	
