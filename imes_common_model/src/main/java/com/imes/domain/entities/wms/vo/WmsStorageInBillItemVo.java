package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.wms.WmsPutawayTask;
import com.imes.domain.entities.wms.WmsStorageInBillItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInBillItemVo extends WmsStorageInBillItem {	
	
    /**	
     * 入库单主键	
     */	
	@ApiModelProperty("入库单主键")
    private String id;
	
	
    /**	
     * 入库单主键	
     */	
	@ApiModelProperty("入库单主键")
    private String inboundId;	
	
	
    /**	
     * 入库单编码	
     */	
	@ApiModelProperty("入库单编码")
    private String storageInCode;	
	
    /**	
     * 单据来源（如从ERP获得，从MES获取，手工建单等）	
     */	
	@ApiModelProperty("单据来源（如从ERP获得，从MES获取，手工建单等）")
    private String source;	
	
    /**	
     * 入库单类型	
     */	
	@ApiModelProperty("入库单类型")
    private String receiptType;	
	
    /**	
     * 关联的单据号	
     */	
	@ApiModelProperty("关联的单据号")
    private String receiptCode;	
	
    /**	
     * 明细表存储关联的主单据号	
     */	
	@ApiModelProperty("明细表存储关联的主单据号")
    private String itemReceiptCode;	
	
	
    /**	
     * 关联上游单据类型（1:采购订单；2:采购到货单）	
     */	
	@ApiModelProperty("关联上游单据类型（1:采购订单；2:采购到货单）")
    private String receiptSource;	
	
    /**	
     * 单据状态（1-草稿；2-未完成；3-已完成）	
     */	
	@ApiModelProperty("单据状态（1-草稿；2-未完成；3-已完成）")
    private String status;	
	
    /**	
     * 仓库号编码	
     */	
	@ApiModelProperty("仓库号编码")
    private String whCode;	
	
    /**	
     * 存储区编码	
     */	
	@ApiModelProperty("存储区编码")
    private String areaCode;	
	
    /**	
     * 仓位编码	
     */	
	@ApiModelProperty("仓位编码")
    private String binCode;	
	
    /**	
     * 存储单元	
     */	
	@ApiModelProperty("存储单元")
    private String cellCode;	
	
    /**	
     * 上架数量	
     */	
	@ApiModelProperty("上架数量")
    private BigDecimal qty;	
	
    /**	
     * 预计入库时间	
     */	
	@ApiModelProperty("预计入库时间")
    private Date expectOn;	
	
    /**	
     * 入库单申请人	
     */	
	@ApiModelProperty("入库单申请人")
    private String applyBy;	
	
    /**	
     * 入库申请人名称	
     */	
	@ApiModelProperty("入库申请人名称")
    private String applyName;	
	
    /**	
     * 操作时间	
     */	
	@ApiModelProperty("操作时间")
    private Date approveOn;	
	
    /**	
     * 操作人	
     */	
	@ApiModelProperty("操作人")
    private String approveBy;	

	
    /**	
     * 有效期	
     */	
//    (name = "有效期")
//    private Date failureDate;	
	
    /**	
     * 关联主单号	
     */
    private String receipCode;	
	
    /**	
     * 关联明细单号	
     */	
	@ApiModelProperty("关联明细单号")
    private String receiptItemCode;	
	
    /**	
     * 可用库存数量	
     */	
	@ApiModelProperty("可用库存数量")
    private BigDecimal availableQty;	
	
    /**	
     * 已入库数量	
     */	
	@ApiModelProperty("已入库数量")
    private BigDecimal theInventoryQty;	
	
    /**	
     * 税率	
     */	
	@ApiModelProperty("税率")
    private BigDecimal taxRate;	
	
    /**	
     * 入库单二级分类	
     */	
	@ApiModelProperty("入库单二级分类")
    private String purpose;	

    private String batchSupplier;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")
    private String batchNo;	
	
    /**	
     * 部门名称	
     */	
	@ApiModelProperty("部门名称")
    private String departName;	
	
    /**	
     * 关联单号，保存生产入库排产单号	
     */	
	@ApiModelProperty("关联单号，保存生产入库排产单号")
    private String parentCode;	
	
    /**	
     * 品牌	
     */	
	@ApiModelProperty("品牌")
    private String brand;	
	
    /**	
     * SN码	
     */	
	@ApiModelProperty("SN码")
    private String serialNo;	
	
    /**	
     * 第三方系统单号	
     */	
	@ApiModelProperty("第三方系统单号")
    private String thirdOrderCode;	

    private BigDecimal pickedQty;

    private String isSendOut;	
	
    /**	
     * 是否审核	
     */	
	@ApiModelProperty("是否审核")
    private String isConfirm;	
	
    /**	
     * 创建人名称	
     */	
	@ApiModelProperty("创建人名称")
    private String userName;	

    private List<WmsPutawayTask> taskList;	
	
    private List<WmsPutawayTaskVo> taskVoList;	
	
    private List<WmsBusTaskHistoryVo> historiesList;	

    private String isSerial;	

    private String autoGenerateSerialNum;	

    private String isBatch;	

    private String autoGenerateBatchNum;
	
    /**	
     * 销售数量	
     */	
	@ApiModelProperty("销售数量")
    private String saleQty;	
	
    /**	
     * 完成数量	
     */	
	@ApiModelProperty("完成数量")
    private String finishQty;	

    private Integer packInStorageQtyPrecision;	
	
    /**	
     * 是否赠品	
     */	
	@ApiModelProperty("是否赠品")
    private String giftType;	
	
    /**	
     * 明细id	
     */	
	@ApiModelProperty("明细id")
    private String itemId;

    /**
     * 明细id
     */
    @ApiModelProperty("明细id")
    private String detailId;

    /**	
     * 制造商名称	
     */	
	@ApiModelProperty("制造商名称")
    private String manufacturerName;	
	
    /**	
     * 库存说明	
     */
	@ApiModelProperty("备注")	
    private String remarks;	
	

    private String inventoryStatus;	

    private String custom;	

    private String unitName;	

    private String packCodeUnitName;

    @ApiModelProperty("仓库名称")
    private String warehouseName;
	
}	
