package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import com.imes.domain.entities.system.SysUserFieldRe;	
import com.imes.domain.entities.wms.WmsArrivalOrder;	
import com.imes.domain.entities.wms.WmsArrivalOrderItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalOrderVo extends WmsArrivalOrder {	
	
    /**	
     * 用户自定义属性	
     */
    @ApiModelProperty(value = "用户自定义属性")	
    private List<SysUserFieldRe> userFields;	
    /**	
     * 展示字段	
     */
    @ApiModelProperty(value = "展示字段")	
    private List<Map<String, Object>> showFields;	
	
    /**	
     * 到货明细单	
     */
    @ApiModelProperty(value = "到货明细单")	
    private List<WmsArrivalOrderItemVo> itemVos;	
	
    /**	
     * PC端or移动端，pc为1，移动端为0	
     */
    @ApiModelProperty(value = "PC端or移动端，pc为1，移动端为0")	
    public String pCOrMobile;	
}	
