package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "库存日结统计明细表")	
public class WmsStockBinStatistic implements Serializable {	
	
    private static final long serialVersionUID = 888832380896864285L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "工厂编码")	
    private String ftyCode;	
	
    @ApiModelProperty(value = "工厂名称")	
    private String ftyName;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "批次号")	
    private String batch;	
	
    @ApiModelProperty(value = "供应商编码")	
    private String supplierCode;	
	
    @ApiModelProperty(value = "供应商名称")	
    private String supplierName;	
	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "存储区名称")	
    private String areaName;	
	
    @ApiModelProperty(value = "仓位")	
    private String binCode;	
	
    @ApiModelProperty(value = "在库库存数量")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty(value = "可用库存")	
    private BigDecimal availableQty;	
	
    @ApiModelProperty(value = "锁定库存（是指领料时预分配的库存）")	
    private BigDecimal lockQty;	
	
    @ApiModelProperty(value = "基本单位")	
    private String unit;	
	
    @ApiModelProperty(value = "包装在库库存")	
    private BigDecimal packOnhandQty;	
	
    @ApiModelProperty(value = "包装可用库存")	
    private BigDecimal packAvailableQty;	
	
    @ApiModelProperty(value = "包装件单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如盘点，移库，补货等作业而锁定的库存")	
    private Integer lockStatus;	
	
    @ApiModelProperty(value = "库存单价")	
    private BigDecimal unitPrice;	
	
    @ApiModelProperty(value = "统计日期")	
    private Date statisticDate;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "辅助属性")	
    private String skuCode;	
	
    @ApiModelProperty(value = "箱号")	
    private String boxNo;	
	
    @ApiModelProperty(value = "进位方式")	
    private Byte carryMode;	
	
    @ApiModelProperty(value = "小数精度位数")	
    private Byte precisionDigit;	
}	
