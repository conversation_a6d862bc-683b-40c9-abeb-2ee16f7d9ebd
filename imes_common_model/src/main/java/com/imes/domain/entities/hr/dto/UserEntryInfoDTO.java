package com.imes.domain.entities.hr.dto;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.imes.domain.entities.hr.enmus.*;
import com.imes.domain.entities.hr.po.*;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


@ApiModel("用户表")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserEntryInfoDTO implements Serializable {

    private static final long serialVersionUID = 38368361214919316L;
    private String id;
    /**
     * 用户编号
     */
    @NotBlank(message = "用户编码不能为空")
    private String userCode;
    /**
     * 用户名称
     */
    @NotBlank(message = "用户名称不能为空")
    private String userName;
    /**
     * 性别（0：男；1：女）
     */
    @NotNull(message = "性别不能为空")
    @JsonDeserialize(using = UserSexEnumDeserializer.class)
    private UserSexEnum sex;
    /**
     * 手机
     */
    @NotBlank(message = "手机不为空")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$", message = "手机格式错误")
    private String mobile;
    /**
     * 上级领导
     */
    private String leaderUserCode;
    /**
     * 是否在职
     */
    private String isWorker;
    /**
     * 邮箱
     */
    @Pattern(regexp = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$", message = "邮箱格式错误")
    private String email;
    /**
     * 毕业院校
     */
    private String institutions;
    /**
     * 学历
     */
    private String qualifications;
    /**
     * 专业
     */
    private String major;
    /**
     * 头像
     */
    private String photo;
    /**
     * 证件类型
     */
    @JsonDeserialize(using = UserIDEnumDeserializer.class)
    private UserIDEnum idType;
    /**
     * 证件号
     */
//    @Pattern(regexp = "(^\\\\d{18}$)|(^\\\\d{15}$)", message = "证件号格式错误")
    private String idNumber;
    /**
     * 名族
     */
    private String nation;
    /**
     * 籍贯省市
     */
    @NotNull(message = "籍贯省市不为空")
    private String province;
    /**
     * 户籍所在地
     */
    @NotNull(message = "户籍所在地不为空")
    private String domicilePlace;
    /**
     * 户口性质 0农村 1城镇
     */
    @NotNull(message = "户口性质不为空")
    private Integer domicileType;
    /**
     * 是否已婚 0否 1是
     */
    private Integer relatives;
    /**
     * 政治面貌
     */
    @NotBlank(message = "政治面貌不为空")
    private String politicsStatus;
    /**
     * 现居住地
     */
    private String residence;
    /**
     * 所属银行
     */
    private String bank;
    /**
     * 银行卡号
     */
//    @Pattern(regexp = "^([1-9]{1})(\\d{15}|\\d{16}|\\d{18})$", message = "银行卡格式错误")
    private String bankCode;
    /**
     * 外语等级证书
     */
    private Integer englishLevel;
    /**
     * 职称证书
     */
    private String credential;
    /**
     * 证书获取时间
     */
    private Date credentialTime;
    /**
     * 健康状况
     */
    @NotNull(message = "健康状况不为空")
    private String health;
    /**
     * 招聘来源
     */
    @JsonDeserialize(using = UserRecruitSourceEnumDeserializer.class)
    private UserRecruitSourceEnum recruidSource;
    /**
     * 能否出差
     */
    private Integer evection;
    /**
     * 入职时间
     */
    private Date entryTime;
    /**
     * 转正时间
     */
    private Date regularTime;
    /**
     * 出生日期
     */
    private Date birthday;
    /**
     * 政治面貌
     */
    @NotNull(message = "员工状态必须填写")
    @JsonDeserialize(using = UserStatusEnumDeserializer.class)
    private UserStatusEnum status;
    /**
     * 创建时间
     */
    private LocalDateTime createOn;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private LocalDateTime updateOn;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 教育经历
     */
    private List<UserEducation> educations;
    /**
     * 家庭成员
     */
    private List<UserRelatives> relativeList;
    /**
     * 工作经历
     */
    private List<UserExperience> experiences;
    /**
     * 组织信息
     */
    private List<DeptUser> deptUsers;
    /**
     * 流程编码
     */
    private String jobsCode;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 岗位名称
     */
    private String jobsName;

    /**
     * 上级领导名字
     */
    private String leaderUserName;
    /**
     * 奖惩
     */
    private List<HrUserRewardPunishment> rewardPunishmentList;

    private String userEntryId;

}
