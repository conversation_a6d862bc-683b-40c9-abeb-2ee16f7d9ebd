package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.wms.WmsPickTask;
import com.imes.domain.entities.wms.WmsStorageOutBillItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsOutStorageItemQtyVo extends WmsStorageOutBillItem {	
    private BigDecimal availableQty;	
	
    private List<WmsPickTask> taskList;	
	
    private List<WmsBusTaskHistoryVo> historiesList;	
	
    private String whCode;	
    	
    private String batch;	
	
    private BigDecimal onhandQty;	
	
    private BigDecimal qty;	
	
    private String receiptType;	
	
    private String status;	
	
    private String isInStorage;	
	
    private String isConfirm;	
	
    private BigDecimal theInventoryQty;	
	
    private String purpose;	
	
    private String orderType;	
	
    private String applyOn;	
	
    private String receiptCode;	
	
    private Date approveOn;	
	
    private String applyBy;	
	
    private String applyName;

    private String approveBy;

    private String approveName;
	
    private String customerCode;	
	
    private String customerName;	
	
    private String thirdOrderCode;	
	
    private String whName;	
	
    private BigDecimal packInventoryQty;	
	
    private String fromWhCode;	
	
    private String fromWhName;	
	
    private String fromAreaCode;	
	
    private String fromAreaName;	
	
    private String fromBinCode;	
	
    private String returnPackUnit;	
	
    private BigDecimal returnPackQty;	
	
    private String serialNo;	
	
    private String parentOrderCode;	
	
    private String userName;	
	
    private BigDecimal packAvailableQty;	
	
    private String detailId;	
	
    private String workshopName;	
	
    private Integer packInventoryQtyPrecision;	
	
    private Integer theInventoryQtyPrecision;	
	
    private Integer packApplyQtyPrecision;	
	
    private String warehouseName;

    /**
     * 物料分类
     */
    private String materialTypeCode;
	
    /**	
     * 新增或更新或删除标识 1:新增 2:更新 3:删除	
     */	
	@ApiModelProperty("新增或更新或删除标识 1:新增 2:更新 3:删除")	
    private String  updateFlg;	
	
    /**	
     * 明细备注	
     */	
	@ApiModelProperty("明细备注")	
    private String detailRemark;

    @ApiModelProperty("基本单位名称")
    private String unitName;

    @ApiModelProperty("库存单位名称")
    private String packCodeUnitName;

    @ApiModelProperty("销售单名称")
    private String saleName;

    @ApiModelProperty("生产对象名称")
    private String productionObjectName;
}	
