package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.scs.po.ScsSaleMain;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ScsSaleMainDetailVo extends ScsSaleMain {


    private String detailId;

    private String detailNo;

    private String ppNo;

    private String ppId;

    private Date actualEndDate;


    //用于判断主单是否需要提示联动关闭下游单据
    private String checkIsNeed;

    //总金额
    private BigDecimal sumPrice;

    //最大到货日期
    private Date maxDeliveryDate;
    //最大发货日期
    private Date maxShipDate;
    //单据状态
    private String statusName;
    //业务单据状态
    private String businessStatusName;
    //发货类型
    private String docTypeName;
    //子单明细
    private List<ScsSaleDetailVo> scsSaleDetailVoList;

    //更新标志 add update delete
    private String updateFlg;

    private String uniqueCode;

    private String soNo;

    private String soName;

    private String custom;

    private String customerCode;

    private String customerName;

    private Date receiveDate;

    /**
     * 子单字段
     **/
    //商品类型名称
    private String productTypeName;


    /**
     * 子订单号
     */
    private String sdNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     *
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 图号
     */
    private String dwgNo;

    private String oldDwgNo;

    /*
     * 型号
     * */
    private String modelNumber;

    //型号 (目前materialMarker存到modelNumber)
    private String materialMarker;

    /**
     * 材质
     */
    private String quality;

    /**
     * 颜色
     */
    private String color;

    /**
     * 订单数量
     */
    private BigDecimal qty;

    /**
     * 已发货数量
     */
    private BigDecimal sendedQty;

    /**
     * 单位
     */
    private String unit;

    /**
     * 包装规格
     */
    private String packCode;

    /**
     * 包装规格名称
     */
    private String packName;

    /**
     * 规格数量
     */
    private BigDecimal packNum;

    /**
     * 生产标准
     */
    private String produceStandard;

    /**
     * BOM编码
     */
    private String bomCode;

    /**
     * bom版本(可指定版本，如无值，则找启用的bom版本)
     */
    private String bomVer;

    /**
     * 10-录入；20-下达；30-完工；90-终止
     */
    private String detailStatus;

    private String detailStatusName;

    /**
     * 主生产计划生成状态，0未生成，1已生成
     */
    private Integer mpsStatus;

    /**
     * 销售计划员id
     */
    private String salesPersonCode;
    /**
     * 销售计划员id
     */
    private String planerCode;
    /**
     * 销售计划员id
     */
    private String planerName;

    /**
     * 创建时间
     */
    private Date detailCreateOn;

    /**
     * 创建人
     */
    private String detailCreateBy;

    /**
     * 更新时间
     */
    private Date detailUpdateOn;

    /**
     * 更新人
     */
    private String detailUpdateBy;

    /**
     * 备注
     */
    private String detailRemarks;
    private String productInputSite;
    private String productOutputSite;

    /**
     * 商品单价 ++
     */
    private BigDecimal singlePrice;

    /**
     * 单据来源
     */
    private String documentSource;
    private String documentSourceName;
    /**
     * 发货月台
     */
    private String deliveryPlatform;

    /**
     * 发货月台
     */
    private String mergeBatch;

    /**
     * 到货日期
     */
    private Date deliveryDate;

    /**
     * 发货时间
     */
    private Date shipDate;


    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 折扣率
     */
    private BigDecimal discountRate;

    /**
     * 客户订单号
     */
    private String customerSaleNo;
    /**
     * 客户子订单号
     */
    private String customerSaleDetail;
    /**
     * 客户物料编码
     */
    private String customerMaterialCode;
    /**
     * 客户物料名称
     */
    private String customerMaterialName;
    /**
     * 客户物料规格
     */
    private String customerMaterialMarker;
    /**
     * 客户商品描述
     */
    private String customerMaterialDescription;
    /**
     * 商品类型
     */
    private String productType;
    /**
     * 客户指标
     */
    private String customerIndicator;
    /**
     * 是否含税
     */
    private String includeTax;

    private String includeTaxName;
    /**
     * 辅单位
     */
    private String auxiliaryUnit;

    /**
     * 辅单位数量
     */
    private BigDecimal auxiliaryUnitQty;
    /**
     * 业务状态
     */
    private String detailBusinessStatus;
    private String detailBusinessStatusName;

    private String productBackgroundPicName;
    private String productBackgroundViewName;
    private String productBackgroundPicUrl;
    private String customPicBase64;

    //税额
    private BigDecimal taxRatePrice;

    //不含税金额
    private BigDecimal unTaxRatePrice;

    //价税合计
    private BigDecimal allPrice;

    //不含税单价
    private BigDecimal unIncludePrice;

    //含税单价
    private BigDecimal includePrice;

    //用于判断客户+物料联合主键
    private String customerUniqueCode;

    //客户要求到货时间
    private Date customerDeliveryDate;

    private Integer detailVer;
    private String lockupStatus;

    /**
     * 助记码
     */
    private String memonicCode;

    /**
     * 供方型号
     */
    private String supplyModelNumber;

    /**
     * 供方图号
     */
    private String supplyDwgNo;

    private String brand;

    private String standardPart;
}
