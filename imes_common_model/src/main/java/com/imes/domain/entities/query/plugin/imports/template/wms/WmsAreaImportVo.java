package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import com.imes.domain.entities.query.plugin.imports.ImportRemark;
import lombok.Data;

@Data
@ImportModel(
        name = "库区信息导入模板",
        modelName = "0133")
public class WmsAreaImportVo extends BaseModel {

    @ImportField(name = "仓库编码", required = true, remark = "必填", maxLength = 40)
    private String whCode;

    @ImportField(name = "库区编码", required = true, remark = "必填", maxLength = 40)
    private String areaCode;

    @ImportField(name = "库区名称", required = true, remark = "必填", maxLength = 20)
    private String areaName;

    @ImportField(name = "库区类型", required = true, remark = "必填\n写入对应数字\n1:普通区\n2:临存区\n3:出货区", dictOption = "STORAGE_AREA_TYPE")
    private String areaTypeCode;

    @ImportField(name = "最大体积", maxNumber = "99999999.99", isNumber = true, remark = "不必填")
    private String maxVolume;

    @ImportField(name = "最大重量", maxNumber = "99999999.99", isNumber = true, remark = "不必填")
    private String maxWeight;

    @ImportField(name = "部门编码", maxLength = 40, remark = "不必填")
    private String departCode;

    @ImportField(name = "层数", isInteger = true, minNumber = "0", maxNumber = "30", remark = "不必填\n必须是整数")
    private String layer;

    @ImportField(name = "列数", isInteger = true, minNumber = "0", maxNumber = "50", remark = "不必填\n必须是整数")
    private String kind;
}
