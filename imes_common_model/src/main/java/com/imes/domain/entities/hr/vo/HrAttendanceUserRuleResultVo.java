package com.imes.domain.entities.hr.vo;

import com.imes.domain.entities.hr.po.AttendanceShift;
import com.imes.domain.entities.hr.po.HrStatutoryHoliday;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 人员考勤档案表(HrAttendanceUserRule)实体类
 *
 * <AUTHOR>
 * @since 2023-04-25 17:31:52
 */
@Data
public class HrAttendanceUserRuleResultVo  {

    private String id;
    /**
     * 人员编码
     */
    private String userCode;
    /**
     * 日历计划编码
     */
    private String holidayPlanCode;
    /**
     * 班次编码
     */
    private String shiftCode;
    /**
     * 是否打卡上班
     */
    private Integer clockIn;
    /**
     * 自动排班
     */
    private Integer autoSetting;
    /**
     * 创建时间
     */
    private Date createOn;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateOn;
    /**
     * 更新人
     */
    private String updateBy;

    private String holidayPlanName;

    private String shiftName;

    private List<HrStatutoryHoliday> holidayList;

    private List<AttendanceShift> shiftList;

    private String userName;



}

