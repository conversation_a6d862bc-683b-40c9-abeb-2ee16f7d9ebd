package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
@Data	
	
public class WmsStatisticsQtyVo {	
    /**	
     * 今日入库量	
     */	
	@ApiModelProperty("今日入库量")	
    private BigDecimal dayInboundQty;	
	
    /**	
     * 今日出库量	
     */	
	@ApiModelProperty("今日出库量")	
    private BigDecimal dayOutboundQty;	
	
    /**	
     * 本月出库量	
     */	
	@ApiModelProperty("本月出库量")	
    private BigDecimal monthOutboundQty;	
	
    /**	
     * 本月入库量	
     */	
	@ApiModelProperty("本月入库量")	
    private BigDecimal monthInboundQty;	
	
    /**	
     * 当前库存品类	
     */	
	@ApiModelProperty("当前库存品类")	
    private int curStockCategory;	
	
    /**	
     * 当前库存总量	
     */	
	@ApiModelProperty("当前库存总量")	
    private BigDecimal curTotalStockQty;	
	
    /**	
     * 当前库存金额	
     */	
	@ApiModelProperty("当前库存金额")	
    private BigDecimal curStockCost;	
}	
