package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.scs.po.ScsSaleForecast;
import com.imes.domain.entities.scs.po.ScsSaleForecastMaterail;
import com.imes.domain.entities.scs.po.ScsSaleMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoToScsForecastVo extends ScsSaleForecast {
    //子对象
    private List<ScsSaleForecastMaterailVo> scsSaleForecastMaterailVos;
    //供应商平台机构编码
    private String orgPlatfromCode;
    //用于存放供应商code+供应商名称
    private String supplierCodeName;

    private String sendMsg;

}
