package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsOutboundTaskAndItemVo {	
    @ApiModelProperty(value = "任务类型")	
    private String orderType;	
	
    @ApiModelProperty(value = "载具编码")	
    private String CellCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "任务数量")	
    private BigDecimal  orderQty;	
}	
