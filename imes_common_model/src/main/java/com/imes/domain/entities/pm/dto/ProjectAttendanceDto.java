package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.imes.domain.entities.pm.po.EmployeePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProjectAttendance)实体类
 *
 * <AUTHOR>
 * @since 2021-05-10 14:31:20
 */
@ApiModel("打卡签到实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAttendanceDto implements Serializable {
    private static final long serialVersionUID = -41797779199367817L;
    public interface AddGroup {
    }

    public interface UpdateGroup {
    }
    /**
    * id
    */
    @ApiModelProperty("id")
    @NotNull(message = "打卡id不能为空", groups = UpdateGroup.class)
    @Null(message = "打卡id必须为空", groups = AddGroup.class)
    private String id;
    /**
     * 时间范围
     */
    @ApiModelProperty("时间范围")
    private List<LocalDate> dates;
    /**
    * 工号
    */
    @ApiModelProperty("工号")
    @Null(message = "工号必须为空", groups = AddGroup.class)
    private String employeeCode;
    /**
     * 姓名
     */
    @ApiModelProperty("姓名")
    private String employeeName;
    /**
     * 工号Po
     */
    @ApiModelProperty("工号Po")
    private EmployeePo employeePo;
    /**
    * 项目id
    */
    @ApiModelProperty("项目id")
    // @NotNull(message = "项目id不能为空")
    private String projectId;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 部门编号
     */
    @ApiModelProperty("部门编号")
    private String deptCode;
    /**
     * 项目Po
     */
    @ApiModelProperty("项目Po")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ProjectDto project;
    /**
    * 打卡地点
    */
    @ApiModelProperty("打卡地点")
    @NotNull(message = "打卡地点不能为空")
    private String address;
    /**
     * 打卡详细地点
     */
    @ApiModelProperty("打卡详细地点")
    @NotNull(message = "打卡详细地点不能为空")
    private String detailAddress;
    /**
     * 经纬度
     */
    @ApiModelProperty("经纬度")
    @NotNull(message = "打卡经纬度不能为空")
    private String latlng;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
    * 审核状态（0：待审核 , 1：审核通过，2：审批中, 3：已驳回）
    */
    @ApiModelProperty("审核状态")
    private Integer approvalStatus;
}