package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "销售预测计划需求回复表")
public class ScsSaleForecastDetail implements Serializable {

    private static final long serialVersionUID = -45246413490881760L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "回复数量")
    private BigDecimal replyQty;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private int version;

}