package com.imes.domain.entities.hr.enmus;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;

/**
 * 部门类型
 *
 * <AUTHOR> z
 * @since 2022-06-20 15:44:39
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DeptTypeEnum {

    DEPARTMENT_TYPE_PRODUCE("001", "生产班组"),
    DEPARTMENT_TYPE_LIMS("002", "化验班组"),
    DEPARTMENT_TYPE_WHOUSE("003", "仓库班组"),
    DEPARTMENT_TYPE_MARKET("004", "销售部门"),
    DEPARTMENT_TYPE_DEPT("005", "化验部门"),
    DEPARTMENT_TYPE_WORKSHOP("006", "生产车间"),
    DEPARTMENT_TYPE_SAMPLE("007", "取样小组"),
    DEPARTMENT_TYPE_WAREHOUSE("008", "仓储部门"),
    DEPARTMENT_TYPE_LAB("009", "化验室"),
    REPIAIRE_GROUP("010", "维修小组"),
    CHECK_GROUP("011", "点检小组"),
    INSPECTION_GROUP("012", "点检小组"),
    LUBRICATION_GROUP("013", "润滑小组"),
    MAINTAIN_GROUP("014", "保养小组"),
    DEPARTMENT_TYPE_QMS("016", "质检班组"),
    CALIBRATION_GROUP("017", "校准小组"),
    INSPECTION_PRODUCE_GROUP("018", "生产巡检"),
    DEPARTMENT_TYPE_PURCHAS("019", "采购部门"),
    SALE_BACK_OFFICE("020", "销售内勤"),
    DEPARTMENT_MATERIAL_QMS("021", "来料检验班组"),
    DEPARTMENT_OUTBILL_QMS("023", "入库检验班组"),
    DEPARTMENT_INBILL_QMS("022", "出库检验班组"),
    DEPARTMENT_RETURN_QMS("024", "退货检验班组"),
    PLAN_TYPE_DEPT("025","计划部门");
    @EnumValue
    private final String dept;
    private final String deptName;


    DeptTypeEnum(String dept, String deptName) {
        this.dept = dept;
        this.deptName = deptName;
    }

    public String getDept() {
        return dept;
    }

    public String getDeptName() {
        return deptName;
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static DeptTypeEnum des(final JsonNode jsonNode) {
        String dept = jsonNode.isTextual() ? jsonNode.asText() : jsonNode.get("dept").asText();
        return DeptTypeEnum.match(dept);
    }

    public static DeptTypeEnum match(String dept) {
        return Arrays.stream(DeptTypeEnum.values())
                .filter(e -> e.getDept().equals(dept) || e.getDeptName().equals(dept))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("部门类型错误"));
    }
}
