package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (CostTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2021-06-21 17:03:00
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_cost_template_item",resultMap = "BaseResultMap")
public class CostTemplateItem implements Serializable {
    private static final long serialVersionUID = -99749438963675240L;
    /**
    * id
    */
    private String id;
    /**
     * 模板id
     */
    private String templateId;
    /**
     * 税收种类编号
     */
    private String code;
    /**
    * 内容
    */
    @TableField(condition = SqlCondition.LIKE)
    private String content;
    /**
    * 描述
    */
    private String description;
    /**
    * 预估金额
    */
    private BigDecimal estimatedMoney;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}