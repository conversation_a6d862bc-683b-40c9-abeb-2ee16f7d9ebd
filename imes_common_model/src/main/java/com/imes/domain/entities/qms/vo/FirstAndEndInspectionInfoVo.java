package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.FirstAndEndInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.FirstAndEndInspectionDetail;	
import com.imes.domain.entities.qms.po.ProcessTask;	
import com.imes.domain.entities.qms.po.TechnologyInspection;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class FirstAndEndInspectionInfoVo implements Serializable {	
	
    private FirstAndEndInspectionVo main;	
	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    Map<String, FirstAndEndDetailBadVo> detail;	
	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 报工信息	
     */	
	@ApiModelProperty("报工信息")	
    List<TechnologyInspection> info;	
	
    /**	
     * 合并条目	
     */	
	@ApiModelProperty("合并条目")	
    List<String> orderIds;	
    private static final long serialVersionUID = 1L;	
}	
