package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (PerEvaluationTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 10:15:40
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_template_item",resultMap = "BaseResultMap")
public class EvaluationTemplateItem implements Serializable {
    private static final long serialVersionUID = 890302171427946518L;
    /**
    * id
    */
    private String id;
    /**
    * 模板id
    */
    private String templateId;
    /**
    * 指标名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 指标类型
    */
    private Integer type;
    /**
    * 计算规则
    */
    private String rules;
    /**
    * 目标
    */
    private String target;
    /**
    * 分值
    */
    private Integer score;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

}