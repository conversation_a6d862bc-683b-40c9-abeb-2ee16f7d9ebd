package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsInventoryRatioVo {	
	
    @ApiModelProperty(value = "两周内")	
    private BigDecimal fifteenRatio;	
	
    @ApiModelProperty(value = "15-30天占比")	
    private BigDecimal thirtyRatio;	
	
    @ApiModelProperty(value = "30-60天占比")	
    private BigDecimal sixtyRatio;	
	
    @ApiModelProperty(value = "60-90天占比")	
    private BigDecimal ninetyRatio;	
	
    @ApiModelProperty(value = "90-180天占比")	
    private BigDecimal threeMonthRatio;	
	
    @ApiModelProperty(value = "180-360天占比")	
    private BigDecimal sixMonthRatio;	
	
    @ApiModelProperty(value = "360天以上占比")	
    private BigDecimal twelveMonthRatio;	
}	
