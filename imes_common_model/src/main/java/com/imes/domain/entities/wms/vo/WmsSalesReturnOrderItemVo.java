package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsSalesReturnOrderItem;	
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
	
@Data	
public class WmsSalesReturnOrderItemVo extends WmsSalesReturnOrderItem {	
	
    @ApiModelProperty("客户编码")	
    private String customerCode;	
	
    @ApiModelProperty("客户名称")	
    private String customerName;	
	
    @ApiModelProperty("默认存储仓库编码")	
    private String defaultWhCode;	
	
    @ApiModelProperty("默认存储仓库名称")	
    private String defaultWhName;	
	
    @ApiModelProperty("默认存储库位编码")	
    private String defaultBinCode;	
	
    @ApiModelProperty("规格")	
    private String specification;	
	
    @ApiModelProperty("型号")	
    private String materialMarker;	
	
    @ApiModelProperty("申请部门名称")	
    private String deptName;	
	
    @ApiModelProperty("申请部门编码")	
    private String deptCode;	
	
    @ApiModelProperty("申请人")	
    private String applyUserName;	
	
    @ApiModelProperty("申请人编码")	
    private String applyUserCode;	
	
    @ApiModelProperty("单据类型")	
    private String orderType;	
	
    @ApiModelProperty("单据状态")	
    private String orderStatus;	
	
    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    @ApiModelProperty("小数精度位数")	
    private Byte carryMode;	
	
    @ApiModelProperty("进位方式")	
    private Byte precisionDigit;	
	
    @ApiModelProperty("退货原因")	
    private String reason;

    @ApiModelProperty("是否自动生产批次")
    private String autoGenerateBatchNum;

    @ApiModelProperty("是否启用批次管理")
    private String isBatch;

    /**
     * 出库数量
     */
    private BigDecimal allowReturnNum;

    private String detailId;
}	
