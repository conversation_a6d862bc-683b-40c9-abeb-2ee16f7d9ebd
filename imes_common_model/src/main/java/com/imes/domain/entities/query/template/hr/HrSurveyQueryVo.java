package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 问卷调查表(HrSurvey)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-03-14 17:01:58	
 */	
@Data	
@ApiModel("人才库")	
@QueryModel(name = "0705",	
        remark = "人才库",	
        alias = "isu",	
        searchApi = "/api/hr/survey")	
public class HrSurveyQueryVo extends BaseModel {	
	
    private String id;	
    /**	
     * 问卷编码	
     */	
	@ApiModelProperty("问卷编码")	
    @QueryField(name = "问卷编码")	
    private String surveyNo;	
    /**	
     * 标题	
     */	
	@ApiModelProperty("标题")	
    @QueryField(name = "标题")	
    private String title;	
    /**	
     * 类型	
     */	
	@ApiModelProperty("类型")	
    @QueryField(name = "类型")	
    private String type;	
    /**	
     * 描述	
     */	
	@ApiModelProperty("描述")	
    @QueryField(name = "描述")	
    private String description;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
    /**	
     * 答题数量	
     */	
	@ApiModelProperty("答题数量")	
    @QueryField(name = "答题数量")	
    private String answerNumber;	
    /**	
     * 发布时间	
     */	
	@ApiModelProperty("发布时间")	
    @QueryField(name = "发布时间", type = Type.Date)	
    private String pushTime;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人")	
    private String createBy;	
	
	
	
}	
	
