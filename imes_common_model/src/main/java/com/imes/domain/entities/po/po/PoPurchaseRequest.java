package com.imes.domain.entities.po.po;

import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("采购申请单主单")
public class PoPurchaseRequest implements Serializable {

    private static final long serialVersionUID = -90178292971001392L;

    @JSONField(name = "id")
    private String id;

    @ApiModelProperty(value = "申请单号")
    @JSONField(name = "申请单号")
    private String requestNo;

    @JSONField(name = "申请部门编码")
    @ApiModelProperty(value = "申请部门编码")
    private String requestDepartmentCode;

    @JSONField(name = "申请部门名称")
    @ApiModelProperty(value = "申请部门名称")
    private String requestDepartmentName;

    @JSONField(name = "申请日期")
    @ApiModelProperty(value = "申请日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date requestDate;

    @JSONField(name = "申请人工号")
    @ApiModelProperty(value = "申请人工号")
    private String requestUserCode;

    @JSONField(name = "申请人姓名")
    @ApiModelProperty(value = "申请人姓名")
    private String requestUserName;

    @JSONField(name = "源单类型")
    @ApiModelProperty(value = "源单类型")
    private String businessSource;

    @JSONField(name = "单据类型10直接采购20间接采购")
    @ApiModelProperty(value = "单据类型10直接采购20间接采购")
    private String billType;

    @JSONField(name = "单据状态10已录入20提交待审核30已审核")
    @ApiModelProperty(value = "单据状态10已录入20提交待审核30已审核")
    private String status;

    @JSONField(name = "业务状态 10-正常 20-已关闭 30-合并关闭")
    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭 30-合并关闭")
    private String businessStatus;

    @JSONField(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @JSONField(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JSONField(name = "更新时间")
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @JSONField(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @JSONField(name = "备注")
    @ApiModelProperty(value = "备注")
    private String remarks;

    //更新保存标识  add  update  merge
    @ApiModelProperty(value = "update-修改,add-新增,delete-删除")
    private String updateFlag;

    @ApiModelProperty(value = "采购申请单明细List")
    private List<PoPurchaseRequestDetail> detailList;

    private String custom;

    //工作流进程ID
    private String processId;
    //变更原因
    @ApiModelProperty(value = "变更原因")
    private String changeRemarks;

    // 提交时间
    @ApiModelProperty(value = "提交时间")
    private Date submitOn;

    // 终审时间
    @ApiModelProperty(value = "终审时间")
    private Date finalHearOn;

    // 审批意见
    @ApiModelProperty(value = "审批意见")
    private String approveRemark;

    //创建模式 1下推创建
    @TableField(exist = false)
    private String createType;

}