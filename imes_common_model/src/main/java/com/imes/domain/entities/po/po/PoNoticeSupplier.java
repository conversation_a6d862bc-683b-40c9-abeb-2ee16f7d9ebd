package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "公告供应商列表")
public class PoNoticeSupplier implements Serializable {

    private static final long serialVersionUID = 758496666631242923L;

    private String id;

    @ApiModelProperty(value = "公告号")
    private String noticeNo;

    @ApiModelProperty(value = "供应商排序")
    private Integer supplierIndex;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "10已录入，20发送中，30部分成功，40全部发送成功")
    private String status;

    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "发送失败信息")
    private String sendInfo;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    private int pageNum;

    private int pageSize;
}