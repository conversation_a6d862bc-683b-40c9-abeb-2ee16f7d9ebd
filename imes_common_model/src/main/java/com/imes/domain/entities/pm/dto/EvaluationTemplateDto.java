package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (PerEvaluationTemplate)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 10:15:37
 */
@ApiModel("考评模板实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationTemplateDto implements Serializable {
    private static final long serialVersionUID = -34199375076353345L;
    /**
    * id
    */
    @ApiModelProperty("id")
    private String id;
    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
    private String name;
    /**
    * 考评类型
    */
    @ApiModelProperty("考评类型")
    private Integer type;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;

}