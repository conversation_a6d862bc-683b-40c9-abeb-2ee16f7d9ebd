package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 10:45
 */
@ApiModel("人员项目工时")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectHoursVo {
    @ApiModelProperty("项目id")
    private String projectId;
    @ApiModelProperty("项目名称")
    private String name;
    @ApiModelProperty("计划工时")
    private BigDecimal plannedWorkHours;
    @ApiModelProperty("实际总工时")
    private BigDecimal actualWorkHours;
    @ApiModelProperty("超时")
    private BigDecimal timeOut;
    @ApiModelProperty("状态")
    private Integer status;
    @ApiModelProperty("里程碑工时")
    private List<ProjectMilePostHoursVo> milepostHoursVos;
}
