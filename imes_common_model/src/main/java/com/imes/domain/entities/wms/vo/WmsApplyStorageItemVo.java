package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import com.imes.domain.entities.wms.WmsStorageApplyItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsApplyStorageItemVo extends WmsStorageApplyItem {	
    private String whCode;	
	
    private String whName;	
	
    private String orderStatus;	
	
    private String applyUserCode;	
	
    private String applyUserName;	
	
    private String receipCode;	
	
    /**	
     * 待入库数量	
     */	
	@ApiModelProperty("待入库数量")	
    private BigDecimal inQty;	
    	
    private String applyDepartName;	
	
    private Integer isBatch;	
	
    private BigDecimal packInQty;	
	
    private String applyDepartCode;	
	
    private Integer isSku;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 销售订单号	
     */	
	@ApiModelProperty("销售订单号")	
    private String saleNo;	
	
    /**	
     * 完成数量	
     */	
	@ApiModelProperty("完成数量")	
    private BigDecimal finishQty;	
	
    private Date productionDate;	
	
    /**	
     * 申请单类型	
     */	
	@ApiModelProperty("申请单类型")	
    private String orderType;	
	
    /**	
     * 计划入库日期	
     */	
	@ApiModelProperty("计划入库日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date applyOn;

    @ApiModelProperty("是否自动生成批次")
    private String autoGenerateBatchNum;

    /**
     * 单位名称
     */
    private String unitName;
}	
