package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "采购对账-退库单")
public class PoAccountStorageReturn implements Serializable {

    private static final long serialVersionUID = 180375952510461238L;

    private String id;

    @ApiModelProperty(value = "总订单号")
    private String soNo;

    @ApiModelProperty(value = "订单行号")
    private String sdNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "退库单号")
    private String storageReturnNo;

    @ApiModelProperty(value = "退库库时间")
    private Date storageReturnTime;

    @ApiModelProperty(value = "退库数量")
    private BigDecimal storageNum;

    @ApiModelProperty(value = "对应入库单号")
    private String relationStorageInNo;

    @ApiModelProperty(value = "是否大宗物料")
    private String isLargeMaterial;

    @ApiModelProperty(value = "物料价格")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "未税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "未税额金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "价税合计(总金额)")
    private BigDecimal allPrice;

    @ApiModelProperty(value = "退库单行号")
    private String irdrowno;

    @ApiModelProperty(value = "本币单价")
    private BigDecimal icost;

    @ApiModelProperty(value = "本币金额")
    private BigDecimal imoney;

    @ApiModelProperty(value = "本币税额")
    private BigDecimal itaxprice;

    @ApiModelProperty(value = "本币价税合计")
    private BigDecimal isum;

    @ApiModelProperty(value = "对账单号")
    private String verifyNo;

    @ApiModelProperty(value = "对账状态10待对账20对账中30已对账")
    private String accountStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;
    //汇率
    private BigDecimal cexchrate;
    //业务类型
    private String cbustype;
    //采购类型编码
    private String cptcode;
    //部门编码
    private String cdepcode;
    //汇率
    private String cpersoncode;
    //收付款协议编码
    private String cvenpuomprotocol;
    private String materialName;
    private String specification;
    private String dwgNo;
    private String oldDwgNo;
    private String modelNumber;
    private String type;
    private String unit;
}