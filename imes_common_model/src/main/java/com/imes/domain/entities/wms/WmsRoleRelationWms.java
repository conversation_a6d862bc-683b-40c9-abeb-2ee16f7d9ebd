package com.imes.domain.entities.wms;	
	
import java.util.Date;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
	
@Data	
@ApiModel(value = "文档角色类目权限关系表")	
public class WmsRoleRelationWms implements Serializable {	
	
    private static final long serialVersionUID = 987146682965738870L;	
	
@ApiModelProperty(value = "流水id")	
private String id;	
	
@ApiModelProperty(value = "角色ID")	
private String wmsRoleId;	
	
@ApiModelProperty(value = "仓库编码")	
private String whCode;	
	
@ApiModelProperty(value = "仓库名称")	
private String whName;	
	
@ApiModelProperty(value = "审核0否，1是")	
private String whAuditAuth;	
	
@ApiModelProperty(value = "查看0否，1是")	
private String whQueryAuth;	
	
@ApiModelProperty(value = "更新0否，1是")	
private String whUpdateAuth;	
	
@ApiModelProperty(value = "反审核0否，1是")	
private String whUnauditAuth;	
	
@ApiModelProperty(value = "创建时间")	
private Date createdOn;	
	
@ApiModelProperty(value = "创建人")	
private String createdBy;	
}	
