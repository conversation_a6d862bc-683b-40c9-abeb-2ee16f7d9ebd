package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.*;	
import lombok.Data;	
	
	
@Data	
@ApiModel("采购申请（华顿）")	
@QueryModel(	
        name = "1219",	
        remark = "采购申请（华顿）",	
        searchApi = "/api/po/poPurchaseRequest/queryList",	
        alias = {"po_purchase_request", "po_purchase_request_detail"},	
        customBind = "0633",	
        showMode = true)	
public class PoPurchaseRequestQueryHdVo extends BaseModel {	
	
    /**	
     * 主订单id	
     */	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
    /**	
     * 申请单号	
     */	
	@ApiModelProperty("申请单号")	
    @QueryField(name = "申请单号")	
    private String requestNo;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    @QueryField(name = "申请部门编码")	
    private String requestDepartmentCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    @QueryField(name = "申请部门名称")	
    private String requestDepartmentName;	
	
    /**	
     * 申请日期	
     */	
	@ApiModelProperty("申请日期")	
    @QueryField(name = "申请日期", type = Type.Date,format = "yyyy-MM-dd")	
    private String requestDate;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    @QueryField(name = "申请人工号")	
    private String requestUserCode;	
	
    /**	
     * 申请人姓名	
     */	
	@ApiModelProperty("申请人姓名")	
    @QueryField(name = "申请人姓名")	
    private String requestUserName;	
	
    /**	
     * 源单类型	
     */	
 //   @QueryField(name = "源单类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")	
    private String businessSource;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    @QueryField(name = "单据类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_TYPE")	
    private String billType;	
	
    /**	
     * 采购申请单颜色状态	
     */	
	@ApiModelProperty("采购申请单颜色状态")	
    @QueryField(name = "采购申请单颜色状态", type = Type.MultiSelect, dictOption = "P0_REQUEST_COLOR_STATUS")	
    private String colorStatus;	
	
//    /**	
//     * 对账状态	
//     */	
//    @QueryField(name = "对账状态", type = Type.MultiSelect, dictOption = "PO_CHECK_STATUS")	
//    private String checkStatus;	
//	
//    /**	
//     * 采购下推状态	
//     */	
//    @QueryField(name = "采购下推状态", type = Type.MultiSelect, dictOption = "P0_REQUEST_PUSH_STATUS")	
//    private String pushStatus;	
	
    /**	
     * 单据状态	
     */	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS")	
    private String status;	
	
    /**	
     * 业务状态	
     */	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS",value = "10")	
    private String businessStatus;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", show = false, type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", show = false)	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", show = false, type = Type.Date)	
    private String updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    @QueryField(name = "更新人", show = false)	
    private String updateBy;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
    /**	
     * 子单id	
     */	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", show = false, alias = "po_purchase_request_detail")	
    private String detailId;	
	
    /**	
     * 行号	
     */	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号", alias = "po_purchase_request_detail")	
    private String lineNo;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "po_purchase_request_detail")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "po_purchase_request_detail")	
    private String materialName;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "po_purchase_request_detail")	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "po_purchase_request_detail")	
    private String modelNumber;	
	
    /**	
     * 申请数量	
     */	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", type = Type.Number, alias = "po_purchase_request_detail")	
    private String requestQty;	
	
    /**	
     * 申请数量单位	
     */	
	@ApiModelProperty("申请数量单位")	
    @QueryField(name = "申请数量单位", alias = "po_purchase_request_detail")	
    private String unit;	
	
    /**	
     * 关联数量	
     */	
	@ApiModelProperty("关联数量")	
    @QueryField(name = "关联数量", alias = "po_purchase_request_detail", type = Type.Number,query = false,sort = false)	
    private String relationQty;	
	
	
	@ApiModelProperty("即时库存")	
    @QueryField(name = "即时库存",type = Type.Number,query = false,sort = false)	
    private String availableQty;	
	
    /**	
     * 业务状态	
     */	
	@ApiModelProperty("子单业务状态")	
    @QueryField(name = "子单业务状态", type = Type.MultiSelect, option = {"10", "正常", "20", "已关闭"}, alias = "po_purchase_request_detail.business_status")	
    private String detailBusinessStatus;	
	
    /**	
     * 基础单位	
     */	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "po_purchase_request_detail")	
    private String baseUnit;	
	
    /**	
     * 基础单位数量	
     */	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", alias = "po_purchase_request_detail", type = Type.Number)	
    private String baseUnitQty;	
	
    /**	
     * 申请到货日期	
     */	
	@ApiModelProperty("申请到货日期")	
    @QueryField(name = "申请到货日期", type = Type.Date, alias = "po_purchase_request_detail",format = "yyyy-MM-dd")	
    private String requestArriveDate;	
	
    /**	
     * 来源单据类型	
     */	
	@ApiModelProperty("来源单据类型")	
    @QueryField(name = "来源单据类型", alias = "po_purchase_request_detail.business_source", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")	
    private String detailBusinessSource;	
	
    /**	
     * 来源单据号	
     */	
	@ApiModelProperty("来源单据号")	
    @QueryField(name = "来源单据号", alias = "po_purchase_request_detail")	
    private String businessNo;	
	
	@ApiModelProperty("来源单据行号")	
    @QueryField(name = "来源单据行号", alias = "po_purchase_request_detail")	
    private String businessLineNo;	
	
    /**	
     * 需求运算号	
     */	
	@ApiModelProperty("需求运算号")	
    @QueryField(name = "需求运算号", alias = "po_purchase_request_detail.calc_no")	
    private String calcNo;	
	
    /**	
     * 子单备注	
     */	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "po_purchase_request_detail.remarks")	
    private String detailRemarks;	
	
    /**	
     * 源申请部门 编码-名称	
     */	
	@ApiModelProperty("源申请部门编码-名称")	
    @QueryField(name = "源申请部门编码-名称", alias = "po_purchase_request_detail")	
    private String businessDepartmentName;	
	
    /**	
     * 源申请人 编码-名称	
     */	
	@ApiModelProperty("源申请人编码-名称")	
    @QueryField(name = "源申请人编码-名称", alias = "po_purchase_request_detail")	
    private String businessUserName;	
	
	
}	
