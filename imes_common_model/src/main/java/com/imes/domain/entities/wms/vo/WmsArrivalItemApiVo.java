package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalItemApiVo {	
	
    /**	
     * 到货计划明细单号	
     */	
    @ApiModelProperty("到货计划明细单号")	
    private String aoPlanItemCode;	
	
    /**	
     * 关联明细单号	
     */	
    @ApiModelProperty("关联明细单号")	
    private String receiptItemCode;	
	
    /**	
     * 物料编码----必填项	
     */	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称----必填项	
     */	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 规格	
     */	
    @ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 型号	
     */	
    @ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 批次号	
     */	
    @ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 单据数量（基本单位数量）----必填项	
     */	
    @ApiModelProperty("单据数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 基本单位----必填项	
     */	
    @ApiModelProperty("基本单位")	
    private String primaryUnit;	
	
    /**	
     * 包装单位	
     */	
    @ApiModelProperty("包装单位")	
    private String wmsMainUnit;	
	
    /**	
     * 包装单位数量	
     */	
    @ApiModelProperty("包装单位数量")	
    private BigDecimal wmsMainUnitQty;	
	
    /**	
     * 采购单价	
     */	
    @ApiModelProperty("采购单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 物料总金额	
     */	
    @ApiModelProperty("物料总金额")	
    private BigDecimal amount;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty("备注")	
    private String remarks;	
	
}	
