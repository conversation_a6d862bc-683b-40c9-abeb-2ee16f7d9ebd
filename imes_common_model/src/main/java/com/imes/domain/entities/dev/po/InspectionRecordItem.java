/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "巡检任务巡检项表")
@Entity
@Data
@Table(name = "dev_inspection_record_item")
public class InspectionRecordItem implements Serializable {

	private static final long serialVersionUID = 4686519626125640787L;

	@ApiModelProperty(value = "主键")
	@Id
	private String id;

	@ApiModelProperty(value = "记录id")
	private String recordId;

	@ApiModelProperty(value = "1: 正常；9: 异常；8：已报修 ")
	private Integer status;

	/**
	 * 0-未完成；1-已完成
	 * */
	@ApiModelProperty(value = "0-未完成；1-已完成")
	private Integer inputStatus;

	@ApiModelProperty(value = "异常处理方法")
	private String exceptionHandleMethod;

	@ApiModelProperty(value = "异常处理结果")
	private String exceptionHandleResult;

	@ApiModelProperty(value = "异常原因")
	private String exceptionReason;


	@ApiModelProperty(value = "开始时间", dataType = "Date")
	private Date startTime;

	@ApiModelProperty(value = "结束时间")
	private Date endTime;

	@ApiModelProperty(value = "花费时间")
	private Date tokeTime;

	@ApiModelProperty(value = "现场情况")
	private String realtimeData;

	@ApiModelProperty(value = "现场图片/视频")
	private String photo;

	@ApiModelProperty(value = "设备名称")
	private String devName;

	/**
	 * 巡检项信息编码
	 */
	@ApiModelProperty(value = "巡检项信息编码")
	private String itemInfoNo;


	/**
	 * 部件
	 */
	@ApiModelProperty(value = "部位名称")
	private String partsName;

	/**
	 * 巡检内容
	 */
	@ApiModelProperty(value = "巡检内容")
	private String projectName;

	/**
	 * 巡检方法
	 */
	@ApiModelProperty(value = "巡检方法")
	private String methodName;

	/**
	 * 巡检标准
	 */
	@ApiModelProperty(value = "巡检标准")
	private String criteriaName;

	/**
	 * 巡检单号
	 */
	@ApiModelProperty(value = "巡检单号")
	private String inspectionRecordNo;

	/**
	 * 设备编码
	 */
	@ApiModelProperty(value = "设备编码", dataType = "String")
	private String devCode;

	/**
	 * 2级排序
	 */
	@ApiModelProperty(value = "巡检项序号", dataType = "Integer")
	private Integer sort;

	/**
	 * 1级排序
     */
	@ApiModelProperty(value = "巡检设备序号", dataType = "Integer")
	private Integer devSort;

	@ApiModelProperty(value = "创建人")
	private String createdBy;
	@ApiModelProperty(value = "创建时间")
	private Date createdOn;
	@ApiModelProperty(value = "更新时间")
	private Date updatedOn;
	@ApiModelProperty(value = "更新人")
	private String updatedBy;

	@ApiModelProperty(value = "超期原因")
	@Transient
    private String overReason;

	@ApiModelProperty(value = "设备编码")
	@Transient
    private String devNo;

	/**
	 * 报修描述id
	 * */
	private String repairDescriptionId;

	/**
	 * 报修描述
	 * */
	@ApiModelProperty(value = "故障描述")
	private String repairDescription;

	/**
	 * 是否开启安环
	 * */
	@ApiModelProperty(value = "是否开启安环；0-不开启；1-开启")
	@Transient
	private String repairRisk;

	/**
	 * 删除的图片id
	 */
	@ApiModelProperty(value = "删除的图片id")
	@Transient
	private String delFiles;

	@ApiModelProperty(value = "设备安装位置")
	@Transient
	private String installAddress;

	/**
	 * 设备状态
	 * */
	@ApiModelProperty(value = "设备状态")
	@Transient
	private String devStatus;

}
