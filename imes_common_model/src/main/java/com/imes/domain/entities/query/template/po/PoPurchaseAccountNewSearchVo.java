package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("采购订单执行情况")	
@QueryModel(	
        name = "1310",	
        remark = "采购订单执行情况",	
        alias = "a",	
        searchApi = "/api/po/poAccountVerify/planQueryList")	
public class PoPurchaseAccountNewSearchVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
	@ApiModelProperty("采购订单号")	
    @QueryField(name = "采购订单号")	
    private String purchaseNo;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码")	
    private String supplierCode;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("业务类型")	
    @QueryField(name = "业务类型")	
    private String ywlx;	
	
	@ApiModelProperty("订单日期")	
    @QueryField(name = "订单日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String orderDate;	
	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型",dictOption = "PO_PURCHASE_TYPE",type = Type.MultiSelect)	
    private String cglx;	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种",dictOption = "CUSTOMER_CURRENCY",type = Type.MultiSelect)	
    private String currency;	
	
	@ApiModelProperty("存货编码")	
    @QueryField(name = "存货编码")	
    private String materialCode;	
	
	@ApiModelProperty("存货名称")	
    @QueryField(name = "存货名称")	
    private String materialName;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
	
	@ApiModelProperty("主计量")	
    @QueryField(name = "主计量")	
    private String unit;	
	
	@ApiModelProperty("订单数量")	
    @QueryField(name = "订单数量")	
    private String qty;	
	
	@ApiModelProperty("本币价税合计")	
    @QueryField(name = "本币价税合计", type = Type.Number, format = "0.00")	
    private String hj;	
	
	@ApiModelProperty("计划到货日期")	
    @QueryField(name = "计划到货日期",type = Type.Date, format = "yyyy-MM-dd")	
    private String planDate;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量")	
    private String dhQty;	
	
	@ApiModelProperty("最晚到货日期")	
    @QueryField(name = "最晚到货日期",type = Type.Date, format = "yyyy-MM-dd")	
    private String lastDate;	
	
	@ApiModelProperty("退库数量")	
    @QueryField(name = "退库数量")	
    private String tkQty;	
	
	@ApiModelProperty("拒收数量")	
    @QueryField(name = "拒收数量")	
    private String jsQty;	
	
	@ApiModelProperty("累计入库数量")	
    @QueryField(name = "累计入库数量")	
    private String rkQty;	
	
	@ApiModelProperty("订单未入库数量(订单数量-累计入库量)")	
    @QueryField(name = "订单未入库数量(订单数量-累计入库量)")	
    private String unrkQty;	
	
	@ApiModelProperty("累计入库金额(入库金额合计)")	
    @QueryField(name = "累计入库金额(入库金额合计)", type = Type.Number, format = "0.00")	
    private String rkhj;	
    //(退库对账数量+入库对账数量)	
	@ApiModelProperty("累计对账数量")	
    @QueryField(name = "累计对账数量", type = Type.Number)	
    private String dzQty;	
    //(退库对账金额+入库对账金额)	
	@ApiModelProperty("对账本币价税合计")	
    @QueryField(name = "对账本币价税合计", type = Type.Number, format = "0.00")	
    private String dzhj;	
    //(红字开票数量+蓝字开票数量)	
	@ApiModelProperty("累计发票数量")	
    @QueryField(name = "累计发票数量")	
    private String fpQty;	
    //(红字开票金额+蓝字开票金额)	
	@ApiModelProperty("发票本币价税合计")	
    @QueryField(name = "发票本币价税合计", type = Type.Number, format = "0.00")	
    private String fbhj;	
    //(到货数量+退库数量+拒收数量>=订单数量)	
	@ApiModelProperty("到货状态")	
    @QueryField(name = "到货状态",type = Type.MultiSelect,option = {"0","到货未完成","1","到货完成"})	
    private String dhzt;	
    //入库总数+退库总数 >= 订单数量	
	@ApiModelProperty("入库状态")	
    @QueryField(name = "入库状态",type = Type.MultiSelect,option = {"0","入库未完成","1","入库完成"})	
    private String rkzt;	
    //对账数量 == 入库+退库 && 对账数量 >= 订单数量	
	@ApiModelProperty("对账状态")	
    @QueryField(name = "对账状态",type = Type.MultiSelect,option = {"0","对账未完成","1","对账完成"})	
    private String dzzt;	
    //开票数量 == 对账数量	
	@ApiModelProperty("开票状态")	
    @QueryField(name = "开票状态",type = Type.MultiSelect,option = {"0","开票未完成","1","开票完成"})	
    private String kpzt;	
}	
