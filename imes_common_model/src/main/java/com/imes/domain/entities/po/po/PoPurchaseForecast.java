package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "采购需求预测")
public class PoPurchaseForecast implements Serializable {

    private static final long serialVersionUID = -24140058457136077L;

    private String id;

    @ApiModelProperty(value = "需求批次号")
    private String demandForecastNo;

    @ApiModelProperty(value = "需求版本")
    private String demandVersion;

    @ApiModelProperty(value = "采购员工号")
    private String demandUserCode;

    @ApiModelProperty(value = "采购员名称")
    private String demandUserName;

    @ApiModelProperty(value = "采购部门编号")
    private String demandUserDep;

    @ApiModelProperty(value = "采购部门名称")
    private String demandUserDepName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商联系人")
    private String supplierLinkMan;

    @ApiModelProperty(value = "需求预测日期")
    private Date forecastDate;

    @ApiModelProperty(value = "需求发布日期")
    private Date publishDate;

    @ApiModelProperty(value = "需求回复截止日期")
    private Date responseDeadline;

    @ApiModelProperty(value = "需求回复时间")
    private Date responseDate;

    @ApiModelProperty(value = "需求回复人姓名")
    private String responsePersonName;

    @ApiModelProperty(value = "需求状态10待发布，20已发布")
    private String demandStatus;

    @ApiModelProperty(value = "供应商回复状态10-，20待收，30已收待回复，40已回复")
    private String replyStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private String refuseReason;

    //当前版本号
    private int version;
    //供应商备注
    private String supplierRemarks;


}