package com.imes.domain.entities.dev.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(value = "点检模板点检项表")
@Data
@NoArgsConstructor
@Entity
@Table(name = "dev_checking_temp_item")
public class CheckingTempItem implements Serializable {

    private static final long serialVersionUID = 8832124337731797819L;

    @Id
    private String id;

    /**
     * 点检模板id
     */
    @ApiModelProperty(value = "模板编码")
    private String tempNo;

    @ApiModelProperty(value = "设备编码")
    private String devNo;


    private java.util.Date createdOn;
    private String createdBy;
    private String updatedBy;
    private java.util.Date updatedOn;

    /**
     * 点检项信息
     */
    @ApiModelProperty(value = "点检项信息")
    @JoinColumn(name="itemInfoNo", referencedColumnName="itemInfoNo", insertable=false, updatable=false)
    @ManyToOne(cascade = CascadeType.REFRESH)
    private CheckingItemInfo item;

    /**
     * 点检项信息编码
     */
    @ApiModelProperty(value = "点检项信息编码")
    private String itemInfoNo;
}
