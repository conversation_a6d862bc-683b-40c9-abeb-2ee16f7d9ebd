package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class GetSupplierIncomingByIdVo {	
    String id;	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    String materialCode;	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    String materialName;	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    String supplierName;	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    String supplierCode;	
    /**	
     * 是否来料检验	
     */	
	@ApiModelProperty("是否来料检验")	
    String isIncomingInspection;	
    /**	
     * 抽样方式	
     */	
	@ApiModelProperty("抽样方式")	
    String samplingMethod;	
    /**	
     * 合格判定方式	
     */	
	@ApiModelProperty("合格判定方式")	
    String conformityJudgmentMethod;	
    /**	
     * 检验水平	
     */	
	@ApiModelProperty("检验水平")	
    String inspectionLevel;	
    /**	
     * 抽检比率	
     */	
	@ApiModelProperty("抽检比率")	
    String spotCheckRatio;	
    /**	
     * 抽检数量	
     */	
	@ApiModelProperty("抽检数量")	
    String numberOfCheck;	
    /**	
     * AQL值	
     */	
	@ApiModelProperty("AQL值")	
    String aqlValue;	
    /**	
     * 合格率	
     */	
	@ApiModelProperty("合格率")	
    String rate;	
    /**	
     * 合格数量	
     */	
	@ApiModelProperty("合格数量")	
    String numberOfRate;	
	
    /**	
     * 生产许可证开始日期	
     */	
	@ApiModelProperty("生产许可证开始日期")	
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")	
    private String productionLicenseStartDate;	
	
    /**	
     * 生产许可证结束日期	
     */	
	@ApiModelProperty("生产许可证结束日期")	
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")	
    private String productionLicenseEndDate;	
	
    /**	
     * 产品注册证开始日期	
     */	
	@ApiModelProperty("产品注册证开始日期")	
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")	
    private String productRegistrationStartDate;	
	
    /**	
     * 产品注册证结束日期	
     */	
	@ApiModelProperty("产品注册证结束日期")	
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")	
    private String productRegistrationEndDate;	
	
    /**	
     * 最小装箱数量	
     */	
	@ApiModelProperty("最小装箱数量")	
    private BigDecimal minNumberOfBoxes;	
	
    /**	
     * 来料检验顺序1先到货后检验2先检验后到货	
     */	
	@ApiModelProperty("来料检验顺序1先到货后检验2先检验后到货")	
    private String incomingInspectionSequence;	
	
}	
