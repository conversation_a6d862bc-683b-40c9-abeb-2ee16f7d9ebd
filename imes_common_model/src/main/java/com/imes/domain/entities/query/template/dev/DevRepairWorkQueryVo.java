package com.imes.domain.entities.query.template.dev;		
		
import com.imes.domain.entities.query.model.base.*;		
import io.swagger.annotations.ApiModel;		
import io.swagger.annotations.ApiModelProperty;		
import lombok.Data;		
		
import java.util.Date;		
@ApiModel(value = "维修作业查询模型")		
@Data		
@QueryModel(		
        name = "0261",		
        remark = "维修作业",		
        alias = "repair",		
        searchApi = "/dev/repair/order/findPcRepairWork")		
public class DevRepairWorkQueryVo extends BaseModel {		
		
	@ApiModelProperty("id")		
    @QueryField(name = "id", show = false)		
    private String id;		
		
    /**		
     * 维修单号		
     */		
    @ApiModelProperty(value = "维修单号")		
    @QueryField(name = "维修单号")		
    private String orderNo;		
		
    /**		
     * 维修单名称		
     */		
    @ApiModelProperty(value = "维修单名称")		
    @QueryField(name = "维修单名称")		
    private String orderName;		
		
    /**		
     * 维修单类型		
     */		
    @ApiModelProperty(value = "维修类型：1-故障维修；2-定修")		
    @QueryField(name = "维修类型", type = Type.Select, option = {"1", "故障维修", "2", "定修"})		
    private String orderType;		
		
    /**		
     * 生成时间		
     */		
    @ApiModelProperty(value = "生成时间")		
    @QueryField(name = "生成时间", type = Type.Date, order = OrderBy.DESC, show = false)		
    private String createdOn;		
		
    /**		
     * 计划完成时间		
     */		
    @ApiModelProperty(value = "计划完成时间")		
    @QueryField(name = "计划完成时间", type = Type.Date)		
    private String planFinishTime;		
		
    /**		
     * 实际完成时间		
     */		
    @ApiModelProperty(value = "实际完成时间")		
    @QueryField(name = "实际完成时间", type = Type.Date)		
    private String realFinishTime;		
		
    /**		
     * 维修部门		
     */		
    @ApiModelProperty(value = "维修部门")		
    @QueryField(name = "维修部门", type = Type.Select,		
            sqlOption = "select dept.depart_name as label, dept.depart_code as value  from co_department dept inner join co_department_user du\n" +		
                    "on dept.depart_code = du.departmant_code \n" +		
                    "where FIND_IN_SET('010',dept.depart_type)>0 group by dept.depart_code, dept.depart_name\n")		
    private String deptCode;		
		
    /**		
     * 主修人		
     */		
    @ApiModelProperty(value = "主修人")		
    @QueryField(name = "主修人")		
    private String executorName;		
		
    /**		
     * 辅修人		
     */		
    @ApiModelProperty(value = "辅修人")		
    @QueryField(name = "辅修人")		
    private String minorName;		
		
    /**		
     * 维修状态		
     */		
    @ApiModelProperty(value = "维修状态：20-待执行；30-维修中")		
    @QueryField(name = "维修状态", type = Type.Select, option = {"20", "待执行", "30", "维修中"})		
    private String status;		
		
    /**		
     * 是否超期		
     */		
    @ApiModelProperty(value = "是否超期：0-正常；1-已过期")		
    @QueryField(name = "是否超期", type = Type.Select, option = {"0", "正常", "1", "已过期"})		
    private String overTime;		
		
		
    /**		
     * 超期原因		
     */		
    @ApiModelProperty(value = "超期原因")		
    @QueryField(name = "超期原因")		
    private String overReason;		
		
    /**		
     * 备注		
     */		
    @ApiModelProperty(value = "备注")		
    @QueryField(name = "备注")		
    private String remarks;		
		
		
		
    /**		
     * 主修人编码		
     */		
    @ApiModelProperty(value = "主修人编码")		
    @QueryField(name = "主修人编码", show = false)		
    private String executor;		
		
    /**		
     * 辅修人编码		
     */		
    @ApiModelProperty(value = "辅修人编码")		
    @QueryField(name = "辅修人编码", show = false)		
    private String minorCode;		
		
		
    /**		
     * 创建人		
     */		
    @ApiModelProperty(value = "创建人")		
    private String createdBy;		
		
    /**		
     * 更新时间		
     */		
    @ApiModelProperty(value = "更新时间")		
    private Date updatedOn;		
		
    /**		
     * 更新人		
     */		
    @ApiModelProperty(value = "更新人")		
    private String updatedBy;		
		
		
}		
