package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目参与人>>
 * @company 捷创智能技术有限公司
 * @create 2021-06-15 9:57
 */
@ApiModel("项目参与人")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectEmployeeVo {
    private String projectName;
    private List<Map<String, BigDecimal>> employeeHours;
    private BigDecimal totalHours;

}
