package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import lombok.Data;	
	
/**	
 * 待入职人员(HrUserEntry)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-03-30 14:36:54	
 */	
	
@Data	
@ApiModel("待入职人员")	
@QueryModel(	
        name = "0698",	
        remark = "待入职人员",	
        alias = "hr_user_entry",	
        searchApi = "/api/hr/user/extend/query")	
public class HrUserEntryQueryVo extends BaseModel {	
    /**	
     * 用户名称	
     */	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称")	
    private String userName;	
    /**	
     * 预入职部门编号	
     */	
	@ApiModelProperty("预入职部门编号")	
    @QueryField(name = "预入职部门编号")	
    private String deptCode;	
    /**	
     * 预入职岗位编号	
     */	
	@ApiModelProperty("预入职岗位编号")	
    @QueryField(name = "预入职岗位编号")	
    private String jobsCode;	
    /**	
     * 性别（0：男；1：女）	
     */	
	@ApiModelProperty("性别")	
    @QueryField(name = "性别")	
    private String sex;	
    /**	
     * 手机	
     */	
	@ApiModelProperty("手机")	
    @QueryField(name = "手机")	
    private String mobile;	
    /**	
     * 招聘来源	
     */	
	@ApiModelProperty("招聘来源")	
    @QueryField(name = "招聘来源")	
    private String recruidSource;	
    /**	
     * 用户状态	
     */	
	@ApiModelProperty("用户状态")	
    @QueryField(name = "用户状态")	
    private Integer status;	
	
    /**	
     * 预入职部门编号	
     */	
//    @QueryField(name = "人员编码")	
    private String deptName;	
    /**	
     * 预入职岗位编号	
     */	
//    @QueryField(name = "人员编码")	
    private String jobsName;	
	
}	
	
