package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
	
@Data	
@ApiModel("采购申请变更单")	
@QueryModel(	
        name = "1212",	
        remark = "采购申请变更单",	
        searchApi = "/api/po/poPurchaseRequestChange/queryList",	
        alias = {"po_purchase_request_change", "po_purchase_request_detail_change"},	
        pushApi = PushApi.PoPurchaseRequestChangeSearchVo,	
        auth = Auth.ALL,	
        authTenant = true,	
        customExp = true,
        openapi = true,
        showMode = true)	
public class PoPurchaseRequestChangeSearchVo extends BaseModel {	
	
	@ApiModelProperty("采购申请变更单号")	
    @QueryField(name = "采购申请变更单号", order = OrderBy.DESC)	
    private String changeNo;	
	
	@ApiModelProperty("申请单号")	
    @QueryField(name = "申请单号")	
    @EditField(required = true)	
    private String requestNo;	
	
	@ApiModelProperty("申请部门编码")	
    @QueryField(name = "申请部门编码")	
    @EditField(readonly = true)	
    private String requestDepartmentCode;	
	
	@ApiModelProperty("申请部门")	
    @QueryField(name = "申请部门", alias = "co_department.depart_name", level = Level.Main)	
    @EditField(required = true)	
    private String requestDepartmentName;	
	
	@ApiModelProperty("申请日期")	
    @QueryField(name = "申请日期", type = Type.Date, format = "yyyy-MM-dd")	
    @EditField(required = true)	
    private String requestDate;	
	
	@ApiModelProperty("申请人工号")	
    @QueryField(name = "申请人工号")	
    @EditField(readonly = true)	
    private String requestUserCode;	
	
	@ApiModelProperty("申请人")	
    @QueryField(name = "申请人", alias = "pe_user.user_name", level = Level.Main)	
    @EditField(required = true)	
    private String requestUserName;	
	
/*	
	@ApiModelProperty("单据来源")	
    @QueryField(name = "单据来源", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")	
    private String businessSource;	
*/	
	
	@ApiModelProperty("单据类型")	
    @QueryField(name = "单据类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_TYPE")	
    private String billType;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS")	
    @EditField(readonly = true)	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", level = Level.Main, type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS", value = "10")	
    @EditField(readonly = true)	
    private String businessStatus;	
	
	@ApiModelProperty("变更原因")	
    @QueryField(name = "变更原因")	
    @EditField(required = true)	
    private String changeReason;	
	
	@ApiModelProperty("主单备注")	
    @QueryField(name = "主单备注")	
    private String remarks;	
	
    //@QueryField(name = "工作流进程ID")	
    private String processId;	
	
	@ApiModelProperty("变更类型")	
    @QueryField(name = "变更类型", alias = "$1", type = Type.MultiSelect, option = { "1", "新增", "2", "修改", "3", "删除"})
    @EditField(required = true)
    private String changeType;	
	
    private String detailId;	
	
	@ApiModelProperty("原单行号")	
    @QueryField(name = "原单行号", alias = "$1")
    @EditField(readonly = true)
    private String lineNo;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "$1")
    @EditField(required = true)
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "ppc_material")
    @EditField(readonly = true)
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "ppc_material")
    @EditField(readonly = true)
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "ppc_material.material_marker")
    @EditField(readonly = true)
    private String modelNumber;	
	
	@ApiModelProperty("原辅助属性")	
    @QueryField(name = "原辅助属性", alias = "$1")
    @EditField(readonly = true)
    private String oldSkuCode;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性", alias = "$1")	
    private String skuCode;	
	
	@ApiModelProperty("成品辅助属性")	
    @QueryField(name = "成品辅助属性", alias = "$1")
    @EditField(readonly = true)
    private String productSkuCode;	
	
	@ApiModelProperty("源申请部门")	
    @QueryField(name = "源申请部门", alias = "$1")
    @EditField(readonly = true)
    private String businessDepartmentName;	
	
	@ApiModelProperty("源申请人")	
    @QueryField(name = "源申请人", alias = "$1")
    @EditField(readonly = true)
    private String businessUserName;	
	
	@ApiModelProperty("原申请数量")	
    @QueryField(name = "原申请数量", type = Type.Number, alias = "$1")
    @EditField(readonly = true)
    private String oldRequestQty;	
	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", type = Type.Number, alias = "$1")
    @EditField(required = true)
    private String requestQty;	
	
	@ApiModelProperty("采购单位")	
    @QueryField(name = "采购单位", alias = "$1",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(required = true)
    private String unit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", type = Type.Number, alias = "$1")
    @EditField(readonly = true)
    private String baseUnitQty;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "$1",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String baseUnit;	
	
	@ApiModelProperty("原申请到货日期")	
    @QueryField(name = "原申请到货日期", type = Type.Date, format = "yyyy-MM-dd", alias = "$1")
    @EditField(readonly = true)
    private String oldRequestArriveDate;	
	
	@ApiModelProperty("申请到货日期")	
    @QueryField(name = "申请到货日期", type = Type.Date, format = "yyyy-MM-dd", alias = "$1")
    @EditField(required = true)
    private String requestArriveDate;	
	
//    @QueryField(name = "关联数量", type = Type.Number, sort = false, query = false,level = Level.Detail)	
    private String relationQty;	
	
	@ApiModelProperty("子单来源单据类型")	
    @QueryField(name = "子单来源单据类型", alias = "$1.business_source", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")
    @EditField(readonly = true)
    private String detailBusinessSource;
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号", alias = "$1")
    @EditField(readonly = true)
    private String businessNo;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号", alias = "$1")
    @EditField(readonly = true)
    private String businessLineNo;	
	
	@ApiModelProperty("子单来源方式")	
    @QueryField(name = "子单来源方式", alias = "$1", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_WAY")
    @EditField(readonly = true)
    private String businessWay;	
	
	@ApiModelProperty("MRP计算号")	
    @QueryField(name = "MRP计算号", alias = "$1")
    @EditField(readonly = true)
    private String calcNo;	
	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "$1.remarks")	
    private String detailRemarks;	
	
	@ApiModelProperty("原子单备注")	
    @QueryField(name = "原子单备注", alias = "$1")
    @EditField(readonly = true)
    private String oldRemarks;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人",show = false)	
    private String createBy;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", level = Level.Main, sort = false, alias = ".(select user_name from pe_user where pe_user.user_code = po_purchase_request_change.create_by limit 1)")	
    private String createByName;	
}	
	
	
