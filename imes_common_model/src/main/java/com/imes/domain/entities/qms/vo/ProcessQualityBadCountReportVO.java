package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.ProcessQualityReportDetail;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class ProcessQualityBadCountReportVO implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 报工单号	
     */	
	@ApiModelProperty("报工单号")	
    private String wfNo;	
	
    /**	
     * 工序编号	
     */	
	@ApiModelProperty("工序编号")	
    private String processCode;	
	
    /**	
     * 工序名称	
     */	
	@ApiModelProperty("工序名称")	
    private String processName;	
	
    /**	
     * 报工人	
     */	
	@ApiModelProperty("报工人")	
    private String finisherName;	
	
    /**	
     * 所属班组	
     */	
	@ApiModelProperty("所属班组")	
    private String teamName;	
	
    /**	
     * 所属车间	
     */	
	@ApiModelProperty("所属车间")	
    private String workshopName;	
	
    /**	
     * 报工数量	
     */	
	@ApiModelProperty("报工数量")	
    private int finishNum;	
	
    /**	
     * 报工合格数	
     */	
	@ApiModelProperty("报工合格数")	
    private int finishGoodQty;	
	
    /**	
     * 报工不合格数	
     */	
	@ApiModelProperty("报工不合格数")	
    private int finishBadQty;	
	
    /**	
     * 检验编号	
     */	
	@ApiModelProperty("检验编号")	
    private String inspectionCode;	
	
    /**	
     * 报工时间	
     */	
	@ApiModelProperty("报工时间")	
    private Date finishTime;	
	
    /**	
     * 检验时间	
     */	
	@ApiModelProperty("检验时间")	
    private Date inspectionTime;	
	
    /**	
     * 检验人	
     */	
	@ApiModelProperty("检验人")	
    private String inspector;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    private int inspectionNum;	
	
    /**	
     * 检验合格数	
     */	
	@ApiModelProperty("检验合格数")	
    private int inspectionGoodQty;	
	
    /**	
     * 检验不合格数	
     */	
	@ApiModelProperty("检验不合格数")	
    private int inspectionBadQty;	
	
    /**	
     * 检验是否合格	
     */	
	@ApiModelProperty("检验是否合格")	
    private String isQualified;	
	
    /**	
     * 总合格数	
     */	
	@ApiModelProperty("总合格数")	
    private int allGoodQty;	
	
    /**	
     * 不良品数量	
     */	
	@ApiModelProperty("不良品数量")	
    private int allBadQty;	
	
    /**	
     * 不良项数量	
     */	
	@ApiModelProperty("不良项数量")	
    private int badItemQty;	
	
    /**	
     * 不良率	
     */	
	@ApiModelProperty("不良率")	
    private BigDecimal badPercent;	
	
    private static final long serialVersionUID = 1L;	
}	
