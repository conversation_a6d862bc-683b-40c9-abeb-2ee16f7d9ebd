package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportSaleZoneVo {	
	
    /**	
     * 销售区域	
     */	
    @ApiModelProperty("销售区域")	
    private String saleArea;	
	
    /**	
     * 时间区间	
     */	
    @ApiModelProperty("时间区间")	
    private String timeZones;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal onHandPrice;	
	
	
}	
