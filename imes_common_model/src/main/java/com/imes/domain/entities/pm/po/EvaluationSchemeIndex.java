package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * (PerEvaluationSchemeIndex)实体类
 *
 * <AUTHOR>
 * @since 2022-01-06 17:26:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_scheme_index",resultMap = "BaseResultMap")
public class EvaluationSchemeIndex implements Serializable {
    private static final long serialVersionUID = -96778996494978376L;
    /**
    * id
    */
    private String id;
    /**
    * 方案id
    */
    private String schemeId;
    /**
    * 指标名称
    */
    private String name;
    /**
    * 指标类型
    */
    private Integer type;
    /**
     * 指标权重
     */
    @TableField(exist = false)
    private String typeStr;
    /**
    * 权重
    */
    private Integer weight;
    /**
     * 最高分
     */
    private BigDecimal highestScore;
    /**
    * 考核说明
    */
    private String rules;
    /**
    * 考核内容
    */
    private String target;
    /**
     * 指标考评人
     */
    @TableField(exist = false)
    private List<EvaluationSchemeAssessor> assessors;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

}