package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
public class WcsOutTaskResultVo {	
	
    /**	
     * 任务编码	
     */	
    @ApiModelProperty("任务编码")	
    private String taskCode;	
	
    /**	
     * 载具编码	
     */	
    @ApiModelProperty("载具编码")	
    private String cellCode;	
	
    /**	
     * 设备编码	
     */	
    @ApiModelProperty("设备编码")	
    private String deviceCode;	
	
    /**	
     * 库位排	
     */	
    @ApiModelProperty("库位排")	
    private String binRow;	
	
    /**	
     * 库位层	
     */	
    @ApiModelProperty("库位层")	
    private String binLayer;	
	
    /**	
     * 库位列	
     */	
    @ApiModelProperty("库位列")	
    private String binKind;	
	
    /**	
     * 最终点放货位排	
     */	
    @ApiModelProperty("最终点放货位排")	
    private String destRow;	
	
    /**	
     * 最终点放货位层	
     */	
    @ApiModelProperty("最终点放货位层")	
    private String destLayer;	
	
    /**	
     * 最终点放货位列	
     */	
    @ApiModelProperty("最终点放货位列")	
    private String destKind;	
	
}	
