package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<人员工时>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 10:22
 */
@ApiModel("人员工时Vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ParticiHoursVo {
    @ApiModelProperty("工号")
    private String code;
    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("计划总工时")
    private BigDecimal totalPlannedWorkHours;
    @ApiModelProperty("实际总工时")
    private BigDecimal actualTotalWorkHours;
    @ApiModelProperty("总超时")
    private BigDecimal totalTimeout;
    @ApiModelProperty("项目工时")
    private List<ProjectHoursVo>ProjectHoursVos;
}

