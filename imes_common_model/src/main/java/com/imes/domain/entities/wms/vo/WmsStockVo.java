package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockVo {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料条形码	
     */	
	@ApiModelProperty("物料条形码")	
    private String barCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 品牌	
     */	
	@ApiModelProperty("品牌")	
    private String brand;	
	
    /**	
     * 在库库存数量	
     */	
	@ApiModelProperty("在库库存数量")	
    private BigDecimal onhandQty;	
	
    /**	
     * 可用库存	
     */	
	@ApiModelProperty("可用库存")	
    private BigDecimal availableQty;	
	
    /**	
     * 锁定库存（是指领料时预分配的库存）	
     */	
	@ApiModelProperty("锁定库存（是指领料时预分配的库存）")	
    private BigDecimal lockQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 安全库存	
     */	
	@ApiModelProperty("安全库存")	
    private BigDecimal safeQty;	
	
    /**	
     * 库存短缺量	
     */	
	@ApiModelProperty("库存短缺量")	
    private BigDecimal lackQty;	
	
    private String materialMarker;	
	
    private String whCode;	
	
    private String whName;	
	
    private String specification;	
	
    private String binCode;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 包装可用库存	
     */	
	@ApiModelProperty("包装可用库存")	
    private BigDecimal packAvailableQty;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 物料辅助属性	
     */	
	@ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱码	
     */	
	@ApiModelProperty("箱码")	
    private String boxNo;	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
	
    /**	
     * 库区名称	
     */	
	@ApiModelProperty("库区名称")	
    private String areaName;	
	
    /**	
     * SN码	
     */	
	@ApiModelProperty("SN码")	
    private String snNo;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /***********页面控制所需参数*************/	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean active;	
	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean show;	
	
    /**	
     * 库位库存	
     */	
	@ApiModelProperty("库位库存")	
    private List<WmsMbStockBinVo> stockBinVos;	
	
}	
