package com.imes.domain.entities.po.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 采购合同明细(PoContractDetailStandard)实体类
 *
 * <AUTHOR>
 * @since 2023-11-14 10:10:53
 */
@Data
public class PoContractDetailStandard implements Serializable {
    private static final long serialVersionUID = -94011753846949038L;

    private String id;

    @ApiModelProperty(value = "合同主单id")
    private String mainId;

    @ApiModelProperty(value = "行号")
    private String sdNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "型号")
    private String materialMarker;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "净价")
    private BigDecimal netPrice;

    @ApiModelProperty(value = "折扣方式")
    private String discountType;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "折扣额")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "未税金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "价税合计")
    private BigDecimal allPrice;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "税率编码")
    private String taxCode;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    @ApiModelProperty(value = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "基础单位数量")
    private BigDecimal baseUnitQty;

    @ApiModelProperty(value = "辅助属性")
    private String skuCode;

    @ApiModelProperty(value = "数量控制 1是 0否")
    private String needQty;

    @ApiModelProperty(value = "单价控制 1是 0否")
    private String needSinglePrice;

    @ApiModelProperty(value = "金额控制 1是 0否")
    private String needAllPrice;

    @ApiModelProperty(value = "累计基础单位数量")
    private BigDecimal sendedQty;

    @ApiModelProperty(value = "累计金额")
    private BigDecimal sendedPrice;

    @ApiModelProperty(value = "关联采购订单数量")
    private BigDecimal relationPoQty;

    @ApiModelProperty(value = "关联采购订单价格")
    private BigDecimal relationPoPrice;

    @TableField(exist = false)
    private String materialBaseUnit;

    @TableField(exist = false)
    private String soNo;

}

