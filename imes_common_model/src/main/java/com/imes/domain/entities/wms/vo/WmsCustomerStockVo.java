package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsCustomerStock;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsCustomerStockVo extends WmsCustomerStock {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 售卖方式(买断、回收)	
     */	
	@ApiModelProperty("售卖方式(买断、回收)")	
    private String buyType;	
	
    /**	
     * 桶数量(发货单回执回收业务为负数)	
     */	
	@ApiModelProperty("桶数量(发货单回执回收业务为负数)")	
    private BigDecimal qty;	
	
    /**	
     * 操作方式	
     */	
	@ApiModelProperty("操作方式")	
    private String optionType;	
	
	
}	
