package com.imes.domain.entities.wms;	
	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
/**	
 * (WmsScrapOrder)实体类	
 *	
 * <AUTHOR>	
 * @since 2022-11-03 10:32:45	
 */	
@Data	
@ApiModel(value = "报废单主表")	
public class WmsScrapOrder implements Serializable {	
	
    private static final long serialVersionUID = -22819914955493164L;	
	
    @ApiModelProperty(value = "主键", dataType = "String")	
    private String id;	
	
    @ApiModelProperty(value = "移库单号", dataType = "String")	
    private String scrapCode;	
	
    @ApiModelProperty(value = "单据类型（存货报废单、生产报废单）", dataType = "Integer")	
    private Integer orderType;	
	
    @ApiModelProperty(value = "单据来源（手工建单、第三方系统）", dataType = "Integer")	
    private Integer source;	
	
    @ApiModelProperty(value = "第三方单据号", dataType = "String")	
    private String thirdOrderCode;	
	
    @ApiModelProperty(value = "关联单号", dataType = "String")	
    private String receiptCode;	
	
    @ApiModelProperty(value = "单据状态（10：已录入、15：待审核、20：已生效、50：已完成）", dataType = "Integer")	
    private Integer orderStatus;	
	
    @ApiModelProperty(value = "申请部门编码", dataType = "String")	
    private String applyDepartCode;	
	
    @ApiModelProperty(value = "申请部门名称", dataType = "String")	
    private String applyDepartName;	
	
    @ApiModelProperty(value = "申请人", dataType = "String")	
    private String applyBy;	
	
    @ApiModelProperty(value = "申请人姓名", dataType = "String")	
    private String applyName;	
	
    @ApiModelProperty(value = "申请时间", dataType = "Date")	
    private Date applyOn;	
	
    @ApiModelProperty(value = "报废原因", dataType = "String")	
    private String purpose;	
	
    @ApiModelProperty(value = "备注", dataType = "String")	
    private String remarks;	
	
    @ApiModelProperty(value = "创建时间", dataType = "Date")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人", dataType = "String")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间", dataType = "Date")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人", dataType = "String")	
    private String updateBy;	
	
    @ApiModelProperty(value = "逻辑删除标识", dataType = "Integer")	
    private Integer deleteStatus;	
	
    @ApiModelProperty(value = "流程Id")	
    private String activityId;	
	
    private List<WmsScrapItemOrder> items;	
}	
