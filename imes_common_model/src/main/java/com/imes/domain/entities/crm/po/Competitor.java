package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (Competitor)实体类
 *
 * <AUTHOR>
 * @since 2022-04-22 15:27:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_business_competitor", resultMap = "BaseResultMap")
public class Competitor extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 231087638572921703L;
    /**
    * id
    */
    private String id;
    /**
    * 竞争者名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 网站
    */
    private String website;
    /**
    * 商机id
    */
    private String businessId;
    /**
    * 对标产品
    */
    private String productId;
    /**
    * 优势
    */
    private String advantage;
    /**
    * 劣势
    */
    private String inferiority;
    /**
    * 备注
    */
    private String remarks;

}