package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr考勤请假记录(HrAttendanceLeave)实体类	
 *	
 * <AUTHOR> z	
 * @since 2022-12-28 13:12:31	
 */	
@Data	
@ApiModel("请假记录管理")	
@QueryModel(name = "0683",	
        remark = "请假记录管理",	
        alias = "leaves",	
        searchApi = "/api/hr/attendance/query/leave")	
public class HrAttendanceLeaveQueryVo  extends BaseModel {	
	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称" )	
    private String id;	
    /**	
     * 请假人	
     */	
	@ApiModelProperty("请假人")	
    @QueryField(name = "请假人" )	
    private String userCode;	
    /**	
     * 原因	
     */	
	@ApiModelProperty("原因")	
    @QueryField(name = "原因" )	
    private String reason;	
    /**	
     * 开始时间	
     */	
	@ApiModelProperty("开始时间")	
    @QueryField(name = "开始时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String startTime;	
    /**	
     * 结束时间	
     */	
	@ApiModelProperty("结束时间")	
    @QueryField(name = "结束时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String endTime;	
    /**	
     * 天数	
     */	
	@ApiModelProperty("天数")	
    @QueryField(name = "天数" )	
    private String days;	
    /**	
     * 类型	
     */	
	@ApiModelProperty("类型")	
    @QueryField(name = "类型", type = Type.MultiSelect, dictOption = "VACATION_TYPE")	
    private String type;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态" )	
    private String status;	
	
}	
	
