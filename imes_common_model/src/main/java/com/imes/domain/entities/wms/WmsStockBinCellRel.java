package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "")	
public class WmsStockBinCellRel implements Serializable {	
	
    private static final long serialVersionUID = 473617906850922117L;	
	
@ApiModelProperty(value = "主键")	
private String id;	
	
@ApiModelProperty(value = "关联类型：1:有货载具；2:空载具")	
private String receiptType;	
	
@ApiModelProperty(value = "库位编码")	
private String binCode;	
	
@ApiModelProperty(value = "载具编码")	
private String cellCode;	
	
@ApiModelProperty(value = "载具数量")	
private BigDecimal cellQty;	
	
@ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如盘点，移库，补货等作业而锁定的库存")	
private String lockStatus;	
	
@ApiModelProperty(value = "创建时间")	
private Date createOn;	
	
@ApiModelProperty(value = "创建人")	
private String createBy;	
	
@ApiModelProperty(value = "更新时间")	
private Date updateOn;	
	
@ApiModelProperty(value = "更新人")	
private String updateBy;	
}	
