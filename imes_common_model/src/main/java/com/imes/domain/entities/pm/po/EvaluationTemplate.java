package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (PerEvaluationTemplate)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 10:15:37
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_template",resultMap = "BaseResultMap")
public class EvaluationTemplate implements Serializable {
    private static final long serialVersionUID = -34199375076353345L;
    /**
    * id
    */
    private String id;
    /**
    * 模板名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 考评类型
    */
    private Integer type;
    /**
    * 备注
    */
    private String remarks;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

}