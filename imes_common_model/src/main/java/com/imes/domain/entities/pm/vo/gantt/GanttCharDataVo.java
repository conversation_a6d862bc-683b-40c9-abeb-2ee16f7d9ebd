package com.imes.domain.entities.pm.vo.gantt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<甘特图数据>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 15:08
 */
@ApiModel("甘特图数据VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GanttCharDataVo {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("是否展开")
    private Integer open;
    @ApiModelProperty("项目id")
    private String projectId;
    @ApiModelProperty("任务名称")
    private String text;
    @ApiModelProperty("顺序")
    private String sort_template;
    @ApiModelProperty("占比")
    private String ratio_template;
    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("开始日期")
    private LocalDate start_date;
    @ApiModelProperty("结束日期")
    private LocalDate end_date;
    @ApiModelProperty("计划工时")
    private BigDecimal plan_hours;
    @ApiModelProperty("天数")
    private Long duration;
    @ApiModelProperty("进度")
    private BigDecimal progress;
    @ApiModelProperty("参与人")
    private List<GanttCharOwnerVo> owner;
    @ApiModelProperty("是否存在子里程碑")
    private Boolean hasChildren = true;
    @ApiModelProperty("父任务")
    private String parent;
    @ApiModelProperty("优先权")
    private Integer priority;
    @ApiModelProperty("颜色")
    private String color;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("负责人")
    private String principal;
    @ApiModelProperty("是否有子任务")
    private Boolean has_child;
    @ApiModelProperty("计算方式")
    private String calculation;
}
