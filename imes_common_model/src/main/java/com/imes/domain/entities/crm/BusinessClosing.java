package com.imes.domain.entities.crm;

import com.imes.domain.entities.crm.enums.BusinessClosingEnum;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<商机关闭>>
 * @company 捷创智能技术有限公司
 * @create 2022-02-18 10:39
 */
@Data
public class BusinessClosing {
    @NotBlank(message = "商机id不能为空")
    private String businessId;
    @NotNull(message = "关闭状态不能为空")
    private BusinessClosingEnum offState;
    private String closingCompetitor;
    private List<String> closingReason;
    private String closeNotes;
}
