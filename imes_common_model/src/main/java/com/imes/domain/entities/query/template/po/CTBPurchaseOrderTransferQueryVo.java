package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
@ApiModel("采购到货调入采购单（CTB版）")	
@QueryModel(	
        name = "0240-03-select",	
        remark = "采购到货调入采购单（CTB版）",	
        searchApi = "/po/poPurchaseDetail/queryPoAndDetailList",	
        alias = "po")	
public class  CTBPurchaseOrderTransferQueryVo extends BaseModel {	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号")	
    private String purchaseNo;	
	
	@ApiModelProperty("采购单类型")	
    @QueryField(name = "采购单类型", type = Type.Select, dictOption = "PO_PURCHASE_TYPE")	
    private String purchaseType;	
	
	@ApiModelProperty("采购部门")	
    @QueryField(name = "采购部门")	
    private String demandDepName;	
	
	@ApiModelProperty("采购员")	
    @QueryField(name = "采购员")	
    private String demandUserName;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
//    @QueryField(name = "物料类型")	
//    private String typeName;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格")	
    private String specification;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量")	
    private String qty;	
	
	@ApiModelProperty("已到货数量")	
    @QueryField(name = "已到货数量")	
    private String arriveQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String unit;	
	
//    @QueryField(name = "单价")	
//    private String singlePrice;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价")	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价")	
    private String includePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("含税金额")	
    @QueryField(name = "含税金额")	
    private String allPrice;	
	
//    @QueryField(name = "预计到货日期", type = Type.Date)	
//    private String dueArrivalDate;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("采购日期")	
    @QueryField(name = "采购日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String orderDate;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "POPURCHASE_STATSU", value = "50", query = false)	
    private String status;	
	
	@ApiModelProperty("单据日期")	
    @QueryField(name = "单据日期", type = Type.Date, format = "yyyy-MM-dd", order = OrderBy.DESC)	
    private String createOn;	
}	
