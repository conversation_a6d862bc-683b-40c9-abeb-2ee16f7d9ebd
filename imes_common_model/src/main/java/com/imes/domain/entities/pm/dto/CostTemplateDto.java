package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (CostTemplate)实体类
 *
 * <AUTHOR>
 * @since 2021-06-21 16:42:15
 */
@ApiModel("成本模板实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostTemplateDto implements Serializable {
    private static final long serialVersionUID = 498533169571554849L;
    public interface AddGroup {
    }

    public interface UpdateGroup {
    }
    /**
    * id
    */
    @ApiModelProperty("id")
    private String id;
    /**
    * 模板名称
    */
    @ApiModelProperty("模板名称")
    private String name;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}