package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockItem implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 总库存表主键	
     */	
	@ApiModelProperty("总库存表主键")	
    private String stockId;	
	
    /**	
     * 库位库存表主键	
     */	
	@ApiModelProperty("库位库存表主键")	
    private String stockBinId;	
	
    /**	
     * 库存类型（101：采购入库、102：手工入库、103：退货入库、104：退料入库、105：盘亏入库、106：生成入库、107：维修入库、108：调拨入库；201：采购在途、202：调拨在途、203：退货在途、204：锁定待发货、205：锁定待领料；301：销售出库、302：手工出库、303：领料出库、304：维修出库、305：调拨出库）	
     */	
	@ApiModelProperty("库存类型（101：采购入库、102：手工入库、103：退货入库、104：退料入库、105：盘亏入库、106：生成入库、107：维修入库、108：调拨入库；201：采购在途、202：调拨在途、203：退货在途、204：锁定待发货、205：锁定待领料；301：销售出库、302：手工出库、303：领料出库、304：维修出库、305：调拨出库）")	
    private String stockType;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料品牌	
     */	
	@ApiModelProperty("物料品牌")	
    private String brand;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specifications;	
	
    /**	
     * 物料系列	
     */	
	@ApiModelProperty("物料系列")	
    private String series;	
	
    /**	
     * 单个重量	
     */	
	@ApiModelProperty("单个重量")	
    private BigDecimal singleWeight;	
	
    /**	
     * 重量单位	
     */	
	@ApiModelProperty("重量单位")	
    private String weightUnit;	
	
    /**	
     * 单个体积	
     */	
	@ApiModelProperty("单个体积")	
    private BigDecimal singleVolume;	
	
    /**	
     * 体积单位	
     */	
	@ApiModelProperty("体积单位")	
    private String volumeUnit;	
	
    /**	
     * 存货公司编码	
     */	
	@ApiModelProperty("存货公司编码")	
    private String stockCompanyCode;	
	
    /**	
     * 存货公司名称	
     */	
	@ApiModelProperty("存货公司名称")	
    private String stockCompanyName;	
	
    /**	
     * 主单位	
     */	
	@ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 库存单价	
     */	
	@ApiModelProperty("库存单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 单据数量	
     */	
	@ApiModelProperty("单据数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 单据数量	
     */	
	@ApiModelProperty("单据数量")	
    private BigDecimal onHandQty;	
	
    /**	
     * 可用库存	
     */	
	@ApiModelProperty("可用库存")	
    private BigDecimal availableQty;	
	
    /**	
     * 锁定库存	
     */	
	@ApiModelProperty("锁定库存")	
    private BigDecimal lockQty;	
	
    /**	
     * 残次品数量	
     */	
	@ApiModelProperty("残次品数量")	
    private BigDecimal defectiveQty;	
	
    /**	
     * 维修品数量	
     */	
	@ApiModelProperty("维修品数量")	
    private BigDecimal repairQty;	
	
    /**	
     * 采购在途数量	
     */	
	@ApiModelProperty("采购在途数量")	
    private BigDecimal poUnderWayQty;	
	
    /**	
     * 库存金额	
     */	
	@ApiModelProperty("库存金额")	
    private BigDecimal orderAmount;	
	
    /**	
     * 库存金额	
     */	
	@ApiModelProperty("库存金额")	
    private BigDecimal onHandAmount;	
	
    /**	
     * 可用库存金额	
     */	
	@ApiModelProperty("可用库存金额")	
    private BigDecimal availableAmount;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
	
    /**	
     * 库区名称	
     */	
	@ApiModelProperty("库区名称")	
    private String areaName;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 库存说明	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 关联单号（采购合同号、生产单号、领料单号、退货单号。。。。。。）	
     */	
	@ApiModelProperty("关联单号（采购合同号、生产单号、领料单号、退货单号。。。。。。）")	
    private String relatedOrderCode;	
	
    /**	
     * 关联单号（销售合同号。。。。。。）	
     */	
	@ApiModelProperty("关联单号（销售合同号。。。。。。）")	
    private String relatedOrderExtCode;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String inboundCode;	
	
    /**	
     * 入库日期	
     */	
	@ApiModelProperty("入库日期")	
    private Date inboundDate;	
	
    /**	
     * 入库员工号	
     */	
	@ApiModelProperty("入库员工号")	
    private String inboundUserCode;	
	
    /**	
     * 入库员工名称	
     */	
	@ApiModelProperty("入库员工名称")	
    private String inboundUserName;	
	
    /**	
     * 入库单据类型	
     */	
	@ApiModelProperty("入库单据类型")	
    private String inboundType;	
	
    /**	
     * 出库单号	
     */	
	@ApiModelProperty("出库单号")	
    private String outboundCode;	
	
    /**	
     * 出库日期	
     */	
	@ApiModelProperty("出库日期")	
    private Date outboundDate;	
	
    /**	
     * 出库员工号	
     */	
	@ApiModelProperty("出库员工号")	
    private String outboundUserCode;	
	
    /**	
     * 出库员工名称	
     */	
	@ApiModelProperty("出库员工名称")	
    private String outboundUserName;	
	
    /**	
     * 出库单据类型	
     */	
	@ApiModelProperty("出库单据类型")	
    private String outboundType;	
	
    /**	
     * 有效状态（'0':无效,'1':有效）	
     */	
	@ApiModelProperty("有效状态（'0':无效,'1':有效）")	
    private String effectStatus;	
	
    /**	
     * 锁定库存状态（0：自由库存；1：锁定库存（销售锁定、领料锁定））	
     */	
	@ApiModelProperty("锁定库存状态（0：自由库存；1：锁定库存（销售锁定、领料锁定））")	
    private String lockStatus;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private String updateOn;	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
