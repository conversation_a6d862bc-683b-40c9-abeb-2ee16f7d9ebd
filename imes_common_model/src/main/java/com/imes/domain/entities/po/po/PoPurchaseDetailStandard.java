package com.imes.domain.entities.po.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imes.domain.entities.query.model.base.BaseModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "采购订单明细")
public class PoPurchaseDetailStandard  extends BaseModel implements Serializable {

    private static final long serialVersionUID = -72498326554168731L;

    private String id;

    @ApiModelProperty(value = "主单Id")
    private String mainId;

    @ApiModelProperty(value = "子订单号")
    private String sdNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "材质")
    private String quality;

    @ApiModelProperty(value = "型号")
    private String materialMarker;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "采购单位")
    private String unit;

    @ApiModelProperty(value = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "基础单位数量")
    private BigDecimal baseUnitQty;

    /**
     * 到货计划数量（采购单位）
     */
    @ApiModelProperty(value = "到货计划数量")
    private BigDecimal planQty;

    /**
     * 到货数量（采购单位）
     */
    @ApiModelProperty(value = "到货数量")
    private BigDecimal arriveQty;

    /**
     * 入库数量（采购单位）
     */
    @ApiModelProperty(value = "入库数量")
    private BigDecimal inboundQty;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "净价")
    private BigDecimal netPrice;

    @ApiModelProperty(value = "折扣方式")
    private String discountType;

    @ApiModelProperty(value = "折扣额")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "不税额金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "价税合计(总金额)")
    private BigDecimal allPrice;

    @ApiModelProperty(value = "到货时间")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date deliveryDate;

    @ApiModelProperty(value = "源单类型")
    private String requestDocType;

    @ApiModelProperty(value = "源单单号")
    private String requestDocCode;

    @ApiModelProperty(value = "源单申请部门编码")
    private String requestDocDepCode;

    @ApiModelProperty(value = "源单申请部门名称")
    private String requestDocDepName;

    @ApiModelProperty(value = "源单申请人工号")
    private String requestDocPersonCode;

    @ApiModelProperty(value = "源单申请人名称")
    private String requestDocPersonName;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税率编码")
    private String taxCode;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    //退货不补货数量（采购单位）
    @ApiModelProperty(value = "退货不补货数量")
    private BigDecimal cancelQty;

    //退货补货数量（采购单位）
    @ApiModelProperty(value = "退货补货数量")
    private BigDecimal cancelReplenishQty;

    //源单行号
    @ApiModelProperty(value = "源单行号")
    private String lineNo;
    //辅助属性
    @ApiModelProperty(value = "辅助属性")
    private String skuCode;

    //推荐单价
    @ApiModelProperty(value = "推荐单价")
    private BigDecimal recommedPrice;

    //价格上限
    @ApiModelProperty(value = "价格上限")
    private BigDecimal priceMax;

    //价格下限
    @ApiModelProperty(value = "价格下限")
    private BigDecimal priceMin;

    // 取价来源 1-采购合同 2-采购价目表 3-供应商有效期最小单价 4-最近物料采购单价 5-最近供应商采购单价
    private String priceSource;

    //取价来源关联单Id
    private String priceAssociationId;

    @TableField(exist = false)
    private String materialBaseUnit;

}