package com.imes.domain.entities.po.vo;

import com.imes.domain.entities.po.po.PoPurchaseForecastDetail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class PoUpdateScsVo {
    //主表Id
    private String id;
    //主表版本号
    private int version;
    //是否通过 1通过 2未通过
    private int type;
    //供应商code+name集合
    private String supplierCodeName;
    //供应商平台机构编码
    private String orgPlatfromCode;
    //SCS用来更新的状态
    private String scsStatus;
    //拒绝理由
    private String poRemarks;
    //对账单号
    private String verifyNo;
    //采购发票号
    private String invoiceCode;
    //驳回时间
    private String refuseDate;
    //确认回复时间
    private String tyDate;
    //实际金额
    private String actualAmount;
    //驳回对象
    private List<PoPurchaseForecastDetail> lists;

    private String sendMsg;

    //收票人 收票时间
    private String spName;

}
