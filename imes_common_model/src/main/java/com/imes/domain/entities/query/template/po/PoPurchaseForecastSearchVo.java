package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("采购需求预测")	
@QueryModel(	
        name = "1160",	
        remark = "采购需求预测",	
        alias = "a",	
        searchApi = "/api/po/poPurchaseForecast/queryList")	
public class PoPurchaseForecastSearchVo extends BaseModel {	
	
    private String id;	
	
    private String detailId;	
	
    private String mainId;	
	
    private String remarks;	
	
    private String statusType;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", show = false)	
    private String createOn;	
	
	@ApiModelProperty("需求批次号")	
    @QueryField(name = "需求批次号")	
    private String demandForecastNo;	
	
/*    @QueryField(name = "需求版本")	
    private String demandVersion;*/	
	
	@ApiModelProperty("需求预测日期")	
    @QueryField(name = "需求预测日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String forecastDate;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码")	
    private String supplierCode;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("供应商联系人")	
    @QueryField(name = "供应商联系人")	
    private String supplierLinkMan;	
	
	@ApiModelProperty("采购部门")	
    @QueryField(name = "采购部门")	
    private String demandUserDepName;	
	
    private String demandUserDep;	
	
	@ApiModelProperty("采购员编号")	
    @QueryField(name = "采购员编号")	
    private String demandUserCode;	
	@ApiModelProperty("采购员名称")	
    @QueryField(name = "采购员名称")	
    private String demandUserName;	
	
	@ApiModelProperty("需求发布日期")	
    @QueryField(name = "需求发布日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String publishDate;	
	
	@ApiModelProperty("需求回复截止时间")	
    @QueryField(name = "需求回复截止时间", type = Type.Date,  format = "yyyy-MM-dd")	
    private String responseDeadline;	
	
	@ApiModelProperty("需求回复时间")	
    @QueryField(name = "需求回复时间", type = Type.Date)	
    private String responseDate;	
	
	@ApiModelProperty("需求回复人姓名")	
    @QueryField(name = "需求回复人姓名")	
    private String responsePersonName;	
	
	@ApiModelProperty("供应商提交备注")	
    @QueryField(name = "供应商提交备注")	
    private String supplierRemarks;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, option = {"10", "已录入", "20", "审核中", "30", "生效待发布"})	
    private String demandStatus;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "b")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "b")	
    private String materialName;	
	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "b")	
    private String specification;	
	
	@ApiModelProperty("物料图号")	
    @QueryField(name = "物料图号", alias = "b")	
    private String dwgNo;	
	
	@ApiModelProperty("物料新图号")	
    @QueryField(name = "物料新图号", alias = "b")	
    private String newDwgNo;	
	
	@ApiModelProperty("物料采购预测数量")	
    @QueryField(name = "物料采购预测数量", sort = false, alias = ".ifnull((select sum(ifnull(c.demand_qty,0)) from po_purchase_forecast_detail c where c.main_id = b.id  group by c.main_id),0)")	
    private String qty;	
	
  /*  @QueryField(name = "物料单位", alias = "b")	
    private String unit;*/	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "b")	
    private String materialMarker;	
	
	@ApiModelProperty("产品大类")	
    @QueryField(name = "产品大类", type = Type.Select, dictOption = "MATERIAL_CATEGORY", alias = "b")	
    private String category;	
	
	@ApiModelProperty("产品类型")	
    @QueryField(name = "产品类型", type = Type.Select, alias = "b", treeKey = {"typeCode", "parentCode", "0"}, sqlOption = "select type_code as value, type_name as label, type_code as typeCode, parent_type_code as parentCode from sys_material_type order by type_code")	
    private String materialTypeCode;	
/*	
	@ApiModelProperty("物料备注")	
    @QueryField(name = "物料备注", alias = "b.remarks")	
    private String detailRemarks;*/	
}	
