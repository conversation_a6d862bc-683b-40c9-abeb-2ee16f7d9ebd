package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;
import java.util.Optional;

/**
 * 风险状态
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum RiskStateEnum {
    PROPOSED("proposed", "已提出"),
    SOLVED("solved", "已解决"),
    CLOSED("closed", "已关闭");
    @EnumValue
    public final String state;
    public final String stateName;

    RiskStateEnum(String state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }


    public String getState() {
        return state;
    }

    public String getStateName() {
        return stateName;
    }

    public static RiskStateEnum match(String state) {
        return Arrays.stream(RiskStateEnum.values())
                .filter(e -> e.getState().equals(state))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("风险状态错误"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static RiskStateEnum des(final JsonNode jsonNode) {
        String state = jsonNode.isTextual() ? jsonNode.asText() : Optional.ofNullable(jsonNode.get("state")).map(JsonNode::asText).orElse("-1");
        if ("-1".equals(state)) {
            return null;
        }
        return RiskStateEnum.match(state);
    }
}
