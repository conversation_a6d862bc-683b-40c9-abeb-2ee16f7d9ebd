package com.imes.domain.entities.pm.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<部门员工树>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-30 9:43
 */
@ApiModel("部门员工树")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CoDepartmentTypeVo {
    private String id;
    private String departTypeCode;
    private String departTypeName;
    private String parentId;
    private String remarks;
    private String createBy;
    private String updateBy;
    private Date createOn;
    private Date updateOn;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CoDepartmentTypeVo> children;
}
