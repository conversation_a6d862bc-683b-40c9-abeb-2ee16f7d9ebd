package com.imes.domain.entities.crm.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 人员数据权限注解
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface UserPermission {
    /**
     * 权限字段
     * @return
     */
    String field() default "user_code";

    /**
     * 数据范围
     * @return
     */
    ScopeType scopeType() default UserPermission.ScopeType.SELF;


    public static enum ScopeType {
        /**
         * 所有数据
         */
        ALL(),
        /**
         * 我的数据
         */
        SELF(),
        /**
         * 我的及下属数据
         */
        CHILD();

        private ScopeType() {
        }
    }
}
