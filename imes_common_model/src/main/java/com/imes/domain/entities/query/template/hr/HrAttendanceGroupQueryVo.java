package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 考勤组表(HrAttendanceGroupResultVo)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-25 10:20:55	
 */	
@Data	
@ApiModel("考勤组管理")	
@QueryModel(name = "0783",	
        remark = "考勤组管理",	
        alias = "hr_attendance_group",	
        searchApi = "/api/hr/attendance/rule/group/query")	
public class HrAttendanceGroupQueryVo  extends BaseModel {	
	
    /**	
     * 考勤组编码	
     */	
	@ApiModelProperty("考勤组编码")	
    @QueryField(name = "考勤组编码" )	
    private String groupCode;	
    /**	
     * 小组名称	
     */	
	@ApiModelProperty("小组名称")	
    @QueryField(name = "小组名称" )	
    private String name;	
    /**	
     * 共享策略	
     */	
	@ApiModelProperty("共享策略")	
    @QueryField(name = "共享策略" )	
    private String sharingStrategy;	
    /**	
     * 负责组织编码	
     */	
	@ApiModelProperty("负责组织编码")	
    @QueryField(name = "负责组织编码" )	
    private String organization;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态" )	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人编码")	
    @QueryField(name = "创建人编码", show = false)	
    private String createBy;	
	
	
	
	
}	
	
