package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsInspectionListForSupplierVo {	
    /**	
     * 检验项名称	
     */	
	@ApiModelProperty("检验项名称")	
    String itemName;	
    /**	
     * 检验项编码	
     */	
	@ApiModelProperty("检验项编码")	
    String itemCode;	
    /**	
     * 子项数量	
     */	
	@ApiModelProperty("子项数量")	
    String childNum;	
    /**	
     * 检验文件	
     */	
	@ApiModelProperty("检验文件")	
    String fileId;	
}	
