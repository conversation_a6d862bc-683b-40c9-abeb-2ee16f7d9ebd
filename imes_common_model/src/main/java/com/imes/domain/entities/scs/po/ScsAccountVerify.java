package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "销售对账确认")
public class ScsAccountVerify implements Serializable {

    private static final long serialVersionUID = 824613178177042950L;

    private String id;

    @ApiModelProperty(value = "对账单号")
    private String verifyNo;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "纳税人识别号")
    private String companyCode;

    @ApiModelProperty(value = "供应商")
    private String supplierName;

    @ApiModelProperty(value = "对账开始日期")
    private Date startDate;

    @ApiModelProperty(value = "对账截止日期")
    private Date endDate;

    @ApiModelProperty(value = "账款金额")
    private BigDecimal totalMoney;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "税率（0~1）")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税率编码")
    private String taxCode;

    @ApiModelProperty(value = "账款到账日期")
    private Date moneyEndDate;

    @ApiModelProperty(value = "支付方式")
    private String payMethod;

    @ApiModelProperty(value = "制单人工号")
    private String madeUserCode;

    @ApiModelProperty(value = "制单人名称")
    private String madeUserName;

    @ApiModelProperty(value = "开票状态1已开票，0未开票")
    private String receiptStatus;

    @ApiModelProperty(value = "10未关闭，20关闭")
    private String businessStatus;

    @ApiModelProperty(value = "单据状态10录入20待客户确认30客户已拒绝40已确认")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "是否是代管挂账")
    private String isAccount;

    private String remarks;

    private String refuseReason;

    private String fileId;
    //锁铜数量
    @ApiModelProperty(value = "锁铜数量")
    private BigDecimal stQty;
    //锁铜均价
    @ApiModelProperty(value = "锁铜均价")
    private BigDecimal stAvgPrice;
    //市铜均价
    @ApiModelProperty(value = "市铜均价")
    private BigDecimal shtAvgPrice;
    //实际结算系数
    @ApiModelProperty(value = "实际结算系数")
    private BigDecimal settlementRatio;

    @ApiModelProperty(value = "市铜结算金额")
    private BigDecimal shtSettlementPrice;
}