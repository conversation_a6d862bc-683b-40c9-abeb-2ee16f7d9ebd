package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr入职汇总表(HrEntryOrder)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-04-21 10:34:17	
 */	
@Data	
@ApiModel("入职单")	
@QueryModel(name = "0780",	
        remark = "入职单",	
        alias = "hr_entry_order",	
        searchApi = "/api/hr/user/extend/entry/order/query")	
public class HrEntryOrderQueryVo extends BaseModel {	
	
    /**	
     * 姓名	
     */	
	@ApiModelProperty("姓名")	
    @QueryField(name = "姓名" )	
    private String name;	
    /**	
     * 身份证	
     */	
	@ApiModelProperty("身份证")	
    @QueryField(name = "身份证", show = false )	
    private String idCard;	
    /**	
     * 手机号	
     */	
	@ApiModelProperty("手机号")	
    @QueryField(name = "手机号" )	
    private String mobile;	
    /**	
     * 性别	
     */	
	@ApiModelProperty("性别")	
    @QueryField(name = "性别" )	
    private String male;	
    /**	
     * 入职时间	
     */	
	@ApiModelProperty("入职时间")	
    @QueryField(name = "入职时间", type = Type.Date)	
    private String entryTime;	
    /**	
     * 部门编码	
     */	
	@ApiModelProperty("部门编码")	
    @QueryField(name = "部门编码" , show = false )	
    private String deptCode;	
    /**	
     * 岗位编码	
     */	
	@ApiModelProperty("岗位编码")	
    @QueryField(name = "岗位编码" , show = false )	
    private String jobsCode;	
    /**	
     * 审核状态	
     */	
	@ApiModelProperty("审核状态")	
    @QueryField(name = "审核状态" )	
    private String auditStatus;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态" )	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人" )	
    private String createBy;	
	
	@ApiModelProperty("")	
    @QueryField(name = "" )	
    private String deptName;	
	@ApiModelProperty("级别")	
    @QueryField(name = "级别" )	
    private String jobsName;	
	
}	
	
