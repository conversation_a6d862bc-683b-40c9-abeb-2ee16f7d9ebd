package com.imes.domain.entities.pm.query;

import com.imes.domain.entities.pm.enums.BugState;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@ApiModel("《问题》查询条件")
@Data
public class BugQuery  extends BaseQuery{
    private String id;
    private String title;
    private Integer priority;
    private BugState state;
    private String projectId;
    private String taskId;
    private String principal;
    private String severity;
    private String defect;
    private LocalDateTime expire;
    private List<LocalDateTime> expireRange;
}
