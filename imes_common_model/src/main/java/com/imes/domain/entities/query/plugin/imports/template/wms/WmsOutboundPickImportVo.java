package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(
        name = "拣选出库导入模板",
        modelName = "1352")
public class WmsOutboundPickImportVo extends BaseModel {
    @ImportField(name = "物料编码", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialCode;

    @ImportField(name = "辅助属性", maxLength = 255, remark = "参考【物料基本信息】")
    private String skuCode;

    @ImportField(name = "批次", maxLength = 255, remark = "出库物料批次号")
    private String batch;

    @ImportField(name = "申请出库数量", required = true, isNumber = true, remark = "必填", minNumber = "1", maxNumber = "999999999.999999")
    private String pickedQty;

    @ImportField(name = "明细备注", maxLength = 255, remark = "明细备注")
    private String remark;
}
