package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import io.swagger.models.auth.In;	
import lombok.Data;	
	
import java.util.Date;	
import java.util.List;	
	
/**	
 * 生产领料	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-04-13 10:22	
 */	
@Data	
public class C_WmsOutboundVo {	
	
    /**	
     * 领料类型（生产领料：1；换料：3;退料：4;替代料：5）	
     */	
	@ApiModelProperty("领料类型（生产领料：1；换料：3;退料：4;替代料：5）")	
    private String orderType;	
	
    /**	
     * 生产相关单号--必填	
     */	
	@ApiModelProperty("生产相关单号--必填")	
    private String orderCode;	
	
    /**	
     * 操作人（员工工号）--必填	
     */	
	@ApiModelProperty("操作人（员工工号）--必填")	
    private String operator;	
	
    /**	
     * 预期领料日期	
     */	
	@ApiModelProperty("预期领料日期")	
    private Date expectDate;	
	
    /**	
     * 预留仓库编码	
     */	
    @ApiModelProperty("预留仓库编码")	
    private String whCode;	
	
    private String whName;	
	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 出库单明细数组--必填	
     */	
	@ApiModelProperty("出库单明细数组--必填")	
    private List<C_WmsOutboundItemVo> items;	
	
    /**	
     * 是否需要扫码，默认true需要扫码，false不需要扫码	
     */	
	@ApiModelProperty("是否需要扫码，默认true需要扫码，false不需要扫码")	
    private Boolean isScan;	
	
    private String binCode;	
	
    private String warehouseType;	
	
    /**	
     * 订单来源编码	
     */	
	@ApiModelProperty("订单来源编码")	
    private String receiptSource;	
	
    /**	
     * 订单来源名称	
     */	
	@ApiModelProperty("订单来源名称")	
    private String receiptSourceName;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 领用人工号	
     */	
	@ApiModelProperty("领用人工号")	
    private String pickUserCode;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
	
    /**	
     * 生产投料单主键	
     */	
	@ApiModelProperty("生产投料单主键")	
    private String feedingOrderId;	
	
    /**	
     * 第三方系统生产投料单单号	
     */	
	@ApiModelProperty("第三方系统生产投料单单号")	
    private String erpOrderCode;	
	
    /**	
     * 车间编码	
     */	
	@ApiModelProperty("车间编码")	
    private String workshopCode;	
	
    /**	
     * 车间名称	
     */	
	@ApiModelProperty("车间名称")	
    private String workshopName;	
	
    /**	
     * 班组编码	
     */	
	@ApiModelProperty("班组编码")	
    private String teamCode;	
	
    /**	
     * 班组名称	
     */	
	@ApiModelProperty("班组名称")	
    private String teamName;	
	
    /**	
     * 销售订单号	
     */	
	@ApiModelProperty("销售订单号")	
    private String saleNo;	
	
    @ApiModelProperty("自定义字段")	
    private String custom;	
	
	
}	
