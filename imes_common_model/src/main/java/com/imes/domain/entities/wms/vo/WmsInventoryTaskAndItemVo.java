package com.imes.domain.entities.wms.vo;	
import io.swagger.annotations.ApiModelProperty;	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsInventoryTaskAndItemVo implements Serializable {	
	
    @ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty("盘点单号")	
    private String inventoryCode;	
	
    /**	
     * 仓库编码	
     */	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    @ApiModelProperty("盘点类型")	
    private String checkType;	
	
    /**	
     * 盘点员工号	
     */	
    @ApiModelProperty("操作人工号")	
    private String checkBy;	
	
    @ApiModelProperty("操作人名称")	
    private String checkByName;	
    /**	
     * 复核人工号	
     */	
    @ApiModelProperty("复核人工号")	
    private String reviewBy;	
	
    @ApiModelProperty("复核人名称")	
    private String reviewByName;	
    /**	
     * 盘点状态	
     */	
    @ApiModelProperty("盘点状态")	
    private String checkStatus;	
	
    @ApiModelProperty("创建日期")	
    private Date createOn;	
	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    @ApiModelProperty("库存数量")	
    private BigDecimal stockQty;	
	
    @ApiModelProperty("盘点数量")	
    private BigDecimal existQty;	
	
    @ApiModelProperty("复盘数量")	
    private BigDecimal replayQty;	
	
    @ApiModelProperty("单位")	
    private String unit;	
	
    @ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty("批次")	
    private String batch;	
	
    @ApiModelProperty("备注")	
    private String remarks;

    @ApiModelProperty("库区名称")
    private String areaName;

    @ApiModelProperty("明细id")
    private String detailId;
}	
