package com.imes.domain.entities.crm.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.imes.domain.entities.crm.dto.ex.BaseDto;
import com.imes.domain.entities.crm.po.Region;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * (CrmRegion)实体类
 *
 * <AUTHOR>
 * @since 2022-02-09 17:12:24
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RegionDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 917066037489567100L;
    /**
    * id
    */
    private String id;
    /**
    * 父id
    */
    private String pid;
    /**
     * 编号
     */
    @NotBlank(message = "编号不能为空")
    private String code;
    /**
    * 区域名称
    */
    @NotBlank(message = "区域名称不能为空")
    private String name;
    /**
     * 渠道
     */
    private Boolean channel;
    /**
    * 排序
    */
    private Integer sort;
    /**
    * 备注
    */
    private String remarks;
    /**
     * 客户数
     */
    private Integer customerCount;
    /**
     * 商机数
     */
    private Integer businessCount;
    /**
     * 存在子区域
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hasChildren;
    /**
     * 子区域
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Region> children;

}