package com.imes.domain.entities.query.template.wms;

import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data	
@ApiModel("调拨管理")	
@QueryModel(	
        name = "0232_report",
        remark = "调拨管理-自定义报表",
        searchApi = "/wms/wmsAllotOrder/queryAllotOrderList",
        alias = {"wms_move_order", "wms_move_order_item"},
        showMode = true,
        report = true)
public class WmsMoveOrderReportQueryVo extends BaseModel {
	
	@ApiModelProperty("调拨单号")	
    @QueryField(name = "调拨单号")	
    private String moCode;	
	
	@ApiModelProperty("调拨类型")	
    @QueryField(name = "调拨类型", type = Type.Select, option = {"2", "调拨单"}, value = "2", query = false)	
    private String orderType;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
	@ApiModelProperty("调出仓库")
    @QueryField(name = "调出仓库", alias = "sys_wh_warehouse2.warehouse_name", level = Level.Main)
    private String fromWhName;	
	
	@ApiModelProperty("调入仓库")	
    @QueryField(name = "调入仓库", alias = "sys_wh_warehouse1.warehouse_name", level = Level.Main)
    private String toWhName;	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")	
    private String status;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "wms_move_order_item")
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "ppc_material.material_name")
    private String materialName;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "ppc_material.material_marker")
    private String materialMarker;	
	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "ppc_material.specification")
    private String specification;	
	
	@ApiModelProperty("物料辅助属性")	
    @QueryField(name = "物料辅助属性", alias = "wms_move_order_item")
    private String skuCode;	
	
	@ApiModelProperty("箱码")	
    @QueryField(name = "箱码", alias = "wms_move_order_item")
    private String boxNo;

	@ApiModelProperty("申请人")	
    @QueryField(name = "申请人", alias = "pe_user.user_name", level = Level.Main)
    private String userName;	
	
	@ApiModelProperty("批次")	
    @QueryField(name = "批次", alias = "wms_move_order_item")
    private String batch;	
	
	@ApiModelProperty("移出库位")	
    @QueryField(name = "移出库位", alias = "wms_move_order_item")
    private String outBinCode;	
	
	@ApiModelProperty("移入库位")	
    @QueryField(name = "移入库位", alias = "wms_move_order_item")
    private String inBinCode;	
	
	@ApiModelProperty("调拨基本数量")	
    @QueryField(name = "调拨基本数量", alias = "wms_move_order_item")
    private String orderQty;	
	
	@ApiModelProperty("基本单位")	
    @QueryField(name = "基本单位", alias = "wms_move_order_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    private String unit;	
	
	@ApiModelProperty("调拨数量")	
    @QueryField(name = "调拨数量", alias = "wms_move_order_item")
    private String packQty;	
	
	@ApiModelProperty("库存单位")	
    @QueryField(name = "库存单位", alias = "wms_move_order_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    private String packCodeUnit;	
	
	@ApiModelProperty("第三方系统单号")	
    @QueryField(name = "第三方系统单号")	
    private String thirdOrderCode;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remark;	
	
	@ApiModelProperty("调出公司")	
    @QueryField(name = "调出公司", alias = "co_department1.depart_name", level = Level.Main)
    private String fromCompanyName;	
	
	@ApiModelProperty("调入公司")	
    @QueryField(name = "调入公司", alias = "co_department2.depart_name", level = Level.Main)
    private String toCompanyName;	
}	
