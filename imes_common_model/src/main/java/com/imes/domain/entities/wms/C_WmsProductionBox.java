package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsProductionBox implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 排产单号	
     */	
	@ApiModelProperty("排产单号")	
    private String productionNo;	
	
    /**	
     * 箱码	
     */	
	@ApiModelProperty("箱码")	
    private String boxNo;	
	
}	
