package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.util.Date;	
import java.util.List;	
	
/**	
 * 生产入库	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-04-13 10:21	
 */	
@Data	
public class WmsInboundVo {	
	
    /**	
     * 入库类型（生产入库：1；领料换料：3；领料退料：4; 销售退货入库:5）--必填	
     */	
	@ApiModelProperty("入库类型（生产入库：1；领料换料：3；领料退料：4; 销售退货入库:5）--必填")	
    private String orderType;	
	
    /**	
     * 关联单据类型（生产计划单：1；QMS质检单：2；LIMS检验单：3; 默认值：1）	
     */	
	@ApiModelProperty("关联单据类型（生产计划单：1；QMS质检单：2；LIMS检验单：3; 默认值：1）")	
    private String receiptType;	
	
    /**	
     * 生产相关业务单号--必填	
     */	
	@ApiModelProperty("生产相关业务单号--必填")	
    private String orderCode;	
	
    /**	
     * 生产入库关联单号	
     */	
	@ApiModelProperty("生产入库关联单号")	
    private String receiptCode;	
	
    /**	
     * 是否需要扫码上架（true：需要扫码；false：不需要扫码；默认true）	
     */	
	@ApiModelProperty("是否需要扫码上架（true：需要扫码；false：不需要扫码；默认true）")	
    private Boolean isScan;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    private Date productionDate;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 入库仓库	
     */	
	@ApiModelProperty("入库仓库")	
    private String whCode;	
	
    /**	
     * 入库仓库名称	
     */	
	@ApiModelProperty("入库仓库名称")	
    private String whName;	
	
    /**	
     * 仓库类型	
     */	
	@ApiModelProperty("仓库类型")	
    private String warehouseType;	
	
    /**	
     * 库位类型	
     */	
	@ApiModelProperty("库位类型")	
    private String binCode;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartName;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartCode;	
	
	
    /**	
     * 是否自动入库(true:自动入库 false:不自动入库 默认false)	
     */	
	@ApiModelProperty("是否自动入库(true:自动入库 false:不自动入库 默认false)")	
    private boolean isAutoInStorage;	
	
    /**	
     * 排产模式(1:离散模式，2:流程模式)	
     */	
	@ApiModelProperty("排产模式(1:离散模式，2:流程模式)")	
    private String productionMode;	
	
    /**	
     * 销售订单号	
     */	
	@ApiModelProperty("销售订单号")	
    private String saleNo;	
	
    private String purpose;	
	
    /**	
     * 入库单明细数组--必填	
     */	
	@ApiModelProperty("入库单明细数组--必填")	
    private List<WmsInboundItemVo> items;	
	
    /**	
     * 金蝶生产订单编号	
     */	
	@ApiModelProperty("金蝶生产订单编号")	
    private String FMoBillNo;	
	
    /**	
     * 金蝶生产订单内码	
     */	
	@ApiModelProperty("金蝶生产订单内码")	
    private String FMoId;	
	
    /**	
     * 金蝶生产订单分录内码(生产订单ppNo)	
     */	
	@ApiModelProperty("金蝶生产订单分录内码(生产订单ppNo)")	
    private String FMoEntryId;	
	
    /**	
     * 金蝶生产订单行号	
     */	
	@ApiModelProperty("金蝶生产订单行号")	
    private Integer FBillNoSeq;	
	
    /**	
     * 生产汇报单号	
     */	
	@ApiModelProperty("生产汇报单号")	
    private String PrdMorptNo;	
	
    /**	
     * 金蝶单位	
     */	
	@ApiModelProperty("金蝶单位")	
    private String FUnitID;	
	
    /**	
     * 倒扣料自动投料对象	
     */	
	@ApiModelProperty("倒扣料自动投料对象")	
    private List<WmsOutboundItemVo> voList;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
}	
