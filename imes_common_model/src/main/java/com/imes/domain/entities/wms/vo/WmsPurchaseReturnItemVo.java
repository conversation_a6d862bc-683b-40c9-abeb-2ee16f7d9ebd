package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsPurchaseReturnOrderItem;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsPurchaseReturnItemVo extends WmsPurchaseReturnOrderItem {	
    /**	
     * 标识	
     * 0：保存	
	@ApiModelProperty("标识")	
     * 1：提交	
	@ApiModelProperty("0：保存")	
     */	
	@ApiModelProperty("1：提交")	
    private int flag;	
	
    private String ptoId;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 领用人工号	
     */	
	@ApiModelProperty("领用人工号")	
    private String pickUserCode;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String receiptType;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String orderType;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String orderStatus;	
	
    /**	
     * 可用库存	
     */	
	@ApiModelProperty("可用库存")	
    private BigDecimal availableQty;	
	
    /**	
     * 可用库存	
     */	
	@ApiModelProperty("可用库存")	
    private BigDecimal packAvailableQty;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 第三方系统单号	
     */	
	@ApiModelProperty("第三方系统单号")	
    private String thirdOrderCode;	
	
    /**	
     * 第三方系统单号	
     */	
	@ApiModelProperty("第三方系统单号")	
    private String reason;	
	
    /**	
     * 第三方系统单号	
     */	
	@ApiModelProperty("第三方系统单号")	
    private String purpose;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 可退货数量	
     */	
	@ApiModelProperty("可退货数量")	
    private BigDecimal allowReturnNum;

    /**
     * 基本单位
     */
    @ApiModelProperty("基本单位")
    private String primaryUnit;

    @ApiModelProperty("包装单位")
    private String packCodeUnit;

    @ApiModelProperty("明细id")
    private String detailId;
	
}	
