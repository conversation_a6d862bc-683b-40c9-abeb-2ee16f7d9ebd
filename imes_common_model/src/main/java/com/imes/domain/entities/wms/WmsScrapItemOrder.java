package com.imes.domain.entities.wms;	
	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import javax.persistence.Transient;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
/**	
 * (WmsScrapItemOrder)实体类	
 *	
 * <AUTHOR>	
 * @since 2022-11-03 10:31:55	
 */	
@Data	
@ApiModel(value = "报废单子表")	
public class WmsScrapItemOrder implements Serializable {	
	
    private static final long serialVersionUID = -39607652849716551L;	
	
    @ApiModelProperty(value = "主键", dataType = "String")	
    private String id;	
	
    @ApiModelProperty(value = "报废单主键", dataType = "String")	
    private String scrapId;	
	
    @ApiModelProperty(value = "报废单号", dataType = "String")	
    private String scrapCode;	
	
    @ApiModelProperty(value = "报废单明细号", dataType = "String")	
    private String scrapItemCode;	
	
    @ApiModelProperty(value = "物料编码", dataType = "String")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称", dataType = "String")	
    private String materialName;	
	
    @ApiModelProperty(value = "型号", dataType = "String")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格", dataType = "String")	
    private String specification;	
	
    @ApiModelProperty(value = "基本单位", dataType = "String")	
    private String primaryUnit;	
	
    @ApiModelProperty(value = "报废基本数量", dataType = "BigDecimal")	
    private BigDecimal primaryQty;	
	
    @ApiModelProperty(value = "包装单位", dataType = "String")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "报废包装数量", dataType = "BigDecimal")	
    private BigDecimal packQty;	
	
    @ApiModelProperty(value = "基本单价", dataType = "BigDecimal")	
    private BigDecimal primaryUnitPrice;	
	
    @ApiModelProperty(value = "批次号", dataType = "String")	
    private String batch;	
	
    @ApiModelProperty(value = "关联明细单号", dataType = "String")	
    private String receiptItemCode;	
	
    @ApiModelProperty(value = "第三方系统明细单唯一标识", dataType = "String")	
    private String thirdItemCode;	
	
    @ApiModelProperty(value = "仓库编码", dataType = "String")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称", dataType = "String")	
    private String whName;	
	
    @ApiModelProperty(value = "存储区编码", dataType = "String")	
    private String areaCode;	
	
    @ApiModelProperty(value = "存储区名称", dataType = "String")	
    private String areaName;	
	
    @ApiModelProperty(value = "仓位编码", dataType = "String")	
    private String binCode;	
	
    @ApiModelProperty(value = "仓位名称", dataType = "String")	
    private String binName;	
	
    @ApiModelProperty(value = "优先级", dataType = "Integer")	
    private Integer priority;	
	
    @ApiModelProperty(value = "备注", dataType = "String")	
    private String remarks;	
	
    @ApiModelProperty(value = "创建时间", dataType = "Date")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人", dataType = "String")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间", dataType = "Date")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人", dataType = "String")	
    private String updateBy;	
	
    /**	
     * 库存数量	
     */	
	@ApiModelProperty("库存数量")	
    @Transient	
    private BigDecimal onhandQty;	
	
    /**	
     * 可用数量	
     */	
	@ApiModelProperty("可用数量")	
    @Transient	
    private BigDecimal availableQty;	
	
    /**	
     * 包装可用数量	
     */	
	@ApiModelProperty("包装可用数量")	
    @Transient	
    private BigDecimal packAvailableQty;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    @Transient	
    private int precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    @Transient	
    private int carryMode;

    @ApiModelProperty("辅助属性")
    private String skuCode;

    @ApiModelProperty("箱码")
    private String boxNo;
}	
