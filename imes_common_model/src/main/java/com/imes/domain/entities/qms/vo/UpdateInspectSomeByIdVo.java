package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.validation.constraints.NotBlank;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class UpdateInspectSomeByIdVo implements Serializable {	
    /**	
     *子项id	
     */	
    @NotBlank(message = "子项id不能为空")	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 子项名称	
     */	
	@ApiModelProperty("子项名称")	
    @NotBlank(message = "子项名称不能为空")	
    private String itemName;	
	
    /**	
     * 检测工具数据字典	
     */	
	@ApiModelProperty("检测工具数据字典")	
    private String toolType;	
	
    /**	
     * 判定方式"1", "参数判定", "2", "主观判定"	
     */	
	@ApiModelProperty("判定方式\"1\", \"参数判定\", \"2\", \"主观判定\"")
    private String judgeMethod;	
	
    /**	
     * 上限	
     */	
	@ApiModelProperty("上限")	
    private BigDecimal upperLimit;	
	
    /**	
     * 下限	
     */	
	@ApiModelProperty("下限")	
    private BigDecimal lowerLimit;	
	
    /**	
     * 非理化标准	
     */	
	@ApiModelProperty("非理化标准")	
    private String standardDesc;	
	
    /**	
     * 是否免检；1免检	
     */	
	@ApiModelProperty("是否免检；1免检")	
    private String isExempt;	
	
    private static final long serialVersionUID = 1L;	
}	
