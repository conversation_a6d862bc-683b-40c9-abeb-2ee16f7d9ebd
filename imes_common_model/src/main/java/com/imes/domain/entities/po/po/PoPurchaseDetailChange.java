package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "江森采购变更单-子表")
public class PoPurchaseDetailChange implements Serializable {

    private static final long serialVersionUID = 893164860100127123L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "物料行号")
    private String sdNo;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "物料型号")
    private String materialMarker;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "产品大类")
    private String category;

    @ApiModelProperty(value = "产品类型")
    private String materialTypeCode;

    @ApiModelProperty(value = "单位")
    private String primaryUnit;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "未税金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "需求回复数量")
    private BigDecimal replyQty;

    @ApiModelProperty(value = "总金额")
    private BigDecimal allPrice;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "物料行号")
    private Integer sort;

    @ApiModelProperty(value = "计划到货时间")
    private Date estimatedDeliveryDate;

    @ApiModelProperty(value = "需求回复时间")
    private Date requestedDeliveryDate;

    @ApiModelProperty(value = "实际到货时间")
    private Date actualDeliveryDate;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    @ApiModelProperty(value = "行关闭人")
    private String closePerson;

    @ApiModelProperty(value = "业务状态")
    private String businessStatus;

    @ApiModelProperty(value = "旧图号")
    private String oldDwgNo;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "是否含税 0是1否")
    private String includeTax;

    @ApiModelProperty(value = "刻印号")
    private String engravingNumber;

    @ApiModelProperty(value = "行状态 10-采购已提交 20-采购已生效 30-供应商已拒绝")
    private String status;

    @ApiModelProperty(value = "锁定状态 10-未锁定 20-已锁定")
    private String lockupStatus;

    @ApiModelProperty(value = "行版本 用于区分数据")
    private Integer detailVer;

    @ApiModelProperty(value = "推送供应商状态 10-未推送 20-已推送")
    private String supplierStatus;

    //品牌
    private String brand;
    //标准/非标件
    private String standardPart;
}