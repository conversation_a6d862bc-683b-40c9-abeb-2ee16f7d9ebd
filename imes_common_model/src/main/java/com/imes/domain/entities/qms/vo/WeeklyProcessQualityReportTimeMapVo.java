package com.imes.domain.entities.qms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WeeklyProcessQualityReportTimeMapVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("时间范围")
    private String weekRange;

    @ApiModelProperty("值")
    private BigDecimal weekNum;

    @ApiModelProperty("面积")
    private BigDecimal area;

    @ApiModelProperty("件/1000平方米数量")
    private BigDecimal itemsPerThousandSqm;

    @ApiModelProperty("时间集合")
    private List<String> dateList;



}	
