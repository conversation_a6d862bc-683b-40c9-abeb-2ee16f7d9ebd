package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
/**	
 * 黑名单管理(HrBlacklist)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-03-16 14:17:51	
 */	
@Data	
@ApiModel("黑名单管理")	
@QueryModel(name = "0697",	
        remark = "黑名单管理",	
        alias = "hr_blacklist",	
        searchApi = "/api/hr/blacklist/query")	
public class HrBlacklistQueryVo extends BaseModel {	
	
    /**	
     * 姓名	
     */	
	@ApiModelProperty("姓名")	
    @QueryField(name = "姓名" )	
    private String name;	
    /**	
     * 手机	
     */	
	@ApiModelProperty("手机")	
    @QueryField(name = "手机" )	
        private String phone;	
    /**	
     * 邮箱	
     */	
	@ApiModelProperty("邮箱")	
    @QueryField(name = "邮箱" )	
    private String mail;	
    /**	
     * 身份证	
     */	
	@ApiModelProperty("身份证")	
    @QueryField(name = "身份证", show = false)	
    private String idCard;	
    /**	
     * 来源	
     */	
	@ApiModelProperty("来源")	
    @QueryField(name = "来源", type = Type.MultiSelect, dictOption = "HR_BLACKLIST_SOURCE")	
    private String source;	
    /**	
     * 是否强制拒绝入职或返岗	
     */	
	@ApiModelProperty("费用明细")	
    @QueryField(name = "费用明细" , type = Type.Select, option = {"1", "是", "2", "否"}, logic= Logic.Eq)	
    private String compulsoryRefusal;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人" )	
    private String createBy;	
	
	
}	
	
