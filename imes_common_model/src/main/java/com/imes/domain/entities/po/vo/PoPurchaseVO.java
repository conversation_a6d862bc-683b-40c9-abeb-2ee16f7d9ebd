package com.imes.domain.entities.po.vo;

import com.imes.domain.entities.po.po.PoPurchase;
import com.imes.domain.entities.ppc.vo.PpcSaleDetailVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class PoPurchaseVO extends PoPurchase {
    //订单下单开始日期
    private String beginTime;
    //订单下单结束日期
    private String endTime;
    //订单状态中文
    private String orderStatusName;
    //订单状态List 用于分页查询
    private List<String> orderStatusList;
    //子单明细
    private List<PoPurchaseDetailVo> poPurchaseDetailVoList;
    //更新标志 add update delete
    private String updateFlg;
    //总金额
    private BigDecimal allPrice;
    //金额是否需要重新计算 1需要
    private String needComputePrice;
    //用于对外接口下单日期
    private String orderDateString;

}
