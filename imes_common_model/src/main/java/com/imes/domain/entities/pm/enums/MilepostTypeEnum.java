package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;
import java.util.Optional;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum MilepostTypeEnum {
    TASK(0, "任务"),
    // 开始->结束
    MILEPOST(1, "里程碑");
    @EnumValue
    private final Integer type;
    private final String typeStr;

    MilepostTypeEnum(Integer type, String typeStr) {
        this.type = type;
        this.typeStr = typeStr;
    }

    public static MilepostTypeEnum match(Integer type) {
        return Arrays.stream(MilepostTypeEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("里程碑类型"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static MilepostTypeEnum des(final JsonNode jsonNode) {
        Integer state = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("type")).map(JsonNode::asInt).orElse(-1);
        if (state == -1) {
            return null;
        }
        return MilepostTypeEnum.match(state);
    }

    public Integer getType() {
        return type;
    }

    public String getTypeStr() {
        return typeStr;
    }


    @Override
    public String toString() {
        return this.getTypeStr();
    }
}
