package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockHistory implements Serializable {	
	
    private static final long serialVersionUID = 506655432536977327L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "操作类型（上架，下架，移库，报废.....）")	
    private String optionType;	
	
    @ApiModelProperty(value = "工厂编码")	
    private String ftyCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "批次")	
    private String batch;	
	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "仓位")	
    private String binCode;	
	
    @ApiModelProperty(value = "老的在库库存")	
    private BigDecimal oldOnhandQty;	
	
    @ApiModelProperty(value = "新的在库库存")	
    private BigDecimal newOnhandQty;	
	
    @ApiModelProperty(value = "库存变化量")	
    private BigDecimal qty;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "关联单据单号(入/出库单)")	
    private String receiptCode;	
	
    @ApiModelProperty(value = "关联单据类型")	
    private String receiptType;	
	
    @ApiModelProperty(value = "老的库位库存")	
    private BigDecimal oldBinQty;	
	
    @ApiModelProperty(value = "新的库位库存")	
    private BigDecimal newBinQty;	
	
    @ApiModelProperty(value = "老的仓库库存")	
    private BigDecimal oldWhQty;	
	
    @ApiModelProperty(value = "新的仓库库存")	
    private BigDecimal newWhQty;	
	
    @ApiModelProperty(value = "包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "包装在库库存")	
    private BigDecimal packOnhandQty;	
	
    @ApiModelProperty(value = "包装可用库存")	
    private BigDecimal packAvailableQty;	
	
    @ApiModelProperty(value = "辅助属性")	
    private String skuCode;	
	
    @ApiModelProperty(value = "箱号")	
    private String boxNo;	
	
    @ApiModelProperty(value = "载具编码")	
    private String cellCode;	
	
}	
