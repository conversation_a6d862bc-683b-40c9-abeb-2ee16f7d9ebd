package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsDailySituationAnalysis {	
	
    @ApiModelProperty(value = "入库作业")	
    private int inboundTask;	
	
    @ApiModelProperty(value = "出库作业")	
    private int outboundTask;	
	
    @ApiModelProperty(value = "移库")	
    private int moveOrderTask;	
	
    @ApiModelProperty(value = "调拨")	
    private int allotOrderTask;	
	
    @ApiModelProperty(value = "已发货")	
    private int deliveredTask;	
	
    @ApiModelProperty(value = "待发货")	
    private int waitDeliverTask;	
}	
