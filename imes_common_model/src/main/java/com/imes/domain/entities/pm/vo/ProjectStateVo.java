package com.imes.domain.entities.pm.vo;

import com.imes.domain.entities.pm.enums.StateExEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@ApiModel("项目状态")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectStateVo {
    @NotBlank(message = "项目id不能为空")
    private String id;
    @NotNull(message = "项目状态不能为空")
    private String status;


}
