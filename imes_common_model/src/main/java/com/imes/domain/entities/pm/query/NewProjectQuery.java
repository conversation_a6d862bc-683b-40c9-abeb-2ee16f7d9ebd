package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<新增项目条件查询>>
 * @company 捷创智能技术有限公司
 * @create 2021-07-09 9:25
 */
@ApiModel("《新增项目》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class NewProjectQuery {
    @ApiModelProperty("月份")
    private Integer month;
    @ApiModelProperty("公司")
    private String company;
}
