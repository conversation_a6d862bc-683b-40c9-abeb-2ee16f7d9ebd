package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsOutStorageVo {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 出库单编码	
     */	
	@ApiModelProperty("出库单编码")	
    private String storageOutCode;	
	
    /**	
     * 单据来源（如从ERP获得，从MES获取，手工建单等）	
     */	
	@ApiModelProperty("单据来源（如从ERP获得，从MES获取，手工建单等）")	
    private String source;	
	
    /**	
     * 出库单类型	
     */	
	@ApiModelProperty("出库单类型")	
    private String receiptType;	
	
    /**	
     * 关联的单据号	
     */	
	@ApiModelProperty("关联的单据号")	
    private String receiptCode;	
	
    /**	
     * 过账日期	
     */	
	@ApiModelProperty("过账日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date postingDate;	
	
    /**	
     * 单据状态（1-草稿；2-未完成；3-已完成）	
     */	
	@ApiModelProperty("单据状态（1-草稿；2-未完成；3-已完成）")	
    private String status;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 仓库	
     */	
	@ApiModelProperty("仓库")	
    private String whName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 出账单明细集合	
     */	
	@ApiModelProperty("出账单明细集合")	
    private List<WmsOutStorageItemVo> details;	
	
    private List<WmsStorageOutBillItemVo> itemVos;	
	
	
}	
