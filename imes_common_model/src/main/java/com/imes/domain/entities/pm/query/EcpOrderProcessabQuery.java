package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《AB采购合同》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-28 14:29
 */
@ApiModel("《AB采购合同》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpOrderProcessabQuery extends BaseQuery {
    @ApiModelProperty("销售合同号")
    private String htBid;
    @ApiModelProperty("品牌")
    private String gBrand;

}
