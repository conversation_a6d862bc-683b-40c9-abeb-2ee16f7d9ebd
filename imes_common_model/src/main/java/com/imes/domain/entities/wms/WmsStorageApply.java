package com.imes.domain.entities.wms;	
	
import com.imes.domain.entities.wms.vo.WmsStorageApplyItemVo;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@ApiModel(value = "入库申请表")	
public class WmsStorageApply implements Serializable {	
	
    private static final long serialVersionUID = 373439648618524361L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "作业申请单号")	
    private String applyCode;	
	
    @ApiModelProperty(value = "单据来源（手工建单,第三方系统）")	
    private String source;	
	
    @ApiModelProperty(value = "申请单类型（1:生产入库、2:领料出库）")	
    private String orderType;	
	
    @ApiModelProperty(value = "关联单号")	
    private String receipCode;	
	
    @ApiModelProperty(value = "第三方系统单号")	
    private String thirdOrderCode;	
	
    @ApiModelProperty(value = "仓库号")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库号")	
    private String whName;	
	
    @ApiModelProperty(value = "所属公司编码")	
    private String companyCode;	
	
    @ApiModelProperty(value = "所属公司名称")	
    private String companyName;	
	
    @ApiModelProperty(value = "申请部门编号")	
    private String applyDepartCode;	
	
    @ApiModelProperty(value = "申请部门名称")	
    private String applyDepartName;	
	
    @ApiModelProperty(value = "申请员工工号")	
    private String applyUserCode;	
	
    @ApiModelProperty(value = "申请员工名称")	
    private String applyUserName;	
	
    @ApiModelProperty(value = "单据状态（未完成、已审核、已完成、已取消）")	
    private String orderStatus;	
	
    @ApiModelProperty(value = "备注")	
    private String remark;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "逻辑删除标识")	
    private Integer deleteStatus;	
	
    @ApiModelProperty(value = "关联类型")	
    private String receiptType;	
	
    @ApiModelProperty(value = "原因")	
    private String reason;	
	
    @ApiModelProperty(value = "生产日期")	
    private Date productionDate;	
	
    @ApiModelProperty(value = "申请日期")	
    private Date applyOn;	
	
    @ApiModelProperty(value = "销售订单号")	
    private String saleNo;	
	
    @ApiModelProperty(value = "关联父级单据")	
    private String parentCode;	
	
    private List<WmsStorageApplyItem> items;	
	
    private List<WmsStorageApplyItemVo> itemVoList;	
}	
