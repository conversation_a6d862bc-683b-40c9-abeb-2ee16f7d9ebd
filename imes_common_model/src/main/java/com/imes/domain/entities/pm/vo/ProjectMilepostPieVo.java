package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目里程碑饼图>>
 * @company 捷创智能技术有限公司
 * @create 2021-10-29 13:18
 */
@ApiModel("项目里程碑饼图")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostPieVo {
    private String name;
    private String color;
    private Map<String,Object> itemStyle;
    private Long value;
}
