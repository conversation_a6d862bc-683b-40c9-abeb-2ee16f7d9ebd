package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.ppc.po.Promaterial;
import com.imes.domain.entities.wms.WmsArrivalOrderItem;
import com.imes.domain.entities.wms.WmsBusTaskHistory;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalOrderItemVo extends WmsArrivalOrderItem {	
	
    /**	
     * 到货单主表主键	
     */	
	@ApiModelProperty("到货单主表主键")
    private String aoId;


    /**
     * 到货单明细表主键
     */
    @ApiModelProperty("到货单明细表主键")
    private String detailId;
	
    /**	
     * 到货单类型	
     */	
	@ApiModelProperty("到货单类型")
    private String orderType;	
	
    /**	
     * 到货单状态	
     */	
	@ApiModelProperty("到货单状态")
    private String orderStatus;	
	
    /**	
     * 审核人编码	
     */	
	@ApiModelProperty("审核人编码")
    private String approveUserCode;	
	
    /**	
     * 审核人名称	
     */	
	@ApiModelProperty("审核人名称")
    private String approveUserName;	
	
    /**	
     * 到货时间	
     */	
	@ApiModelProperty("到货时间")
    private Date arrivalTime;	
	
    /**	
     * 收货人编码	
     */	
	@ApiModelProperty("收货人编码")
    private String receiverCode;	
	
    /**	
     * 收货人名称	
     */	
	@ApiModelProperty("收货人名称")
    private String receiverName;	
	
    /**	
     * 物料类型	
     */	
	@ApiModelProperty("物料类型")
    private String category;	
	
    /**	
     * 物料类型	
     */	
	@ApiModelProperty("物料类型")
    private String materialTypeCode;	
	
    /**	
     * 物料类型	
     */	
	@ApiModelProperty("物料类型")
    private String typeName;	
	
    /**	
     * 采购单号	
     */	
	@ApiModelProperty("采购单号")
    private String poCode;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")
    private String applyCompanyCode;
	
    /**	
     * 工厂名称	
     */	
	@ApiModelProperty("工厂名称")
    private String applyCompanyName;	
	
    /**	
     * 车牌号	
     */	
	@ApiModelProperty("车牌号")
    private String licensePlate;	
	
    /**	
     * 送货司机	
     */	
	@ApiModelProperty("送货司机")
    private String deliveryDriver;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")
    private String supplierName;	
	
    /**	
     * 检验方式1批检2波检	
     */	
	@ApiModelProperty("检验方式1批检2波检")
    private String inspectionType;	
	
    /**	
     * 默认存储仓库	
     */	
	@ApiModelProperty("默认存储仓库")
    private String defaultWhCode;	
	
    /**	
     * 默认存储仓库	
     */	
	@ApiModelProperty("默认存储仓库")
    private String defaultWhName;	
	
    /**	
     * 默认存储库位	
     */	
	@ApiModelProperty("默认存储库位")
    private String defaultBinCode;	
	
    /**	
     * 物流公司编码	
     */	
	@ApiModelProperty("物流公司编码")
    private String shipperCode;	
	
    /**	
     * 物流公司名称	
     */	
	@ApiModelProperty("物流公司名称")
    private String shipperName;	
	
    /**	
     * 关联单据类型：采购单、退货单、到货计划单	
     */	
	@ApiModelProperty("关联单据类型：采购单、退货单、到货计划单")
    private String receiptType;	
	
    /**	
     * 收货员	
     */	
	@ApiModelProperty("收货员")
    private String receiver;	
	
    /**	
     * 司机联系方式	
     */	
	@ApiModelProperty("司机联系方式")
    private String driverPhone;	
	
    /**	
     * 司机证件号	
     */	
	@ApiModelProperty("司机证件号")
    private String idCard;
	
    /**	
     * 车辆类型	
     */	
	@ApiModelProperty("车辆类型")
    private String carType;	

    @ApiModelProperty("制造商编码")	
    private String manufacturerCode;	

    @ApiModelProperty("制造商名称")	
    private String manufacturerName;	

    @ApiModelProperty("供应商单号")	
    private String customerOrderCode;	

    @ApiModelProperty("第三方系统单号")	
    private String thirdOrderCode;	

    @ApiModelProperty("可退货数量")	
    private BigDecimal returnQty;	

    @ApiModelProperty("生产日期")	
    private String productionDateStr;	

    @ApiModelProperty("失效日期")	
    private String failureDateStr;
    @ApiModelProperty("是否启用批次管理")	
    private String isBatch;	

    @ApiModelProperty("是否自动生成批次号")	
    private String autoBatchNo;	

    @ApiModelProperty("是否启用序列号管理")	
    private String isSerial;	

    @ApiModelProperty("是否自动生成SN码")	
    private String autoSerialNo;	

    @ApiModelProperty("进位方式")	
    private Byte carryMode;	

    @ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	

    @ApiModelProperty("库存可用数量")	
    private BigDecimal availableQty;	
	

    @ApiModelProperty("库存可用包装数量")	
    private BigDecimal packAvailableQty;	
	

    @ApiModelProperty("到货单位")	
    private String packCodeUnit;	
	
    /**	
     * 备注	
     */	

    @ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 可退货数量	
     */
    @ApiModelProperty("可退货数量")	
    private BigDecimal allowReturnNum;	
	
    /**	
     * 箱数	
     */
    @ApiModelProperty("箱数")	
    private String boxNum;	
	
    /**	
     * 待上架数量	
     */
    @ApiModelProperty(value = "待上架数量")	
    private BigDecimal needPutQty;	
	
    /**	
     * 预入库列表	
     */	
	@ApiModelProperty("预入库列表")
    private List<WmsArrivalTaskVo> taskList;	
	
	
    private Map<String, List<WmsArrivalTaskVo>> taskMap;	
	
	
    private Promaterial promaterial;	
	
	@ApiModelProperty("创建人")	
    private String createByName;	
	
    //采购订单行号
    private String poItemCode;	
	
    /**	
     * 单据来源	
     */	
	@ApiModelProperty("单据来源")
    private String source;	
	
	
	
    /**	
     * 销售订单明细单号	
     */	
	@ApiModelProperty("销售订单明细单号")
    private String soItemCode;	
	
    /**	
     * 是否启用箱码管理	
     */	
	@ApiModelProperty("是否启用箱码管理")
    private String enableCaseNumber;	
	
    /**	
     * 最小装箱数量	
     */	
	@ApiModelProperty("最小装箱数量")
    private BigDecimal minNumberOfBoxes;	
	
    /**	
     * 来料检验顺序1先到货后检验2先检验后到货	
     */	
	@ApiModelProperty("来料检验顺序1先到货后检验2先检验后到货")
    private String incomingInspectionSequence;	
	
    /**	
     * SN码	
     */	
	@ApiModelProperty("SN码")
    private List<WmsBusTaskHistory> snList;	
	
    /**	
     * 箱码	
     */	
	@ApiModelProperty("箱码")
    private List<WmsPackingBoxMaterialVo> boxMaterialList;	
	
    /**	
     * 物料包装单位	
     */	
	@ApiModelProperty("物料包装单位")	
    private List<String> packUnitCodeList;	
	
    /**	
     * 仓库编码（物料默认）	
     */	
	@ApiModelProperty("仓库编码（物料默认）")	
    private String whCode;	
	
    /**	
     * 仓库名称（物料默认）	
     */	
	@ApiModelProperty("仓库名称（物料默认）")	
    private String whName;	
	
    /**	
     * 库位编码（物料默认）	
     */	
	@ApiModelProperty("库位编码（物料默认）")	
    private String binCode;	
	
    /**	
     * 包装单位名称	
     */	
	@ApiModelProperty("包装单位名称")	
    private String packNameUnit;

    @ApiModelProperty("是否自动生产批次")
    private String autoGenerateBatchNum;
	
}	
