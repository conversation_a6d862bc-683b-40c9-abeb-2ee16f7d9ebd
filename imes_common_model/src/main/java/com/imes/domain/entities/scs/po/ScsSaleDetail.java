package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "供应链销售订单子表")
public class ScsSaleDetail implements Serializable {

    private static final long serialVersionUID = -53423882078630028L;

    private String id;

    @ApiModelProperty(value = "销售总订单id")
    private String mainId;

    @ApiModelProperty(value = "子订单号")
    private String sdNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "旧图号")
    private String oldDwgNo;

    @ApiModelProperty(value = "材质")
    private String quality;

    @ApiModelProperty(value = "型号")
    private String modelNumber;

    @ApiModelProperty(value = "颜色")
    private String color;

    @ApiModelProperty(value = "订单数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "已发货数量")
    private BigDecimal sendedQty;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "包装规格")
    private String packCode;

    @ApiModelProperty(value = "规格数量")
    private BigDecimal packNum;

    @ApiModelProperty(value = "生产标准")
    private String produceStandard;

    @ApiModelProperty(value = "BOM编码")
    private String bomCode;

    @ApiModelProperty(value = "bom版本(可指定版本，如无值，则找启用的bom版本)")
    private String bomVer;

    @ApiModelProperty(value = "10-未下达, 20-下达生产,30-已开工,40-生产完工")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    //行锁状态
    private String lockupStatus;
    //行版本
    private Integer detailVer;

    @ApiModelProperty(value = "主生产计划生成状态，0未生成，1已生成")
    private Integer mpsStatus;

    @ApiModelProperty(value = "销售员工号")
    private String salesPersonCode;

    @ApiModelProperty(value = "计划员工号")
    private String planerCode;

    @ApiModelProperty(value = "计划员名称")
    private String planerName;

    @ApiModelProperty(value = "成品率，计算计划加工量用")
    private BigDecimal goodRate;

    @ApiModelProperty(value = "产品背景图片名称")
    private String productBackgroundPicName;

    @ApiModelProperty(value = "产品背景图片网络地址")
    private String productBackgroundPicUrl;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    @ApiModelProperty(value = "产品背景图片显示名称")
    private String productBackgroundViewName;

    @ApiModelProperty(value = "自定义背景图")
    private String customPicBase64;

    @ApiModelProperty(value = "产品入库库位")
    private String productInputSite;

    @ApiModelProperty(value = "产品出库库位")
    private String productOutputSite;

    @ApiModelProperty(value = "包装名称")
    private String packName;

    @ApiModelProperty(value = "商品单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "不税额金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "价税合计(总金额)")
    private BigDecimal allPrice;

    @ApiModelProperty(value = "到货时间")
    private Date deliveryDate;

    @ApiModelProperty(value = "发货时间")
    private Date shipDate;

    @ApiModelProperty(value = "发货月台")
    private String deliveryPlatform;

    @ApiModelProperty(value = "单据来源")
    private String documentSource;

    @ApiModelProperty(value = "合并批次号")
    private String mergeBatch;

    @ApiModelProperty(value = "税率（0~1）")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "折扣率（0~1）")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "客户订单号")
    private String customerSaleNo;

    @ApiModelProperty(value = "客户子订单号")
    private String customerSaleDetail;

    @ApiModelProperty(value = "客户物料编码")
    private String customerMaterialCode;

    @ApiModelProperty(value = "客户物料名称")
    private String customerMaterialName;

    @ApiModelProperty(value = "客户物料规格")
    private String customerMaterialMarker;

    @ApiModelProperty(value = "客户商品描述")
    private String customerMaterialDescription;

    @ApiModelProperty(value = "商品类型1商品；2备品；3赠品")
    private String productType;

    @ApiModelProperty(value = "客户指标")
    private String customerIndicator;

    @ApiModelProperty(value = "客户要求到货时间")
    private Date customerDeliveryDate;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "辅单位")
    private String auxiliaryUnit;

    @ApiModelProperty(value = "辅单位数量")
    private BigDecimal auxiliaryUnitQty;

    //取价类型 0普通1大宗2锁铜
    private String isBulk;

    private String brand;

    private String standardPart;
}