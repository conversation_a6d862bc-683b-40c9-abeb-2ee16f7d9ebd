package com.imes.domain.entities.query.template.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.system.SysUserFieldRe;	
	
import com.imes.domain.entities.wms.WmsArrivalOrderItem;	
import com.imes.domain.entities.wms.vo.WmsArrivalOrderItemVo;	
import lombok.Data;	
	
import java.util.List;	
import java.util.Map;	
	
/**	
 * 采购到货（总仓版）	
 */	
@Data	
@ApiModel("采购到货")	
@QueryModel(name = "0240-1",	
            remark = "采购到货",	
            alias = "wms_arrival_order",	
            showMode = true,	
            searchApi = "/wms/arrival/queryAoAndItemListVo")	
public class WmsProcuredGoodsQueryVo extends BaseModel {	
	
    private String id;	
	
	@ApiModelProperty("到货单号")	
    @QueryField(name = "到货单号")	
    private String aoCode;	
	
	@ApiModelProperty("到货类型")	
    @QueryField(name = "到货类型", type = Type.Select, dictOption = "ARRIVAL_TYPE")	
    private String orderType;	
	
	@ApiModelProperty("到货日期")	
    @QueryField(name = "到货日期", type = Type.Date)	
    private String arrivalTime;	
	
	@ApiModelProperty("关联单号")	
    @QueryField(name = "关联单号", show = false)	
    private String mainReceiptCode;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "wms_arrival_order_item")	
    private String materialMarker;	
	
	@ApiModelProperty("RA系统号")	
    @QueryField(name = "RA系统号", alias = "wms_arrival_order_item")	
    private String raSysCode;	
	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "wms_arrival_order_item")	
    private String specification;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", alias = "wms_arrival_order_item", type = Type.Number)	
    private String arrivalQty;	
	
	@ApiModelProperty("订单号")	
    @QueryField(name = "订单号", alias = "po_purchase_detail_standard")	
    private String requestDocCode;	
	
	@ApiModelProperty("主单位")	
    @QueryField(name = "主单位", alias = "wms_arrival_order_item")	
    private String unit;	
	
	@ApiModelProperty("箱号")	
    @QueryField(name = "箱号", alias = "wms_arrival_order_item")	
    private String boxNo;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "wms_arrival_order_item")	
    private String materialName;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号", alias = "wms_arrival_order_item")	
    private String receiptCode;	
	
	@ApiModelProperty("项目名称")	
    @QueryField(name = "项目名称", alias = "wms_arrival_order_item")	
    private String projectName;	
	
	@ApiModelProperty("供应商")	
    @QueryField(name = "供应商", alias = "wms_arrival_order_item")	
    private String supplierName;	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称", alias = "wms_arrival_order_item")	
    private String customerName;	
	
	@ApiModelProperty("仓库编码")	
    @QueryField(name = "仓库编码")	
    private String whCode;	
	
	@ApiModelProperty("收货仓库")	
    @QueryField(name = "收货仓库")	
    private String whName;	
	
	@ApiModelProperty("收货员工号")	
    @QueryField(name = "收货员工号")	
    private String receiver;	
	
	@ApiModelProperty("收货员名称")	
    @QueryField(name = "收货员名称")	
    private String receiverName;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")	
    private String orderStatus;	
	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", type = Type.Date)	
    private String updateOn;	
	
	@ApiModelProperty("发送状态")	
    @QueryField(name = "发送状态", type = Type.Select, option = {"0", "未发送", "1", "已发送"})	
    private String sendStatus;	
	
	@ApiModelProperty("入库状态")	
    @QueryField(name = "入库状态", type = Type.Select, dictOption = "ARRIVAL_INBOUND_STATUS")	
    private String storageStatus;	
	
	@ApiModelProperty("库存状态")	
    @QueryField(name = "库存状态", type = Type.Select, dictOption = "ARRIVAL_STOCK_STATUS")	
    private String stockStatus;	
	
	@ApiModelProperty("物流公司")	
    @QueryField(name = "物流公司", type = Type.Select, dictOption = "LOGISTICS_COMPANY")	
    private String shipperCode;	
	
	@ApiModelProperty("物流单号")	
    @QueryField(name = "物流单号")	
    private String logisticCode;	
	
	@ApiModelProperty("主单备注")	
    @QueryField(name = "主单备注")	
    private String remarks;	
	
	@ApiModelProperty("单据来源")	
    @QueryField(name = "单据来源", type = Type.Select, dictOption = "ORDER_SOURCE")	
    private String source;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
    /**	
     * 用户自定义属性	
     */	
    private List<SysUserFieldRe> userFields;	
    /**	
     * 展示字段	
     */	
    private List<Map<String, Object>> showFields;	
	
    /**	
     * 到货明细单	
     */	
    private List<WmsArrivalOrderItemVo> itemVos;	
	
    /**	
     * 到货明细单	
     */	
    private List<WmsArrivalOrderItem> items;	
}	
