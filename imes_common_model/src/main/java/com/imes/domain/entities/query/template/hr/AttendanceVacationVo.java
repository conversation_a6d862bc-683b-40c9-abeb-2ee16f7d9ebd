package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * (AttendanceShift)实体类	
 *	
 * <AUTHOR> z 	
 * @since 2022-10-11 14:59:07	
 */	
@Data	
@ApiModel("考勤假期")	
@QueryModel(name = "0670",	
        remark = "考勤假期",	
        alias = "hr_attendance_vacation",	
        searchApi = "/api/hr/attendance/query/vacation")	
public class AttendanceVacationVo extends BaseModel {	
    /**	
     * 员工工号	
     */	
	@ApiModelProperty("员工工号")	
    @QueryField(name = "员工工号")	
    private String userCode;	
    /**	
     * 员工姓名	
     */	
	@ApiModelProperty("员工姓名")	
    @QueryField(name = "员工姓名")	
    private String userName;	
    /**	
     * 所在部门编码	
     */	
	@ApiModelProperty("所在部门编码")	
    @QueryField(name = "所在部门编码")	
    private String deptCode;	
    /**	
     * 假期类型	
     */	
	@ApiModelProperty("假期类型")	
    @QueryField(name = "假期类型", type = Type.MultiSelect, dictOption = "VACATION_TYPE")	
    private String vacationType;	
    /**	
     * 假期年度	
     */	
	@ApiModelProperty("假期年度")	
    @QueryField(name = "假期年度")	
    private String vacationYear;	
    /**	
     * 标准额度	
     */	
	@ApiModelProperty("标准额度")	
    @QueryField(name = "标准额度")	
    private String standardLine;	
    /**	
     * 增减额度	
     */	
	@ApiModelProperty("增减额度")	
    @QueryField(name = "增减额度")	
    private String changeLine;	
    /**	
     * 已用额度	
     */	
	@ApiModelProperty("已用额度")	
    @QueryField(name = "已用额度")	
    private String usedLine;	
    /**	
     * 剩余额度	
     */	
	@ApiModelProperty("剩余额度")	
    @QueryField(name = "剩余额度")	
    private String remainingLine;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
	
}	
	
