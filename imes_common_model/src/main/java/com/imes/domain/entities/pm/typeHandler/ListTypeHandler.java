package com.imes.domain.entities.pm.typeHandler;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;
import org.springframework.util.CollectionUtils;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<List集合转换>>
 * @company 捷创智能技术有限公司
 * @create 2021-12-20 16:58
 */
public class ListTypeHandler implements TypeHandler<List<String>> {
    @Override
    public void setParameter(PreparedStatement preparedStatement, int i, List<String> strings, JdbcType jdbcType) throws SQLException {
        if (!CollectionUtils.isEmpty(strings)) {
            StringBuffer sb = new StringBuffer();
            for (String s : strings) {
                sb.append(s).append(",");
            }
            preparedStatement.setString(i, sb.toString().substring(0, sb.toString().length() - 1));
        } else {
            preparedStatement.setString(i, null);
        }
    }

    @Override
    public List<String> getResult(ResultSet resultSet, String s) throws SQLException {
        String arrayStr = resultSet.getString(s);
        if (StringUtils.isBlank(arrayStr)) {
            return Lists.newArrayList();
        }
        String[] arr = arrayStr.split(",");
        return Lists.newArrayList(arr);
    }

    @Override
    public List<String> getResult(ResultSet resultSet, int i) throws SQLException {
        String arrayStr = resultSet.getString(i);
        if (StringUtils.isBlank(arrayStr)) {
            return Lists.newArrayList();
        }
        String[] arr = arrayStr.split(",");
        return Lists.newArrayList(arr);
    }

    @Override
    public List<String> getResult(CallableStatement callableStatement, int i) throws SQLException {
        String arrayStr = callableStatement.getString(i);
        if (StringUtils.isBlank(arrayStr)) {
            return Lists.newArrayList();
        }
        String[] arr = arrayStr.split(",");
        return Lists.newArrayList(arr);
    }
}
