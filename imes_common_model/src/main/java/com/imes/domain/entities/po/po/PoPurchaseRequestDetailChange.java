package com.imes.domain.entities.po.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 采购申请变更单明细(PoPurchaseRequestDetailChange)实体类
 *
 * <AUTHOR>
 * @since 2023-07-21 14:26:33
 */
@Data
@ApiModel("采购申请变更单明细")
public class PoPurchaseRequestDetailChange implements Serializable {
    private static final long serialVersionUID = 996369470291223146L;

    private String id;

    private String mainId;

    @ApiModelProperty(value = "申请单号")
    private String requestNo;

    @ApiModelProperty(value = "行号")
    private Integer lineNo;

    @ApiModelProperty(value = "原申请数量")
    private BigDecimal oldRequestQty;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "原申请到货日期")
    private Date oldRequestArriveDate;

    @ApiModelProperty(value = "原备注")
    private String oldRemarks;

    @ApiModelProperty(value = "原辅助属性")
    private String oldSkuCode;

    @ApiModelProperty(value = "变更类型0-无变更 1-新增 2-修改 3-删除")
    private String changeType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "型号")
    private String modelNumber;

    @ApiModelProperty(value = "申请数量")
    private BigDecimal requestQty;

    @ApiModelProperty(value = "申请数量单位")
    private String unit;

    @ApiModelProperty(value = "关联数量")
    private BigDecimal relationQty;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "基础单位数量")
    private BigDecimal baseUnitQty;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "申请到货日期")
    private Date requestArriveDate;

    @ApiModelProperty(value = "来源单据类型")
    private String businessSource;

    @ApiModelProperty(value = "来源单据号")
    private String businessNo;

    @ApiModelProperty(value = "源单行号")
    private String businessLineNo;

    @ApiModelProperty(value = "来源方式")
    private String businessWay;

    @ApiModelProperty(value = "需求运算号")
    private String calcNo;

    @ApiModelProperty(value = "辅助属性")
    private String skuCode;

    @ApiModelProperty(value = "成品辅助属性")
    private String productSkuCode;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    //写入如果是null设置为null
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "源申请部门编码-名称")
    private String businessDepartmentName;

    @ApiModelProperty(value = "源申请人编码-名称")
    private String businessUserName;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

}

