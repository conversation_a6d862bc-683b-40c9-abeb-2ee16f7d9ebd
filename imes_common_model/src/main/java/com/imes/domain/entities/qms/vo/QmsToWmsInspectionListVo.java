package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsToWmsInspectionListVo implements Serializable {	
	
    /**	
     * 采购编号	
     */	
	@ApiModelProperty("采购编号")	
    private String buyOrder;	
	
    /**	
     * 检验编号	
     */	
	@ApiModelProperty("检验编号")	
    private String inspectionCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 供应商code	
     */	
	@ApiModelProperty("供应商code")	
    private String supplierCode;	
	
    /**	
     * 供应商Name	
     */	
	@ApiModelProperty("供应商Name")	
    private String supplierName;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    private int inspectionNum;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    private String auditTime;	
    /**	
     * 检验人	
     */	
	@ApiModelProperty("检验人")	
    private String inspector;	
	
    /**	
     * 检验人姓名	
     */	
	@ApiModelProperty("检验人姓名")	
    private String inspectorName;	
	
    /**	
     * 是否合格	
     */	
	@ApiModelProperty("是否合格")	
    private String isQualified;	
    /**	
     *行号id	
     */	
    private String poId;	
}	
