package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("GC-OTD-汇总表")	
@QueryModel(	
        name = "1305",	
        remark = "GC-OTD-汇总表",	
        searchApi = "/api/po/poPurchase/queryPoOTDSummaryVo",	
        alias = "t1"	
)	
public class PoOTDSummaryVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
	@ApiModelProperty("年")	
    @QueryField(name = "年", order = OrderBy.DESC)	
    private String year;	
	
	@ApiModelProperty("月")	
    @QueryField(name = "月", order = OrderBy.DESC)	
    private String month;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码")	
    private String supplierCode;	
	
	@ApiModelProperty("行数")	
    @QueryField(name = "行数")	
    private String lineNumber;	
	
	@ApiModelProperty("系统统计到货准确率")	
    @QueryField(name = "系统统计到货准确率",format = "{}%")	
    private String sysAccuracy;	
	
	@ApiModelProperty("业务员判定后的到货准确率")	
    @QueryField(name = "业务员判定后的到货准确率",format = "{}%")	
    private String perAccuracy;	
	
	@ApiModelProperty("原因分析及行动计划")	
    @QueryField(name = "原因分析及行动计划")	
    private String cause;	
	
	@ApiModelProperty("类型(1为订单，2为承诺)")	
    @QueryField(name = "类型(1为订单，2为承诺)",show = false,query = false)	
    private String type;	
}	
