package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: 武新宇
 * @version: v1.0
 * @Package: com.imes.domain.entities.scs.po
 * @description:
 * @date:2022/12/7 - 15:19
 */
@Data
@ApiModel(value = "U8采购发票数据推送单")
public class U8PushPurchaseInvoice implements Serializable {

    private String id;

    @ApiModelProperty(value = "发票号")
    private String docno;

    @ApiModelProperty(value = "开票日期")
    private String dpbvdate;

    @ApiModelProperty(value = "开票识别号")
    private String chdefine1;

    @ApiModelProperty(value = "单据类型")
     private String doctype;

    @ApiModelProperty(value = "发票类型")
    private String cpbvbilltype;

    @ApiModelProperty(value = "业务类型")
    private String cbustype;

    @ApiModelProperty(value = "参照类型")
    private String upsotype;

    @ApiModelProperty(value = "采购类型编码")
    private String cptcode;

    @ApiModelProperty(value = "供应商编码")
    private String cvencode;

    @ApiModelProperty(value = "代垫单位编码")
    private String cunitcode;

    @ApiModelProperty(value = "部门编码")
    private String cdepcode;

    @ApiModelProperty(value = "业务员编码")
    private String cpersoncode;

    @ApiModelProperty(value = "币种名称")
    private String cexch_name;

    @ApiModelProperty(value = "汇率")
    private BigDecimal cexchrate;

    @ApiModelProperty(value = "表头税率")
    private BigDecimal ipbvtaxrate;

    @ApiModelProperty(value = "发票日期")
    private String dvoudate;

    @ApiModelProperty(value = "专票发票日期摘要")
    private String cdefine6;

    @ApiModelProperty(value = "专票收付款协议编码")
    private String cvenpuomprotocol;

    @ApiModelProperty(value = "入库数量")
    private BigDecimal storageNum;

    @ApiModelProperty(value = "发票记账到期日")
    private String cdefine4;

    @ApiModelProperty(value = "备注")
    private String cpbvmemo;

    @ApiModelProperty(value = "操作人")
    private String operator;

    //红蓝发票
    private Integer bred;

    private List<U8InvoiceRows> rows;
}
