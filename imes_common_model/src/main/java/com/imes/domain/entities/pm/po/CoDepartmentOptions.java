package com.imes.domain.entities.pm.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<部门选择>>
 * @company 捷创智能技术有限公司
 * @create 2020-11-19 16:01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoDepartmentOptions implements Serializable {
    private static final long serialVersionUID = -7124311502555395921L;
    private String value;
    private String label;
    private List<CoDepartmentOptions> children;
}
