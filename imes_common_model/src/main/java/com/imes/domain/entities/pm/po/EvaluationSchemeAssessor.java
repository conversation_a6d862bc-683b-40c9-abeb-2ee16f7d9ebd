package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<考评指标考评人>>
 * @company 捷创智能技术有限公司
 * @create 2022-03-03 10:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_scheme_assessor")
public class EvaluationSchemeAssessor extends Model<EvaluationSchemeAssessor> {
    private String id;
    private String schemeItemId;
    private String assessor;
    private Integer weight;
}
