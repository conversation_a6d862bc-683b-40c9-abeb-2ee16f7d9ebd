package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<参与人>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-16 14:31
 */
@ApiModel("计划预览-参与人")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ParticiEmployeesVo {
    @ApiModelProperty("工号")
    private String employeeCode;
    @ApiModelProperty("姓名")
    private String employeeName;
    @ApiModelProperty("计划工时")
    private BigDecimal hour;
    @ApiModelProperty("实际工时")
    private BigDecimal actualHour;
}
