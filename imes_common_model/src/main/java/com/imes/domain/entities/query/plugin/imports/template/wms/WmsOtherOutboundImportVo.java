package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(
        name = "其他出库导入模板",
        modelName = "0264")
public class WmsOtherOutboundImportVo extends BaseModel {
    @ImportField(name = "出库类型", required = true, remark = "必填\n参考【数据字典->领料申请类型】", isNumber = true, maxLength = 40)
    private String purpose;

    @ImportField(name = "物料编码", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialCode;

    @ImportField(name = "物料名称", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialName;

    @ImportField(name = "物料型号", remark = "不必填\n参考【物料基本信息】", maxLength = 255)
    private String materialMarker;

    @ImportField(name = "规格", remark = "不必填\n参考【物料基本信息】", maxLength = 255)
    private String specification;

    @ImportField(name = "批次", maxLength = 255)
    private String batch;

    @ImportField(name = "仓库", required = true, remark = "必填\n参考【仓库信息】", maxLength = 255)
    private String whCode;

    @ImportField(name = "库位", remark = "不必填\n参考【库位信息】\n数据字典【仓储自动化参数->是否启用库区库位】启用，则必填", maxLength = 255)
    private String binCode;

    @ImportField(name = "库存单位", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String packCodeUnit;

    @ImportField(name = "出库数量", required = true, remark = "必填", isNumber = true, maxNumber = "999999999.999999")
    private String packApplyQty;

    @ImportField(name = "辅助属性")
    private String skuCode;

    @ImportField(name = "箱码")
    private String boxNo;
}
