package com.imes.domain.entities.po.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 价目表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoPrice implements Serializable {

    private static final long serialVersionUID = 906734800361584991L;

    private String id;

    /**
     * 定价单号
     */
    private String priceNo;

    /**
     * 定价单名称
     */
    private String priceName;

    /**
     * 币种
     */
    private String currency;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 价格类型
     */
    private String priceType;

    /**
     * 定价时间
     */
    private Date effectTime;

    /**
     * 制单人工号
     */
    private String madeUserCode;

    /**
     * 制单人名称
     */
    private String madeUserName;

    /**
     * 状态:10录入20审核中30审核通过
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remarks;
}