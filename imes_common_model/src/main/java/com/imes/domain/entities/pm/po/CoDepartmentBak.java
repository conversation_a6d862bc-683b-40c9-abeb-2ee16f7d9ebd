package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (CoDepartmentBak)实体类
 *
 * <AUTHOR>
 * @since 2020-11-19 09:54:58
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoDepartmentBak implements Serializable {
    private static final long serialVersionUID = 812978902443672668L;
    /**
     * id
     */
    private String id;
    /**
    * 部门编号
    */
    private String departCode;
    /**
    * 部门名称
    */
    private String departName;
    /**
    * 上级部门
    */
    private String parentCode;
    /**
    * 部门分类
    */
    private String departType;
    /**
    * 部门负责人(废弃)
    */
    private String managerUserCode;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createOn;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.UPDATE)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateOn;
    /**
    * 更新人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;
    /**
     * 备注
     */
    private String remarks;



}