package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class InBillInspectionMaterialVo implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 生产单号	
     */	
	@ApiModelProperty("生产单号")	
    private String ppNo;	
	
    /**	
     * 排产单号	
     */	
	@ApiModelProperty("排产单号")	
    private String pcNo;	
	
    /**	
     * 报工单号	
     */	
	@ApiModelProperty("报工单号")	
    private String wfNo;	
	
    /**	
     * 报工时间	
     */	
	@ApiModelProperty("报工时间")	
    private Date finishedTime;	
	
    /**	
     * 物料编号	
     */	
	@ApiModelProperty("物料编号")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料单位	
     */	
	@ApiModelProperty("物料单位")	
    private String primaryUnit;	
	
    /**	
     * 报工数量	
     */	
	@ApiModelProperty("报工数量")	
    private String goodQty;	
	
    /**	
     * 报工人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 报工人编码	
     */	
	@ApiModelProperty("报工人编码")	
    private String createCode;	
	
    /**	
     * 检验状态	
     */	
	@ApiModelProperty("检验状态")	
    private String inspectionStatus;	
	
    /**	
     * 关联id	
     */	
	@ApiModelProperty("关联id")	
    private String orderId;	
	
    private static final long serialVersionUID = 1L;	
}	
