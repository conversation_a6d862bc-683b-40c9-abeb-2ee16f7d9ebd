package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "销售对账差异-到货单")
public class ScsAccountReceive implements Serializable {

    private static final long serialVersionUID = -60735720602562894L;

    private String id;

    @ApiModelProperty(value = "总订单号")
    private String soNo;

    @ApiModelProperty(value = "订单行号")
    private String sdNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "到货单号")
    private String receiveNo;

    @ApiModelProperty(value = "收货部门")
    private String receiveDepartmentName;

    @ApiModelProperty(value = "到货时间")
    private Date receiveTime;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal receiveNum;

    @ApiModelProperty(value = "对账单号")
    private String verifyNo;

    @ApiModelProperty(value = "对账状态10待对账20对账中30已对账")
    private String accountStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    private String irdrowno;
}