package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;
import java.util.Optional;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DynamicEnum {
    // 评论
    COMMENT("comment", "评论"),
    // 点赞
    LIKE("like", "点赞"),
    // 通知
    NOTICE("notice", "通知"),
    // 更改
    CHANGE("change", "更改"),
    // 解决方案
    SOLUTION("solution", "解决方案");

    @EnumValue
    public final String type;
    public final String typeName;

    DynamicEnum(String type, String typeName) {
        this.type = type;
        this.typeName = typeName;
    }

    public static DynamicEnum match(String type) {
        return Arrays.stream(DynamicEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("动态类型错误"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static DynamicEnum des(final JsonNode jsonNode) {
        String type = jsonNode.isTextual() ? jsonNode.asText() : Optional.ofNullable(jsonNode.get("type")).map(JsonNode::asText).orElse(null);
        if (type == null) {
            throw new IllegalArgumentException("动态类型错误");
        }
        return match(type);
    }

    public String getType() {
        return type;
    }

    public java.lang.String getTypeName() {
        return typeName;
    }

}
