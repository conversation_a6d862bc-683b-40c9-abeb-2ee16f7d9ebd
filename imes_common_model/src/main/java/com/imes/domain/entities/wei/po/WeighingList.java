package com.imes.domain.entities.wei.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "wei_weighing_list")
public class WeighingList implements Serializable {
    @Id
    private String id;
    private String weighingNo;
    private String truckNo;
    private String weighingPlace;
    private String goodsName;
    private Double gross;
    private Double tare;
    private Double net;
    private String weighingBy;
    private java.util.Date weighingOn;
    private String createdBy;
    private java.util.Date createdOn;
    private java.util.Date updatedOn;
    private String updatedBy;
    private String weighingType;
    private String remarks;

    public void setId(String value) {
        this.id = value;
    }
    public String getId() {
       return this.id;
    }
    public void setWeighingNo(String value) {
        this.weighingNo = value;
    }
    public String getWeighingNo() {
       return this.weighingNo;
    }
    public void setTruckNo(String value) {
        this.truckNo = value;
    }
    public String getTruckNo() {
       return this.truckNo;
    }
    public void setWeighingPlace(String value) {
        this.weighingPlace = value;
    }
    public String getWeighingPlace() {
       return this.weighingPlace;
    }
    public void setGoodsName(String value) {
        this.goodsName = value;
    }
    public String getGoodsName() {
       return this.goodsName;
    }
    public void setGross(Double value) {
        this.gross = value;
    }
    public Double getGross() {
       return this.gross;
    }
    public void setTare(Double value) {
        this.tare = value;
    }
    public Double getTare() {
       return this.tare;
    }
    public void setNet(Double value) {
        this.net = value;
    }
    public Double getNet() {
       return this.net;
    }
    public void setWeighingBy(String value) {
        this.weighingBy = value;
    }
    public String getWeighingBy() {
       return this.weighingBy;
    }
    public void setWeighingOn(java.util.Date value) {
        this.weighingOn = value;
    }
    public java.util.Date getWeighingOn() {
       return this.weighingOn;
    }
    public void setCreatedBy(String value) {
        this.createdBy = value;
    }
    public String getCreatedBy() {
       return this.createdBy;
    }
    public void setCreatedOn(java.util.Date value) {
        this.createdOn = value;
    }
    public java.util.Date getCreatedOn() {
       return this.createdOn;
    }
    public void setUpdatedOn(java.util.Date value) {
        this.updatedOn = value;
    }
    public java.util.Date getUpdatedOn() {
       return this.updatedOn;
    }
    public void setUpdatedBy(String value) {
        this.updatedBy = value;
    }
    public String getUpdatedBy() {
       return this.updatedBy;
    }
    public void setWeighingType(String value) {
        this.weighingType = value;
    }
    public String getWeighingType() {
       return this.weighingType;
    }
    public void setRemarks(String value) {
        this.remarks = value;
    }
    public String getRemarks() {
       return this.remarks;
    }
}
