package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsStockBin;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
/**	
 * 移动端库位库存	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-03-17 15:01	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMbStockBinVo extends WmsStockBin {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 物料条形码	
     */	
	@ApiModelProperty("物料条形码")	
    private String barCode;	
	
    /**	
     * 序列码	
     */	
	@ApiModelProperty("序列码")	
    private String serialNo;	
	
    /**	
     *	
     */	
    private BigDecimal orderQty;	
	
    /**	
     * 本次变化库存数量	
     */	
	@ApiModelProperty("本次变化库存数量")	
    private BigDecimal qty;	
	
    /**	
     * 前端提交 序列号（SN码）	
     */	
	@ApiModelProperty("前端提交 序列号（SN码）")	
    private List<String> serialNumbers;	
	
    /**	
     * 库位条码	
     */	
	@ApiModelProperty("库位条码")	
    private String binQrCode;	
	
    /**	
     * 后端返回 序列号（SN码）	
     */	
//    private List<String> selSerialNumbers;
	
}	
