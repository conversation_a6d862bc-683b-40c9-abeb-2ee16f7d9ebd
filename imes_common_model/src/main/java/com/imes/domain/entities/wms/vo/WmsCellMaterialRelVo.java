package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsCellMaterialRel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
public class WmsCellMaterialRelVo extends WmsCellMaterialRel {	
	
    /**	
     * 关联单据类型	
     */	
	@ApiModelProperty("关联单据类型")	
    private String receiptType;	
	
    /**	
     * 关联单据单号	
     */	
	@ApiModelProperty("关联单据单号")	
    private String receiptCode;	
	
    /**	
     * 库存操作类型	
     */	
	@ApiModelProperty("库存操作类型")	
    private String optionType;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 锁定状态（未锁定，锁定）	
     */	
	@ApiModelProperty("锁定状态（未锁定，锁定）")	
    private String lockStatus;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 是否启用批次	
     */	
	@ApiModelProperty("是否启用批次")	
    private String isBatch;	
	
    /**	
     * 是否自动生成	
     */	
	@ApiModelProperty("是否自动生成")	
    private String autoGenerateBatchNum;	
}	
