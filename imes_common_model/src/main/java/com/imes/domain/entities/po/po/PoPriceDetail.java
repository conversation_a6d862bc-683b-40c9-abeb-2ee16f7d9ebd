package com.imes.domain.entities.po.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 价目表
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoPriceDetail implements Serializable {

    private static final long serialVersionUID = 906734800361584991L;

    private String id;

    /**
     * 主表id
     */
    private String mainId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 规格
     */
    private String specification;

    /**
     * 型号
     */
    private String materialMarker;

    /**
     * 单位
     */
    private String unit;

    /**
     * 物料单价
     */
    private BigDecimal singlePrice;

    /**
     * 是否含税
     */
    private String isIncludeTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 税率编码
     */
    private String taxCode;

    /**
     * 未税单价
     */
    private BigDecimal unIncludePrice;

    /**
     * 含税单价
     */
    private BigDecimal includePrice;

    /**
     * 生效时间
     */
    private Date effectTime;

    /**
     * 失效时间
     */
    private Date uneffectTime;

    /**
     * 生效状态: 0 未生效 1 生效
    private String effectStatus; */

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remarks;
}