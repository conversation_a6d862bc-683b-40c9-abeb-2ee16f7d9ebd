package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsOutboundTask;	
import com.imes.domain.entities.wms.WmsOutboundTaskItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.util.List;	
	
@Data	
public class WmsOutboundTaskVo extends WmsOutboundTask {	
	
    private List<WmsOutboundTaskItem> items;	
	
    private List<WmsOutboundTaskItemVo> itemVos;	
	
    // 路径名称	
    @ApiModelProperty(value = "线体名称")	
    private String routeName;	
	
    // 扫码设备名称	
    @ApiModelProperty(value = "输送设备名称")	
    private String conveyerName;	
	
    // 仓库名称	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    // 起始设备	
    @ApiModelProperty(value = "起始设备名称")	
    private String startConveyerName;	
	
    // 终点设备	
    @ApiModelProperty(value = "终点设备名称")	
    private String endConveyerName;	
	
    // 是否整出()	
    @ApiModelProperty(value = "是否整出")	
    private String isWhole;	
	
}	
