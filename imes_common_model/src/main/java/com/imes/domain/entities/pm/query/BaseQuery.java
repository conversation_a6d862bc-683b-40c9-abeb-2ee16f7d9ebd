package com.imes.domain.entities.pm.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-28 14:30
 */
@Getter
@Setter
public class BaseQuery {
    @ApiModelProperty("第几页")
    private Integer page;
    @ApiModelProperty("每页显示数")
    private Integer limit;
    @ApiModelProperty("创建日期")
    private LocalDateTime createTime;
    @ApiModelProperty("生效日期")
    private LocalDateTime updateTime;
}
