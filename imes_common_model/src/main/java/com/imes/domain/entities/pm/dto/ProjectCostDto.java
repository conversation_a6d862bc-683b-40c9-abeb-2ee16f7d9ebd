package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (ProjectCost)实体类
 *
 * <AUTHOR>
 * @since 2021-06-18 16:22:03
 */
@ApiModel("项目成本实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectCostDto implements Serializable {
    private static final long serialVersionUID = -80625222938034968L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }
    /**
    * id
    */
    @ApiModelProperty("id")
    @NotNull(message = "项目成本id不能为空", groups = UpdateGroup.class)
    @Null(message = "项目成本id必须为空", groups = AddGroup.class)
    private String id;
    /**
    * 项目id
    */
    @ApiModelProperty("项目id")
    private String projectId;
    /**
     * 税收分类编号
     */
    @ApiModelProperty("税收分类编号")
    private String code;
    /**
    * 内容
    */
    @ApiModelProperty("内容")
    private String content;
    /**
    * 描述
    */
    @ApiModelProperty("描述")
    private String description;
    /**
    * 预估成本(不含税)
    */
    @ApiModelProperty("预估成本(不含税)")
    private BigDecimal estimatedMoney;
    /**
     * 预估成本(含税)
     */
    @ApiModelProperty("预估成本(含税)")
    private BigDecimal estimatedMoneyTax;
    /**
    * 实际成本(不含税)
    */
    @ApiModelProperty("实际成本(不含税)")
    private BigDecimal actualMoney;
    /**
     * 实际成本(含税)
     */
    @ApiModelProperty("实际成本(含税)")
    private BigDecimal actualMoneyTax;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}