package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
	
@Data	
@ApiModel("采购订单变更单")	
@QueryModel(	
        name = "1234",	
        remark = "采购订单变更单",	
        alias = {"po_purchase_standard_change", "po_purchase_detail_standard_change"},	
        searchApi = "/api/po/poPurchaseStandardChange/queryList",	
        pushApi = PushApi.PoPurchaseStandardChangeSearchVo,	
        auth =Auth.ALL,	
        authTenant = true,	
        customExp = true,
        openapi = true,
        showMode = true)	
public class PoPurchaseStandardChangeSearchVo extends BaseModel {	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号")	
    @EditField(required = true)	
    private String purchaseNo;	
	
	@ApiModelProperty("变更单号")	
    @QueryField(name = "变更单号")	
    @EditField(readonly = true)	
    private String changeNo;	
	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_TYPE")	
    private String purchaseType;	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种", type = Type.MultiSelect, sqlOption = "select ccy_no as value,ccy_name as label from sys_currency")	
    @EditField(required = true)	
    private String currency;	
	
	@ApiModelProperty("采购部门编码")	
    @QueryField(name = "采购部门编码")	
    @EditField(readonly = true)	
    private String demandDepCode;	
	
	@ApiModelProperty("采购部门")	
    @QueryField(name = "采购部门", alias="co_department.depart_name",level = Level.Main)	
    @EditField(required = true)	
    private String demandDepName;	
	
	@ApiModelProperty("采购员工号")	
    @QueryField(name = "采购员工号", logic = Logic.In)	
    @EditField(readonly = true)	
    private String demandUserCode;	
	
	@ApiModelProperty("采购员")	
    @QueryField(name = "采购员", alias = "pe_user.user_name",level = Level.Main)	
    @EditField(required = true)	
    private String demandUserName;	
	
/*    @QueryField(name = "制单人")	
    private String operatorName;*/	
	
	@ApiModelProperty("变更原因")	
    @QueryField(name = "变更原因")	
    @EditField(required = true)	
    private String changeReason;	
	
  //  @QueryField(name = "单据来源", type = Type.MultiSelect, dictOption = "PO_PURCHASE_DOC_SOURCE")	
    private String docSource;	
	
	@ApiModelProperty("采购日期")	
    @QueryField(name = "采购日期", type = Type.Date, format = "yyyy-MM-dd")	
    @EditField(required = true)	
    private String orderDate;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS")	
    @EditField(readonly = true)	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE", value = "10")	
    @EditField(readonly = true)	
    private String businessStatus;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd", order = OrderBy.DESC)	
    @EditField(readonly = true)	
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", level = Level.Main, sort = false, alias = ".(select user_name from pe_user where pe_user.user_code = po_purchase_standard_change.create_by limit 1)")	
    @EditField(readonly = true)	
    private String createByName;	
	
    private String createBy;	
	
	@ApiModelProperty("主单备注")	
    @QueryField(name = "主单备注")	
    private String remarks;	
	
    private String detailId;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号", alias = "$1")
    @EditField(readonly = true)
    private String sdNo;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码", alias = "$1")
    @EditField(required = true)
    private String supplierCode;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称", alias = "sys_supplier")
    private String supplierName;
	
	@ApiModelProperty("变更类型")	
    @QueryField(name = "变更类型", alias = "$1", type = Type.MultiSelect, option = { "1", "新增", "2", "修改", "3", "删除"})
    @EditField(required = true)
    private String changeType;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "$1")
    @EditField(required = true)
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "ppc_material")
    @EditField(readonly = true)
    private String materialName;	
	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "ppc_material")
    @EditField(readonly = true)
    private String specification;	
	
	@ApiModelProperty("材质")	
    @QueryField(name = "材质", alias = "ppc_material", show = false)
    @EditField(readonly = true)
    private String quality;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "ppc_material")
    @EditField(readonly = true)
    private String materialMarker;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号", alias = "$1")
    @EditField(readonly = true)
    private String lineNo;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量", alias = "$1", type = Type.Number)
    @EditField(required = true)
    private String qty;	
	
	@ApiModelProperty("原采购数量")	
    @QueryField(name = "原采购数量", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String oldQty;	
	
	@ApiModelProperty("采购单位")	
    @QueryField(name = "采购单位", alias = "$1",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(required = true)
    private String unit;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "$1",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String baseUnit;
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String baseUnitQty;	
	
 /*   @QueryField(name = "关联数量", format = "stripZero")	
    private String relationQty;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", format = "stripZero")	
    private String arriveQty;*/	
	
/*    @QueryField(name = "退货不补货数量", format = "stripZero")	
    private String cancelQty;	
	
	@ApiModelProperty("退货补货数量")	
    @QueryField(name = "退货补货数量", format = "stripZero")	
    private String cancelReplenishQty;*/	
	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", alias = "$1", type = Type.Number)
    @EditField(required = true)
    private String singlePrice;	
	
	@ApiModelProperty("原单价")	
    @QueryField(name = "原单价", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String oldSinglePrice;	
	
	@ApiModelProperty("净价")	
    @QueryField(name = "净价", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String netPrice;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "$1", option = {"0", "是", "1", "否"}, type = Type.MultiSelect)
    @EditField(required = true)
    private String includeTax;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", alias = "$1", dictOption = "PO_PURCHASE_TAX_CODE", type = Type.MultiSelect)	
    private String taxCode;	
	
	@ApiModelProperty("折扣方式")	
    @QueryField(name = "折扣方式", alias = "$1", dictOption = "SALE_DISCOUNT_TYPE", type = Type.MultiSelect)
    @EditField(required = true)
    private String discountType;	
	
	@ApiModelProperty("折扣率(%)")	
    @QueryField(name = "折扣率(%)", alias = ".(ifnull(TRUNCATE($1.discount_rate, 2), 0) * 100)")	
    private String discountRate;	
	
	@ApiModelProperty("折扣额")	
    @QueryField(name = "折扣额", alias = "$1", type = Type.Number)	
    private String discountPrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", alias = "$1", type = Type.Number)
    @EditField(readonly = true)
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", alias = "$1", type = Type.Number, format = "0.00")
    @EditField(readonly = true)
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", alias = "$1", type = Type.Number, format = "0.00")
    @EditField(readonly = true)
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", alias = "$1", type = Type.Number, format = "0.00")
    @EditField(readonly = true)
    private String allPrice;	
	
	@ApiModelProperty("到货日期")	
    @QueryField(name = "到货日期", alias = "$1", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(required = true)
    private String deliveryDate;	
	
	@ApiModelProperty("原到货日期")	
    @QueryField(name = "原到货日期", alias = "$1", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(readonly = true)
    private String oldDeliveryDate;	
	
	@ApiModelProperty("源单类型")	
    @QueryField(name = "源单类型", alias = "$1", type = Type.MultiSelect, dictOption = "PO_PURCHASE_DOC_SOURCE")
    @EditField(readonly = true)
    private String requestDocType;	
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号", alias = "$1")
    @EditField(readonly = true)
    private String requestDocCode;	
	
	@ApiModelProperty("源单申请部门")	
    @QueryField(name = "源单申请部门", alias = "cd.depart_name")
    @EditField(readonly = true)
    private String requestDocDepName;	
	
	@ApiModelProperty("源单申请人")	
    @QueryField(name = "源单申请人", alias = "pu.user_name")
    @EditField(readonly = true)
    private String requestDocPersonName;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性", alias = "$1")	
    private String skuCode;	
	
	@ApiModelProperty("原辅助属性")	
    @QueryField(name = "原辅助属性", alias = "$1")
    @EditField(readonly = true)
    private String oldSkuCode;	
	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "$1.remarks")	
    private String detailRemarks;	
	
	@ApiModelProperty("原备注")	
    @QueryField(name = "原备注", alias = "$1")
    @EditField(readonly = true)
    private String oldRemarks;	
	
	
}	
	
