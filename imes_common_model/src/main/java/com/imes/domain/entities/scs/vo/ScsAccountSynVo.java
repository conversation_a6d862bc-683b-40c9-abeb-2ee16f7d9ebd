package com.imes.domain.entities.scs.vo;


import com.imes.domain.entities.scs.po.ScsAccountReceive;
import com.imes.domain.entities.scs.po.ScsAccountReceiveReturn;
import com.imes.domain.entities.scs.po.ScsAccountStorageIn;
import com.imes.domain.entities.scs.po.ScsAccountStorageReturn;
import lombok.Data;

import java.util.List;

@Data
public class ScsAccountSynVo {
    //供应商的数据汇总到江森
    private List<ScsAccountStorageReturn> returnLists;

    private List<ScsAccountStorageIn> inLists;
    //到货单
    private List<ScsAccountReceive> receiveLists;
    //拒收单
    private List<ScsAccountReceiveReturn> receiveReturnLists;
}
