package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * (AttendanceShift)实体类	
 *	
 * <AUTHOR> z 	
 * @since 2022-10-11 14:59:07	
 */	
@Data	
@ApiModel("考勤班次")	
@QueryModel(name = "0673",	
        remark = "考勤班次",	
        alias = "shift",	
        searchApi = "/api/hr/attendance/query/shift")	
public class AttendanceShiftVo extends BaseModel {	
    /**	
     * 班次名称	
     */	
	@ApiModelProperty("班次名称")	
    @QueryField(name = "班次名称")	
    private String name;	
    /**	
     * 适用公司	
     */	
	@ApiModelProperty("适用公司")	
    @QueryField(name = "适用公司")	
    private String company;	
    /**	
     * 适用公司名	
     */	
	@ApiModelProperty("适用公司名")	
    @QueryField(name = "适用公司名")	
    private String companyName;	
    /**	
     * 工作日	
     */	
	@ApiModelProperty("工作日")	
    @QueryField(name = "工作日")	
    private String workingDays;	
    /**	
     * 上班时间限制	
     */	
	@ApiModelProperty("上班时间限制")	
    @QueryField(name="上班时间限制", type = Type.Date )	
    private String workTime;	
    /**	
     * 下班时间限制	
     */	
	@ApiModelProperty("下班时间限制")	
    @QueryField(name="下班时间限制", type = Type.Date)	
    private String afterWorkTime;	
	
}	
	
