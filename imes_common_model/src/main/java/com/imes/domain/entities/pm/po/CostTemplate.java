package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (CostTemplate)实体类
 *
 * <AUTHOR>
 * @since 2021-06-21 16:42:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_cost_template",resultMap = "BaseResultMap")
public class CostTemplate implements Serializable {
    private static final long serialVersionUID = 498533069571554849L;
    /**
    * id
    */
    private String id;
    /**
    * 模板名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 备注
    */
    private String remarks;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}