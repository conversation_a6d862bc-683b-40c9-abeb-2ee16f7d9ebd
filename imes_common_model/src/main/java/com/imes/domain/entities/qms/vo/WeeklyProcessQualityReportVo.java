package com.imes.domain.entities.qms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.omg.PortableInterceptor.INACTIVE;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WeeklyProcessQualityReportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("工序编码")
    private String processCode;

    @ApiModelProperty("工序名称")
    private String processName;

	@ApiModelProperty("上月累计值")
    private BigDecimal beforeMonthNum;

    @ApiModelProperty("对比上周趋势")
    private Integer compareLastWeekTrend;


	@ApiModelProperty("当月累计值")
    private BigDecimal monthNum;


    @ApiModelProperty("周数据")
    private List<WeeklyProcessQualityReportTimeMapVo> weeklList;




}	
