package com.imes.domain.entities.scs.po;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 开票与对账单号关联表
 */
@Data
public class ScsInvoiceReVerify implements Serializable {

    private static final long serialVersionUID = 824613178177042950L;

    private String id;

    /**
     * 开票流水号
     */
    private String invoiceNo;

    /**
     * 销售对账单号
     */
    private String scsAccountVerifyNo;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remarks;
}