package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.imes.domain.entities.crm.enums.ApprovalStatusEnum;
import com.imes.domain.entities.crm.enums.BusinessClosingEnum;
import com.imes.domain.entities.crm.enums.BusinessGradeEnum;
import com.imes.domain.entities.crm.enums.BusinessUrgencyEnum;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * (CrmBusiness)实体类
 *
 * <AUTHOR>
 * @since 2022-02-08 16:42:43
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_business", resultMap = "BaseResultMap")
public class Business extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -43486204474792863L;
    /**
     * id
     */
    private String id;
    /**
     * 编号
     */
    @TableField(fill = FieldFill.INSERT)
    private String code;
    /**
     * 商机名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String subject;
    /**
     * 商机类型
     */
    private String type;
    /**
     * 商机风险
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = JacksonTypeHandler.class)
    private List<String> risk;

    /**
     * 星级
     */
    private String level;
    /**
     * 发现日期
     */
    private LocalDate findDate;
    /**
     * 预计收入
     */
    // @TableField(exist = false)
    private BigDecimal estimatedIncome;
    /**
     * 预计签订日期
     */
    // @TableField(exist = false)
    private LocalDate estimatedSigningDate;
    /**
     * 赢单率
     */
    // @TableField(exist = false)
    private Integer winningRate;
    /**
     * 竞争品牌
     */
    private String compete;
    /**
     * 地点
     */
    private String address;
    /**
     * 所属企业
     */
    private String enterprise;
    /**
     * 销售阶段
     */
    private String stageId;
    /**
     * 销售阶段
     */
    private String stage;
    /**
     * 销售阶段
     */
    @TableField(exist = false)
    private String stageName;
    /**
     * 协助单位
     */
    private String assistance;
    /**
     * 销售员/客服
     */
    private String salesperson;
    /**
     * 销售员/客服(姓名)
     */
    @TableField(exist = false)
    private String salespersonName;
    /**
     * 销售工程师
     */
    private String salesEngineer;
    /**
     * 销售工程师(姓名)
     */
    @TableField(exist = false)
    private String salesEngineerName;
    /**
     * 销售主管
     */
    private String salesSupervisor;
    /**
     * 销售主管(姓名)
     */
    @TableField(exist = false)
    private String salesSupervisorName;
    /**
     * 销售总监
     */
    private String salesDirector;
    /**
     * 销售总监(姓名)
     */
    @TableField(exist = false)
    private String salesDirectorName;
    /**
     * 客户
     */
    @TableField(condition = SqlCondition.LIKE)
    private String customer;
    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;
    /**
     * 客户
     */
    @TableField(exist = false)
    private Customer customerInfo;
    /**
     * 报工时间范围查询
     */
    @TableField(exist = false)
    private List<LocalDateTime> reportTime;
    /**
     * 客户类型
     */
    private String customerType;
    /**
     * 客户行业
     */
    private String industry;
    /**
     * 联系人工号
     */
    private String contacts;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 逻辑删除
     */
    private Integer deleted;
    /**
     * 审核状态（0：待审核 , 1：审核通过，2：审批中）
     */
    // private Integer approvalStatus;
    private ApprovalStatusEnum approvalStatus;
    /**
     * 关闭状态：0=未关闭、1=正式单、2=试用单、3=丢单
     */
    private BusinessClosingEnum offState;
    /**
     * 竞争者Id
     */
    private String closingCompetitor;
    /**
     * 竞争者名称
     */
    @TableField(exist = false)
    private String closingCompetitorName;
    /**
     * 关闭原因
     */
    @TableField(jdbcType = JdbcType.VARCHAR, typeHandler = JacksonTypeHandler.class)
    private List<String> closingReason;
    /**
     * 关闭备注
     */
    private String closeNotes;
    /**
     * 关闭时间
     */
    private LocalDateTime closingTime;
    /**
     * 商机产品
     */
    @TableField(exist = false)
    private List<BusinessProduct> businessProduct;
    /**
     * 国家
     */
    private String country;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String area;
    /**
     * 镇
     */
    private String town;
    /**
     * 等级
     */
    private BusinessGradeEnum grade;
    /**
     * 急迫性
     */
    private BusinessUrgencyEnum urgency;
    /**
     * 客户省
     */
    @TableField(exist = false)
    private String customerProvince;
    /**
     * 客户市
     */
    @TableField(exist = false)
    private String customerCity;
    /**
     * 管道id
     */
    private String pipelineId;

    /**
     * 自定义字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }
}