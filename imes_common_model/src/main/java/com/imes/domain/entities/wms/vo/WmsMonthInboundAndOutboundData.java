package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
public class WmsMonthInboundAndOutboundData {	
	
    @ApiModelProperty(value = "时间")	
    private String date;	
	
    @ApiModelProperty(value = "入库")	
    private int inType;	
	
    @ApiModelProperty(value = "出库")	
    private int outType;	
	
    @ApiModelProperty(value = "数量")	
    private BigDecimal qty;	
	
    @ApiModelProperty(value = "年")	
    private String year;	
	
    @ApiModelProperty(value = "月")	
    private String month;	
	
    @ApiModelProperty(value = "类型")	
    private String type;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库")	
    private String whName;	
	
    @ApiModelProperty(value = "生产分类类型")	
    private String category;	
	
    @ApiModelProperty(value = "查询时间")	
    private String queryDate;	
	
	
}	
