package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
@ApiModel("采购订单(检验)")	
@QueryModel(	
        name = "1195-forqms",	
        remark = "采购订单(检验)",	
        searchApi = "/api/po/poPurchaseStandard/queryListQms",	
        customBind = "1195",	
        alias = "po_purchase_standard"	
)	
public class PoPurchaseStandaradQmsQueryVo extends BaseModel {	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date,show = false, order = OrderBy.DESC)	
    private String createOn;	
	
    /**	
     * 子订单id	
     */	
    private String detailId;	
	
    private String poId;	
	
    /**	
     * mainId	
     */	
    private String mainId;	
	
    private BigDecimal unitPrice;	
	
    private BigDecimal purchaseQty;	
	
    private BigDecimal arrivedQty;	
	
    private BigDecimal baseUnitQty;	
	
    /**	
     * 采购单号	
     */	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号")	
    private String purchaseNo;	
	
	
	@ApiModelProperty("物料行号")	
    @QueryField(name = "物料行号", alias = "po_purchase_detail_standard")	
    private String sdNo;	
	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码", alias = "po_purchase_detail_standard")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称", alias = "po_purchase_detail_standard")	
    private String supplierName;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "po_purchase_detail_standard")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "po_purchase_detail_standard")	
    private String materialName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "po_purchase_detail_standard")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "po_purchase_detail_standard")	
    private String materialMarker;	
	
    //@QueryField(name = "单位", alias = "po_purchase_detail_standard")	
    private String unit;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "u1.name")	
    private String unitName;	
	
    //@QueryField(name = "基础单位", alias = "po_purchase_detail_standard")	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "u2.name")	
    private String baseUnitName;	
	
	@ApiModelProperty("综合查询条件")	
    @QueryField(name = "综合查询条件",show = false,alias = ".CONCAT(ppc_material.material_name,po_purchase_detail_standard.material_code,po_purchase_standard.purchase_no)")	
    String conditions;	
	
}	
