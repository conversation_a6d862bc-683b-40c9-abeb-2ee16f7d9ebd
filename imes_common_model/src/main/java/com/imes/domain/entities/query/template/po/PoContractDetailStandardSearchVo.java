package com.imes.domain.entities.query.template.po;	
	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
	
@Data	
@QueryModel(	
        name = "",	
        remark = "采购合同明细",	
        searchApi = "/api/po/poContractDetailStandard/queryList")	
@ApiModel(value = "采购合同明细高级查询")	
public class PoContractDetailStandardSearchVo extends BaseModel {	
	
	@ApiModelProperty("合同主单id")	
    @QueryField(name = "合同主单id")	
    private String mainId;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号")	
    private String sdNo;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格")	
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String materialMarker;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量", type = Type.Number)	
    private String qty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String unit;	
	
	@ApiModelProperty("业务状态 10-正常 20-已关闭")	
    @QueryField(name = "业务状态 10-正常 20-已关闭")	
    private String businessStatus;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人")	
    private String createBy;	
	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String updateOn;	
	
	@ApiModelProperty("更新人")	
    @QueryField(name = "更新人")	
    private String updateBy;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", type = Type.Number)	
    private String singlePrice;	
	
	@ApiModelProperty("净价")	
    @QueryField(name = "净价", type = Type.Number)	
    private String netPrice;	
	
	@ApiModelProperty("折扣方式")	
    @QueryField(name = "折扣方式")	
    private String discountType;	
	
	@ApiModelProperty("折扣额")	
    @QueryField(name = "折扣额", type = Type.Number)	
    private String discountPrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", type = Type.Number)	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number)	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number)	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", type = Type.Number)	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", type = Type.Number)	
    private String allPrice;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", type = Type.Number)	
    private String taxRate;	
	
	@ApiModelProperty("税率编码")	
    @QueryField(name = "税率编码")	
    private String taxCode;	
	
	@ApiModelProperty("折扣率")	
    @QueryField(name = "折扣率", type = Type.Number)	
    private String discountRate;	
	
	@ApiModelProperty("是否含税 0是 1否")	
    @QueryField(name = "是否含税 0是 1否")	
    private String includeTax;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位")	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", type = Type.Number)	
    private String baseUnitQty;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性")	
    private String skuCode;	
	
	@ApiModelProperty("数量控制 1是 0否")	
    @QueryField(name = "数量控制 1是 0否")	
    private String needQty;	
	
	@ApiModelProperty("单价控制 1是 0否")	
    @QueryField(name = "单价控制 1是 0否")	
    private String needSinglePrice;	
	
	@ApiModelProperty("金额控制 1是 0否")	
    @QueryField(name = "金额控制 1是 0否")	
    private String needAllPrice;	
	
	@ApiModelProperty("累计基础单位数量")	
    @QueryField(name = "累计基础单位数量", type = Type.Number)	
    private String sendedQty;	
	
	@ApiModelProperty("累计金额")	
    @QueryField(name = "累计金额", type = Type.Number)	
    private String sendedPrice;	
	
	
    private Integer singlePricePrecision;	
	
    private Integer discountPricePrecision;	
    //精度	
    private String qtyPrecision;	
	
    private String updateFlg;	
	
    private BigDecimal residueQty;	
	
    private BigDecimal relationPoQty;	
    private BigDecimal relationPoPrice;	
	
    private String unitName;	
    private String baseUnitName;	
}	
	
