package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 武新宇
 * @version: v1.0
 * @Package: com.imes.domain.entities.scs.po.U8Push
 * @description:
 * @date:2022/12/12 - 16:13
 */
@Data
@ApiModel(value = "采购到货单 单据行")
public class U8ArrivalRows implements Serializable {

    @ApiModelProperty(value = "行号")
    private Integer irowno;

    @ApiModelProperty(value = "U8采购订单 行号")
    private Integer iporowno;

    @ApiModelProperty(value = "存货编码")
    private String cinvcode;

    @ApiModelProperty(value = "收货数量")
    private BigDecimal iquantity;

    @ApiModelProperty(value = "批号")
    private String cbatch;

    @ApiModelProperty(value = "是否急料")
    private Integer bexigency;

}
