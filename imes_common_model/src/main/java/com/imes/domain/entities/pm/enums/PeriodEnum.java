package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Arrays;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PeriodEnum {

    MONTHLY(1, "月度"),
    QUARTER(2, "季度"),
    HALF_YEAR(3, "半年"),
    YEAR(4, "年度");

    @EnumValue
    private final Integer period;
    private final String periodStr;

    PeriodEnum(Integer period, String periodStr) {
        this.period = period;
        this.periodStr = periodStr;
    }

    public Integer getPeriod() {
        return period;
    }

    public String getPeriodStr() {
        return periodStr;
    }

    public static PeriodEnum match(Integer period) {
        return Arrays.stream(PeriodEnum.values())
                .filter(e -> e.getPeriod().equals(period))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("周期状态错误"));
    }
}
