package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "采购需求预测物料关联表")
public class PoPurchaseForecastMaterail implements Serializable {

    private static final long serialVersionUID = -19132747490686674L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料型号")
    private String materialMarker;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "新图号")
    private String newDwgNo;

    @ApiModelProperty(value = "产品大类")
    private String category;

    @ApiModelProperty(value = "物料类型")
    private String materialTypeCode;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private Date refuseTime1;
    private Date refuseTime2;
    private Date refuseTime3;
    private Date determineTime;
}