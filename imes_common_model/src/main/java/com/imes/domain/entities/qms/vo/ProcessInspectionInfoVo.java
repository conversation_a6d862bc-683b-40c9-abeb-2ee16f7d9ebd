package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.ProcessInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.ProcessTask;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class ProcessInspectionInfoVo implements Serializable {	
	
    private ProcessInspectionVo main;	
	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    Map<String, ProcessDetailBadVo> detail;	
	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 报工信息	
     */	
	@ApiModelProperty("报工信息")	
    List<ProcessTask> info;	
	
    /**	
     * 合并条目	
     */	
	@ApiModelProperty("合并条目")	
    List<String> orderIds;	
    /**	
     * 质检样本管理	
     */	
	@ApiModelProperty("质检样本管理")	
    String qmsSampleManager;	
    /**	
     * 数据字典是否开启不良项 。1启用0禁用	
     */	
	@ApiModelProperty("数据字典是否开启不良项 。1启用0禁用")	
    String lableFlag;	
    /**	
     * 合格数量	
     */	
	@ApiModelProperty("合格数量")	
    int qualifiedNum;	
	
    /**	
     * 不良项数量	
     */	
	@ApiModelProperty("不良项数量")	
    int unqualifiedNum;	
    /**	
     * 真正的检验数量	
     */	
	@ApiModelProperty("真正的检验数量")	
    Integer reallyInspectionNum;	
	
    /**	
     * 是否合格	
     */	
	@ApiModelProperty("是否合格")	
    String isQualified;	
	
    /**	
     * 检验判定方式1AQL判定2合格率判定3主观判定	
     */	
	@ApiModelProperty("检验判定方式1AQL判定2合格率判定3主观判定")	
    String inspectionJudgmentMethod;	
	
    private static final long serialVersionUID = 1L;	
}	
