package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportBrandVo {	
	
	
    /**	
     * 品牌	
     */	
    @ApiModelProperty("品牌")	
    private String brand;	
	
    /**	
     * 金额	
     */	
    @ApiModelProperty("金额")	
    private BigDecimal amount;	
	
	
}	
