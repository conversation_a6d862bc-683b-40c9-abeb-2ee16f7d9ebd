package com.imes.domain.entities.hr.vo;

import com.imes.domain.entities.hr.po.AttendanceShift;
import com.imes.domain.entities.hr.po.HrStatutoryHoliday;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 考勤组作息排版记录表(HrAttendanceGroupRule)实体类
 *
 * <AUTHOR> z
 * @since 2023-04-25 10:20:55
 */
@Data
public class HrAttendanceGroupRuleResultVo {

    private String id;
    /**
     * 考勤组编码
     */
    private String groupCode;
    /**
     * 日历计划编码
     */
    private String holidayPlanCode;
    /**
     * 班次编码
     */
    private String shiftCode;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date createOn;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateOn;

    private String name;

    /**
     * 更新人
     */
    private String updateBy;

    private String holidayPlanName;

    private String shiftName;

    private List<HrStatutoryHoliday> holidayList;

    private List<AttendanceShift> shiftList;

}

