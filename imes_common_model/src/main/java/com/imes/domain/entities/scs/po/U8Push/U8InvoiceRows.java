package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: 武新宇
 * @version: v1.0
 * @Package: com.imes.domain.entities.scs.po
 * @description:
 * @date:2022/12/7 - 17:29
 */
@Data
@ApiModel(value = "采购发票单据行")
public class U8InvoiceRows implements Serializable {

    @ApiModelProperty(value = "行号")
    private Integer irowno;

    @ApiModelProperty(value = "U8入库单号")
    private String crdno;

    @ApiModelProperty(value = "U8入库单行行号")
    private Integer irdrowno;

    @ApiModelProperty(value = "存货编码")
    private String cinvcode;

    @ApiModelProperty(value = "数量")
    private BigDecimal iquantity;

    @ApiModelProperty(value = "税率")
    private BigDecimal itaxrate;

    @ApiModelProperty(value = "原币单价")
    private BigDecimal ioricost;

    @ApiModelProperty(value = "原币金额")
    private BigDecimal iorimoney;

    @ApiModelProperty(value = "原币税额")
    private BigDecimal ioritaxprice;

    @ApiModelProperty(value = "原币价税合计")
    private BigDecimal iorisum;

    @ApiModelProperty(value = "本币单价")
    private BigDecimal icost;

    @ApiModelProperty(value = "本币金额")
    private BigDecimal imoney;

    @ApiModelProperty(value = "本币税额")
    private BigDecimal itaxprice;

    @ApiModelProperty(value = "本币价税合计")
    private BigDecimal isum;

    @ApiModelProperty(value = "PO单价")
    private BigDecimal cdefine27;

    @ApiModelProperty(value = "行备注")
    private String cbmemo;
}
