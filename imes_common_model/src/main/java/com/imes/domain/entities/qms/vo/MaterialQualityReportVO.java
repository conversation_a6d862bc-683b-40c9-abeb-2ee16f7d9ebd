package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.OutBillQualityReportDetail;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class MaterialQualityReportVO implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 物料编号	
     */	
	@ApiModelProperty("物料编号")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 检验次数	
     */	
	@ApiModelProperty("检验次数")	
    private int inspectionNum;	
	
    /**	
     * 不合格次数	
     */	
	@ApiModelProperty("不合格次数")	
    private int unQualified;	
	
    /**	
     *合格次数	
     */	
    private int isQualified;	
	
    private List<OutBillQualityReportDetail> details;	
	
    private static final long serialVersionUID = 1L;	
}	
