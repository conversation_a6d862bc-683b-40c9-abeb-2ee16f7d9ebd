package com.imes.domain.entities.query.plugin.imports.template.file;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import com.imes.domain.entities.query.plugin.imports.ImportRemark;
import lombok.Data;

@Data
@ImportModel(name = "文档类目权限导入模板", remark = ImportRemark.FileRoleRelationImportVo, skipVersion = true)
public class FileRoleRelationImportVo extends BaseModel {

    @ImportField(name = "类目Id", remark = "不可修改")
    private String categoryId;

    @ImportField(name = "类目名称", remark = "不可修改")
    private String categoryName;

    @ImportField(name = "角色编码", required = true, maxLength = 40, remark = "必填")
    private String fileRoleCode;

    @ImportField(name = "角色说明", required = true, maxLength = 255, remark = "必填")
    private String fileRoleDesc;

    @ImportField(name = "管理", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String manageAuth;

    @ImportField(name = "审核", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String auditAuth;

    @ImportField(name = "查看", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String queryAuth;

    @ImportField(name = "上传", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String uploadAuth;

    @ImportField(name = "下载", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String downloadAuth;

    @ImportField(name = "修改", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String updateAuth;

    @ImportField(name = "删除", isInteger = true, remark = "非必填\n写入对应数字\n0:否\n1:是")
    private String deleteAuth;
}
