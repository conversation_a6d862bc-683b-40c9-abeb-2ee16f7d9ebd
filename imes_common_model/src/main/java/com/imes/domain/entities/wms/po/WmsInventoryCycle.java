package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import com.baomidou.mybatisplus.annotation.TableField;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
	
import javax.validation.constraints.NotBlank;	
	
/**	
 * (WmsInventoryCycle)实体类	
 *	
 * <AUTHOR>	
 * @since 2024-01-18 11:16:08	
 */	
@Data	
public class WmsInventoryCycle implements Serializable {	
    private static final long serialVersionUID = 156165132670862634L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "计划编码")	
    private String planNo;	
	
    @ApiModelProperty(value = "计划名称")	
    @NotBlank(message = "计划名称不能为空")	
    private String planName;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createdOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createdBy;	
	
    @ApiModelProperty(value = "盘点员工号")	
    private String checkBy;	
	
    @ApiModelProperty(value = "盘点员名称")	
    @TableField(exist = false)	
    private String checkByName;	
	
    @ApiModelProperty(value = "复审人")	
    private String reviewBy;	
	
    @ApiModelProperty(value = "复审人名称")	
    @TableField(exist = false)	
    private String reviewByName;	
	
    @ApiModelProperty(value = "计划上次执行时间")	
    private Date lastExecutionTime;	
	
    @ApiModelProperty(value = "备注")	
    private String remark;	
	
    @ApiModelProperty(value = "盘点计划状态")	
    private String status;	
	
    @ApiModelProperty(value = "周期类型")	
    private String cycleCategory;	
	
    @ApiModelProperty(value = "删除状态")	
    private Integer deleteStatus;	
	
    @ApiModelProperty(value = "更新人")	
    private String updatedBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updatedOn;	
	
    @ApiModelProperty(value = "开始时间")	
    private Date startTime;	
	
    @ApiModelProperty(value = "结束时间")	
    private Date endTime;	
	
    @ApiModelProperty(value = "计划最后生成时间")	
    private Date lastPlanGenerationTime;	
	
    @ApiModelProperty(value = "有效期（在该有效期内必须完成）")	
    private Integer validityTime;	
	
    @ApiModelProperty(value = "下次计划开始时间")	
    private Date nextStartTime;	
	
    @ApiModelProperty(value = "间隔数")	
    private Integer gapNum;	
	
    @ApiModelProperty(value = "盘点类型")	
    private String checkType;	
	
    @ApiModelProperty(value = "审核意见")	
    private String auditOpinion;	
	
    @ApiModelProperty(value = "提交时间")	
    private Date commitTime;	
	
    @ApiModelProperty(value = "审核时间")	
    private Date approvalTime;	
	
    @ApiModelProperty(value = "审核人")	
    private String approvalUser;	
	
    @ApiModelProperty(value = "审核人编码")	
    private String approvalUserCode;	
	
    @ApiModelProperty(value = "流程id")	
    private String activitiId;	
	
    @ApiModelProperty(value = "审核标识")	
    private String auditMark;	
	
    @ApiModelProperty(value = "任务模式")	
    private String taskModel;	
	
    @ApiModelProperty(value = "库区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    @TableField(exist = false)	
    private String materialName;	
	
    @ApiModelProperty(value = "日历编码")	
    private String calendarCode;	
	
}	
	
