package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class ProcessBadCountsQualityReportVO implements Serializable {	
    /**	
     * 不良项code	
     */	
	@ApiModelProperty("不良项code")	
    private String badCode;	
	
    /**	
     * 不良项name	
     */	
	@ApiModelProperty("不良项name")	
    private String badName;	
	
    /**	
     * 不良项数量	
     */	
	@ApiModelProperty("不良项数量")	
    private int sumQty;	
    private static final long serialVersionUID = 1L;	
}	
