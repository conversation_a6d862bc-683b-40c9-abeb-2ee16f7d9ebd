package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.*;
import com.google.common.collect.Maps;
import com.imes.domain.entities.pm.enums.ApprovalStatusEnum;
import com.imes.domain.entities.pm.po.EvaluationLevel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * (PerEvaluationScheme)实体类
 *
 * <AUTHOR>
 * @since 2022-01-06 17:05:12
 */
@ApiModel("绩效考评方案")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationSchemeDto implements Serializable {
    private static final long serialVersionUID = 796219717044769006L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;
    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private String pid;
    /**
     * 0=考评方案分类；1=考评模板方案
     */
    @ApiModelProperty("0=考评方案分类；1=考评模板方案")
    private Integer type;
    /**
     * 名称
     */
    @ApiModelProperty("名称")
    @NotBlank(message = "考评方案名称不能为空", groups = AddGroup.class)
    private String name;
    /**
     * 是否为项目方案（1：项目方案 0：其他）
     */
    @ApiModelProperty("是否为项目方案（1：项目方案 0：其他）")
    private Boolean project;
    /**
     * 考评周期
     */
    @ApiModelProperty("考评周期")
    @NotNull(message = "考评周期不能为空", groups = AddGroup.class)
    private Integer cycle;

    /**
     * 考评周期中文
     */
    @ApiModelProperty("考评周期中文")
    private String cycleStr;
    /**
     * 考评人
     */
    @ApiModelProperty("考评人")
    // @NotBlank(message = "考评人能为空", groups = AddGroup.class)
    private String assessor;
    /**
     * 流程模型
     */
    @ApiModelProperty("流程模型")
    @NotBlank(message = "流程模型能为空", groups = AddGroup.class)
    private String modeler;
    /**
     * 被考评人
     */
    @ApiModelProperty("被考评人")
    @NotEmpty(message = "被考评人不能为空", groups = AddGroup.class)
    private List<String> evaluatedPerson;
    /**
     * 权重
     */
    @ApiModelProperty("权重")
    private Integer weight;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 考评指标
     */
    @ApiModelProperty("考评指标")
    @NotEmpty(message = "被考指标不能为空", groups = AddGroup.class)
    private List<EvaluationSchemeIndexDto> indexItems;
    /**
     * 考评等级
     */
    private List<EvaluationLevel> levels;
    /**
     * 子绩效考评方案
     */
    @ApiModelProperty("子绩效考评方案")
    // @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<EvaluationSchemeDto> children;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
     * 显示等级
     */
    @ApiModelProperty("显示等级")
    private Boolean grade;

    /**
     * 审批状态
     */
    @ApiModelProperty("审批状态")
    @JsonIgnore
    private ApprovalStatusEnum approvalStatus;

    @JsonAnyGetter
    public Map<String, Object> getApprovalStatusAny() {
        if (Objects.nonNull(this.approvalStatus)) {
            LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
            map.put("approvalStatus", this.approvalStatus.getApprovalStatus());
            map.put("approvalName", this.approvalStatus.getApprovalName());
            return map;
        }
        return null;
    }

    @JsonAnySetter
    public void setApprovalStatusAny(String name, Object value) {
        if ("approvalStatus".equals(name)) {
            if (value instanceof Integer) {
                this.approvalStatus = ApprovalStatusEnum.match((Integer) value);
            }
        }
    }
}