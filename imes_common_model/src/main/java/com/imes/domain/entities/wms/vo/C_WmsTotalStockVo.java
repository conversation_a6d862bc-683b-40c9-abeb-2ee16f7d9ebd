package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.ppc.po.Promaterial;	
import com.imes.domain.entities.query.model.base.OrderBy;	
import com.imes.domain.entities.query.model.base.QueryField;	
import com.imes.domain.entities.query.model.base.Type;	
import com.imes.domain.entities.wms.WmsStockBin;	
import com.imes.domain.entities.wms.WmsStorageInBill;	
import com.imes.domain.entities.wms.WmsStorageOutBill;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsTotalStockVo {	
	
    private static final long serialVersionUID = 1L;	
	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
    @QueryField(name = "物料型号")	
    private String materialMarker;	
	
    @QueryField(name = "规格")	
    private String specification;	
	
    @QueryField(name = "物料类型", type = Type.Select, treeKey = {"typeCode", "parentCode", "0"}, sqlOption = "select type_code as value, type_name as label, type_code as typeCode, parent_type_code as parentCode from sys_material_type order by type_code")	
    private String materialTypeCode;	
	
    @QueryField(name = "在库库存")	
    private String onhandQty;	
	
    @QueryField(name = "可用库存")	
    private String availableQty;	
	
    @QueryField(name = "锁定库存")	
    private String lockQty;	
	
    @QueryField(name = "主单位")	
    private String unit;	
	
    @QueryField(name = "最近入库", type = Type.Date)	
    private String inboundLately;	
	
    @QueryField(name = "最近出库", type = Type.Date)	
    private String outboundLately;	
	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC, show = false)	
    private String createOn;	
	
}	
