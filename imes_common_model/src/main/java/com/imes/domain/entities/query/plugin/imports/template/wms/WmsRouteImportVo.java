package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(name = "路径信息导入")
public class WmsRouteImportVo extends BaseModel {

    @ImportField(name = "路径编号", required = true, maxLength = 40, remark = "必填")
    private String routeCode;

    @ImportField(name = "路径名称", required = true, maxLength = 50, remark = "必填")
    private String routeName;

    @ImportField(name = "路径类型", required = true, dictOption = "WMS_ROUTE_TYPE", remark = "必填\n写入对应数字\n1:出库\n2:入库\n3:拣选出库\n4:拣选入库")
    private String routeType;

    @ImportField(name = "路径状态", required = true, dictOption = "ROUTE_STATUS", remark = "必填\n写入对应数字\n1:正常\n2:禁用")
    private String routeStatus;

    @ImportField(name = "起点线体设备", required = true, maxLength = 40, remark = "必填\n参考WCS设备信息")
    private String startConveyerCode;

    @ImportField(name = "终点线体设备", required = true, maxLength = 40, remark = "必填\n参考WCS设备信息")
    private String endConveyerCode;
}
