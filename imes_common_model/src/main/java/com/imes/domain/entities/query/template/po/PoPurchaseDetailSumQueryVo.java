package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购明细汇总")	
@QueryModel(	
        name = "0614-sum",	
        remark = "采购明细汇总",	
        alias = "po_purchase_detail",	
        searchApi = "/api/po/poPurchaseDetail/queryListSum")	
public class PoPurchaseDetailSumQueryVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", show = false, type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
   // @QueryField(name = "mainId",show = false,logic = Logic.Eq)	
    private String mainId;	
	
	@ApiModelProperty("月份")	
    @QueryField(name = "月份")	
    private String month;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量")	
    private String purchaseQty;	
	
	@ApiModelProperty("回复数量")	
    @QueryField(name = "回复数量")	
    private String replyQty;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
/*	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String materialMarker;*/	
	
	@ApiModelProperty("图号")	
    @QueryField(name = "图号")	
    private String dwgNo;	
	
	@ApiModelProperty("旧图号")	
    @QueryField(name = "旧图号")	
    private String oldDwgNo;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String primaryUnit;	
}	
