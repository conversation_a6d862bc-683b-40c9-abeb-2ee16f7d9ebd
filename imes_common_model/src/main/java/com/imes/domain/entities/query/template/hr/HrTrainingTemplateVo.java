package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import lombok.Data;	
	
@Data	
@ApiModel("培训模板")	
@QueryModel(	
        name = "0694",	
        remark = "培训模板",	
        alias = "hr_training_template",	
        searchApi = "/api/hr/training/template/query")	
    public class HrTrainingTemplateVo extends BaseModel {	
	
	@ApiModelProperty("编号")	
    @QueryField(name = "编号")	
    private String id;	
    /**	
     * 培训模板名称	
     */	
	@ApiModelProperty("编号")	
    @QueryField(name = "编号")	
    private String name;	
    /**	
     * 培训类型	
     */	
	@ApiModelProperty("培训类型")	
    @QueryField(name = "培训类型")	
    private String type;	
    /**	
     * 培训内容	
     */	
	@ApiModelProperty("培训内容")	
    @QueryField(name = "培训内容")	
    private String content;	
	
}	
