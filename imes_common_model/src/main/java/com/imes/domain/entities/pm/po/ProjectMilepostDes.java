package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目里程碑进度预览>>
 * @company 捷创智能技术有限公司
 * @create 2021-10-14 13:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostDes {
    /**
     * id
     */
    private String id;
    /**
     * pid
     */
    private String pid;
    /**
     * 里程碑名称
     */
    private String name;
    /**
     * 顺序
     */
    private Integer sort;
    /**
     * 项目id
     */
    private String projectId;
    /**
     * 是否已完成
     */
    private Boolean finish;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 实际开始日期
     */
    private LocalDate actualStartDate;
    /**
     * 实际结束日期
     */
    private LocalDate actualFinishDate;
    /**
     * 完成率
     */
    private Integer progress;
    /**
     * 是否精细化
     */
    private Boolean refinement;
    /**
     * 计划工时
     */
    private Integer plannedHours;
    /**
     * 最大工时
     */
    private Integer maxHours;
    /**
     * 最小工时
     */
    private Integer minHours;
    /**
     * 实际工时
     */
    private BigDecimal actualHour;
    /**
     * 子里程碑
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @TableField(exist = false)
    private List<ProjectMilepostDes> children;
    /**
     *  参与人
     */
    @TableField(exist = false)
    private List<ProjectMilepostParticipantsDes> participants;
}
