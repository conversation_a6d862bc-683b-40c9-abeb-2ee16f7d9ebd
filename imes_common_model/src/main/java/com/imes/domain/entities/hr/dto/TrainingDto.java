package com.imes.domain.entities.hr.dto;

import com.imes.domain.entities.hr.enmus.TrainStatusEnum;
import com.imes.domain.entities.hr.enmus.TrainTypeEnum;
import com.imes.domain.entities.hr.po.TrainingUser;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel("培训")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TrainingDto implements Serializable {
    private static final long serialVersionUID = -68555712716273498L;
    
    private String id;
    /**
     * 培训名称
     */
    @NotNull(message = "培训名称不为空")
    private String name;
    /**
     * 组织者编号
     */
//    @NotNull(message = "组织者编号不为空")
    private String organizeCode;
    /**
     * 讲师编号
     */
    @NotNull(message = "讲师编号不为空")
    private String userCode;
    /**
     * 讲师名称
     */
    @NotBlank(message = "讲师名称不为空")
    private String userName;
    /**
     * 位置
     */
    private String location;
    /**
     * 联系电话
     */
    @NotBlank(message = "联系电话不为空")
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$", message = "手机格式错误")
    private String phone;
    /**
     * 介绍
     */
    private String introduction;
    /**
     * 类型
     */
    @NotNull(message = "培训类型不为空")
    private TrainTypeEnum type;
    /**
     * 状态
     */
    private TrainStatusEnum status;
    /**
     * 培训方式
     */
    private Integer methods;
    /**
     * 培训来源
     */
    private Integer source;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 部门编码
     */
    private String deptCode;
    /**
     * 培训时间
     */
    @NotNull(message = "培训时间不为空")
    private Date trainingTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不为空")
    private Date endTime;


    List<TrainingUser> userList;

    /**
     * 是否强制
     */
    private Integer isMandatory;

    private Integer signInFlag;

    List<String> signInIdList;

    /**
     * 场所编码
     */
    private String siteCode;
    /**
     * 所需设备
     */
    private String equipment;

    private String surveyNo;
}

