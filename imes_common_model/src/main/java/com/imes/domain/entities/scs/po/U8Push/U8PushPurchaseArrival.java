package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @author: 武新宇
 * @version: v1.0
 * @Package: com.imes.domain.entities.scs.po.U8Push
 * @description:
 * @date:2022/12/12 - 16:01
 */
@Data
@ApiModel(value = "U8采购到货单推送实体类")
public class U8PushPurchaseArrival implements Serializable {

    @ApiModelProperty(value = "到货单号")
    private String docno;

    @ApiModelProperty(value = "单据日期")
    private String docdate;

    @ApiModelProperty(value = "单据类型")
    final private String doctype = "Pu_ArrivalVouch";

    @ApiModelProperty(value = "U8采购订单号")
    private String cpono;

    @ApiModelProperty(value = "备注")
    private String memo;

    @ApiModelProperty(value = "操作人")
    private String operator;

    private List<U8ArrivalRows> rows;

}
