package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.InBillInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.MaterialInspection;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class MaterialInspectionListVo implements Serializable {	
	
    /**	
     * 主信息	
     */	
	@ApiModelProperty("主信息")	
    MaterialInspection main;	
	
    /**	
     * 来料单号组	
     */	
	@ApiModelProperty("来料单号组")	
    String[] infos;	
	
    /**	
     * 报工单号组长度	
     */	
	@ApiModelProperty("报工单号组长度")	
    int length;	
    private static final long serialVersionUID = 1L;	
}	
