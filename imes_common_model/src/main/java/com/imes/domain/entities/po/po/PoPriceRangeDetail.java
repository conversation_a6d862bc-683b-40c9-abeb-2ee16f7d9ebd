package com.imes.domain.entities.po.po;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 采购取价方式明细(PoPriceRangeDetail)实体类
 *
 * <AUTHOR>
 * @since 2023-11-07 10:05:33
 */
@Data
@ApiModel("采购取价方式明细")
public class PoPriceRangeDetail implements Serializable {
    private static final long serialVersionUID = -47330648180803001L;

    /**
     * id
     */
    private String id;

    /**
     * 主单id
     */
    @ApiModelProperty("主单id")
    private String mainId;

    /**
     * 取价顺序
     */
    @ApiModelProperty("取价顺序")
    private Integer priceNum;

    /**
     * 取价来源
     */
    @ApiModelProperty("取价来源")
    private String priceSource;

    /**
     * 最近有效期（月）
     */
    @ApiModelProperty("最近有效期（月）")
    private Integer nearlyMonth;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createOn;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

}

