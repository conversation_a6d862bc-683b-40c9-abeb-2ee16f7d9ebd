/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(value = "润滑模板润滑项表")
@Data
@NoArgsConstructor
@Entity
@Table(name = "dev_lubrication_temp_item")
public class LubricationTempItem implements Serializable {
	
    private static final long serialVersionUID = 8832124337731797819L;
	
    @Id
    private String id;

    /**
     * 润滑模板id
     */
    @ApiModelProperty(value = "模板编码")
    private String tempNo;

    @ApiModelProperty(value = "设备编码")
    private String devNo;


    private java.util.Date createdOn;
    private String createdBy;
    private String updatedBy;
    private java.util.Date updatedOn;

    /**
     * 润滑项信息
     */
    @ApiModelProperty(value = "润滑项信息")
    @JoinColumn(name="itemInfoNo", referencedColumnName="itemInfoNo", insertable=false, updatable=false)
    @ManyToOne(cascade = CascadeType.REFRESH)
    private LubricationItemInfo item;

    /**
     * 润滑项信息编码
     */
    @ApiModelProperty(value = "润滑项信息编码")
    private String itemInfoNo;

}
