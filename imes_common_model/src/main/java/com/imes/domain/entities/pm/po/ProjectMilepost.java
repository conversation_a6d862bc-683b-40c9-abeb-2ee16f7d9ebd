package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.pm.annotation.ChangeLog;
import com.imes.domain.entities.pm.enums.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProProjectMilepost)实体类
 *
 * <AUTHOR>
 * @since 2020-11-25 09:38:17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_milepost",resultMap = "BaseResultMap")
public class ProjectMilepost implements Serializable {
    private static final long serialVersionUID = 740191189615570935L;
    /**
    * id
    */
    private String id;
    /**
    * 父id
    */
    private String pid;
    /**
     * 型（0：里程碑；1：任务）
     */
    @ChangeLog("类型")
    private MilepostTypeEnum type;
    /**
    * 里程碑名称
    */
    @ChangeLog("名称")
    @TableField(condition = SqlCondition.LIKE)
    private String text;
    /**
    * 里程碑模板id
    */
    private String milestoneTemplateId;
    /**
    * 项目id
    */
    private String projectId;
    /**
     * 人天
     */
    @TableField(exist = false)
    private BigDecimal manDays;
    /**
     * 负责人
     */
    @ChangeLog("负责人")
    private String principal;
    /**
     * 完成率
     */
    @ChangeLog("完成率")
    private Integer progress;
    /**
    * 顺序
    */
    private Integer sort;
    /**
    * 状态
    */
    @ChangeLog("状态")
    private StateEnum state;
    /**
     * 状态别名
     */
    // @ChangeLog("状态")
    private String status;
    /**
    * 开始日期
    */
    @ChangeLog("开始日期")
    private LocalDate startDate;
    /**
    * 结束日期
    */
    @ChangeLog("结束日期")
    private LocalDate endDate;
    /**
    * 实际开始日期
    */
    @ChangeLog("实际开始日期")
    private LocalDate actualStartDate;
    /**
    * 实际完成日期
    */
    @ChangeLog("实际完成日期")
    private LocalDate actualFinishDate;
    /**
     * 是否精细化
     */
    private Boolean refinement;
    /**
     * 计划工时
     */
    @ChangeLog("计划工时")
    private Integer plannedHours;
    /**
     * 合计工时
     */
    @TableField(exist = false)
    private BigDecimal totalPlannedHours;
    /**
     * 实际工时
     */
    @TableField(exist = false)
    private BigDecimal actualHours;
    /**
     * 最大工时
     */
    private Integer maxHours;
    /**
     * 最小工时
     */
    private Integer minHours;
    /**
     * 超期天数
     */
    @TableField(exist = false)
    private Long overdueDays;
    /**
     * 是否超期预警
     */
    private Boolean warning;
    /**
     * 下次预警日期
     */
    private LocalDate alarmDate;
    /**
     * 预警间隔
     */
    private Integer warningInterval;
    /**
    * 备注
    */
    @ChangeLog("备注")
    private String remarks;
    /**
     * 审核状态
     */
    private ApprovalStatusEnum approvalStatus;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 逻辑删除
    */
    private Boolean deleted;
    /**
    * 乐观锁
    */
    private Long version;
    /**
     * 计算方式
     */
    @ChangeLog("计算方式")
    private CalculationEnum calculation;
    /**
     * 是否存在子节点
     */
    @TableField(exist = false)
    private Boolean hasChildren;

    /**
     * 差旅费
     */
    @TableField(exist = false)
    private BigDecimal travelCost;

    /**
     * 子里程碑
     */
    @TableField(exist = false)
    private List<ProjectMilepost> children;
    /**
     * 参与人员
     */
    @TableField(exist = false)
    List<ProjectMilepostEmployee> participants;
    /**
     * 参与人员工号数组
     */
    @TableField(exist = false)
    List<String> participate;
}