package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
public class ProduceInBillCancelVo {	
	
    /**	
     * id	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 生产单号	
     */	
	@ApiModelProperty("生产单号")	
    private String ppCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyName;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxCode;	
	
    /**	
     * 箱码	
     */	
	@ApiModelProperty("箱码")	
    private String boxNo;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String storageCode;	
	
    /**	
     * 物料code	
     */	
	@ApiModelProperty("物料code")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 数量	
     */	
	@ApiModelProperty("数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 创建日期	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String status;	
	
}	
