package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class InBillInspectionDetailVo implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 入库检验id	
     */	
	@ApiModelProperty("入库检验id")	
    private String parentId;	
	
    /**	
     * 物料编号	
     */	
	@ApiModelProperty("物料编号")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 检验编号	
     */	
	@ApiModelProperty("检验编号")	
    private String inspectionCode;	
	
    /**	
     * 检验数量编号	
     */	
	@ApiModelProperty("检验数量编号")	
    private String inspectionNo;	
	
    /**	
     * 检验项id	
     */	
	@ApiModelProperty("检验项id")	
    private String itemId;	
	
    /**	
     * 检验项名称	
     */	
	@ApiModelProperty("检验项名称")	
    private String itemName;	
	
    /**	
     * 检验项编码	
     */	
	@ApiModelProperty("检验项编码")	
    private String itemCode;	
	
    /**	
     * 子项	
     */	
	@ApiModelProperty("子项")	
    private String childItem;	
	
    /**	
     * 参数上限	
     */	
	@ApiModelProperty("参数上限")	
    private int upLimit;	
	
    /**	
     * 参数下限	
     */	
	@ApiModelProperty("参数下限")	
    private int downLimit;	
	
    /**	
     * 非理化标准	
     */	
	@ApiModelProperty("非理化标准")	
    private String unStandard;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    private int inspectionNum;	
	
    /**	
     * 实际参数	
     */	
	@ApiModelProperty("实际参数")	
    private int realityValue;	
	
    /**	
     * 是否合格	
     */	
	@ApiModelProperty("是否合格")	
    private String isQualified;	
	
    /**	
     * 报工单号	
     */	
	@ApiModelProperty("报工单号")	
    private String reportOrder;	
	
    /**	
     * 报工单号id	
     */	
	@ApiModelProperty("报工单号id")	
    private String orderId;	
	
    /**	
     * 报工数量	
     */	
	@ApiModelProperty("报工数量")	
    private int goodQty;	
	
    /**	
     * 判定方式	
     */	
	@ApiModelProperty("判定方式")	
    private String determineType;	
	
    /**	
     *	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     *	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     *	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     *	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     *	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    private static final long serialVersionUID = 1L;	
}	
