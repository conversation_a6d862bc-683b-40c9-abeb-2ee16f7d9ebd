package com.imes.domain.entities.wms;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
	
@Data	
@ApiModel(value = "")	
public class WmsCellMaterialRel implements Serializable {	
	
    private static final long serialVersionUID = -44116604990463104L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "载具编码")	
    private String cellCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "批次")	
    private String batch;	
	
    @ApiModelProperty(value = "供应商编码")	
    private String supplierCode;	
	
    @ApiModelProperty(value = "可用库存（基本单位）")	
    private BigDecimal primaryAvailableQty;	
	
    @ApiModelProperty(value = "基本单位")	
    private String primaryUnit;	
	
    @ApiModelProperty(value = "可用库存（库存单位）")	
    private BigDecimal packAvailableQty;	
	
    @ApiModelProperty(value = "库存单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "含税单价")	
    private BigDecimal includePrice;	
	
    @ApiModelProperty(value = "不含税单价")	
    private BigDecimal unIncludePrice;	
	
    @ApiModelProperty(value = "辅助属性")	
    private String skuCode;	
	
    @ApiModelProperty(value = "箱码")	
    private String boxNo;	
	
    @ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如入库、出库等作业而锁定的库存")	
    private String lockStatus;	
	
    @ApiModelProperty(value = "最近入库")	
    private Date inboundLately;	
	
    @ApiModelProperty(value = "最近出库")	
    private Date outboundLately;	
	
    @ApiModelProperty(value = "生产日期")	
    private Date productionDate;	
	
    @ApiModelProperty(value = "失效日期")	
    private Date failureDate;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
}	
