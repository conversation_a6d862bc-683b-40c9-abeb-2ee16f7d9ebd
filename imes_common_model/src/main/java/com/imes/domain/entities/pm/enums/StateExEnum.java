package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目状态>>
 * @company 捷创智能技术有限公司
 * @create 2022-07-06 16:57
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum StateExEnum {
    NOT_START(0, "未开始"),
    PAUSED(2, "暂停"),
    STOP(3, "停止"),
    RESUME(1, "恢复"),
    COMPLETE(4, "完成");

    @EnumValue
    public final Integer state;
    public final String stateName;

    StateExEnum(Integer state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }


    public Integer getState() {
        return state;
    }

    public String getStateName() {
        return stateName;
    }

    /**
     * 转换为StateEnum
     * @return
     */
    public StateEnum toStateEnum() {
        return StateEnum.match(state);
    }


    public static StateExEnum match(Integer state) {
        return Arrays.stream(StateExEnum.values())
                .filter(e -> e.getState().equals(state))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("项目状态错误"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static StateExEnum des(final JsonNode jsonNode) {
        Integer state = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("state")).map(JsonNode::asInt).orElse(-1);
        if (state == -1) {
            return null;
        }
        return StateExEnum.match(state);
    }
}
