package com.imes.domain.entities.wei.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "wei_in_metering")
public class InMetering implements Serializable {

    @Id
    private String id;
    private String weighingNo;
    private String truckNo;
    private String supplier;
    private String goodsName;
    private Double gross;
    private Double tare;
    private Double net;
    private String weiUnit;
    private String weighingMan;
    private java.util.Date emptyWeiTime;
    private java.util.Date loadWeiTime;
    private String status;
    private String createdBy;
    private java.util.Date createdOn;
    private String updatedBy;
    private java.util.Date updatedOn;
    private String weighingPlace;
    private String remarks;

    public void setId(String value) {
        this.id = value;
    }
    public String getId() {
        return this.id;
    }
    public void setWeighingNo(String value) {
        this.weighingNo = value;
    }
    public String getWeighingNo() {
        return this.weighingNo;
    }
    public void setTruckNo(String value) {
        this.truckNo = value;
    }
    public String getTruckNo() {
        return this.truckNo;
    }
    public void setSupplier(String value) {
        this.supplier = value;
    }
    public String getSupplier() {
        return this.supplier;
    }
    public void setGoodsName(String value) {
        this.goodsName = value;
    }
    public String getGoodsName() {
        return this.goodsName;
    }
    public void setGross(Double value) {
        this.gross = value;
    }
    public Double getGross() {
        return this.gross;
    }
    public void setTare(Double value) {
        this.tare = value;
    }
    public Double getTare() {
        return this.tare;
    }
    public void setNet(Double value) {
        this.net = value;
    }
    public Double getNet() {
        return this.net;
    }
    public void setWeiUnit(String value) {
        this.weiUnit = value;
    }
    public String getWeiUnit() {
        return this.weiUnit;
    }
    public void setWeighingMan(String value) {
        this.weighingMan = value;
    }
    public String getWeighingMan() {
        return this.weighingMan;
    }
    public void setEmptyWeiTime(java.util.Date value) {
        this.emptyWeiTime = value;
    }
    public java.util.Date getEmptyWeiTime() {
        return this.emptyWeiTime;
    }
    public void setLoadWeiTime(java.util.Date value) {
        this.loadWeiTime = value;
    }
    public java.util.Date getLoadWeiTime() {
        return this.loadWeiTime;
    }
    public void setStatus(String value) {
        this.status = value;
    }
    public String getStatus() {
        return this.status;
    }
    public void setCreatedBy(String value) {
        this.createdBy = value;
    }
    public String getCreatedBy() {
        return this.createdBy;
    }
    public void setCreatedOn(java.util.Date value) {
        this.createdOn = value;
    }
    public java.util.Date getCreatedOn() {
        return this.createdOn;
    }
    public void setUpdatedBy(String value) {
        this.updatedBy = value;
    }
    public String getUpdatedBy() {
        return this.updatedBy;
    }
    public void setUpdatedOn(java.util.Date value) {
        this.updatedOn = value;
    }
    public java.util.Date getUpdatedOn() {
        return this.updatedOn;
    }
    public void setWeighingPlace(String value){this.weighingPlace = value;}
    public String getWeighingPlace(){return this.weighingPlace;}
    public void setRemarks(String value){this.remarks = value;}
    public String getRemarks(){return this.remarks;}
}