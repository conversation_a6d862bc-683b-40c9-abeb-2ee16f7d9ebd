package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * (ProjectMilepostProgress)实体类
 *
 * <AUTHOR>
 * @since 2020-12-07 14:11:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_milepost_progress",resultMap = "BaseResultMap")
public class ProjectMilepostProgress implements Serializable {
    private static final long serialVersionUID = 308487428248907022L;
    /**
     * id
     */
    private String id;
    /**
     * 新阳guid保证不会重复同步
     */
    private String guid;
    /**
     * 总上报id
     */
    private String sumId;
    /**
     * 上报类型
     */
    private Boolean type;
    /**
     * 项目id
     */
    @TableField(exist = false)
    private String projectId;
    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;
    /**
     * 项目信息
     */
    @TableField(exist = false)
    private Map<String,Object> other;

    public void setOther(Map<String, Object> other) {
        if(CollectionUtils.isEmpty(other)){
            return;
        }
        this.projectId = other.get("projectId").toString();
        this.projectName = other.get("projectName").toString();
        this.milepostName = other.get("milepostName").toString();
        this.other = other;
    }
    /**
     * 里程碑id
     */
    private String milepostId;
    /**
     * 里程碑名称
     */
    @TableField(exist = false)
    private String milepostName;
    /**
     * 上报id
     */
    private String reportId;
    /**
     * 参与人工号
     */
    private String employeeCode;
    /**
     * 参与人姓名
     */
    @TableField(exist = false)
    private String employeeName;
    /**
     * 上报日期
     */
    private LocalDate reportDate;
    /**
     * 工作时长
     */
    private BigDecimal workHour;
    /**
     * 进度
     */
    private Integer progress;
    /**
     * 内容
     */
    private String content;
    /**
     * 实际开始日期
     */
    private LocalDate actualStartDate;
    /**
     * 实际结束日期
     */
    private LocalDate actualFinishDate;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 更新人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 完结
     */
    private Boolean complete = true;
    /**
     * 日期范围
     */
    @TableField(exist = false)
    private List<LocalDate> dates;
    /*======================================成本=======================================*/
    /**
     * '飞机费用'
     */
    private BigDecimal aircraftCost;
    /**
     * 飞机票数量
     */
    private Integer numberAirTickets;
    /**
     * 火车费用
     */
    private BigDecimal trainCost;
    /**
     * 火车票数量
     */
    private Integer numberTrainTickets;
    /**
     * 大巴费用
     */
    private BigDecimal busCost;
    /**
     * 大巴票数量
     */
    private Integer numberBusTickets;
    /**
     * 的士费用
     */
    private BigDecimal taxiCost;
    /**
     * 的士票数量
     */
    private Integer numberTaxiTickets;
    /**
     * 过路费用
     */
    private BigDecimal tollCost;
    /**
     * 过路票数量
     */
    private Integer numberTollTickets;
    /**
     * 停车费用
     */
    private BigDecimal parkingCost;
    /**
     * 停车票数量
     */
    private Integer numberParkingTickets;
    /**
     * 其他交通费用
     */
    private BigDecimal otherTrafficCost;
    /**
     * 其他交通票数量
     */
    private Integer numberOtherTrafficTickets;
    /**
     * 住宿费用
     */
    private BigDecimal stayCost;
    /**
     * 住宿费用票数量
     */
    private Integer numberStayTickets;
    /**
     * 招待费用
     */
    private BigDecimal entertainCost;
    /**
     * 招待费用票数量
     */
    private Integer numberEntertainTickets;
    /**
     * 招待人名
     */
    private String guests;
    /**
     * 招待人数
     */
    private Integer numberGuests;
    /**
     * 招待目的
     */
    private String guestsObjectives;
    /**
     * 差旅补助
     */
    private BigDecimal travelAllowance;
    /**
     * 差旅补助费用票数
     */
    private Integer numberAllowanceTickets;
    /**
     * 其他费用
     */
    private BigDecimal otherCost;
    /**
     * 其他费用票数量
     */
    private Integer numberOtherTickets;
    /**
     * 报销说明
     */
    private String reimbursementInstructions;
    /**
     * 补贴金额
     */
    private BigDecimal subsidy;
    /**
     * 经纬度
     */
    private String latlng;
    /**
     * 国家
     */
    private String nation;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String district;
    /**
     * 详细地址
     */
    private String detailAddress;
}