package com.imes.domain.entities.po.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "采购订单主单")
public class PoPurchaseStandard implements Serializable {

    private static final long serialVersionUID = -20319720001001274L;

    private String id;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "采购类型")
    private String purchaseType;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "采购部门")
    private String demandDepCode;

    @ApiModelProperty(value = "采购部门名称")
    private String demandDepName;

    @ApiModelProperty(value = "采购员工号")
    private String demandUserCode;

    @ApiModelProperty(value = "采购员名称")
    private String demandUserName;

    @ApiModelProperty(value = "制单人")
    private String operatorName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "单据来源")
    private String docSource;

    @ApiModelProperty(value = "来源单号")
    private String docCode;

    @ApiModelProperty(value = "采购日期")
    private Date orderDate;

    @ApiModelProperty(value = "单据状态 10-已录入,20-审核中,30-已审核")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    //变更备注
    @ApiModelProperty(value = "变更备注")
    private String changeRemarks;

    //取价方式
    @ApiModelProperty(value = "取价方式")
    private String priceType;
}