package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("销售订单结算对账")	
@QueryModel(	
        name = "1303",	
        remark = "销售订单结算对账",	
        alias = "g",	
        searchApi = "/api/po/poPurchase/accountQueryList")	
public class PoPurchaseAccountSearchVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
	@ApiModelProperty("销售订单号")	
    @QueryField(name = "销售订单号", order = OrderBy.DESC)	
    private String purchaseNo;	
	
	@ApiModelProperty("订单类型")	
    @QueryField(name = "订单类型", dictOption = "PO_PURCHASE_TYPE", type = Type.Select)	
    private String purchaseType;	
	
	@ApiModelProperty("订单日期")	
    @QueryField(name = "订单日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String orderDate;	
	
	@ApiModelProperty("订单项次")	
    @QueryField(name = "订单项次")	
    private String sdNo;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("物料规格型号")	
    @QueryField(name = "物料规格型号")	
    private String specification;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量")	
    private String purchaseQty;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", option = {"0", "是", "1", "否"}, type = Type.MultiSelect)	
    private String includeTax;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)")	
    private String taxRate;	
	
	@ApiModelProperty("采购单价")	
    @QueryField(name = "采购单价", type = Type.Number, format = "0.00")	
    private String singlePrice;	
	
	@ApiModelProperty("发货数量")	
    @QueryField(name = "发货数量")	
    private String deliveryNum;	
	
	@ApiModelProperty("签收数量")	
    @QueryField(name = "签收数量")	
    private String receiveNum;	
	
	@ApiModelProperty("签退数量")	
    @QueryField(name = "签退数量")	
    private String returnNum;	
	
	@ApiModelProperty("签差异数量")	
    @QueryField(name = "签差异数量")	
    private String signDiffNum;	
	
	@ApiModelProperty("入库数量")	
    @QueryField(name = "入库数量")	
    private String storageNum;	
	
	@ApiModelProperty("退库数量")	
    @QueryField(name = "退库数量")	
    private String storageReturnNum;	
	
	@ApiModelProperty("已对账数量")	
    @QueryField(name = "已对账数量")	
    private String reconciledNum;	
	
	@ApiModelProperty("待对账数量")	
    @QueryField(name = "待对账数量")	
    private String reconciledNeedNum;	
	
	@ApiModelProperty("待对账金额")	
    @QueryField(name = "待对账金额", type = Type.Number, format = "0.000000")	
    private String reconciledNeedPrice;	
}	
