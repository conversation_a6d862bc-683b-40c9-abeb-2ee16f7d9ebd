package com.imes.domain.entities.query.template.dev;		
		
		
import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
		
@ApiModel(value = "设备台账高级查询模型")		
@Data		
@QueryModel(		
        name = "0227",		
        remark = "设备台账",		
        alias = "dev_device",
        auth = Auth.PC,
        authTenant = true,
        resume = "devCode",
        searchApi = "/dev/devDevice/queryByDeviceQueryVo",
        openapi = true
)
public class DevDeviceQueryVo extends BaseModel {		
		
	@ApiModelProperty("id")		
    @QueryField(name = "id", show = false)
    private String id;		
		
    @ApiModelProperty(value = "区域编码")
    @QueryField(name = "区域编码", show = false)
    @EditField(required = true)
    private String areaCode;

    @ApiModelProperty(value = "区域")
    @QueryField(name = "区域")
    @EditField(show = false)
    private String areaName;


    @ApiModelProperty(value = "区域级别：0-本区域；1-含子区域；2-所有")		
    @QueryField(name = "区域级别", type = Type.Select, option = {"0", "本区域", "1", "含子区域", "2", "所有"})		
    private String areaLevel;		
		
    @ApiModelProperty(value = "设备编码")		
    @QueryField(name = "设备编码")		
    private String devCode;		
		
    @ApiModelProperty(value = "设备名称")		
    @QueryField(name = "设备名称", order = OrderBy.DESC)
    @EditField(required = true)
    private String devName;		
		
    @ApiModelProperty(value = "设备种类，参考数据字典：DEVICE_TYPE")		
    @QueryField(name = "设备种类", type = Type.Select, dictOption = "DEVICE_TYPE")		
    private String devCategory;		
		
    @ApiModelProperty(value = "设备类型，参考数据字典：DEV_TYPE")		
    @QueryField(name = "设备类型", type = Type.Select, option = {"1", "生产"})
    private String devType;		
		
    @ApiModelProperty(value = "ABC分类，请参考数据字典：DEVICE_ABC_TYPE")		
    @QueryField(name = "ABC分类", type = Type.Select, dictOption = "DEVICE_ABC_TYPE", alias = "dev_device.abcType", level = Level.Main)
    private String abcType;		
		
    @ApiModelProperty(value = "父级设备")		
    @QueryField(name = "父级设备")		
    private String parentName;		
		
    @ApiModelProperty(value = "父级设备编码")		
    @QueryField(name = "父级设备编码")		
    private String devPcode;		
		
    @ApiModelProperty(value = "设备路径")		
    @QueryField(name = "设备路径", alias = ".CONVERT(dev_path USING utf8)", level = Level.Main)
    private String devPath;		
		
    @ApiModelProperty(value = "设备负责人")		
    @QueryField(name = "设备负责人", query = false, sort = false)
    @EditField(required = true)
    private String chargeName;
		
    @ApiModelProperty(value = "设备型号")		
    @QueryField(name = "设备型号")		
    private String modelNumber;		
		
    @ApiModelProperty(value = "制造单位")		
    @QueryField(name = "制造单位")		
    private String madeFactory;		
		
    @ApiModelProperty(value = "设备重量（kg）")		
    @QueryField(name = "设备重量（kg）")		
    private String devWeight;		
		
    @ApiModelProperty(value = "制造日期")		
    @QueryField(name = "制造日期", type = Type.Date, format = "yyyy-MM-dd")		
    private String madeTime;		
		
    @ApiModelProperty(value = "制造编码")		
    @QueryField(name = "制造编码")		
    private String madeCode;		
		
    @ApiModelProperty(value = "警报编码")		
    @QueryField(name = "警报编码", type = Type.Select, sqlOption = "select warning_code as value, warning_name as label from sys_warning_main where warning_category = 'W201'")		
    private String warningCode;		
		
    @ApiModelProperty(value = "安装地点")		
    @QueryField(name = "安装地点")		
    private String installAddress;		
		
    @ApiModelProperty(value = "采购日期")		
    @QueryField(name = "采购日期", type = Type.Date, format = "yyyy-MM-dd")		
    private String purchaseDate;		
		
    @ApiModelProperty(value = "投用日期")		
    @QueryField(name = "投用日期", type = Type.Date, format = "yyyy-MM-dd")		
    private String useTime;		
		
    @ApiModelProperty(value = "有效日期")		
    @QueryField(name = "有效日期", type = Type.Date, format = "yyyy-MM-dd")		
    private String effectiveData;		
		
    @ApiModelProperty(value = "设备使用状态：  1-在用；2-待用；3-停用；4-封存；5-委外；6-报废")		
    @QueryField(name = "状态", type = Type.Select, option = {"1", "在用", "2", "待用",		
            "3", "停用", "4", "封存", "5", "委外", "6", "报废"})
    @EditField(required = true)
    private String devUseStatus;
		
    @ApiModelProperty(value = "运行状态：0-停机；1-运行")		
    @QueryField(name = "运行状态", type = Type.Select, option = {"0", "停机", "1", "运行"})
    @EditField(required = true)
    private String devRunStatus;
		
    @ApiModelProperty(value = "额定功率")		
    @QueryField(name = "额定功率")		
    private String ratedPower;		
		
    @ApiModelProperty(value = "额定电压")		
    @QueryField(name = "额定电压")		
    private String ratedVoltage;		
		
    @ApiModelProperty(value = "温度上限")		
    @QueryField(name = "温度上限")		
    private String temperatureMax;		
		
    @ApiModelProperty(value = "温度下限")		
    @QueryField(name = "温度下限")		
    private String temperatureMin;		
		
    @ApiModelProperty(value = "主要技术特征")		
    @QueryField(name = "主要技术特征")		
    private String devFeatures;		
		
    @ApiModelProperty(value = "使用部门")		
    @QueryField(name = "使用部门",  type = Type.Select, sqlOption = "SELECT depart_code as value, depart_name as label from co_department")		
    private String deptCode;		
		
    @ApiModelProperty(value = "备注")		
    @QueryField(name = "备注")		
    private String remarks;		
		
    @ApiModelProperty(value = "设备负责人编码")		
    @QueryField(name = "设备负责人编码", show = false)		
    private String chargeCode;



}		
