package com.imes.domain.entities.query.template.dev;


import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "计量器具台账高级查询模型")
@Data
@QueryModel(
        name = "1538",
        remark = "计量器具台账",
        alias = "mate",
        auth = Auth.PC,
        authTenant = true,
        searchApi = "/dev/attr/mate/queryMeasureQueryVo",
        openapi = true
)
public class DevMeasureQueryVo extends BaseModel {

    @ApiModelProperty(value = "计量器具名称")
    @QueryField(name = "计量器具名称", alias = "mate")
    @EditField(required = true)
    private String measureName;

    @ApiModelProperty(value = "计量器具编码")
    @QueryField(name = "计量器具编码", alias = "mate")
    @EditField(readonly = false)
    private String devCode;


    @ApiModelProperty(value = "计量器具类型")
    @QueryField(name = "计量器具类型", alias = "mate", type = Type.Select, option = {"计量工作器具", "计量工作器具", "计量标准器具", "计量标准器具"})
    @EditField(required = true)
    private String measuringDeviceType;

    @ApiModelProperty(value = "量程下限")
    @QueryField(name = "量程下限", alias = ".SUBSTRING_INDEX(mate.range, ',', 1)")
    private String min;

    @ApiModelProperty(value = "量程上限")
    @QueryField(name = "量程上限", alias = ".SUBSTRING_INDEX(mate.range, ',', -1)")
    private String max;

    @ApiModelProperty(value = "精度等级")
    @QueryField(name = "精度等级", alias = "mate", type = Type.Select, dictOption = "ACCURACY_LEVEL")
    @EditField(required = true)
    private String accuracy;

    @ApiModelProperty(value = "参数单位")
    @QueryField(name = "参数单位", alias = "mate", type = Type.Select, sqlOption = "select code as value ,name as label from sys_unit")
    @EditField(required = true)
    private String parameterUnit;

    @ApiModelProperty(value = "允许误差")
    @QueryField(name = "允许误差", alias = "mate")
    private String offset;

    @ApiModelProperty(value = "分度值")
    @QueryField(name = "分度值", alias = "mate")
    private String scaleInterval;

    @ApiModelProperty(value = "测量设备等级")
    @QueryField(name = "测量设备等级", alias = "mate", type = Type.Select, dictOption = "EQUIP_LEVEL")
    @EditField(required = true)
    private String equipmentLevel;

    @ApiModelProperty(value = "入库检定百分比")
    @QueryField(name = "入库检定百分比", alias = ".(ROUND(mate.standard_percentage * 100))")
    @EditField(readonly = true)
    private String standardPercentage;


    @ApiModelProperty(value = "计量使用部门")
    @QueryField(name = "计量使用部门", alias = "mate", query = false, sort = false)
    private String departName;

    @ApiModelProperty(value = "计量使用部门编码")
    @QueryField(name = "计量使用部门编码", alias = "mate", show = false)
    private String departCode;

    @ApiModelProperty(value = "过检合格率（%）")
    @QueryField(name = "过检合格率（%）", alias = ".(ifnull(ROUND((mate.send_ratio * 100), 2), NULL))")
    @EditField(required = true)
    private String sendRatio;

    @ApiModelProperty(value = "最新计量日期")
    @QueryField(name = "最新计量日期", alias = "mate", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(readonly = true)
    private String latestCheckDate;

    @ApiModelProperty("送检机构编码")
    @QueryField(name = "送检机构", alias = "mate", show = false)
    @EditField(required = false)
    private String supplierCode;

    @ApiModelProperty("送检机构名称")
    @QueryField(name = "送检机构", alias = "sys")
    @EditField(show = false)
    private String supplierName;

    @ApiModelProperty(value = "计量状态：0-待检；1-合格；2-准用；3-报废；4-维修中；5-不合格", dataType = "String")
    @QueryField(name = "计量状态", alias = "mate", type = Type.Select, option = {"0","待检","1","合格","2","准用","3","报废","4", "维修中","5","不合格"})
    @EditField(readonly = true)
    private String measuringState;

    @ApiModelProperty(value = "创建日期")
    @QueryField(name = "创建日期", show = false, order = OrderBy.DESC)
    private String createdOn;


}
