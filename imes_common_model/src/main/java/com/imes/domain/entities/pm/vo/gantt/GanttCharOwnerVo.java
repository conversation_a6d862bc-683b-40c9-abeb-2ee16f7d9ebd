package com.imes.domain.entities.pm.vo.gantt;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<参与人vo>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-26 14:15
 */
@ApiModel("甘特图任务参与人")
@JsonInclude(value = JsonInclude.Include.NON_NULL)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GanttCharOwnerVo {
    @ApiModelProperty("参与人id")
    private String resource_id;
    @ApiModelProperty("工时")
    private String value;
}
