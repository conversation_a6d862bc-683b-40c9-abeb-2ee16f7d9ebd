package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalOrderApiVo {	
	
	
    /**	
     * 关联主单号----必填项	
     */	
    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 到货类型(1:采购单到货;2:销售退货到货;3:到货计划到货)----必填项	
     */	
    @ApiModelProperty("到货类型(1:采购单到货;2:销售退货到货;3:到货计划到货)")	
    private String orderType;	
	
    /**	
     * 收货仓库	
     */	
    @ApiModelProperty("收货仓库")	
    private String whCode;	
	
    /**	
     * 收货仓库名称	
     */	
    @ApiModelProperty("收货仓库名称")	
    private String whName;	
	
    /**	
     * 到货时间	
     */	
    @ApiModelProperty("到货时间")	
    private Date arrivalTime;	
	
    /**	
     * 供应商编码	
     */	
    @ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
    @ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 收货员工号	
     */	
    @ApiModelProperty("收货员工号")	
    private String receiver;	
	
    /**	
     * 收货员名称	
     */	
    @ApiModelProperty("收货员名称")	
    private String receiverName;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 送货方式(101:物流快递;102:供应商货运;103:客户自提)----必填项	
     */	
    @ApiModelProperty("送货方式）")	
    private String logisticType;	
	
    /**	
     * 到货明细单	
     */	
    @ApiModelProperty("到货明细单")	
    private List<WmsArrivalItemApiVo> items;	
	
	
}	
