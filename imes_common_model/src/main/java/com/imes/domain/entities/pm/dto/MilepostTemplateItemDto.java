package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.google.common.collect.Maps;
import com.imes.domain.entities.pm.enums.MilepostTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * (ProMilepostTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2020-11-25 09:23:33
 */
@ApiModel("里程碑实体类")
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class MilepostTemplateItemDto implements Serializable {
    private static final long serialVersionUID = -66494696508759944L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "里程碑id不能为空", groups = UpdateGroup.class)
    @Null(message = "里程碑id必须为空", groups = AddGroup.class)
    private String id;
    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private String pid;
    /**
     * 里程碑类型（0：里程碑；1：任务）
     */
    @ApiModelProperty("里程碑类型")
    private MilepostTypeEnum type;
    /**
     * 里程碑名称
     */
    @ApiModelProperty("里程碑名称")
    @NotEmpty(message = "里程碑名称不能为空", groups = AddGroup.class)
    private String text;

    /**
     * 里程碑模板id
     */
    @ApiModelProperty("里程碑模板id")
    @NotEmpty(message = "里程碑模板id不能为空", groups = AddGroup.class)
    private String milepostTemplateId;

    /**
     * 顺序
     */
    @ApiModelProperty("顺序")
    @NotNull(message = "顺序不能为空",groups = AddGroup.class)
    private Integer sort;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 子节点
     */
    @ApiModelProperty("子里程碑")
    List<MilepostTemplateItemDto> children;
    /**
     * 子节点
     */
    @ApiModelProperty("存在子里程碑")
    private boolean hasChildren;

    /**
     * 扩展字段处理
     * @param name
     * @param value
     */
    @JsonAnySetter
    public void setExpandField(String name, Object value) {
        // 里程碑类型
        if ("type".equals(name)) {
            if (value instanceof Integer) {
                this.type = MilepostTypeEnum.match((Integer) value);
            }
        }
    }

    @JsonAnyGetter
    public Map<String, Object> getExpandField() {
        LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
        // 里程碑类型
        if (Objects.nonNull(this.type)) {
            map.put("type", this.type.getType());
            map.put("typeStr", this.type.getTypeStr());
        }
        return map;
    }

}