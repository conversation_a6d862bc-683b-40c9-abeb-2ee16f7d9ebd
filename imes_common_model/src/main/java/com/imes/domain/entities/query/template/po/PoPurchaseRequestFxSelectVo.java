package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.*;	
	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
	
@Data	
@ApiModel("采购申请（选择）")	
@QueryModel(	
        name = "0633-select",	
        remark = "采购申请（选择）",	
        searchApi = "/api/po/poPurchaseRequest/queryFxList",	
        alias = "po_purchase_request",	
        customBind = "0633")	
public class PoPurchaseRequestFxSelectVo extends BaseModel {	
	
    /**	
     * 主订单id	
     */	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
    /**	
     * 申请单号	
     */	
	@ApiModelProperty("申请单号")	
    @QueryField(name = "申请单号", alias = "po_purchase_request")	
    private String requestNo;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    @QueryField(name = "申请部门编码")	
    private String requestDepartmentCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    @QueryField(name = "申请部门名称")	
    private String requestDepartmentName;	
	
    /**	
     * 申请日期	
     */	
	@ApiModelProperty("申请日期")	
    @QueryField(name = "申请日期", type = Type.Date)	
    private String requestDate;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    @QueryField(name = "申请人工号")	
    private String requestUserCode;	
	
    /**	
     * 申请人姓名	
     */	
	@ApiModelProperty("申请人姓名")	
    @QueryField(name = "申请人姓名")	
    private String requestUserName;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    @QueryField(name = "单据类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_TYPE")	
    private String billType;	
	
    /**	
     * 单据状态	
     */	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS",required = true)	
    private String status;	
	
    /**	
     * 业务状态	
     */	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS",required = true)	
    private String businessStatus;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC, alias = "po_purchase_request")	
    private String createOn;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
    /**	
     * 子单id	
     */	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", show = false)	
    private String detailId;	
	
    /**	
     * 行号	
     */	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号", alias = "po_purchase_request_detail")	
    private String lineNo;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "po_purchase_request_detail")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "po_purchase_request_detail")	
    private String materialName;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "po_purchase_request_detail")	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "po_purchase_request_detail")	
    private String modelNumber;	
	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性", alias = "po_purchase_request_detail")	
    private String skuCode;	
	
    /**	
     * 申请数量	
     */	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", type = Type.Number, query = false, show = false)	
    private String qty;	
	
    /**	
     * 申请数量单位	
     */	
	@ApiModelProperty("申请数量单位")	
    @QueryField(name = "申请数量单位", alias = "po_purchase_request_detail",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String unit;	
	
    /**	
     * 申请数量单位名称	
     */	
	@ApiModelProperty("申请数量单位名称")	
    @QueryField(name = "申请数量单位名称", alias = "sys_unit", show = false)	
    private String unitName;	
	
    /**	
     * 业务状态	
     */	
	@ApiModelProperty("子单业务状态")	
    @QueryField(name = "子单业务状态", type = Type.MultiSelect, option = {"10", "正常", "20", "已关闭"}, alias = "po_purchase_request_detail.business_status",required = true)	
    private String detailBusinessStatus;	
	
    /**	
     * 基础单位	
     */	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "po_purchase_request_detail",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String baseUnit;	
	
    /**	
     * 基础单位名称	
     */	
	@ApiModelProperty("基础单位名称")	
    @QueryField(name = "基础单位名称", alias = "su", show = false)	
    private String baseUnitName;	
	
    /**	
     * 申请到货日期	
     */	
	@ApiModelProperty("申请到货日期")	
    @QueryField(name = "申请到货日期", type = Type.Date, alias = "po_purchase_request_detail.request_arrive_date")	
    private String deliveryDate;	
	
    /**	
     * 子单备注	
     */	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "po_purchase_request_detail.remarks")	
    private String detailRemarks;	
	
    //设置精度位数	
    // private String purchaseQtyPrecision;	
    private String qtyPrecision;	
	
    //即时库存	
    private BigDecimal availableQty;	
    //用于采购变更单的2个参数，需要前端传	
    private String mainId;	
	
    private String soNo;	
	
}	
