package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("下午茶看板库位库存实体类")	
public class WmsLocationInventoryVo {	
	
    @ApiModelProperty("仓位编码")	
    private String binCode;	
	
    @ApiModelProperty("仓位在库库存数量")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty("是否有货物")	
    private Boolean isGoods;	
}	
