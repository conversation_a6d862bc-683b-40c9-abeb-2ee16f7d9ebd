package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "库存日结统计主表")	
public class WmsStockStatistic implements Serializable {	
	
    private static final long serialVersionUID = -96326600442126005L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "工厂编码")	
    private String ftyCode;	
	
    @ApiModelProperty(value = "工厂名称")	
    private String ftyName;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "在库库存")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty(value = "可用库存")	
    private BigDecimal availableQty;	
	
    @ApiModelProperty(value = "锁定库存（是指领料单或发货单预分配的库存）")	
    private BigDecimal lockQty;	
	
    @ApiModelProperty(value = "单位")	
    private String unit;	
	
    @ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如盘点，移库，补货等作业而锁定的库存")	
    private Integer lockStatus;	
	
    @ApiModelProperty(value = "统计日期")	
    private Date statisticDate;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "小数精度位数")	
    private Byte precisionDigit;	
	
    @ApiModelProperty(value = "进位方式")	
    private Byte carryMode;	
}	
