package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProMilepostTemplate)实体类
 *
 * <AUTHOR>
 * @since 2020-10-23 14:15:52
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_milepost_template",resultMap = "BaseResultMap")
public class MilepostTemplate implements Serializable {
    private static final long serialVersionUID = -66419809955449259L;
    /**
     * id
     */
    private String id;
    /**
     * 父id
     */
    private String pid;
    /**
     * 模板名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
     * 创建人id
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 创建人姓名
     */
    @TableField(exist = false)
    private String createdByName;
    /**
     * 修改人id
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
     * 版本号
     */
    private Long version;
    /**
     * 是否为默认里程碑模板
     */
    private Boolean isDefault;
    /**
     * 首选默认里程碑模板
     */
    private Boolean preferred;
    /**
     * 旧版本里程碑模板
     */
    @TableField(exist = false)
    private List<MilepostTemplate> children;
}