package com.imes.domain.entities.query.template.dev;		
		
import io.swagger.annotations.ApiModel;		
		
import io.swagger.annotations.ApiModelProperty;		
import com.imes.domain.entities.query.model.base.*;		
		
import lombok.Data;		
import lombok.experimental.Accessors;		
		
@Data		
@Accessors(chain = true)		
@ApiModel("备刀，排产单信息")		
@QueryModel(		
        name = "0713",		
        remark = "备刀，排产单信息",		
        searchApi = "/api/ppc/knife/productionPage"		
)		
public class YuZhouPrepareKnifeVo extends BaseModel {		
		
    // 排产单号		
	@ApiModelProperty("派工单号")		
    @QueryField(name = "派工单号", alias = "wo")		
    private String woNo;		
	@ApiModelProperty("排产单号")		
    @QueryField(name = "排产单号", alias = "wo")		
    private String productionNo;		
    /* @QueryField(name = "计划单")		
     private String productionNo;*/		
    // 工艺路线		
	@ApiModelProperty("工艺路线")		
    @QueryField(name = "工艺路线", alias = "ps")		
    private String routeCode;		
	@ApiModelProperty("工序")		
    @QueryField(name = "工序",alias = "wo")		
    private String processCode;		
    // 工序号		
    private String processNo;		
    // 物料		
	@ApiModelProperty("物料编码")		
    @QueryField(name = "物料编码", alias = "wo")		
    private String materialCode;		
    // 物料		
	@ApiModelProperty("物料名")		
    @QueryField(name = "物料名", alias = "wo")		
    private String materialName;		
		
	@ApiModelProperty("规格")		
    @QueryField(name = "规格", alias = "wo")		
    private String specification;		
		
		
	@ApiModelProperty("产线编码")		
    @QueryField(name = "产线编码", alias = "wo")		
    private String lineCode;		
		
	@ApiModelProperty("产线名称")		
    @QueryField(name = "产线名称", alias = "wo")		
    private String lineName;		
		
	@ApiModelProperty("设备编码")		
    @QueryField(name = "设备编码", alias = "wo")		
    private String devCode;		
		
	@ApiModelProperty("设备名称")		
    @QueryField(name = "设备名称", alias = "wo")		
    private String devName;		
		
	@ApiModelProperty("加工数量")		
    @QueryField(name = "加工数量", alias = "ps")		
    private String produceQty;		
		
	@ApiModelProperty("派工状态")		
    @QueryField(name = "派工状态", alias = "wo", type = Type.MultiSelect, option = {"10", "录入", "20", "已下达", "30", "已开工", "35", "暂停", "40", "生产完工", "90", "强制完成"}, value = {"20", "30"})		
    private String status;		
		
	@ApiModelProperty("计划开始日期")		
    @QueryField(name = "计划开始日期", alias = "wo", type = Type.Date, format = "yyyy-MM-dd")		
    private String planStartDate;		
		
	@ApiModelProperty("计划完工日期")		
    @QueryField(name = "计划完工日期", alias = "wo", type = Type.Date, format = "yyyy-MM-dd")		
    private String planEndDate;		
		
	@ApiModelProperty("实际开工日期")		
    @QueryField(name = "实际开工日期", alias = "wo", type = Type.Date)		
    private String actualStartDate;		
		
	@ApiModelProperty("实际完工日期")		
    @QueryField(name = "实际完工日期", alias = "wo", type = Type.Date)		
    private String actualEndDate;		
		
	@ApiModelProperty("创建时间")		
    @QueryField(name = "创建时间", alias = "wo", order = OrderBy.DESC, show = false)		
    private String createOn;		
}		
