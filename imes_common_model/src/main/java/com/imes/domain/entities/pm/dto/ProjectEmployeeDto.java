package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (ProProjectEmployee)实体类
 *
 * <AUTHOR>
 * @since 2020-12-04 17:24:35
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("里程碑实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectEmployeeDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = -2441590100036910264L;
    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
    * id
    */
    @ApiModelProperty("id")
    @NotNull(message = "项目参与人id不能为空", groups = UpdateGroup.class)
    @Null(message = "项目参与人id必须为空", groups = AddGroup.class)
    private String id;
    /**
    * 项目id
    */
    @ApiModelProperty("项目id")
    @NotEmpty(message = "项目id不能为空", groups = AddGroup.class)
    private String projectId;
    /**
    * 参与人员工号
    */
    @ApiModelProperty("参与人员工号")
    @NotEmpty(message = "参与人不能为空", groups = AddGroup.class)
    private String employeeCode;
    /**
    * 参与人员姓名
    */
    @ApiModelProperty("参与人员姓名")
    private String employeeName;
    /**
    * 加入时间
    */
    @ApiModelProperty("加入时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;
    /**
    * 角色
    */
    @ApiModelProperty("角色")
    private String role;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

}