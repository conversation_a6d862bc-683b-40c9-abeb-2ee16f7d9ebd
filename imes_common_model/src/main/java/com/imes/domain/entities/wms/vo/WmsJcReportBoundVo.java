package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportBoundVo {	
	
	
    /**	
     * 出入库数量	
     */	
    @ApiModelProperty("出入库数量")	
    private BigDecimal qty;	
	
    /**	
     * 单据数量	
     */	
    @ApiModelProperty("单据数量")	
    private BigDecimal number;	
	
    /**	
     * 出入库金额	
     */	
    @ApiModelProperty("出入库金额")	
    private BigDecimal amount;	
	
    /**	
     * 单据时间	
     */	
    @ApiModelProperty("单据时间")	
    private String createOn;	
	
    /**	
     * 员工工号	
     */	
    @ApiModelProperty("员工工号")	
    private String userCode;	
	
}	
