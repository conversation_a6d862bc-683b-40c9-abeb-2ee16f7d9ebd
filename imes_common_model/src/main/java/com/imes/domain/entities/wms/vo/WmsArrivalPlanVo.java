package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.system.SysUserFieldRe;	
import com.imes.domain.entities.wms.WmsArrivalPlan;	
import com.imes.domain.entities.wms.WmsArrivalPlanItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalPlanVo extends WmsArrivalPlan {	
	
    /**	
     * 到货单主键	
     */	
	@ApiModelProperty("到货单主键")
    private String aoId;	
	
    /**	
     * 到货单号	
     */	
	@ApiModelProperty("到货单号")
    private String aoCode;	
	
    /**	
     * 用户自定义属性	
     */	
	@ApiModelProperty("用户自定义属性")
    private List<SysUserFieldRe> userFields;	
	
    /**	
     * 展示字段	
     */	
	@ApiModelProperty("展示字段")
    private List<Map<String, Object>> showFields;	
	
    /**	
     * 到货明细单	
     */	
	@ApiModelProperty("到货明细单")
    private List<WmsArrivalPlanItemVo> itemVos;	
	
    /**	
     * 到货明细单	
     */	
	@ApiModelProperty("到货明细单")
    private List<WmsArrivalPlanItem> itemList;	
	
    /**	
     * 质量凭证	
     */	
	@ApiModelProperty("质量凭证")
    private List<Map> fileList;	
	
    /**	
     * 拒绝原因	
     */	
	@ApiModelProperty("拒绝原因")
    private String reason;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")
    private String receiptCode;	
	
    /**	
     * 销售订单单号	
     */	
	@ApiModelProperty("销售订单单号")
    private String soCode;	
	
}	
