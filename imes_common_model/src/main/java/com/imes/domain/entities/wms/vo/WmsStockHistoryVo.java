package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
import org.springframework.format.annotation.DateTimeFormat;	
	
import javax.persistence.Transient;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockHistoryVo implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 操作类型（上架，下架，盘点，报废.....）	
     */	
	@ApiModelProperty("操作类型（上架，下架，盘点，报废.....）")	
    private String optionType;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")	
    private String ftyCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 仓库号编码	
     */	
	@ApiModelProperty("仓库号编码")	
    private String whCode;	
	
    /**	
     * 存储区编码	
     */	
	@ApiModelProperty("存储区编码")	
    private String areaCode;	
	
    /**	
     * 仓位	
     */	
	@ApiModelProperty("仓位")	
    private String binCode;	
	
    /**	
     * 老的在库库存	
     */	
	@ApiModelProperty("老的在库库存")	
    private BigDecimal oldOnhandQty;	
	
    /**	
     * 新的在库库存	
     */	
	@ApiModelProperty("新的在库库存")	
    private BigDecimal newOnhandQty;	
	
    /**	
     * 变化库存量	
     */	
	@ApiModelProperty("变化库存量")	
    private BigDecimal qty;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")	
    private Date startDate;	
	
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")	
    private Date endDate;	
	
    private Boolean isIn;//类型 true为入，false为出	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String primaryUnit;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 关联类型	
     */	
	@ApiModelProperty("关联类型")	
    private String receiptType;	
	
    /**	
     * 入库数量	
     */	
	@ApiModelProperty("入库数量")	
    private BigDecimal inBillQty;	
	
    /**	
     * 出库数量	
     */	
	@ApiModelProperty("出库数量")	
    private BigDecimal outBillQty;	
	
    /**	
     * 结存数量	
     */	
	@ApiModelProperty("结存数量")	
    private BigDecimal balanceQty;	
	
    /**	
     * 类型	
     */	
	@ApiModelProperty("类型")	
    private String type;	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 用户名称	
     */	
	@ApiModelProperty("用户名称")	
    private String userName;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String code;	
	
    private String warehouseCode;	
	
    private String binName;	
	
    private String areaName;	
	
    /**	
     * 入库类型	
     */	
	@ApiModelProperty("入库类型")	
    private String inBillType;	
	
    /**	
     * 出库类型	
     */	
	@ApiModelProperty("出库类型")	
    private String outBillType;	
	
    private BigDecimal newBinQty;	
	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 物料类型	
     */	
	@ApiModelProperty("物料类型")	
    private String materialTypeCode;	
	
    private static final long serialVersionUID = 1L;	
}	
