package com.imes.domain.entities.scs.vo;


import com.imes.domain.entities.po.vo.PoPurchaseForecastDaySumVo;
import com.imes.domain.entities.po.vo.PoPurchaseForecastSumVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ScsSaleForecastCfList {
    private Integer version;
    //总计
    private BigDecimal sumQty;
    //回复时间
    private Date replyDate;
    //月总计
    private List<PoPurchaseForecastSumVo> monthLists;
    //日回复数
    private List<PoPurchaseForecastDaySumVo> dayLists;
    //是否少了
    private Integer canLess;
    //差异数
    private BigDecimal diffQty;

    private BigDecimal yearSumQty1;

    private BigDecimal yearSumQty2;

    private BigDecimal yearSumQty3;
    //差异数
    private BigDecimal diffQty1;
    //差异数
    private BigDecimal diffQty2;
    //差异数
    private BigDecimal diffQty3;
}
