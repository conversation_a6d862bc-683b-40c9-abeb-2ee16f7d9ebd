package com.imes.domain.entities.qms.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WeeklyProcessQualityAndProduceAreaReportVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("日期字符串")
    private String dateStr;

    @ApiModelProperty("工序不良数量")
    private List<WeeklyProcessQualityReportVo> processBadQtyList;

    @ApiModelProperty("生产面积")
    private List<WeeklyProcessQualityReportTimeMapVo> produceAreaList;

}	
