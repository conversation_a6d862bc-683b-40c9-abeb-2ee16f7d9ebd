package com.imes.domain.entities.pm.po;

import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ZhongHaoProject {
    private String id;
    private String name;
    private String customerName;
    private String productType;
    private String principalName;
    private Integer state;
    private String status;
    // 计划开始时间
    private LocalDate startDate;
    // 计划结束时间
    private LocalDate endDate;
    // 计划天数
    private Long planDays;
    // 技术阶段完成率
    private String techStageCompleteRate;
    // 外协阶段完成率
    private String externalStageCompleteRate;
    // 采购阶段完成率
    private String purchaseStageCompleteRate;
    // 生产完成率
    private String productionCompleteRate;
    // 售后完成率
    private String afterSaleCompleteRate;
    private List<ZhongHao> children;
}
