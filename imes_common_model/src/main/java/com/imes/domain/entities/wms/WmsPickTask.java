package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPickTask implements Serializable {	
    /**	
     * 主键	
     */
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 拣货任务号	
     */	
	@ApiModelProperty("拣货任务号")
    private String pickTaskCode;	
	
    /**	
     * 出库任务编码	
     */	
	@ApiModelProperty("出库任务编码")
    private String storageOutTaskCode;	
	
    /**	
     * 出库单号	
     */	
	@ApiModelProperty("出库单号")
    private String storageOutCode;	
	
    /**	
     * 出库单明细号	
     */	
	@ApiModelProperty("出库单明细号")
    private String storageOutItemCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")
    private String batch;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")
    private String materialCode;	
	
    /**	
     * 物料描述	
     */	
	@ApiModelProperty("物料描述")
    private String materialName;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")
    private String ftyCode;	
	
    /**	
     * 仓库号编码	
     */	
	@ApiModelProperty("仓库号编码")
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")
    private String whName;	
	
    /**	
     * 存储区编码	
     */	
	@ApiModelProperty("存储区编码")
    private String areaCode;	
	
    /**	
     * 存储区名称	
     */	
	@ApiModelProperty("存储区名称")
    private String areaName;	
	
    /**	
     * 仓位编码	
     */	
	@ApiModelProperty("仓位编码")
    private String binCode;	
	
    /**	
     * 存储单元编码	
     */	
	@ApiModelProperty("存储单元编码")
    private String cellCode;	
	
    /**	
     * 本次下架数量	
     */	
	@ApiModelProperty("本次下架数量")
    private BigDecimal theInventoryQty;	
	
    /**	
     * 匹配库存数量	
     */	
	@ApiModelProperty("匹配库存数量")
    private BigDecimal matchQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")
    private String unit;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")
    private String remark;	
	
    /**	
     * 创建时间	
     */
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")
    private String packCodeUnit;	
	
    /**	
     * 包装单位数量	
     */	
	@ApiModelProperty("包装单位数量")
    private BigDecimal packInventoryQty;	
	
    private String returnPackUnit;	
	
    private BigDecimal returnPackQty;	
	
    /**	
     * 物料辅助属性	
     */	
	@ApiModelProperty("物料辅助属性")
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")
    private String boxNo;	
	
    private List<WmsBusTaskHistory> historiesList;	
	
    private static final long serialVersionUID = 1L;	
}	
