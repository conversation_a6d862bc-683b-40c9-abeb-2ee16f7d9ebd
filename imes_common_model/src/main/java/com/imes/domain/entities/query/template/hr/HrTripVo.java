package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 行程表(HrTrip)实体类	
 *	
 * <AUTHOR> z	
 * @since 2022-11-24 11:01:53	
 */	
@Data	
@ApiModel("行程表")	
@QueryModel(	
        name = "0695",	
        remark = "行程表",	
        alias = "hr_trip",	
        searchApi = "/api/hr/trip/query")	
public class HrTripVo  extends BaseModel {	
	
    private String id;	
    /**	
     * 人员编码	
     */	
	@ApiModelProperty("人员编码")	
    @QueryField(name = "人员编码")	
    private String userCode;	
    /**	
     * 行程名称	
     */	
	@ApiModelProperty("行程名称")	
    @QueryField(name = "行程名称")	
    private String tripName;	
    /**	
     * 行程开始时间	
     */	
	@ApiModelProperty("行程开始时间")	
    @QueryField(name = "行程开始时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String tripStartTime;	
    /**	
     * 行程结束时间	
     */	
	@ApiModelProperty("行程结束时间")	
    @QueryField(name = "行程结束时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String tripEndTime;	
    /**	
     * 行程类型	
     */	
	@ApiModelProperty("行程类型")	
    @QueryField(name = "行程类型", type = Type.MultiSelect, dictOption = "HR_TRIP_TYPE")	
    private String type;	
    /**	
     * 行程状态	
     */	
	@ApiModelProperty("行程状态")	
    @QueryField(name = "行程状态")	
    private String status;	
	
    /**	
     * 用户名称	
     */	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称")	
    private String userName;	
	
}	
	
