package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<周报信息>>
 * @company 捷创智能技术有限公司
 * @create 2021-06-29 17:02
 */
@ApiModel("《周报信息》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpWeeklyfinishQuery extends BaseQuery {
    @ApiModelProperty("项目编号")
    private String guid;
    @ApiModelProperty("周报计划guid值")
    private String pGuid;
    @ApiModelProperty("行号")
    private String lid;
    @ApiModelProperty("单据编码")
    private String nid;
    @ApiModelProperty("单据编号")
    private String bid;
    @ApiModelProperty("项目id")
    private String vProjectId;
    @ApiModelProperty("制单人")
    private String bUser;
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    @ApiModelProperty("生效时间")
    private LocalDateTime updateTime;
    @ApiModelProperty("生效时间_开始")
    private LocalDate updateTime_s;
    @ApiModelProperty("生效时间_结束")
    private LocalDate updateTime_e;
    @ApiModelProperty("总结日期")
    private LocalDate dFinishDate;
    @ApiModelProperty("总结日期_开始")
    private LocalDate dFinishDate_s;
    @ApiModelProperty("总结日期_结束")
    private LocalDate dFinishDate_e;
    @ApiModelProperty("项目经审批")
    private Integer checkxmjlysh;
    @ApiModelProperty("是否已审批")
    private Boolean effectStock;
    @ApiModelProperty("是否已选里程碑(1:已选)")
    private Integer checkylcb;
    @ApiModelProperty("是否未选里程碑(1:未选)")
    private Integer checkwlcb;
    @ApiModelProperty("已驳回数据 (1:已驳回)")
    private Integer checkybh;
    @ApiModelProperty("已删除数据(1:已删除)")
    private Integer checkysc;
    @ApiModelProperty("已报销")
    private Integer checkybx;
    @ApiModelProperty("未报销")
    private Integer checkwbx;
    @ApiModelProperty("无需报销")
    private Integer checkbbx;
    @ApiModelProperty("报销状态 (无需报销、未报销、报销审核中、已报销)")
    private String vBxZt;
    @ApiModelProperty("有退回制单数据")
    private Integer checkythzd;
}
