package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("打卡范围配置")	
@QueryModel(	
        name = "0682",	
        remark = "打卡范围配置",	
        alias = "hr_range",	
        searchApi = "/hr/range/query")	
public class HrRangeQueryVo extends BaseModel {	
	
    /**	
     * 试用公司编码	
     */	
	@ApiModelProperty("试用公司编码")	
    @QueryField(name = "试用公司编码")	
    private String company;	
    /**	
     * 范围名称	
     */	
	@ApiModelProperty("范围名称")	
    @QueryField(name = "范围名称")	
    private String rangeName;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, option = {"0", "使用", "1", "禁用"},logic= Logic.Eq)	
    private String status;	
	
	
}	
	
