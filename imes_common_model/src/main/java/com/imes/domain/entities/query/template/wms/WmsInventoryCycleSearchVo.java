package com.imes.domain.entities.query.template.wms;

import io.swagger.annotations.ApiModel;
import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


@Data
@ApiModel("盘点周期管理")
@QueryModel(
        name = "1395",
        remark = "盘点周期管理",
        alias = "wms_inventory_cycle",
        searchApi = "/api/wms/wmsInventoryCycle/queryList",
        auth = Auth.ALL,
        openapi = true,
        authTenant = true,
        resume = "planNo")
public class WmsInventoryCycleSearchVo extends BaseModel {

    private String id;

    @ApiModelProperty("计划编码")
    @QueryField(name = "计划编码")
    @EditField(show = false)
    private String planNo;

    @ApiModelProperty("计划名称")
    @QueryField(name = "计划名称")
    @EditField(required = true)
    private String planName;

    @ApiModelProperty("创建时间")
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)
    @EditField(show = false)
    private String createdOn;

    @ApiModelProperty("创建人")
    @QueryField(name = "创建人", alias = "d.user_name", level = Level.Main)
    @EditField(show = false)
    private String createdBy;

    @ApiModelProperty("操作人")
    @QueryField(name = "操作人", alias = ".(select group_concat(user_name) as userName from pe_user g where find_in_set(g.user_code, wms_inventory_cycle.check_by))", sort = false, level = Level.Main)
    @EditField(required = true)
    private String checkBy;

    @ApiModelProperty("复核人")
    @QueryField(name = "复核人", alias = ".(select group_concat(user_name) as userName from pe_user g where find_in_set(g.user_code, wms_inventory_cycle.review_by))", sort = false, level = Level.Main)
    @EditField(required = true)
    private String reviewBy;

    @ApiModelProperty("计划上次执行时间")
    @QueryField(name = "计划上次执行时间", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(required = true)
    private String lastExecutionTime;

    @ApiModelProperty("备注")
    @QueryField(name = "备注")
    @EditField
    private String remark;

    @ApiModelProperty("盘点计划状态")
    @QueryField(name = "盘点计划状态", type = Type.Select, dictOption = "WMS_INVENTORY_CYCLE_STATUS")
    @EditField(show = false)
    private String status;

    @ApiModelProperty("盘点类型")
    @QueryField(name = "盘点类型", type = Type.Select, dictOption = "INVENTORY_TYPE")
    @EditField(required = true)
    private String checkType;

    @ApiModelProperty("删除状态")
    @QueryField(name = "删除状态")
    @EditField(show = false)
    private String deleteStatus;

    @ApiModelProperty("更新时间")
    @QueryField(name = "更新时间", type = Type.Date)
    @EditField(show = false)
    private String updatedOn;

    @ApiModelProperty("计划开始时间")
    @QueryField(name = "计划开始时间", type = Type.Date)
    @EditField(required = true)
    private String startTime;

    @ApiModelProperty("计划结束时间")
    @QueryField(name = "计划结束时间", type = Type.Date)
    @EditField(required = true)
    private String endTime;

    @ApiModelProperty("计划最后生成时间")
    @QueryField(name = "计划最后生成时间", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(show = false)
    private String lastPlanGenerationTime;

    @ApiModelProperty("有效期（在该有效期内必须完成）")
    @QueryField(name = "有效期（在该有效期内必须完成）")
    @EditField(show = false)
    private String validityTime;

    @ApiModelProperty("下次计划开始时间")
    @QueryField(name = "下次计划开始时间", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(show = false)
    private String nextStartTime;

    @ApiModelProperty("间隔数")
    @QueryField(name = "间隔数")
    @EditField(required = true)
    private String gapNum;

    @ApiModelProperty("间隔类型")
    @QueryField(name = "间隔类型", type = Type.Select, option = {"none", "无", "day", "天", "week", "周", "month", "月", "quarter", "季度", "year", "年"})
    @EditField(required = true)
    private String cycleCategory;

    @ApiModelProperty("审核意见")
    @QueryField(name = "审核意见")
    @EditField(show = false)
    private String auditOpinion;

    @ApiModelProperty("提交时间")
    @QueryField(name = "提交时间", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(show = false)
    private String commitTime;

    @ApiModelProperty("审核时间")
    @QueryField(name = "审核时间", type = Type.Date, format = "yyyy-MM-dd")
    @EditField(show = false)
    private String approvalTime;

    @ApiModelProperty("审核人")
    @QueryField(name = "审核人")
    @EditField(show = false)
    private String approvalUser;

    @ApiModelProperty("审核人编码")
    @QueryField(name = "审核人编码")
    @EditField(show = false)
    private String approvalUserCode;

    private String activitiId;

//    @QueryField(name = "审核标识")	
//    private String auditMark;	

//    @QueryField(name = "任务模式")	
//    private String taskModel;	

    @ApiModelProperty("库区编码")
    @QueryField(name = "库区编码", alias = "b.area_name", level = Level.Main)
    @EditField(required = true)
    private String areaCode;

    @ApiModelProperty("仓库")
    @QueryField(name = "仓库", alias = "a.warehouse_name", level = Level.Main)
    @EditField(required = true)
    private String whCode;

    @ApiModelProperty("物料编码")
    @QueryField(name = "物料编码")
    @EditField(required = true)
    private String materialCode;

    @ApiModelProperty("日历编码")
    @QueryField(name = "日历编码")
    @EditField(required = true)
    private String calendarCode;

    @ApiModelProperty("系统默认审核人")
    @QueryField(name = "系统默认审核人", show = false, flowableApprovalUser = true)
    private List<String> toUserCodes;


}	
	
