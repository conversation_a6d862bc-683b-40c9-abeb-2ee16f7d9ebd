package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.*;
import com.google.common.collect.Maps;
import com.imes.domain.entities.pm.enums.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-01-25 9:36
 */
@ApiModel("项目里程碑实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = -245315698118092164L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("父id")
    //@NotEmpty(message = "父里程碑id不能为空")
    private String pid;
    @ApiModelProperty("里程碑名称")
    //@NotEmpty(message = "项目里程碑名称不能为空")
    private String text;
    @ApiModelProperty("里程碑类型（0：里程碑；1：任务）")
    @JsonIgnore
    private MilepostTypeEnum type;
    @ApiModelProperty("里程碑模板id")
    private String milestoneTemplateId;
    @ApiModelProperty("项目id")
    //@NotEmpty(message = "项目id不能为空")
    private String projectId;
    @ApiModelProperty("人天")
    private BigDecimal manDays;
    @ApiModelProperty("完成率")
    // @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Integer progress;
    @ApiModelProperty("顺序")
    //@NotNull(message = "项目里程碑顺序不能为空")
    private Integer sort;
    @ApiModelProperty("负责人")
    private String principal;
    @ApiModelProperty("完成情况")
    private Boolean finish;
    @ApiModelProperty("状态")
    @JsonIgnore
    private StateEnum state;
    @ApiModelProperty("状态别名")
    private String status;
    @ApiModelProperty("计算方式")
    private CalculationEnum calculation;
    @ApiModelProperty("详细状态")
    private String detailState;
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    @ApiModelProperty("结束日期")
    private LocalDate endDate;
    @ApiModelProperty("实际开始日期")
    private LocalDate actualStartDate;
    @ApiModelProperty("实际完成日期")
    private LocalDate actualFinishDate;
    @ApiModelProperty("是否精细化")
    private Boolean refinement;
    @ApiModelProperty("计划工时")
    @Min(value = 1, message = "计划工时不能小于1")
    private Integer plannedHours;
    @ApiModelProperty("合计工时")
    private BigDecimal totalPlannedHours;
    @ApiModelProperty("实际工时")
    private BigDecimal actualHours;
    @ApiModelProperty("最大工时")
    private Integer maxHours;
    @ApiModelProperty("最小工时")
    private Integer minHours;
    @ApiModelProperty("是否超期预警")
    private Boolean warning;
    @ApiModelProperty("下次预警日期")
    private LocalDate alarmDate;
    @ApiModelProperty("预警间隔")
    private Integer warningInterval;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("审核状态")
    @JsonIgnore
    private ApprovalStatusEnum approvalStatus;
    @ApiModelProperty("存在子里程碑")
    private boolean hasChildren;
    @ApiModelProperty("子里程碑")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ProjectMilepostDto> children;
    @ApiModelProperty("差旅费")
    private BigDecimal travelCost;
    @ApiModelProperty("参与人员")
    List<ProjectMilepostEmployeeDto> participants;
    @ApiModelProperty("参与人员工号数组")
    List<String> participate;
    @ApiModelProperty("超期天数")
    private Long overdueDays;

    /**
     * 扩展字段处理
     *
     * @param name
     * @param value
     */
    @JsonAnySetter
    public void setExpandField(String name, Object value) {
        // 审批状态
        if ("approvalStatus".equals(name)) {
            if (value instanceof Integer) {
                this.approvalStatus = ApprovalStatusEnum.match((Integer) value);
            }
        }
        // 状态
        if ("state".equals(name)) {
            if (value instanceof Integer) {
                this.state = StateEnum.match((Integer) value);
            }
        }
        // 计算方式
        if ("calculation".equals(name)) {
            if (value instanceof Integer) {
                this.calculation = CalculationEnum.match((Integer) value);
            }
        }
        // 里程碑类型
        if ("type".equals(name)) {
            if (value instanceof Integer) {
                this.type = MilepostTypeEnum.match((Integer) value);
            }
        }
    }

    /**
     * 扩展字段处理
     *
     * @return
     */
    @JsonAnyGetter
    public Map<String, Object> getExpandField() {
        LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
        // 审批状态
        if (Objects.nonNull(this.approvalStatus)) {
            map.put("approvalStatus", this.approvalStatus.getApprovalStatus());
            map.put("approvalName", this.approvalStatus.getApprovalName());
        }
        // 状态
        if (Objects.nonNull(this.state)) {
            map.put("state", this.state.getState());
            map.put("stateName", this.state.getStateName());
        }
        // 计算方式
        if (Objects.nonNull(this.calculation)) {
            map.put("calculation", this.calculation.getCalculation());
            map.put("calculationName", this.calculation.getCalculationName());
        }
        // 里程碑类型
        if (Objects.nonNull(this.type)) {
            map.put("type", this.type.getType());
            map.put("typeStr", this.type.getTypeStr());
        }
        return map;
    }
}
