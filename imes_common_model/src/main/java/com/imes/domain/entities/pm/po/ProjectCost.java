package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (ProjectCost)实体类
 *
 * <AUTHOR>
 * @since 2021-06-18 16:22:03
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_cost",resultMap = "BaseResultMap")
public class ProjectCost implements Serializable {
    private static final long serialVersionUID = -80625222938024968L;
    /**
    * id
    */
    private String id;
    /**
    * 项目id
    */
    private String projectId;
    /**
     * 税收分类编号
     */
    private String code;
    /**
    * 内容
    */
    @TableField(condition = SqlCondition.LIKE)
    private String content;
    /**
    * 描述
    */
    private String description;
    /**
    * 预估成本（不含税）
    */
    private BigDecimal estimatedMoney;
    /**
     * 预估成本（含税）
     */
    private BigDecimal estimatedMoneyTax;
    /**
    * 实际成本（不含税）
    */
    private BigDecimal actualMoney;

    /**
     * 实际成本（含税）
     */
    private BigDecimal actualMoneyTax;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}