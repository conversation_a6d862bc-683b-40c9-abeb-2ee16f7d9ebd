package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<差旅车补信息>>
 * @company 捷创智能技术有限公司
 * @create 2021-11-01 13:56
 */
@ApiModel("《差旅车补信息》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpWeeklyfinishaccountQuery extends BaseQuery {
    @ApiModelProperty("单据编码")
    private String nid;
    @ApiModelProperty("单据编号")
    private String bid;
    @ApiModelProperty("周报总结单号")
    private String pBid;
    @ApiModelProperty("制单人工号")
    private String bUser;
    @ApiModelProperty("汇报人工号")
    private String gUser;
    @ApiModelProperty("客户名称")
    private String gCName;
    @ApiModelProperty("总结日期")
    private LocalDate dFinishDate;
    @ApiModelProperty("项目名称")
    private String vProjectName;
    @ApiModelProperty("区域")
    private String vArea;
    @ApiModelProperty("计划日期")
    private LocalDate dPDate;
    @ApiModelProperty("所属公司")
    private String cidName;
    @ApiModelProperty("制单人")
    private String vUserName;
    @ApiModelProperty("仅未生效")
    private Boolean checkwsx;
    @ApiModelProperty("仅已生效")
    private Boolean checkysx;
    @ApiModelProperty("仅未同意")
    private Boolean checkwty;
    @ApiModelProperty("仅已同意")
    private Boolean checkyty;
}
