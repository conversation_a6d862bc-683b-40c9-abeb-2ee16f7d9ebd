package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.baomidou.mybatisplus.annotation.TableField;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPickingOrderItem implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 领料单主键	
     */	
	@ApiModelProperty("领料单主键")	
    private String poId;	
	
    /**	
     * 领料单单号	
     */	
	@ApiModelProperty("领料单单号")	
    private String poCode;	
	
    /**	
     * 领料单明细号	
     */	
	@ApiModelProperty("领料单明细号")	
    private String poItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 品牌	
     */	
	@ApiModelProperty("品牌")	
    private String brand;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 系列	
     */	
	@ApiModelProperty("系列")	
    private String series;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 领用数量	
     */	
	@ApiModelProperty("领用数量")	
    private BigDecimal qty;	
	
    /**	
     * 已领数量	
     */	
	@ApiModelProperty("已领数量")	
    private BigDecimal pickedQty;	
	
    /**	
     * 库存单价	
     */	
	@ApiModelProperty("库存单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 领用金额	
     */	
	@ApiModelProperty("领用金额")	
    private BigDecimal amount;	
	
    /**	
     * 库存数量	
     */	
	@ApiModelProperty("库存数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 库存类型编码	
     */	
	@ApiModelProperty("库存类型编码")	
    private String stockTypeCode;	
	
    /**	
     * 库存类型	
     */	
	@ApiModelProperty("库存类型")	
    private String stockTypeName;	
	
    /**	
     * 库存说明	
     */	
	@ApiModelProperty("库存说明")	
    private String stockExplain;	
	
    /**	
     * 销售部门编码	
     */	
	@ApiModelProperty("销售部门编码")	
    private String salesDepartCode;	
	
    /**	
     * 销售部门名称	
     */	
	@ApiModelProperty("销售部门名称")	
    private String salesDepartName;	
	
    /**	
     * 销售员工编码	
     */	
	@ApiModelProperty("销售员工编码")	
    private String salesCode;	
	
    /**	
     * 销售员工名称	
     */	
	@ApiModelProperty("销售员工名称")	
    private String salesName;	
	
    /**	
     * 库存公司编码	
     */	
	@ApiModelProperty("库存公司编码")	
    private String stockCompanyCode;	
	
    /**	
     * 库存公司名称	
     */	
	@ApiModelProperty("库存公司名称")	
    private String stockCompanyName;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String inboundCode;	
	
    /**	
     * 入库日期	
     */	
	@ApiModelProperty("入库日期")	
    private Date inboundDate;	
	
    /**	
     * 采购合同号	
     */	
	@ApiModelProperty("采购合同号")	
    private String purchaseContractCode;	
	
    /**	
     * 供应商编号	
     */	
	@ApiModelProperty("供应商编号")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 第三方单据号	
     */	
	@ApiModelProperty("第三方单据号")	
    private String thirdItemCode;	
	
    private List<WmsPickTask> taskList;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 领用人工号	
     */	
	@ApiModelProperty("领用人工号")	
    private String pickUserCode;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 辅助属性字典编码拼接	
     */	
	@ApiModelProperty("辅助属性字典编码拼接")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 关联明细单号	
     */	
	@ApiModelProperty("关联明细单号")	
    private String receiptItemCode;

    @TableField(exist = false)
    @ApiModelProperty("基本单位名称")
    private String unitName;
	
    @TableField(exist = false)	
    private BigDecimal primaryAvailableQty;	
    private static final long serialVersionUID = 1L;	
}	
