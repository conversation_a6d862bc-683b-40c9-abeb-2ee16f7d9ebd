package com.imes.domain.entities.query.template.po;	
	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
	
@Data	
@QueryModel(	
        name = "1363",	
        remark = "采购合同",	
        alias = {"po_contract_main_standard", "po_contract_detail_standard"},	
        link = {"1195"},	
        auth = Auth.ALL,	
        showMode = true,
        openapi = true,
        resume = "soNo",
        authTenant = true,
        pushApi = PushApi.PoContractMainStandardSearchVo,	
        searchApi = "/api/po/poContractMainStandard/queryList")	
@ApiModel(value = "采购合同高级查询")	
public class PoContractMainStandardSearchVo extends BaseModel {	
	
    @QueryField(name = "采购合同编号")	
    @ApiModelProperty("采购合同编号")	
    private String soNo;	
	
    @QueryField(name = "合同名称")	
    @ApiModelProperty("合同名称")
    @EditField(required = true)
    private String soName;	
	
    @QueryField(name = "币种", type = Type.Select, sqlOption = "select ccy_no as value,ccy_name as label from sys_currency")
    @ApiModelProperty("币种")
    @EditField(required = true)
    private String currency;	
	
    @QueryField(name = "采购部门编码")	
    @ApiModelProperty("采购部门编码")
    @EditField(readonly = true)
    private String demandDepCode;	
	
    @QueryField(name = "采购部门名称",alias = ".(co_department.depart_name)",level = Level.Main)	
    @ApiModelProperty("采购部门名称")
    @EditField(required = true)
    private String demandDepName;	
	
    @QueryField(name = "供应商编码")	
    @ApiModelProperty("供应商编码")
    @EditField(readonly = true)
    private String supplierCode;	
	
    @QueryField(name = "供应商", logic = Logic.Like,alias = ".(sys_supplier.supplier_name)",level = Level.Main)	
    @ApiModelProperty("供应商")
    @EditField(required = true)
    private String supplierName;	
	
    @QueryField(name = "联系人")	
    @ApiModelProperty("联系人")	
    private String customerLinkPersonName;	
	
    @QueryField(name = "联系方式")	
    @ApiModelProperty("联系方式")	
    private String customerLinkPhone;	
	
    @QueryField(name = "采购员编码", logic = Logic.In)	
    @ApiModelProperty("采购员编码")
    @EditField(readonly = true)
    private String salesPersonCode;	
	
    @QueryField(name = "采购员",alias = ".(pe_user.user_name)",level = Level.Main)	
    @ApiModelProperty("采购员")
    @EditField(required = true)
    private String salesPersonName;	
	
    @QueryField(name = "有效起始时间", type = Type.Date, format = "yyyy-MM-dd")	
    @ApiModelProperty("有效起始时间")
    @EditField(required = true)
    private String beginDate;	
	
    @QueryField(name = "有效截止时间", type = Type.Date, format = "yyyy-MM-dd")	
    @ApiModelProperty("有效截止时间")
    @EditField(required = true)
    private String endDate;	
	
    @QueryField(name = "合同有效期(天)")	
    @ApiModelProperty("合同有效期(天)")
    @EditField(required = true)
    private String periodTime;	
	
    @QueryField(name = "主单单据状态", type = Type.MultiSelect, dictOption = "SALE_BILL_CODE", value = {"10", "20", "30"})	
    @ApiModelProperty("主单单据状态")
    @EditField(readonly = true)
    private String status;	
	
    @QueryField(name = "主单业务状态", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE", value = "10")	
    @ApiModelProperty("主单业务状态")
    @EditField(readonly = true)
    private String businessStatus;	
	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)	
    @ApiModelProperty("创建时间")
    @EditField(readonly = true)
    private String createOn;	
	
    @QueryField(name = "主单备注")	
    @ApiModelProperty("主单备注")	
    private String remarks;	
	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", alias = "po_contract_detail_standard.id", show = false)	
    private String detailId;	
	
    private String mainId;	
	
    @QueryField(name = "行号", alias = "po_contract_detail_standard")	
    @ApiModelProperty("行号")	
    private String sdNo;	
	
    @QueryField(name = "物料编码", alias = "po_contract_detail_standard")	
    @ApiModelProperty("物料编码")
    @EditField(required = true)
    private String materialCode;	
	
    @QueryField(name = "物料名称", alias = ".(ppc_material.material_name)")	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    @QueryField(name = "规格", alias = ".(ppc_material.specification)")	
    @ApiModelProperty("规格")	
    private String specification;	
	
    @QueryField(name = "物料型号", alias = ".(ppc_material.material_marker)", show = false)	
    @ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    @QueryField(name = "数量", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("数量")
    @EditField(required = true)
    private String qty;	
	
    @QueryField(name = "关联单位数量", type = Type.Number, alias = ".(ifnull(po_contract_detail_standard.relation_po_qty,0))")	
    @ApiModelProperty("关联单位数量")	
    private String relationPoQty;	
	
    //@QueryField(name = "可下推数量", type = Type.Number, alias = ".(if(po_contract_detail_standard.need_qty = '1',(po_contract_detail_standard.qty - po_contract_detail_standard.relation_po_qty),po_contract_detail_standard.qty))")	
    private String residueQty;	
	
    @QueryField(name = "关联总金额", type = Type.Number, alias = ".(ifnull(po_contract_detail_standard.relation_po_price,0))")	
    @ApiModelProperty("关联总金额")	
    private String relationPoPrice;	
	
    @QueryField(name = "累计数量", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("累计数量")	
    private String sendedQty;	
	
    @QueryField(name = "累计金额", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("累计金额")	
    private String sendedPrice;	
	
    @QueryField(name = "单位", alias = "po_contract_detail_standard",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    @ApiModelProperty("单位")
    @EditField(required = true)
    private String unit;	
	
    @QueryField(name = "单价", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("单价")
    @EditField(required = true)
    private String singlePrice;	
	
    @QueryField(name = "净价", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("净价")	
    private String netPrice;	
	
    @QueryField(name = "折扣方式", alias = "po_contract_detail_standard", dictOption = "SALE_DISCOUNT_TYPE", type = Type.MultiSelect)	
    @ApiModelProperty("折扣方式")
    @EditField(required = true)
    private String discountType;	
	
    @QueryField(name = "折扣额", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("折扣额")	
    private String discountPrice;	
	
    @QueryField(name = "不含税单价", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("不含税单价")	
    private String unIncludePrice;	
	
    @QueryField(name = "含税单价", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("含税单价")	
    private String includePrice;	
	
    @QueryField(name = "税额", type = Type.Number, format = "0.00", alias = "po_contract_detail_standard")	
    @ApiModelProperty("税额")	
    private String taxRatePrice;	
	
    @QueryField(name = "未税金额", type = Type.Number, format = "0.00", alias = "po_contract_detail_standard")	
    @ApiModelProperty("未税金额")	
    private String unTaxRatePrice;	
	
    @QueryField(name = "价税合计", type = Type.Number, format = "0.00", alias = "po_contract_detail_standard")	
    @ApiModelProperty("价税合计")	
    private String allPrice;	
	
    private String taxRate;	
	
    @QueryField(name = "税率(%)", alias = "po_contract_detail_standard", dictOption = "SALE_TAX_CODE", type = Type.MultiSelect)	
    @ApiModelProperty("税率编码")	
    private String taxCode;	
	
    @QueryField(name = "折扣率(%)", alias = ".(ifnull(TRUNCATE(po_contract_detail_standard.discount_rate, 2), 0) * 100)", type = Type.Number)	
    @ApiModelProperty("折扣率")	
    private String discountRate;	
	
    @QueryField(name = "是否含税", alias = "po_contract_detail_standard", dictOption = "PPC_INCLUDE_TAX", type = Type.MultiSelect)	
    @ApiModelProperty("是否含税")
    private String includeTax;	
	
    @QueryField(name = "基础单位", alias = "po_contract_detail_standard",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    @ApiModelProperty("基础单位")	
    private String baseUnit;	
	
    @QueryField(name = "基础单位数量", type = Type.Number, alias = "po_contract_detail_standard")	
    @ApiModelProperty("基础单位数量")	
    private String baseUnitQty;	
	
    @QueryField(name = "辅助属性", alias = "po_contract_detail_standard")	
    @ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    @QueryField(name = "数量控制", type = Type.MultiSelect, alias = "po_contract_detail_standard", option = {"0", "否", "1", "是"})	
    @ApiModelProperty("数量控制")	
    private String needQty;	
	
    @QueryField(name = "单价控制", type = Type.MultiSelect, alias = "po_contract_detail_standard", option = {"0", "否", "1", "是"})	
    @ApiModelProperty("单价控制")	
    private String needSinglePrice;	
	
    @QueryField(name = "金额控制", type = Type.MultiSelect, alias = "po_contract_detail_standard", option = {"0", "否", "1", "是"})	
    @ApiModelProperty("金额控制")	
    private String needAllPrice;	
	
    @QueryField(name = "行状态", type = Type.MultiSelect, alias = "po_contract_detail_standard.business_status", dictOption = "SALE_BUSINESS_CODE")	
    @ApiModelProperty("行状态")	
    private String detailBusinessStatus;	
	
    @QueryField(name = "子单备注", alias = "po_contract_detail_standard.remarks")	
    @ApiModelProperty("子单备注")	
    private String detailRemarks;	
	
    //用于是否反选 =1 反选页签使用	
    private String fxType;	
	
    private String souceNo;	
	
    private String souceSdNo;	
	
    private String modelNumber;	
	
    private String qtyPrecision;	
    //是否需要合并生成一个采购订单	
    private String needMerge;

    private String sId;

    private String rowId;
}	
	
