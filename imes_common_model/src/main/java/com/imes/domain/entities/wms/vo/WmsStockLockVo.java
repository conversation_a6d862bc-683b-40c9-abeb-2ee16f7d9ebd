package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsStockLock;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockLockVo extends WmsStockLock {	
	
    /**	
     * 库存可用数量	
     */	
	@ApiModelProperty("库存可用数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 库存基本单位	
     */	
	@ApiModelProperty("库存基本单位")	
    private String stockUnit;	
	
    /**	
     * 库存可用包装数量	
     */	
	@ApiModelProperty("库存可用包装数量")	
    private BigDecimal packAvailableQty;	
	
    /**	
     * 库存包装单位	
     */	
	@ApiModelProperty("库存包装单位")	
    private String stockPackCodeUnit;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String likeBatch;	
	
	
}	
