package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalOrderItem implements Serializable {	
    /**	
     * 	
     */
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    /**	
     * 到货单主键	
     */
    @ApiModelProperty(value = "到货单主键")	
    private String aoId;	
	
    /**	
     * 到货单号	
     */
    @ApiModelProperty(value = "到货单号")	
    private String aoCode;	
	
    /**	
     * 到货单明细号	
     */
    @ApiModelProperty(value = "到货单明细号")	
    private String aoItemCode;	
	
    /**	
     * 物料编码	
     */	

    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	

    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    /**	
     * 型号	
     */	

    @ApiModelProperty(value = "型号")	
    private String materialMarker;	
	
    /**	
     * 物料条码	
     */	

    @ApiModelProperty(value = "物料条码")	
    private String barCode;	
	
    /**	
     * 主单位（基本单位）	
     */	

    @ApiModelProperty(value = "主单位")	
    private String unit;	
	
    /**	
     * 采购含税单价	
     */	

    @ApiModelProperty(value = "采购含税单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 采购未税单价	
     */	

    @ApiModelProperty(value = "采购未税单价")	
    private BigDecimal unIncludePrice;	
	
    /**	
     * 采购数量（包装单位）	
     */	

    @ApiModelProperty(value = "采购数量")	
    private BigDecimal purchaseQty;	
	
    /**	
     * 到货基本单位数量	
     */	

    @ApiModelProperty(value = "到货基本单位数量")	
    private BigDecimal arrivalQty;	
	
    /**	
     * 已到货数量（包装单位）	
     */	

    @ApiModelProperty(value = "已到货数量")	
    private BigDecimal arrivaledQty;	
	
    /**	
     * 接收数量（包装单位）	
     */
    @ApiModelProperty(value = "接收数量")	
    private BigDecimal receivedQty;	
	
    /**	
     * 拒收数量（包装单位）	
     */
    @ApiModelProperty(value = "拒收数量")	
    private BigDecimal rejectionQty;	
	
    /**	
     * 批次号	
     */	

    @ApiModelProperty(value = "批次号")	
    private String batch;	
	
    /**	
     * 供应商批次号	
     */	

    @ApiModelProperty(value = "供应商批次号")	
    private String batchSupplier;	
	
    /**	
     * 生产日期	
     */	

    @ApiModelProperty(value = "生产日期")	
    private Date productionDate;	
	
    /**	
     * 失效日期	
     */
    @ApiModelProperty(value = "失效日期")	
    private Date failureDate;	
	
    /**	
     * 备注	
     */	

    @ApiModelProperty(value = "备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	

    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	

    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	

    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	

    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")
    private Integer deleteStatus;	
	
    /**	
     * 第三方系统明细单唯一标识	
     */	

    @ApiModelProperty("第三方系统明细单唯一标识")	
    private String thirdItemCode;	
	
    /**	
     * 公司编码	
     */	

    @ApiModelProperty("公司编码")	
    private String companyCode;	
	
    /**	
     * 公司名称	
     */	

    @ApiModelProperty("公司名称")	
    private String companyName;	
	
    /**	
     * 客户编码	
     */	

    @ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	

    @ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 销售合同号	
     */	

    @ApiModelProperty("销售合同号")	
    private String salesContractCode;	
	
    /**	
     * RA系统号	
     */	

    @ApiModelProperty("RA系统号")	
    private String raSysCode;	
	
    /**	
     * 关联单号	
     */	

    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 单据来源	
     */	

    @ApiModelProperty("单据来源")	
    private String source;	
	
    /**	
     * 库存说明	
     */	

    @ApiModelProperty("库存说明")	
    private String stockDesc;	
	
    /**	
     * 物料品牌	
     */
    @ApiModelProperty("物料品牌")	
    private String brand;	
	
    /**	
     * 打印物料条形码	
     */
    @ApiModelProperty("打印物料条形码")	
    private String barCodePrinting;	

    @ApiModelProperty("项目名称")	
    private String projectName;	

    @ApiModelProperty("优先级")	
    private Integer priority;	

    @ApiModelProperty("关联明细单单号")	
    private String receiptItemCode;	

    @ApiModelProperty("物料规格")	
    private String specification;	

    @ApiModelProperty("供应商编码")	
    private String supplierCode;	

    @ApiModelProperty("供应商名称")	
    private String supplierName;	

    @ApiModelProperty("制造商编码")	
    private String manufacturerCode;	

    @ApiModelProperty("制造商名称")	
    private String manufacturerName;	

    @ApiModelProperty("物流单号")	
    private String logisticCode;	

    @ApiModelProperty("物流类型（1：汽运；2：物流；3：快递）")	
    private String logisticType;	

    @ApiModelProperty("物料总金额")	
    private BigDecimal amount;	

    @ApiModelProperty("装箱单号")	
    private String packingNo;	

    @ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 入库数量（包装单位）	
     */
    @ApiModelProperty("入库数量")	
    private BigDecimal inboundQty;	

    @ApiModelProperty("入库状态（10：未入库；20：部分入库；30：全部入库）")	
    private String inboundStatus;	

    @ApiModelProperty("核检状态（10：未核检；20：部分核检；30：全部核检）")	
    private String qualityStatus;	

    @ApiModelProperty("核检结果（10：合格；20：让步接收；30：不合格）")	
    private String qualityResult;	
	
    /**	
     * 合格数量（包装单位）	
     */
    @ApiModelProperty("合格数量")	
    private BigDecimal qualifiedQty;	
	
    /**	
     * 不合格数量（包装单位）	
     */
    @ApiModelProperty("不合格数量")	
    private BigDecimal unQualifiedQty;

    @ApiModelProperty("上架状态（10：未上架；20：部分上架；30：全部上架）")	
    private String putStatus;	
	
    /**	
     * 上架数量（包装单位）	
     */	

    @ApiModelProperty("上架数量")	
    private BigDecimal putQty;	
	

    @ApiModelProperty("默认存储仓库编码")	
    private String defaultWhCode;	
	

    @ApiModelProperty("默认存储仓库名称")	
    private String defaultWhName;	
	

    @ApiModelProperty("默认存储库位编码")	
    private String defaultBinCode;	
	
    /**	
     * 采购单位	
     */
    @ApiModelProperty("采购单位")	
    private String poUnit;	
	
    /**	
     * 采购单位实际到货数量	
     */
    @ApiModelProperty("采购单位实际到货数量")	
    private BigDecimal poArrivalQty;	
    /**	
     * 包装件单位	
     */
    @ApiModelProperty("包装件单位")	
    private String packCodeUnit;	
	
    /**	
     * 包装单位实际到货数量	
     */
    @ApiModelProperty("包装单位实际到货数量")	
    private BigDecimal packArrivalQty;	

    @ApiModelProperty("退货包装单位")	
    private String returnPackUnit;	

    @ApiModelProperty("退货包装数量")	
    private BigDecimal returnPackQty;	
	

    @ApiModelProperty("辅助属性")	
    private String skuCode;	
	

    @ApiModelProperty("最小装箱数量")	
    private BigDecimal minNumberOfBoxes;	
	

    @ApiModelProperty("赠品")	
    private String giftType;	
	

    @ApiModelProperty("是否有箱码")	
    private String hasBoxNo;	
	
    @ApiModelProperty("自定义字段")	

    private String custom;	
	

    @ApiModelProperty("质检单号")	
    private String inspectionCode;	
	

    @ApiModelProperty("采购订单明细主键")	
    private String poDetailId;	
	

    @ApiModelProperty("采购订单单号")	
    private String poCode;	


    @ApiModelProperty("采购订单行号")	
    private String poItemCode;	
	
    /**	
     * 订单号	
     */
    @ApiModelProperty("订单号")	
    private String applyOrderCode;	
	
    private static final long serialVersionUID = 1L;	
}	
