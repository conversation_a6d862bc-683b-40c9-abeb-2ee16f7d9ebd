package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.MaterialTask;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class MaterialTaskForMobileVo extends MaterialTask {	
    /**	
     * 是否合格	
     */	
	@ApiModelProperty("是否合格")	
    private String isQualified;	
	
    /**	
     * 检验结果的id	
     */	
	@ApiModelProperty("检验结果的id")	
    private String inspectionId;	
	
}	
