package com.imes.domain.entities.pm.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<考评项目数据>>
 * @company 捷创智能技术有限公司
 * @create 2022-02-06 21:47
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationProjectData {
    private String projectId;
    private String projectName;
    private String managerCode;
    private String directorCode;
    private String employeeCode;
    private BigDecimal workHour;
}
