package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.scs.po.ScsInvoice;
import com.imes.domain.entities.scs.po.ScsInvoiceDetail;
import com.imes.domain.entities.scs.po.ScsInvoiceReVerify;
import lombok.Data;

import java.util.List;

@Data
public class ScsInoviceToPoVo extends ScsInvoice {
    //发票和对账单关联明细
    private List<ScsInvoiceReVerify> verifyList;
    //客户编码+name集合
    private String customerCodeName;
    //供应商平台机构编码
    private String orgPlatfromCode;

    private String base64String;

    private String fileName;

    //开票明细
    private List<ScsInvoiceDetail> detailList;

    private String delType;

    private List<String> idList;
}
