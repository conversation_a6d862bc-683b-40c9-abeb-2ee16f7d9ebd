package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageInBill;	
import com.imes.domain.entities.wms.WmsStorageInBillItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInBillAndItemAndTaskVo extends WmsStorageInBill {	

    private List<WmsStorageInBillItemVo> itemTasks;	

    int flag;	
}	
