package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
	
/**	
 * 仓库组装拆卸明细单(WmsAssembleItemOrder)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-08-07 15:57:34	
 */	
@Data	
public class WmsAssembleItemOrder implements Serializable {	
    private static final long serialVersionUID = -57851182375997982L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "装卸单号")	
    private String assembleNo;	
	
    @ApiModelProperty(value = "装卸明细单号")	
    private String assembleItemNo;	
	
    @ApiModelProperty(value = "存储类型（1:入库；2:出库）")	
    private String storageType;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "批次")	
    private String batch;	
	
    @ApiModelProperty(value = "辅助属性字典编码拼接")	
    private String skuCode;	
	
    @ApiModelProperty(value = "箱码")	
    private String boxNo;	
	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "存储区名称")	
    private String areaName;	
	
    @ApiModelProperty(value = "仓位")	
    private String binCode;	
	
    @ApiModelProperty(value = "基本数量")	
    private BigDecimal qty;	
	
    @ApiModelProperty(value = "基本单位")	
    private String unit;	
	
    @ApiModelProperty(value = "包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "包装数量")	
    private BigDecimal packQty;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
}	
	
