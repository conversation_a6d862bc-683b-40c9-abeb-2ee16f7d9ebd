package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.io.Serializable;

@ApiModel(value = "点检模板表")
@Data
@Entity
@Table(name = "dev_checking_temp")
public class CheckingTemp implements Serializable {

    private static final long serialVersionUID = -2121222600776762599L;
    @Id
    private String id;

    @ApiModelProperty(value = "模板编码")
    @NotBlank(message = "模板编码不能为空！")
    @Size(max = 40, message = "模板编码长度不能超过{max}个字符")
    private String tempNo;

    @ApiModelProperty(value = "模板名称")
    @NotBlank(message = "模板名称不能为空！")
    @Size(max = 50, message = "模板名称长度不能超过{max}个字符")
    private String tempName;

    @ApiModelProperty(value = "创建时间")
    private java.util.Date createdOn;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private java.util.Date updatedOn;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(hidden = true)
    private java.util.Date lastExecutionTime;


    @ApiModelProperty(hidden = true)
    private String executor;

    @ApiModelProperty(hidden = true)
    private Integer enabled;

    @ApiModelProperty(hidden = true)
    private String frequently;

    @ApiModelProperty(hidden = true)
    private Integer deleteStatus;
    @ApiModelProperty(hidden = true)
    private String vdf1;
    @ApiModelProperty(hidden = true)
    private String vdf2;
    @ApiModelProperty(hidden = true)
    private String vdf3;

}
