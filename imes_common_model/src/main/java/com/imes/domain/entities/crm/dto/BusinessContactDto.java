package com.imes.domain.entities.crm.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (CrmBusinessCustomer)实体类
 *
 * <AUTHOR>
 * @since 2022-02-28 15:57:56
 */
@Data
public class BusinessContactDto implements Serializable {
    private static final long serialVersionUID = 685196028063915500L;
    /**
    * id
    */
    private String id;
    /**
    * 商机id
    */
    @NotBlank(message = "商机不能为空")
    private String businessId;
    /**
    * 联系人id
    */
    @NotBlank(message = "联系人不能为空")
    private String contactId;
    /**
     * 客户名称
     */
    private String customerName;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人手机
     */
    private String phone;
    /**
     * 联系人电话
     */
    private String telephone;
    /**
     * 联系人邮件
     */
    private String email;
    /**
    * 职位
    */
    private String position;
    /**
    * 备注
    */
    private String remark;
    /**
    * 加入时间
    */
    private LocalDateTime joinTime;
    /**
    * 创建人
    */
    private String createdBy;

}