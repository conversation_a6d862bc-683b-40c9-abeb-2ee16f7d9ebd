package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
/**	
 * 生产报工投料	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2022-09-21 10:21	
 */	
@Data	
public class WmsWorkReleaseVo {	
	
    /**	
     * 物料编码--必填	
     */	
	@ApiModelProperty("物料编码--必填")	
    private String materialCode;	
	
    /**	
     * 投料数量--必填	
     */	
	@ApiModelProperty("投料数量--必填")	
    private BigDecimal qty;	
	
    /**	
     * 投料单位--必填	
     */	
	@ApiModelProperty("投料单位--必填")	
    private String unit;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batchNo;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
}	
