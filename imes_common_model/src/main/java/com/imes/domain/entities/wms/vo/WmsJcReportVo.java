package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportVo {	
	
	
    /**	
     * 当前库存金额	
     */	
    @ApiModelProperty("当前库存金额")	
    private BigDecimal totalStockAmount;	
	
    /**	
     * 当前库存数量	
     */	
    @ApiModelProperty("当前库存数量")	
    private BigDecimal totalStockQty;	
	
    /**	
     * 当前待上架数量	
     */	
    @ApiModelProperty("当前待上架数量")	
    private BigDecimal needPutQty;	
	
    /**	
     * 当前待下架数量	
     */	
    @ApiModelProperty("当前待下架数量")	
    private BigDecimal needPickQty;	
	
    /**	
     * 当前入库金额	
     */	
    @ApiModelProperty("当前入库金额")	
    private BigDecimal totalInboundAmount;	
	
    /**	
     * 当前出库金额	
     */	
    @ApiModelProperty("当前出库金额")	
    private BigDecimal totalOutboundAmount;	
	
    /**	
     * 今日入库金额	
     */	
    @ApiModelProperty("今日入库金额")	
    private BigDecimal todayInboundAmount;	
	
    /**	
     * 今日入库数量	
     */	
    @ApiModelProperty("今日入库数量")	
    private BigDecimal todayInboundQty;	
	
	
}	
