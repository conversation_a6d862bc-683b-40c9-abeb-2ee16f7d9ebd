package com.imes.domain.entities.pm.dto;


import com.imes.domain.entities.pm.po.ProjectPieSeries;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-09 13:23
 */
@ApiModel("项目饼图实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPieChartDto implements Serializable {
    private static final long serialVersionUID = -8443693384608402810L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "项目id不能为空", groups = UpdateGroup.class)
    @Null(message = "项目id必须为空", groups = AddGroup.class)
    private String id;

    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
    @NotNull(message = "项目类型不能为空", groups = AddGroup.class)
    private String type;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @Null(message = "创建时间必须为空")
    private LocalDateTime createTime;

    /**
     * 创建时间
     */
    @ApiModelProperty("修改时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @Null(message = "创建时间必须为空")
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    //@NotNull(message = "版本号不能为空", groups = ProjectPieChartDto.AddGroup.class)
    private Integer version;

    /**
     * 版本号
     */
    @ApiModelProperty("系列")
    //@NotNull(message = "系列不能为空", groups = ProjectPieChartDto.AddGroup.class)
    private String series;

    @ApiModelProperty("系列实体")
    private List<ProjectPieSeries> projectPieSeriese;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
     * 创建人名
     */
    @ApiModelProperty("创建人名")
    private String creator;
    /**
     * 修改人名
     */
    @ApiModelProperty("修改人名")
    private String updatedName;

}
