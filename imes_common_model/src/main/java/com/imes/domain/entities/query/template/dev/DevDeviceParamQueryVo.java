package com.imes.domain.entities.query.template.dev;		
		
import com.imes.domain.entities.query.model.base.BaseModel;		
import com.imes.domain.entities.query.model.base.QueryField;		
import com.imes.domain.entities.query.model.base.QueryModel;		
import com.imes.domain.entities.query.model.base.Type;		
import io.swagger.annotations.ApiModel;		
import io.swagger.annotations.ApiModelProperty;		
import lombok.Data;		
		
@ApiModel(value = "设备参数高级查询模型")		
@Data		
@QueryModel(		
        name = "0475-select",		
        remark = "设备参数高级查询",		
        searchApi = "/dev/devDevice/queryByDeviceParamQueryVo")		
public class DevDeviceParamQueryVo extends BaseModel {		
		
		
	@ApiModelProperty("id")		
    @QueryField(name = "id", show = false, alias = "param")		
    private String id;		
		
    @ApiModelProperty(value = "查询点位")		
    @QueryField(name = "查询点位", alias = "param")		
    private String searchPoint;		
		
    @ApiModelProperty(value = "参数名称")		
    @QueryField(name = "参数名称", alias = "param")		
    private String paramName;		
		
    @ApiModelProperty(value = "设备编码")		
    @QueryField(name = "设备编码", alias = "dev")		
    private String devCode;		
		
    @ApiModelProperty(value = "设备名称")		
    @QueryField(name = "设备名称", alias = "dev")		
    private String devName;		
		
    @ApiModelProperty(value = "设备区域")		
    @QueryField(name = "设备区域", alias = "area")		
    private String areaName;		
		
    @ApiModelProperty(value = "安装地点")		
    @QueryField(name = "安装地点", alias = "dev")		
    private String installAddress;		
		
    @ApiModelProperty(value = "设备使用状态：  1-在用；2-待用；3-停用；4-封存；5-委外；6-报废")		
    @QueryField(name = "状态", type = Type.Select, option = {"1", "在用", "2", "待用",		
            "3", "停用", "4", "封存", "5", "委外", "6", "报废"}, alias = "dev")		
    private String devUseStatus;		
		
}		
