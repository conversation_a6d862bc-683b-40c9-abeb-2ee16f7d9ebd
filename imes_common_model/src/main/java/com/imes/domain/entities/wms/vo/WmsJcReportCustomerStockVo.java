package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportCustomerStockVo {	
	
	
    /**	
     * 客户编码	
     */	
    @ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
    @ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 在库库存	
     */	
    @ApiModelProperty("在库库存")	
    private BigDecimal onHandQty;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal onHandPrice;	
	
}	
