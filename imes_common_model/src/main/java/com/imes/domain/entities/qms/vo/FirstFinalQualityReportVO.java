package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class FirstFinalQualityReportVO implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
//    /**	
//     * 生产单号	
//     */	
//    private String ppNo;	
//	
//    /**	
//     * 排产单号	
//     */	
//    private String pcNo;	
	
    /**	
     * 物料编号	
     */	
	@ApiModelProperty("物料编号")	
    private String materialCode;	
	
    /**	
     * 检验单号	
     */	
	@ApiModelProperty("检验单号")	
    private String taskInspectionCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 工序编号	
     */	
	@ApiModelProperty("工序编号")	
    private String processCode;	
	
    /**	
     * 工序名称	
     */	
	@ApiModelProperty("工序名称")	
    private String processName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料单位	
     */	
	@ApiModelProperty("物料单位")	
    private String primaryUnit;	
	
	
    /**	
     * 检验数合计	
     */	
	@ApiModelProperty("检验数合计")	
    private int inspectionNum;	
	
    /**	
     * 不良数合计	
     */	
	@ApiModelProperty("不良数合计")	
    private int badNum;	
	
    /**	
     * 不良率	
     */	
	@ApiModelProperty("不良率")	
    private BigDecimal badRate;	
	
    private static final long serialVersionUID = 1L;	
}	
