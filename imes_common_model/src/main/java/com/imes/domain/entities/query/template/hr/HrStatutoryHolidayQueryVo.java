package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import lombok.Data;	
	
/**	
 * hr法定假期表(HrStatutoryHoliday)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-02-16 14:49:12	
 */	
@Data	
@ApiModel("hr法定假期表")	
@QueryModel(name = "0687",	
        remark = "hr法定假期表",	
        alias = "hr_statutory_holiday",	
        searchApi = "/api/hr/holiday/query")	
public class HrStatutoryHolidayQueryVo extends BaseModel {	
	
    /**	
     * 适用公司编码	
     */	
	@ApiModelProperty("计划编码")	
    @QueryField(name = "计划编码")	
    private String planCode;	
    /**	
     * 计划名称	
     */	
	@ApiModelProperty("计划名称")	
    @QueryField(name = "计划名称")	
    private String planName;	
    /**	
     * 假期名称	
     */	
	@ApiModelProperty("假期名称")	
    @QueryField(name = "假期名称")	
    private String holidayName;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("假期名称")	
    @QueryField(name = "假期名称")	
    private String status;	
    /**	
     * 类型	
     */	
	@ApiModelProperty("类型")	
    @QueryField(name = "类型")	
    private String type;	
    /**	
     * 公司名称	
     */	
	@ApiModelProperty("公司名称")	
    @QueryField(name = "公司名称")	
    private String departName;	
	
    private String year;	
	
}	
	
