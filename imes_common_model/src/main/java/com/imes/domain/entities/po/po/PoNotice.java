package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "采购方公告主表")
public class PoNotice implements Serializable {

    private static final long serialVersionUID = 718997621957352845L;

    private String id;

    @ApiModelProperty(value = "公告号")
    private String noticeNo;

    @ApiModelProperty(value = "公告标题")
    private String noticeTitle;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "公告类型")
    private String noticeType;

    @ApiModelProperty(value = "10已录入，20发送中，30部分成功，40全部发送成功")
    private String noticeStatus;

    @ApiModelProperty(value = "发布企业名称")
    private String publishCompanyName;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    private String base64String;

    private String fileName;

    private Date sendTime;
}