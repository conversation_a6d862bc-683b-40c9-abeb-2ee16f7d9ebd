package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(name = "载具信息导入模板", modelName = "0135")
public class WmsCellImportVo extends BaseModel {

    @ImportField(name = "载具编号", required = true, remark = "必填", maxLength = 40)
    private String cellCode;

    @ImportField(name = "载具名称", remark = "不必填", maxLength = 100)
    private String cellName;

    @ImportField(name = "载具类型", required = true, isInteger = true, remark = "必填\n写入对应数字\n1:托盘\n2:料箱", dictOption = "STORAGE_CELL_TYPE")
    private String cellType;

    @ImportField(name = "载具状态", required = true, isInteger = true, remark = "必填\n写入对应数字\n1:空\n2:有货\n3:禁用\n默认1:空", dictOption = "STORAGE_CARRIER_STATUS")
    private String cellStatus;

    @ImportField(name = "载具规格", isInteger = true, remark = "不必填\n写入对应数字\n1:大\n2:中\n3:小", dictOption = "STORAGE_CARRIER_SPECIFICATION")
    private String cellSpecification;
}
