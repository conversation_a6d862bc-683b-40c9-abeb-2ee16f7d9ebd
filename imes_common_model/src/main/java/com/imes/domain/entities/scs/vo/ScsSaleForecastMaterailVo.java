package com.imes.domain.entities.scs.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.imes.domain.entities.po.vo.PoPurchaseForecastDaySumVo;
import com.imes.domain.entities.po.vo.PoPurchaseForecastSumVo;
import com.imes.domain.entities.scs.po.ScsSaleForecastDetail;
import com.imes.domain.entities.scs.po.ScsSaleForecastMaterail;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ScsSaleForecastMaterailVo extends ScsSaleForecastMaterail {
    private List<ScsSaleForecastDetailVo> scsSaleForecastDetailVos;

    private String updateFlg;
    //总计
    private BigDecimal sumQty;
    //一拆5
    private List<ScsSaleForecastCfList> detailLists;

    //日回复数
    private List<PoPurchaseForecastDaySumVo> lastReplyLists;

    private String memonicCode;

    private String supplyModelNumber;

    private String supplyDwgNo;
}
