package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsRecommendVo {	
    private String materialCode;	
	
    private String packCodeUnit;	
	
    private String batch;	
	
    private String customerCode;	
	
    private String saleCode;	
	
    private BigDecimal qty;	
	
    private String flag;	
	
    private String whCode;	
	
    private String skuCode;

    private String receiptCode;

    private String receiptItemCode;
}	
