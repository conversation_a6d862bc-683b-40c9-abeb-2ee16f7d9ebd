package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * (ProjectAttendance)实体类
 *
 * <AUTHOR>
 * @since 2021-05-10 14:31:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_attendance",resultMap = "BaseResultMap")
public class ProjectAttendance implements Serializable {
    private static final long serialVersionUID = -41797779199367817L;
    /**
    * id
    */
    private String id;
    /**
     * 时间范围
     */
    private List<LocalDate> dates;
    /**
    * 工号
    */
    private String employeeCode;
    /**
     * 姓名
     */
    @TableField(condition = SqlCondition.LIKE)
    private String employeeName;
    /**
     * 工号Po
     */
    private EmployeePo employeePo;
    /**
    * 项目id
    */
    private String projectId;
    /**
     * 项目名称
     */
    @TableField(condition = SqlCondition.LIKE)
    private String projectName;
    /**
     * 项目Po
     */
    private Project project;
    /**
    * 打卡地点
    */
    @TableField(condition = SqlCondition.LIKE)
    private String address;
    /**
     * 打卡详细地址
     */
    private String detailAddress;
    /**
     * 经纬度
     */
    private String latlng;
    /**
    * 备注
    */
    private String remarks;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 审核状态（0：待审核 , 1：审核通过，2：审批中, 3：已驳回）
    */
    private Integer approvalStatus;
}