package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目状态>>
 * @company 捷创智能技术有限公司
 * @create 2022-07-06 16:57
 */
// @JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum StateEnum {
    NOT_STARTED(0, "未开始"),
    IN_PROGRESS(1, "进行中"),
    COMPLETED(4, "已完成");

    @EnumValue
    @JsonValue
    // @JsonSerialize(using = ToStringSerializer.class)
    public final Integer state;
    public final String stateName;

    StateEnum(Integer state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }


    public Integer getState() {
        return state;
    }

    public String getStateName() {
        return stateName;
    }

    public static StateEnum match(Integer state) {
        return Arrays.stream(StateEnum.values())
                .filter(e -> e.getState().equals(state))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("项目状态错误"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static StateEnum des(final JsonNode jsonNode) {
        Integer state = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("state")).map(JsonNode::asInt).orElse(-1);
        if (state == -1) {
            return null;
        }
        return StateEnum.match(state);
    }


    @Override
    public String toString() {
        return stateName;
    }
}
