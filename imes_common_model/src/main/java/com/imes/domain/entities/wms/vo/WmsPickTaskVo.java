package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPickTask;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStockBin;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
/**	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-07-12 15:01	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPickTaskVo extends WmsPickTask {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specification;	
	
    /**	
     * 库存在库量	
     */	
	@ApiModelProperty("库存在库量")	
    private BigDecimal onHandQty;	
	
    /**	
     * 库存可用量	
     */	
	@ApiModelProperty("库存可用量")	
    private BigDecimal availableQty;	
	
    /**	
     * 物料总共拣货量	
     */	
	@ApiModelProperty("物料总共拣货量")	
    private BigDecimal totalPickedQty;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    private Date productionDate;	
	
    /**	
     * 入库日期	
     */	
	@ApiModelProperty("入库日期")	
    private Date putAwayDate;	
	
    /**	
     * 包装可用数量	
     */	
	@ApiModelProperty("包装可用数量")	
    private BigDecimal packAvailableQty;	
	
    /**	
     * 包装在库数量	
     */	
	@ApiModelProperty("包装在库数量")	
    private BigDecimal packOnhandQty;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    /**	
     * 小数进度位数	
     */	
	@ApiModelProperty("小数进度位数")	
    private Byte precisionDigit;	
	
    private Integer packInventoryQtyPrecision;

    private String receiptCode;

    private String receiptItemCode;
	
}	
