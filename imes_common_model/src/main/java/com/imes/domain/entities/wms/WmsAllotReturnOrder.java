package com.imes.domain.entities.wms;	
	
import java.util.Date;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.List;	
	
@Data	
@ApiModel(value = "调拨退货单")	
public class WmsAllotReturnOrder implements Serializable {	
	
    private static final long serialVersionUID = -76392754556518316L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "调拨退货单号")	
    private String allotReturnCode;	
	
    @ApiModelProperty(value = "单据类型（调拨退货单）")	
    private String orderType;	
	
    @ApiModelProperty(value = "关联的单据号")	
    private String receiptCode;	
	
    @ApiModelProperty(value = "单据来源（1:手工建单、2：MOM模块、3：第三方系统）")	
    private String source;	
	
    @ApiModelProperty(value = "第三方单据号")	
    private String thirdOrderCode;	
	
    @ApiModelProperty(value = "单据状态（10：已录入、15：待审核、20：已生效、50：已完成、99：已取消）")	
    private String status;	
	
    @ApiModelProperty(value = "退货公司编码")	
    private String fromCompanyCode;	
	
    @ApiModelProperty(value = "退货公司名称")	
    private String fromCompanyName;	
	
    @ApiModelProperty(value = "退货仓库")	
    private String fromWhCode;	
	
    @ApiModelProperty(value = "退货仓库名称")	
    private String fromWhName;	
	
    @ApiModelProperty(value = "接收公司编码")	
    private String toCompanyCode;	
	
    @ApiModelProperty(value = "接收公司名称")	
    private String toCompanyName;	
	
    @ApiModelProperty(value = "接收仓库")	
    private String toWhCode;	
	
    @ApiModelProperty(value = "接收仓库名称")	
    private String toWhName;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "仓库负责人编码")	
    private String warehouseUserCodes;	
    @ApiModelProperty(value = "仓库负责人名称")	
    private String warehouseUserNames;	
	
    @ApiModelProperty(value = "自定义字段")	
    private String custom;

    @ApiModelProperty(value = "备注")
    private String remark;
	
    private List<WmsAllotReturnOrderItem> items;	
}	
