package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPackingBoxMaterial;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsPackingBoxMaterialVo extends WmsPackingBoxMaterial {	
	
    @ApiModelProperty(value = "关联明细单主键")	
    private String receiptItemCode;	
	
    @ApiModelProperty(value = "装箱单号")	
    private String packNo;	
	
    @ApiModelProperty(value = "箱号")	
    private String boxNo;	
	
    @ApiModelProperty(value = "是否混装1是0否")	
    private String isMixed;	
	
    @ApiModelProperty(value = "装箱数量汇总")	
    private BigDecimal packTotalNum;	
	
    @ApiModelProperty("打印次数")	
    private Integer printSum;	
	
}	
