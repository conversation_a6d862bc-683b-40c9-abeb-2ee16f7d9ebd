package com.imes.domain.entities.pm.po;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProjectPieChart)实体类
 *
 * <AUTHOR>
 * @since 2022-02-09 14:12:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_piechart", resultMap = "BaseResultMap")
public class ProjectPieChart implements Serializable {
    private static final long serialVersionUID = -5987816059716682567L;
    /**
     * id
     */
    private String id;

    /**
     * 项目类型
     */
    private String type;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 系列
     */
    private String series;

    /**
     * 系列实体
     */
    private List<ProjectPieSeries> projectPieSeriese;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 创建人名
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
    /**
     * 修改人名
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedName;
}
