package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import com.baomidou.mybatisplus.annotation.TableField;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
import io.swagger.annotations.ApiModelProperty;	
	
/**	
 * (WmsInventorySchedule)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-12-27 18:03:58	
 */	
@Data	
public class WmsInventorySchedule implements Serializable {	
    private static final long serialVersionUID = 574497752261100517L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "盘点任务号")	
    private String scheduleNo;	
	
    @ApiModelProperty(value = "关联单号")	
    private String receiptCode;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "库区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "库位编码")	
    private String binCode;	
	
    @ApiModelProperty(value = "载具编码")	
    private String cellCode;	
	
    @ApiModelProperty(value = "路径编码")	
    private String routeCode;	
	
    @ApiModelProperty(value = "输送设备编码")	
    private String conveyerCode;	
	
    @ApiModelProperty(value = "起点设备")	
    private String startConveyerCode;	
	
    @ApiModelProperty(value = "终点设备")	
    private String endConveyerCode;	
	
    @ApiModelProperty(value = "开始时间")	
    private Date startTime;	
	
    @ApiModelProperty(value = "完成日期")	
    private Date endTime;	
	
    @ApiModelProperty(value = "状态")	
    private String scheduleStatus;	
	
    @ApiModelProperty(value = "优先级")	
    private Integer priority;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "${column.comment}")	
    private String remarks;	
	
    /**	
     * 盘点任务明细	
     */	
	@ApiModelProperty("盘点任务明细")	
    @TableField(exist = false)	
    private List<WmsInventoryScheduleItem> itemList;	
	
}	
	
