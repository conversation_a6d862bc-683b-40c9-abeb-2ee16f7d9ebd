package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.ppc.po.SoDeliveryResponse;
import com.imes.domain.entities.ppc.po.SoDeliveryResponseDetail;
import com.imes.domain.entities.scs.po.ScsSaleDetail;
import com.imes.domain.entities.scs.po.ScsSaleMain;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoToScsVo extends ScsSaleMain {
    //子对象
    private List<ScsSaleDetailVo> saleDetailLists;
    //供应商平台机构编码
    private String orgPlatfromCode;
    //用于存放供应商code+供应商名称
    private String supplierCodeName;
    //采购商备注
    private String poRemarks;
    //ERP备注
    private String erpRemarks;

    private String sendMsg;


}
