package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsBusTaskHistory;	
import com.imes.domain.entities.wms.WmsPickTask;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
/**	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-07-12 15:01	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsBusTaskHistoryVo extends WmsBusTaskHistory {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")
    private String materialMarker;	
	
    /**	
     * 物流型号码	
     */	
	@ApiModelProperty("物流型号码")
    private String barCode;	
	
    /**	
     * 明细编码	
     */	
	@ApiModelProperty("明细编码")
    private String itemCode;	
	
    /**	
     * 关联明细单号	
     */	
	@ApiModelProperty("关联明细单号")
    private String thirdItemCode;	
	
    /**	
     * 拣货任务编码	
     */	
//    private String pickTaskCode;
	
	
	
}	
