package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(name = "WCS设备信息导入")
public class WmsWCSImportVo extends BaseModel {

    @ImportField(name = "设备编号", remark = "必填", maxLength = 40)
    private String conveyerCode;

    @ImportField(name = "设备名称", remark = "必填", maxLength = 50)
    private String conveyerName;

    @ImportField(name = "设备类型", dictOption = "WCS_TYPE", remark = "必填\n写入对应数字\n1:堆垛机\n2:输送线\n3:扫码")
    private String conveyerType;

    @ImportField(name = "设备状态", dictOption = "WCS_STATUS", remark = "必填\n写入对应数字\n1:正常\n2:禁用")
    private String conveyerStatus;

    @ImportField(name = "规格型号", remark = "不必填", maxLength = 40)
    private String model;

    @ImportField(name = "备注", remark = "不必填", maxLength = 100)
    private String remarks;

    @ImportField(name = "归属仓库", remark = "必填\n仓库编码", maxLength = 40)
    private String whCode;

    @ImportField(name = "归属库区", remark = "必填\n库区编码", maxLength = 40)
    private String areaCode;

    @ImportField(name = "说明", remark = "不必填", maxLength = 255)
    private String description;

    @ImportField(name = "列", remark = "不必填\n数字格式", isInteger = true, maxLength = 50)
    private String kind;

    @ImportField(name = "层", remark = "不必填\n数字格式", isInteger = true, maxLength = 30)
    private String layer;
}
