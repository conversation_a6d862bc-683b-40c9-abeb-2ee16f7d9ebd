package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<新阳返回数据接收格式>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-01 14:41
 */
@ApiModel("Ecp返回实体类")
@Data
public class ResultVo {
    @ApiModelProperty("是否成功")
    private Boolean success;
    @ApiModelProperty("状态码")
    private Integer code;
    @ApiModelProperty("信息")
    private String message;
    @ApiModelProperty("数据集合")
    private List<Object> data;
    @ApiModelProperty("总数")
    private Long count;
}
