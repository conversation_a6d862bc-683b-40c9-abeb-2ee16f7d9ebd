package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.OutBillQualityReportDetail;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.TechnologyQualityReportDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class TechnologyQualityReportVO implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 物料编号	
     */	
	@ApiModelProperty("物料编号")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料单位	
     */	
	@ApiModelProperty("物料单位")	
    private String primaryUnit;	
	
    /**	
     * 工序编码
     */	
	@ApiModelProperty("工序编码")
    private String processCode;	
	
    /**	
     * 工序name	
     */	
	@ApiModelProperty("工序name")	
    private String processName;	
	
    /**	
     * 检验次数	
     */	
	@ApiModelProperty("检验次数")	
    private int inspectionNum;	
	
    /**	
     * 不合格次数	
     */	
	@ApiModelProperty("不合格次数")	
    private int unQualified;	
	
    /**	
     *合格次数	
     */	
    private int isQualified;	
	
	
    private static final long serialVersionUID = 1L;	
}	
