package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
@ApiModel("采购订单")	
@QueryModel(	
        name = "1195",	
        remark = "采购订单",	
        searchApi = "/api/po/poPurchaseStandard/queryList",	
        alias = {"po_purchase_standard", "po_purchase_detail_standard"},	
        link = {"1234","0481","0240","0421"},	
        pushApi = PushApi.PoPurchaseStandardQueryVo,	
        showMode = true,	
        customExp = true,
        openapi = true,
        resume = "purchaseNo",
        auth = Auth.PC,	
        authTenant = true,	
        backup = true	
)	
public class PoPurchaseStandardQueryVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, show = false, order = OrderBy.DESC)	
    private String createOn;	
	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", alias = "$1.id", show = false)	
    private String detailId;	
	
    private String mainId;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号", width = 130)	
    private String purchaseNo;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS", width = 100)	
    @EditField(readonly = true)	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS", value = "10", width = 100)	
    @EditField(readonly = true)	
    private String businessStatus;	
	
    //@QueryField(name = "单据来源", type = Type.MultiSelect, dictOption = "PO_PURCHASE_DOC_SOURCE", width = 110)	
    private String docSource;	
	
	@ApiModelProperty("取价方式")	
    @QueryField(name = "取价方式", type = Type.MultiSelect, sqlOption = "select range_no as value ,remarks as label from po_price_range", width = 110)	
    private String priceType;	
	
    //@QueryField(name = "创建人", level = Level.Main, alias = ".(select user_name from pe_user where pe_user.user_code = po_purchase_standard.create_by limit 1)", sort = false, width = 100)	
    //@EditField(show = false)	
    private String createByName;	

	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_TYPE", width = 100)	
    private String purchaseType;	
	
	@ApiModelProperty("制单人")	
    @QueryField(name = "制单人", width = 100)	
    private String operatorName;	
	
	@ApiModelProperty("采购日期")	
    @QueryField(name = "采购日期", order = OrderBy.DESC, type = Type.Date, format = "yyyy-MM-dd", width = 130)	
    @EditField(required = true)	
    private String orderDate;	
	
	@ApiModelProperty("采购部门编码")	
    @QueryField(name = "采购部门编码", width = 0,query = false)
    private String demandDepCode;	
	
	@ApiModelProperty("采购部门")	
    @QueryField(name = "采购部门", width = 100, alias = "co_department.depart_name", level = Level.Main)	
    @EditField(required = true)	
    private String demandDepName;	
	
	@ApiModelProperty("采购员")	
    @QueryField(name = "采购员", width = 100, alias = "pe_user.user_name", level = Level.Main)	
    @EditField(required = true)	
    private String demandUserName;

    @ApiModelProperty("采购员编码")
    @QueryField(name = "采购员编码",query = false)
    @EditField(readonly = true)
    private String demandUserCode;

    @ApiModelProperty("币种")
    @QueryField(name = "币种", type = Type.MultiSelect, sqlOption = "select ccy_no as value,ccy_name as label from sys_currency", width = 100)	
    @EditField(required = true)	
    private String currency;	
	
	@ApiModelProperty("变更原因")	
    @QueryField(name = "变更原因")	
    private String changeRemarks;

    @ApiModelProperty("采购单备注")
    @QueryField(name = "采购单备注", width = 120)
    private String remarks;

    private String createBy;	
	
	@ApiModelProperty("物料行号")	
    @QueryField(name = "物料行号", alias = "$1", width = 120)	
    private String sdNo;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码", alias = "$1", width = 0)	
    @EditField(required = true)	
    private String supplierCode;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称", alias = "sys_supplier", width = 120)	
    @EditField(required = true)	
    private String supplierName;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "$1", width = 100)	
    @EditField(required = true)	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "ppc_material", width = 100)	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "ppc_material")	
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "ppc_material")	
    private String materialMarker;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性", alias = "$1", width = 0)	
    private String skuCode;	
	
    private String dwgNo;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量", alias = "$1", width = 100, type = Type.Number)	
    @EditField(required = true)	
    private String qty;	
	
	@ApiModelProperty("采购单位")	
    @QueryField(name = "采购单位", alias = "$1", width = 100, type = Type.MultiSelect, sqlOption = "select code as value ,name as label from sys_unit")	
    @EditField(required = true, type = Type.TableSelection)	
    private String unit;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "$1", width = 100, type = Type.MultiSelect, sqlOption = "select code as value ,name as label from sys_unit")	
    @EditField(type = Type.TableSelection)	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", alias = "$1", width = 0)	
    private String baseUnitQty;	
	
	@ApiModelProperty("计划数量")	
    @QueryField(name = "计划数量", alias = "$1", type = Type.Number)	
    private String planQty;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", alias = "$1", type = Type.Number)	
    private String arriveQty;	
	
	@ApiModelProperty("入库数量")	
    @QueryField(name = "入库数量", alias = "ifnull(po_purchase_detail_standard.inbound_qty,0)", type = Type.Number, sum = Sum.Define)	
    private String inboundQty;	
	
	@ApiModelProperty("退货数量")	
    @QueryField(name = "退货数量", alias = "$1", type = Type.Number)	
    private String cancelQty;	
	
	@ApiModelProperty("退货补货数量")	
    @QueryField(name = "退货补货数量", alias = ".(ifnull($1.cancel_replenish_qty,0))", type = Type.Number)	
    private String cancelReplenishQty;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "$1", option = {"0", "是", "1", "否"}, type = Type.MultiSelect)	
    private String includeTax;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", alias = "$1", dictOption = "PO_PURCHASE_TAX_CODE", type = Type.MultiSelect)	
    private String taxCode;	
	
	@ApiModelProperty("折扣方式")	
    @QueryField(name = "折扣方式", alias = "$1", dictOption = "SALE_DISCOUNT_TYPE", type = Type.MultiSelect)	
    private String discountType;	
	
	@ApiModelProperty("折扣率(%)")	
    @QueryField(name = "折扣率(%)", alias = ".(ifnull(TRUNCATE($1.discount_rate, 2), 0) * 100)")	
    private String discountRate;	
	
	@ApiModelProperty("折扣额")	
    @QueryField(name = "折扣额", alias = "$1")	
    private String discountPrice;	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", alias = "$1", type = Type.Number, sum = Sum.Define)	
    @EditField(required = true)	
    private String singlePrice;	
	
	@ApiModelProperty("净价")	
    @QueryField(name = "净价", alias = "$1")	
    private String netPrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", alias = "$1", type = Type.Number, sum = Sum.Define)	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", alias = "$1", type = Type.Number, sum = Sum.Define)	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", alias = "$1", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", alias = "$1", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", alias = "$1", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("到货日期")	
    @QueryField(name = "到货日期", type = Type.Date, alias = "$1", format = "yyyy-MM-dd")	
    @EditField(required = true)	
    private String deliveryDate;	
	
	@ApiModelProperty("源单类型")	
    @QueryField(name = "源单类型", alias = "$1", type = Type.MultiSelect, dictOption = "PO_PURCHASE_DOC_SOURCE")	
    private String requestDocType;	
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号", alias = "$1")	
    private String requestDocCode;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号", alias = "$1")	
    private String lineNo;	
	
	@ApiModelProperty("源申请部门")	
    @QueryField(name = "源申请部门", alias = "cd.depart_name")	
    private String requestDocDepName;	
	
	@ApiModelProperty("源申请人")	
    @QueryField(name = "源申请人", alias = "pu.user_name")	
    private String requestDocPersonName;	
	
	@ApiModelProperty("行业务状态")	
    @QueryField(name = "行业务状态", type = Type.MultiSelect, alias = "$1.business_status", dictOption = "SALE_BUSINESS_CODE")	
    private String detailBusinessStatus;	
	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "$1.remarks")	
    private String detailRemarks;	
	
	@ApiModelProperty("附件")	
    @QueryField(name = "附件", show = false)	
    private String file;	
	

	
    private String searchKey;
	
}	
