package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "供应链销售订单配送任务明细拆分项")
public class ScsSaleDetailItem implements Serializable {

    private static final long serialVersionUID = 275973616007486874L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "计划到货时间")
    private Date requestedDeliveryDate;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private String updateFlg;

    private String sdNo;
}