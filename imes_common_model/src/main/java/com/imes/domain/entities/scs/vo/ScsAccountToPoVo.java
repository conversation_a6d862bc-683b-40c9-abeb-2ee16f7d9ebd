package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.scs.po.ScsAccountStorageDetail;
import com.imes.domain.entities.scs.po.ScsAccountVerify;
import lombok.Data;

import java.util.List;

@Data
public class ScsAccountToPoVo extends ScsAccountVerify {
    //供应商平台机构编码
    private String orgPlatfromCode;

    private List<ScsAccountStorageDetail> detailList;

    private String base64String;

    private String fileName;

    private String sendMsg;
    //判断该外部接口 是否为删除数据
    private String delType;
    //删除用IdList
    private List<String> idList ;

}
