package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import lombok.Data;	
	
/**	
 * 员工岗位类	
 *	
 * <AUTHOR> z	
 * @since 2022-08-30 14:58:17	
 */	
@Data	
@ApiModel("员工岗位")
@QueryModel(name = "0653", remark = "员工岗位", alias = "hrjobs", searchApi = "/api/hr/jobs/query")	
public class HrJobsVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id")	
    private String id;	
    /**	
     * 职位	
     */	
	@ApiModelProperty("职位")	
    @QueryField(name = "职位")	
    private String position;	
    /**	
     * 岗位名称	
     */	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称")	
    private String jobsName;	
    /**	
     * 描述	
     */	
	@ApiModelProperty("描述")	
    @QueryField(name = "描述")	
    private String content;	
	
    private String deptCode;	
	
}	
