package com.imes.domain.entities.hr.dto;

import com.imes.domain.entities.hr.enmus.TrainEvaluationEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 培训记录表(TrainingUser)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-31 10:26:56
 */
@ApiModel("培训用户表")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TrainingUserDto implements Serializable {

    private static final long serialVersionUID = 430285887183134835L;
    private String id;
    /**
     * 培训id
     */
    @NotNull(message = "培训id不为空")
    private String trainingId;
    /**
     * 学员编号
     */
    @NotNull(message = "学员编号不为空")
    private String userCode;
    /**
     * 学员名称
     */
    private String userName;
    /**
     * 是否强制
     */
    private Integer isMandatory;
    /**
     * 老师-评价
     */
    private TrainEvaluationEnum evaluationType;
    /**
     * 老师-评价内容
     */
    private String evaluation;
    /**
     * 学员-评价内容
     */
    private String traineesEvaluation;
    /**
     * 学员-内容评分
     */
    private Integer scoreContent;
    /**
     * 学员-案例评分
     */
    private Integer scoreCase;
    /**
     * 学员-描述评分
     */
    private Integer scoreDescribe;
    /**
     * 学员-沟通评分
     */
    private Integer scoreCommunication;
    /**
     * 学员-外表评分
     */
    private Integer scoreSurface;
    /**
     * 学员-内容建议
     */
    private String adviceContent;
    /**
     * 学员-案例建议
     */
    private String adviceCase;
    /**
     * 学员-描述建议
     */
    private String adviceDescribe;
    /**
     * 学员-沟通建议
     */
    private String adviceCommunication;
    /**
     * 学员-外表建议
     */
    private String adviceSurface;
    /**
     * 学员-培训反馈ID
     */
    private String feedbackId;

}

