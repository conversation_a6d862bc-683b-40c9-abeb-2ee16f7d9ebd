package com.imes.domain.entities.query.plugin.imports;

public class ImportRemark {
    /**
     * 通用Excel模板导出提示语(默认)
     */
    public static final String COMMON_REMARK = "1、导入文档必须使用本模板，有效导入数据从第4行开始\n" +
            "2、模板中所有输入框都为“文本”，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式\n" +
            "3、所有纯数字单元，请保证单元格内有”绿色小三角”\n" +
            "4、列名禁止修改，带*标志表示必填项\n" +
            "5、增量导入，不可更新导入;\n"+
            "6、覆盖导入最多可导出1万条数据;\n"+
            "7、标题背景色代表字段类型，黄色为主表字段，橘色为明细字段，绿色则为自定义字段";

    public static final String PoPriceImportVo = "1、导入文档必须使用本模板，有效导入数据从第4行开始，截止L列\n" +
            "2、模板中所有输入框都为“文本”，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式\n" +
            "3、带*标志表示必填项\n" +
            "4、黄色代表头部信息，只能有一行，橘色代表明细信息，可多行\n" +
            "5、最多可导入500条数据";

    public static final String DevOEEImportVo = "1、导入文档必须使用本模板，有效导入数据从第4行开始，截止L列\n" +
            "2、模板中所有输入框都为“文本”，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式\n" +
            "3、带*标志表示必填项\n" +
            "4、所有纯数字单元，请保证单元格内有”绿色小三角”\n" +
            "5、只需填写统计方案为手动录入的字段，不填默认为0\n" +
            "6、理论用时= A产品的节拍(/min)×A产品当天报工的总数+B产品的节拍(/min)×B产品当天报工的总数";


    public static final String FileCategoryImportVo = "1、导入文档必须使用本模板，有效导入数据从第4行开始\n" +
            "2、模板中所有输入框都为“文本”，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式\n" +
            "3、所有纯数字单元，请保证单元格内有”绿色小三角”\n" +
            "4、带*标志表示必填项\n" +
            "5、增量导入，不可更新导入\n"+
            "6、导入的类目名称需要严格按照层级顺序排列，一级类目所属导入行必须于二级类目之前，二级类目导入所属行必须于三级类目之前，以此类推";



    public static final String FileRoleRelationImportVo = "1、导入文档必须使用本模板，有效导入数据从第4行开始\n" +
            "2、模板中所有输入框都为“文本”，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式\n" +
            "3、所有纯数字单元，请保证单元格内有”绿色小三角”\n" +
            "4、带*标志表示必填项\n" +
            "5、增量导入，不可更新导入\n"+
            "6、蓝底代表导入内容，灰底代表匹配内容，不可随意更改";
}