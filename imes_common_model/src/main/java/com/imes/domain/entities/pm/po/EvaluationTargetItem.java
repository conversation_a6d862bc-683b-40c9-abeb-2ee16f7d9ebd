package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (PerEvaluationTargetItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:26:51
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_target_item",resultMap = "BaseResultMap")
public class EvaluationTargetItem implements Serializable {
    private static final long serialVersionUID = -89913551136380801L;
    /**
    * id
    */
    private String id;
    /**
    * 考评目标id
    */
    private String evaluationTargetId;
    /**
    * 考评人工号
    */
    private String assessorCode;
    /**
     * 考评人工号
     */
    @TableField(exist = false)
    private String assessorName;
    /**
    * 指标名称
    */
    private String name;
    /**
    * 指标类型
    */
    private Integer type;
    /**
    * 计算规则
    */
    private String rules;
    /**
    * 目标
    */
    private String target;
    /**
    * 考评结果
    */
    private String actual;
    /**
    * 自评
    */
    private String selfEvaluation;
    /**
    * 领导评
    */
    private String leadershipEvaluation;
    /**
    * 最高分
    */
    private Integer highestScore;
    /**
    * 分值
    */
    private Integer score;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

}