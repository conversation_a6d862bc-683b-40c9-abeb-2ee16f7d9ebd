package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.util.Date;	
import java.util.List;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPackingOrder implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 装箱单号	
     */	
	@ApiModelProperty("装箱单号")	
    private String poCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boCode;	
	
    /**	
     * 发货单主键	
     */	
	@ApiModelProperty("发货单主键")	
    private String doId;	
	
    /**	
     * 发货单号	
     */	
	@ApiModelProperty("发货单号")	
    private String docode;	
	
    /**	
     * 装箱单打印状态	
     */	
	@ApiModelProperty("装箱单打印状态")	
    private Integer orderstatus;	
	
    /**	
     * 箱唛打印状态	
     */	
	@ApiModelProperty("箱唛打印状态")	
    private Integer packmarkstatus;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    private List<WmsPackingOrderItem> orderItems;	
    private static final long serialVersionUID = 1L;	
}	
