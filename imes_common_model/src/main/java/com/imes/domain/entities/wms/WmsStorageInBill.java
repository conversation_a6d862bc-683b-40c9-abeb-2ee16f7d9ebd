package com.imes.domain.entities.wms;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInBill implements Serializable {	

	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 入库单编码	
     */	
    @ApiModelProperty(name = "入库单号")
    private String storageInCode;	
	
    /**	
     * 单据来源（如从ERP获得，从MES获取，手工建单等）	
     */	
    @ApiModelProperty(name = "单据来源")
    private String source;	
	
    /**	
     * 关联单类型（1：采购订单；2：采购到货单）	
     */	
    @ApiModelProperty(name = "关联单类型")
    private String receiptSource;	
	
    /**	
     * 入库单类型	
     */	
    @ApiModelProperty(name = "单据类型")
    private String receiptType;	
	
    /**	
     * 关联的单据号	
     */	
    @ApiModelProperty(name = "关联单号")
    private String receiptCode;	
	
    /**	
     * 过账日期	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    @ApiModelProperty(name = "过账日期")
    private Date postingDate;	
	
    /**	
     * 部门编码	
     */	
    @ApiModelProperty(name = "部门编码")
    private String departCode;	
	
    /**	
     * 部门名称	
     */	
    @ApiModelProperty(name = "部门名称")
    private String departName;	
	
    /**	
     * 单据状态（1-草稿；2-未完成；3-已完成）	
     */	
    @ApiModelProperty(name = "单据状态")
    private String status;	
	
    /**	
     * 批次	
     */	
    @ApiModelProperty(name = "批次")
    private String batch;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty(name = "备注")
    private String remark;	
	
    /**	
     * 预计入库时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    @ApiModelProperty(name = "预计入库时间")
    private Date expectOn;	
	
    /**	
     * 入库单申请人	
     */	
    @ApiModelProperty(name = "申请人工号")
    private String applyBy;	
	
    /**	
     * 操作时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    @ApiModelProperty(name = "操作时间")
    private Date approveOn;	
	
    /**	
     * 操作人	
     */	
    @ApiModelProperty(name = "操作人")
    private String approveBy;	
	
    /**	
     * 创建时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@ApiModelProperty("创建时间")
    private Date createOn;	
	
    /**	
     * 创建人	
     */
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    private Date updateOn;	
	
    /**	
     * 更新人	
     */
    private String updateBy;	
	
    /**	
     * 第三方系统单号	
     */	
    @ApiModelProperty(name = "第三方系统单号")
    private String thirdOrderCode;	
	
    /**	
     * 入库申请人名称	
     */	
    @ApiModelProperty(name = "申请人名称")
    private String applyName;	
	
    /**	
     * 入库仓库编码	
     */	
    @ApiModelProperty(name = "仓库编码")
    private String whCode;	
    /**	
     * 入库仓库名称	
     */	
    @ApiModelProperty(name = "仓库名称")
    private String whName;	
	
    /**	
     * 是否需要仓储作业(0:否,1:是)	
     */	
    @ApiModelProperty(name = "是否需要仓库作业")
    private String isTask;	
	
    @ApiModelProperty("税率")
    private BigDecimal taxRate;	
	
    @ApiModelProperty("是否已经发送数据（0:否,1:是）")
    private String isSendOut;	
	
    @ApiModelProperty("库存状态")
    private String inventoryStatus;	
	
    @ApiModelProperty("是否确认")
    private String isConfirm;	

    private List<WmsStorageInBillItem> itemList;	

    private String isBatch;	
	
    @ApiModelProperty("生产日期")
    private Date productionDate;	
	
    @ApiModelProperty("自定义字段")
    private String custom;	
	
    @ApiModelProperty("二级单据类型")
    private String purpose;	
	
    @ApiModelProperty("关联单号，关联生产入库排产单号")
    private String parentCode;	
	
    @ApiModelProperty("工作流流程主键")
    private String activityId;	
	
    @ApiModelProperty("审核人")
    private String  auditUserCode;	
	
    @ApiModelProperty("审核时间")
    private Date auditDate;	
	
    /**	
     * 移动端审核调入库位是否可以下拉选择	
     */	
	@ApiModelProperty("移动端审核调入库位是否可以下拉选择")	
    private boolean showSelect;

    @ApiModelProperty("tplus主单id")
    private String plusId;

    @ApiModelProperty("tplus明细单id")
    private String plusDetailId;

    private static final long serialVersionUID = 1L;	
}	
