package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;


@Data
@ApiModel(value = "U8采购订单加锁解锁明细行")
public class U8PushPoLockRows implements Serializable {

    @ApiModelProperty(value = "单据行号")
    private Integer irowno;

    @ApiModelProperty(value = "物料编码")
    private String cinvcode;

    @ApiModelProperty(value = "数量")
    private BigDecimal iquantity;

    @ApiModelProperty(value = "是否锁定 1锁 0 开")
    private Integer islock;

}
