package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<开票量本利查询>>
 * @company 捷创智能技术有限公司
 * @create 2021-11-01 14:04
 */
@ApiModel("《开票量本利查询》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpInvoiceoutsalessndcostsQuery extends BaseQuery  {
    @ApiModelProperty("单据来源")
    private String v11;
    @ApiModelProperty("行唯一值")
    private String v12;
    @ApiModelProperty("销售合同唯一值")
    private String v13;
    @ApiModelProperty("销售合同号")
    private String v14;
    @ApiModelProperty("单据编号")
    private String v15;
    @ApiModelProperty("品牌")
    private String v16;
    @ApiModelProperty("型号")
    private String v17;
    @ApiModelProperty("开票型号")
    private String v18;
    @ApiModelProperty("系列")
    private String v19;
    @ApiModelProperty("产品类别")
    private String v20;
    @ApiModelProperty("PGC代码")
    private String v35;
    @ApiModelProperty("发票号码")
    private String v21;
    @ApiModelProperty("发票类型")
    private String v22;
    @ApiModelProperty("销售类型")
    private String v24;
    @ApiModelProperty("销售类型编码")
    private String v23;
    @ApiModelProperty("特价号")
    private String v25;
    @ApiModelProperty("RA主导项目")
    private String v26;
    @ApiModelProperty("订单号")
    private String v27;
    @ApiModelProperty("项目名称")
    private String v28;
    @ApiModelProperty("区域")
    private String v29;
    @ApiModelProperty("销售员")
    private String gUserName;
    @ApiModelProperty("销售员工号")
    private String gUser;
    @ApiModelProperty("销售部门")
    private String gDeptName;
    @ApiModelProperty("销售部门路径")
    private String gDept;
    @ApiModelProperty("商务员")
    private String sUserName;
    @ApiModelProperty("商务员部门")
    private String sDeptName;
    @ApiModelProperty("商务员部门路径")
    private String sDept;
    @ApiModelProperty("客户名称")
    private String gCName;
    @ApiModelProperty("客户编号")
    private String gClient;
    @ApiModelProperty("客户类型")
    private String v30;
    @ApiModelProperty("开票日期")
    private LocalDate d10;
    @ApiModelProperty("所属公司编码")
    private LocalDate cid;
    @ApiModelProperty("所属公司")
    private LocalDate cidName;
    @ApiModelProperty("新系统数据")
    private Boolean checkinfo1;
    @ApiModelProperty("老系统数据")
    private Boolean checkinfo2;
}
