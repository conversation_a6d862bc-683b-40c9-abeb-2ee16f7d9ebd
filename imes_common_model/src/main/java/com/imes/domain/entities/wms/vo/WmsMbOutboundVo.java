package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageOutBill;	
import com.imes.domain.entities.wms.vo.WmsMbOutboundItemVo;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
/**	
 * 移动端出库任务列表	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-03-17 14:30	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMbOutboundVo extends WmsStorageOutBill {	
	
    /**	
     * 发货单出库状态	
     */	
	@ApiModelProperty("发货单出库状态")	
    private Integer deliveryStatus;	
	
    /**	
     * 到货日期	
     */	
	@ApiModelProperty("到货日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date dueDate;	
	
    /**	
     * 仓库号	
     */	
	@ApiModelProperty("仓库号")	
    private String whCode;	
	
    /**	
     * 总需发货数量	
     */	
	@ApiModelProperty("总需发货数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 已拣货数量	
     */	
	@ApiModelProperty("已拣货数量")	
    private BigDecimal pickedQty;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 出库单明细	
     */	
	@ApiModelProperty("出库单明细")	
    private List<WmsMbOutboundItemVo> items;	
	
}	
