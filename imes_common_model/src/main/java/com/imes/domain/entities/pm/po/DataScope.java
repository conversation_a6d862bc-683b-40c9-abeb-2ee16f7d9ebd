package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_data_scope", resultMap = "BaseResultMap")
public class DataScope {
    private static final long serialVersionUID = -65529736581927982L;
    private String id;
    private String userCode;
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> deptIds;
}
