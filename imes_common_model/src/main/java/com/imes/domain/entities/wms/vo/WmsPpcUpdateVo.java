package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
public class WmsPpcUpdateVo {	
    //是否通过 1通过 2未通过	
    private int type;	
    //供应商code+name集合	
    private String supplierCodeName;	
    //供应商平台机构编码	
    private String orgPlatfromCode;	
    //发货单关联号	
    private String deliveryInvoiceNo;	
    //拒绝理由	
    private String refuseRemarks;	
    //id	
	@ApiModelProperty("id")	
    private String id;	
    //消息	
    private String sendMsg;	
}	
