package com.imes.domain.entities.crm.dto.ex;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<分頁信息>>
 * @company 捷创智能技术有限公司
 * @create 2022-04-22 10:16
 */
@Data
public class PageDto {
    @Min(0)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Integer pageNum = 1;
    @Max(200)
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private Integer pageSize = 10;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    private String orderBy;

    public void setOrderBy(String orderBy) {
        this.orderBy = StringUtils.camelToUnderline(orderBy);
    }
}
