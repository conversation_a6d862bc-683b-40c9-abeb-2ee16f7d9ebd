package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 加班规则表(HrOvertimeRule)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-27 10:49:01	
 */	
@Data	
@ApiModel("加班规则管理")	
@QueryModel(name = "0786",	
        remark = "加班规则管理",	
        alias = "hr_overtime_rule",	
        searchApi = "/api/hr/attendance/query/leave")	
public class HrOvertimeRuleQueryVo extends BaseModel {	
	
    /**	
     * 加班编号	
     */	
	@ApiModelProperty("加班编号")	
    @QueryField(name = "加班编号" )	
    private String overtimeNo;	
    /**	
     * 加班规则名称	
     */	
	@ApiModelProperty("加班规则名称")	
    @QueryField(name = "加班规则名称" )	
    private String ruleName;	
    /**	
     * 加班类型	
     */	
	@ApiModelProperty("加班类型")	
    @QueryField(name = "加班类型" )	
    private String type;	
    /**	
     * 时间段	
     */	
	@ApiModelProperty("时间段")	
    @QueryField(name = "时间段" )	
    private String period;	
    /**	
     * 加班福利类型	
     */	
	@ApiModelProperty("加班福利类型")	
    @QueryField(name = "加班福利类型" )	
    private String rewardType;	
    /**	
     * 计算方式	
     */	
	@ApiModelProperty("计算方式")	
    @QueryField(name = "计算方式" )	
    private String calculationWay;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
	
}	
	
