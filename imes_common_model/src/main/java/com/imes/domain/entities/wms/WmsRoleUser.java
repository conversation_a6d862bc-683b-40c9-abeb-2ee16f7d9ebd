package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
	
@Data	
@ApiModel(value = "文档角色用户关联表")	
public class WmsRoleUser implements Serializable {	
	
    private static final long serialVersionUID = 544677817119765150L;	
	
	@ApiModelProperty("id")	
private String id;	
	
@ApiModelProperty(value = "角色编码")	
private String wmsRoleId;	
	
@ApiModelProperty(value = "用户工号")	
private String userCode;	
}	
