package com.imes.domain.entities.query.template.dev;		
		
import com.imes.domain.entities.query.model.base.*;		
import io.swagger.annotations.ApiModel;		
import io.swagger.annotations.ApiModelProperty;		
import lombok.Data;		
@ApiModel(value = "MSA内检计量任务列表高级查询模型")		
@Data		
@QueryModel(		
        name = "0469_task",		
        remark = "计量任务列表",		
        alias = "task",		
        searchApi = "/dev/msa/calibrationTask/findByQueryVo",
        msgAction = {MsgAction.SCHEDULED_TASK}
)
public class DevMsaTaskQueryVo extends BaseModel {		
		
    @ApiModelProperty(value = "任务编号")		
    @QueryField(name = "任务编号")		
    private String taskNo;		
		
    @ApiModelProperty(value = "计划名称")		
    @QueryField(name = "计划名称")		
    private String planName;		
		
    @ApiModelProperty(value = "计量器具类型")		
    @QueryField(name = "计量器具类型", alias = "item", type = Type.Select, option = {"计量标准器具", "计量标准器具", "计量工作器具", "计量工作器具"})		
    private String measuringDeviceType;		
		
    @ApiModelProperty(value = "检具名称")		
    @QueryField(name = "检具名称", alias = "item.measure_name")
    private String calibrationName;		
		
    @ApiModelProperty(value = "检具编码")		
    @QueryField(name = "检具编码")		
    private String calibrationCode;		
		

    @ApiModelProperty(value = "计量单位")		
    @QueryField(name = "计量单位", alias = "item", type = Type.Select, sqlOption = "select concat(TRIM(name), '/' ,TRIM(code)) as label , TRIM(code) as value from sys_unit")		
    private String parameterUnit;		
		
    @ApiModelProperty(value = "计量方式")		
    @QueryField(name = "计量方式", type = Type.Select, dictOption = "CALIBRATION_METHOD")		
    private String executeMethod;		
		
    @ApiModelProperty(value = "执行部门")		
    @QueryField(name = "执行部门")		
    private String executeDept;		
		
    @ApiModelProperty(value = "设备数量")		
    @QueryField(name = "设备数量")		
    private String deviceTotal;		
		
    @ApiModelProperty(value = "测量设备等级：请参考数据字典：EQUIP_LEVEL")		
    @QueryField(name = "测量设备等级", alias = "item", type = Type.Select, dictOption = "EQUIP_LEVEL")		
    private String equipmentLevel;		
		
    @ApiModelProperty(value = "待计量器具数量")		
    @QueryField(name = "待计量器具数量")		
    private String tobeNum;		
		
    @ApiModelProperty(value = "任务状态：0-待执行；1-执行中；3-已驳回；5-待全检")		
    @QueryField(name = "任务状态", type = Type.Select, option = {"0", "待执行", "1", "执行中", "3", "已驳回", "5", "待全检"})		
    private String status;		
		
    @ApiModelProperty(value = "是否超期：0-正常；1-已过期")		
    @QueryField(name = "是否超期", type = Type.Select, option = {"0", "正常", "1", "超期"})		
    private String overTime;		
		
    @ApiModelProperty(value = "合格数量")		
    @QueryField(name = "合格数量")		
    private String qualifiedNum;		
		
    @ApiModelProperty(value = "不合格数量")		
    @QueryField(name = "不合格数量")		
    private String disqualifiedNum;		
		
    @ApiModelProperty(value = "任务开始时间")		
    @QueryField(name = "任务开始时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")		
    private String taskStart;		
		
    @ApiModelProperty(value = "任务结束时间")		
    @QueryField(name = "任务结束时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")		
    private String taskEnd;		
		
    @ApiModelProperty(value = "送检方式：0-内检；1-外检")		
    @QueryField(name = "送检方式", type = Type.Select, option = {"0", "内检", "1", "外检"})		
    private String sendWay;		
		
    @ApiModelProperty(value = "任务类型：0-定时任务；2-临时任务")		
    @QueryField(name = "任务类型", type = Type.Select, option = {"0", "定时任务", "2", "临时任务"})		
    private String taskType;		
		
    @ApiModelProperty(value = "剩余天数")		
    @QueryField(name = "剩余天数")		
    private String remainDay;		
		
    @ApiModelProperty(value = "执行人")		
    @QueryField(name = "执行人")		
    private String executeUser;		
		
    @ApiModelProperty(value = "提交时间")		
    @QueryField(name = "提交时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")		
    private String commitTime;		
		
    @ApiModelProperty(value = "审核时间")		
    @QueryField(name = "审核时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")		
    private String approvalTime;		
		
    @ApiModelProperty(value = "审核人")		
    @QueryField(name = "审核人")		
    private String approvalUser;		
		
    @ApiModelProperty(value = "是否推送消息：0-未推送；1-已推送")		
    @QueryField(name = "是否推送消息", type = Type.Select, option = {"0", "未推送", "1", "已推送"})		
    private String pushSign;		
		
    @ApiModelProperty(value = "预警天数")		
    @QueryField(name = "预警天数")		
    private String warningNum;		
		
    @ApiModelProperty(value = "是否认领")		
    @QueryField(name = "是否认领", show = false)		
    private String claimSign;		
		
    @ApiModelProperty(value = "创建日期")		
    @QueryField(name = "创建日期", show = false, type = Type.Date, order = OrderBy.DESC)		
    private String createdOn;		
		
}		
