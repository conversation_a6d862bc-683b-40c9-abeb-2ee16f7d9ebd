package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《商机》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-27 17:10
 */
@ApiModel("《商机》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpBusinessQuery extends BaseQuery {
    @ApiModelProperty("单据编码")
    private String nid;
    @ApiModelProperty("单据编号(商机号)")
    private String bid;
    @ApiModelProperty("父单据编号")
    private String pBid;
    @ApiModelProperty("制单人工号")
    private String bUser;
    @ApiModelProperty("制单部门路径")
    private String bDept;
    @ApiModelProperty("销售员工号")
    private String gUser;
    @ApiModelProperty("销售部门路径")
    private String gDept;
    @ApiModelProperty("客户编号")
    private String gClient;
    @ApiModelProperty("客户名称")
    private String gcName;
    @ApiModelProperty("整单备注")
    private String vZdbz;
    @ApiModelProperty("数据生效标志")
    private Boolean effectStock;
    /*@ApiModelProperty("创建日期")
    private LocalDateTime createTime;
    @ApiModelProperty("生效日期")
    private LocalDateTime updateTime;*/
    @ApiModelProperty("项目名称")
    private String baXmMc;
    @ApiModelProperty("机器名称")
    private String baJqMc;
    @ApiModelProperty("销售阶段")
    private String baBusStep;
    @ApiModelProperty("商机星级")
    private String baBusLev;
    @ApiModelProperty("产品类别")
    private String baProType;
    @ApiModelProperty("商机类别")
    private String baBusProType;
    @ApiModelProperty("预计成交日期")
    private LocalDate bdDate;
    @ApiModelProperty("所属公司")
    private String cidName;
    @ApiModelProperty("制单人")
    private String bUserName;
    @ApiModelProperty("制单部门")
    private String bDeptName;
    private LocalDate createTime_s;
    private LocalDate createTime_e;
}
