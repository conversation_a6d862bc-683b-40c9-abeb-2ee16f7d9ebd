package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsHuanFaBulletinBoardVo {	
	
    @ApiModelProperty(name = "出库人")	
    private String createBy;	
	
    private String userName;	
	
    @ApiModelProperty(name = "包装申请数量")	
    private BigDecimal packApplyQty;	
	
    @ApiModelProperty(name = "实际出库包装数量")	
    private BigDecimal packOutStorageQty;	
	
    @ApiModelProperty(name = "退货总数")	
    private BigDecimal returnPackQty;	
	
    @ApiModelProperty(name = "出库进度")	
    private String outSchedule;	
	
    @ApiModelProperty(name = "年")	
    private String year;	
	
    @ApiModelProperty(name = "月")	
    private String month;	
	
    @ApiModelProperty(name = "日")	
    private String day;	
	
    @ApiModelProperty(name = "平均出库进度")	
    private String avgOutSchedule;	
	
    // 平均发货速度	
    private String avgSendSchedule;	
	
    // 发货数	
    private BigDecimal sendedQty;	
	
    // 总数	
    private BigDecimal allQty;	
	
    // 平均发货速度	
    private BigDecimal allPrice;	
}	
