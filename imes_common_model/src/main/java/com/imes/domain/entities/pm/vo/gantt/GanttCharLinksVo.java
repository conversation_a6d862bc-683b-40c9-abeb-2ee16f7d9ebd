package com.imes.domain.entities.pm.vo.gantt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<甘特图任务链接>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 15:15
 */
@ApiModel("甘特图数据链接VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GanttCharLinksVo {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("源头")
    private String source;
    @ApiModelProperty("目标")
    private String target;
    @ApiModelProperty("类型")
    private String type;
}
