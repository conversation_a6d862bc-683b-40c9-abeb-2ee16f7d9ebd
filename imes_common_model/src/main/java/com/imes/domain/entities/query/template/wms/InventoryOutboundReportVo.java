package com.imes.domain.entities.query.template.wms;

import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data	
@ApiModel("盘亏出库")	
@QueryModel(	
        name = "0427_report",
        remark = "盘亏出库-自定义报表",
        searchApi = "/wms/outStorage/findInventoryOutboundList",	
        alias = {"wms_storage_out_bill", "wms_storage_out_bill_item"},	
        showMode = true,
        report = true
)
public class InventoryOutboundReportVo extends BaseModel {
	@ApiModelProperty("出库单号")	
    @QueryField(name = "出库单号")
    @EditField
    private String storageOutCode;
	
	@ApiModelProperty("明细单号")	
    @QueryField(name = "明细单号", alias = "wms_storage_out_bill_item")
    @EditField(show = false)
    private String storageOutItemCode;	
	
	@ApiModelProperty("出库类型")	
    @QueryField(name = "出库类型", type = Type.MultiSelect, option = {"4","盘亏出库"}, value = "4", query = false)
    @EditField(show = false)
    private String receiptType;	
	
	@ApiModelProperty("盘点单号")	
    @QueryField(name = "盘点单号")
    @EditField(show = false)
    private String receiptCode;	
	
	@ApiModelProperty("出库日期")	
    @QueryField(name = "出库日期", type = Type.Date)
    @EditField(required = true)
    private String approveOn;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")
    @EditField(show = false)
    private String status;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "mat")
    @EditField(readonly = true)
    private String materialName;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "mat")
    @EditField(readonly = true)
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "mat")
    @EditField(readonly = true)
    private String specification;	
	
	@ApiModelProperty("基本单位数量")	
    @QueryField(name = "基本单位数量", alias = "wms_storage_out_bill_item", type = Type.Number)
    @EditField(readonly = true)
    private String orderQty;	
	
	@ApiModelProperty("出库基本数量")	
    @QueryField(name = "出库基本数量", alias = ".ifnull(wms_storage_out_bill_item.picked_qty, 0)", type = Type.Number)
    @EditField(readonly = true)
    private String pickedQty;	
	
	@ApiModelProperty("基本单位")	
    @QueryField(name = "基本单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String unit;
	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", alias = "wms_storage_out_bill_item", type = Type.Number)
    @EditField(readonly = true)
    private String packApplyQty;	
	
	@ApiModelProperty("实际出库数量")	
    @QueryField(name = "实际出库数量", alias = ".ifnull(wms_storage_out_bill_item.pack_out_storage_qty, 0)", type = Type.Number)
    @EditField(required = true)
    private String packOutStorageQty;	
	
	@ApiModelProperty("库存单位")	
    @QueryField(name = "库存单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String packCodeUnit;
	
	@ApiModelProperty("仓库")	
    @QueryField(name = "仓库", level = Level.Main, sort = false, alias = ".( SELECT wh.warehouse_name AS warehouse_name FROM sys_wh_warehouse wh WHERE wms_storage_out_bill.wh_code = wh.warehouse_code limit 1)")
    @EditField(required = true)
    private String warehouseName;
	
	@ApiModelProperty("操作人工号")	
    @QueryField(name = "操作人工号")
    @EditField
    private String applyBy;	
	
	@ApiModelProperty("操作人")	
    @QueryField(name = "操作人", alias = "u1.user_name", level = Level.Main)
    @EditField(show = false)
    private String applyName;	
	
	@ApiModelProperty("创建日期")	
    @QueryField(name = "创建日期", type = Type.Date, order = OrderBy.DESC, show = false)
    @EditField(show = false)
    private String createOn;	
	
	@ApiModelProperty("第三方系统单号")	
    @QueryField(name = "第三方系统单号")
    @EditField(show = false)
    private String thirdOrderCode;	
	
	@ApiModelProperty("物料辅助属性")	
    @QueryField(name = "物料辅助属性", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String skuCode;	
	
	@ApiModelProperty("批次")	
    @QueryField(name = "批次", alias = "task")
    @EditField
    private String batch;	
	
	@ApiModelProperty("操作部门")	
    @QueryField(name = "操作部门", alias = "c1.depart_name", level = Level.Main)
    @EditField
    private String departName;

    @ApiModelProperty("库位")
    @QueryField(name = "库位", alias = "wms_storage_out_bill_item")
    @EditField
    private String binCode;
}	
