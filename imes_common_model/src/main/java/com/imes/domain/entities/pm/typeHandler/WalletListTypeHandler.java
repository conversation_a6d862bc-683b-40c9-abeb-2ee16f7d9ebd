package com.imes.domain.entities.pm.typeHandler;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.CollectionType;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<集合类型处理器>>
 * @company 捷创智能技术有限公司
 * @create 2022-05-17 10:28
 */
public class WalletListTypeHandler extends JacksonTypeHandler {
    private final Class<?> type;

    public WalletListTypeHandler(Class<?> type) {
        super(type);
        this.type= type;
    }


    @Override
    protected Object parse(String json) {
        try {
            ObjectMapper objectMapper = getObjectMapper();
            CollectionType collectionType = objectMapper.getTypeFactory().constructCollectionType(List.class, type);
            return objectMapper.readValue(json, collectionType);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
