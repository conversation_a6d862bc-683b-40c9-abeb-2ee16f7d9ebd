package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * (DevBreakOff)实体类
 *
 * <AUTHOR>
 * @since 2024-03-27 11:01:48
 */
@ApiModel(value = "设备断连信息表")
@Data
public class DevBreakOff implements Serializable {
    private static final long serialVersionUID = 489079053922425113L;

    private String id;

    @ApiModelProperty(value = "设备编码")
    private String devCode;

    @ApiModelProperty(value = "状态1-正常，2-异常")
    private Integer status;

    @ApiModelProperty(value = "信息")
    private String info;

    @ApiModelProperty(value = "详细信息")
    private String detailInfo;

    @ApiModelProperty(value = "发生时间")
    private Date time;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remark;

}

