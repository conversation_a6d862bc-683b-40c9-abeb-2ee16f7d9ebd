package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr考勤出差单记录表(HrAttendanceBusiness)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-05-04 12:35:37	
 */	
@Data	
@ApiModel("考勤出差单管理")	
@QueryModel(name = "0786",	
        remark = "考勤出差单管理",	
        alias = "hr_attendance_business",	
        searchApi = "/api/hr/attendance/extend/business/query")	
public class HrAttendanceBusinessQueryVo extends BaseModel {	
	
    /**	
     * 出差单号	
     */	
	@ApiModelProperty("出差单号")	
    @QueryField(name = "出差单号" )	
    private String businessNo;	
    /**	
     * 员工编号	
     */	
	@ApiModelProperty("员工编号")	
    @QueryField(name = "员工编号" )	
    private String userCode;	
    /**	
     * 行政组织	
     */	
	@ApiModelProperty("行政组织编号")	
    @QueryField(name = "行政组织编号" )	
    private String deptCode;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态" )	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
	
	@ApiModelProperty("员工姓名")	
    @QueryField(name = "员工姓名" )	
    private String userName;	
	
	@ApiModelProperty("部门名称")	
    @QueryField(name = "部门名称" )	
    private String deptName;	
}	
	
