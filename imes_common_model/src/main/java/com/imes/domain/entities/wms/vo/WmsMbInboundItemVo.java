package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsParts;	
import com.imes.domain.entities.wms.WmsStorageInBillItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
/**	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-03-17 14:53	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMbInboundItemVo extends WmsStorageInBillItem {	
	
    /**	
     * 入库任务主键（多个","分割）	
     */	
	@ApiModelProperty("入库任务主键（多个分割）")
    private String aoTaskCode;

    /**
     * 退货任务单号
     */
    private String stoCode;
	
    /**	
     * 物料条码	
     */	
	@ApiModelProperty("物料条码")	
    private String barCode;	
	
    /**	
     * 是否有序列号(0-否（缺省） 1-是)	
     */	
	@ApiModelProperty("是否有序列号(0-否（缺省） 1-是)")	
    private String isSerial;	
	
    /**	
     * 推荐库位	
     */	
	@ApiModelProperty("推荐库位")	
    private String recommendBinCode;	
	
    /**	
     * 上架仓库	
     */	
	@ApiModelProperty("上架仓库")	
    private String whCode;	
	
    /**	
     * 上架仓库	
     */	
	@ApiModelProperty("上架仓库")	
    private String whName;	
	
    /**	
     * 上架库位	
     */	
	@ApiModelProperty("上架库位")	
    private String binName;	
	
    /**	
     * 任务状态	
     */	
	@ApiModelProperty("任务状态")	
    private String taskStatus;	
	
    /**	
     * 已上架数量	
     */	
	@ApiModelProperty("已上架数量")	
    private BigDecimal putQty;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date productionDate;	
	
    /**	
     * 库位库存	
     */	
	@ApiModelProperty("库位库存")	
    private List<WmsMbStockBinVo> stockBinVos;	
	
    /**	
     * SN码	
     */	
	@ApiModelProperty("SN码")	
    private List<WmsParts> partsList;	
	
    /***********页面控制所需参数*************/	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean active;	
	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean show;	
	
    /**	
     * 库位库存	
     */	
	@ApiModelProperty("库位库存")	
    private List<WmsMbStockBinVo> btmList;	
	
    /**	
     * 库存状态	
     */	
	@ApiModelProperty("库存状态")	
    private String stockStatus;	
	
    /**	
     * 二级分类	
     */	
	@ApiModelProperty("二级分类")	
    private String purpose;	
	
	
}	
