package com.imes.domain.entities.pm.dto;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-18 16:44
 */
@ApiModel("项目历史数据类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectHistoryDataDto implements Serializable {
    private static final long serialVersionUID = 8726787558178372556L;

    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "项目id不能为空", groups = ProjectHistoryDataDto.UpdateGroup.class)
    @Null(message = "项目id必须为空", groups = ProjectHistoryDataDto.AddGroup.class)
    private String id;

    /**
     * 业务id
     */
    @ApiModelProperty("业务Id")
    private String businessId;

    /**
     * 创建时间
     */
    @ApiModelProperty("修改时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    // @Null(message = "创建时间必须为空")
    private LocalDateTime createTime;

    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    //@NotNull(message = "版本号不能为空", groups = ProjectPieChartDto.AddGroup.class)
    private Integer version;

    /**
     * 版本号
     */
    @ApiModelProperty("历史数据")
    //@NotNull(message = "系列不能为空", groups = ProjectPieChartDto.AddGroup.class)
    private String data;

    @ApiModelProperty("实体")
    private Object object;


    /**
     *创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建人名
     */
    @ApiModelProperty("创建人名")
    private String creator;

}
