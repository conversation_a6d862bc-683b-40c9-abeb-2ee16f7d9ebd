package com.imes.domain.entities.po.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 采购订单变更单(PoPurchaseStandardChange)实体类
 *
 * <AUTHOR>
 * @since 2023-08-12 11:50:21
 */
@Data
@ApiModel("采购订单变更单主单")
public class PoPurchaseStandardChange implements Serializable {
    private static final long serialVersionUID = 114829502087523574L;

    private String id;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "采购变更单号")
    private String changeNo;

    @ApiModelProperty(value = "采购类型")
    private String purchaseType;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "采购部门")
    private String demandDepCode;

    @ApiModelProperty(value = "采购部门名称")
    private String demandDepName;

    @ApiModelProperty(value = "采购员工号")
    private String demandUserCode;

    @ApiModelProperty(value = "采购员名称")
    private String demandUserName;

    @ApiModelProperty(value = "制单人")
    private String operatorName;

    @ApiModelProperty(value = "变更原因")
    private String changeReason;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "单据来源")
    private String docSource;

    @ApiModelProperty(value = "来源单号")
    private String docCode;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "采购日期")
    private Date orderDate;

    @ApiModelProperty(value = "单据状态 10-已录入,20-审核中,30-已审核")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

}

