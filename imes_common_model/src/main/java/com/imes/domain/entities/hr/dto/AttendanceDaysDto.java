package com.imes.domain.entities.hr.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 组织与员工关系表(DeptUser)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-29 14:57:51
 */

@NoArgsConstructor
@Data
public class AttendanceDaysDto   {

    private Integer workingDays;
    /**
     * 上班时间限制
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "HH:mm:ss")
    private Date workTime;
    /**
     * 下班时间限制
     */
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "HH:mm:ss")
    private Date afterworkTime;

}

