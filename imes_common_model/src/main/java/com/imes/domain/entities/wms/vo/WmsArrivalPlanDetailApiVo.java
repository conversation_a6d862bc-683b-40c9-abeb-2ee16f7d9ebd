package com.imes.domain.entities.wms.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imes.domain.entities.wms.WmsArrivalPlanItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WmsArrivalPlanDetailApiVo extends WmsArrivalPlanItem {

    /**
     * 到货计划类型：采购到货、销售退货到货
     */
    @JSONField(name = "到货计划类型")
    @ApiModelProperty("到货计划类型：采购到货、销售退货到货")
    private String orderType;

    /**
     * 客户编码
     */
    @JSONField(name = "客户编码")
    @ApiModelProperty("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @JSONField(name = "客户名称")
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 操作人编码
     */
    @JSONField(name = "操作人编码")
    @ApiModelProperty("操作人编码")
    private String operatorCode;

    /**
     * 操作人姓名
     */
    @JSONField(name = "操作人姓名")
    @ApiModelProperty("操作人姓名")
    private String operatorName;

    /**
     * 计划下单日期
     */
    @JSONField(name = "计划下单日期")
    @ApiModelProperty("计划下单日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderDate;

    /**
     * 预计到货日期
     */
    @JSONField(name = "预计到货日期")
    @ApiModelProperty("预计到货日期")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dueArrivalDate;

    /**
     * 部门编号
     */
    @JSONField(name = "部门编号")
    @ApiModelProperty("部门编号")
    private String depCode;

    /**
     * 部门名称
     */
    @JSONField(name = "部门名称")
    @ApiModelProperty("部门名称")
    private String depName;

    /**
     * 供应商编码
     */
    @JSONField(name = "供应商编码")
    @ApiModelProperty("供应商编码")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @JSONField(name = "供应商名称")
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 物流单号
     */
    @JSONField(name = "物流单号")
    @ApiModelProperty("物流单号")
    private String logisticCode;

    /**
     * 物流类型（101：快递；102：货运；103：客户自提；104：送货上门；105：其它；106：月台预约）
     */
    @JSONField(name = "物流类型")
    @ApiModelProperty("物流类型）")
    private String logisticType;

    /**
     * 物流公司编码
     */
    @JSONField(name = "物流公司编码")
    @ApiModelProperty("物流公司编码")
    private String shipperCode;

    /**
     * 物流公司名称
     */
    @JSONField(name = "物流公司名称")
    @ApiModelProperty("物流公司名称")
    private String shipperName;

    /**
     * 车牌号
     */
    @JSONField(name = "车牌号")
    @ApiModelProperty("车牌号")
    private String licensePlate;

    /**
     * 送货司机
     */
    @JSONField(name = "送货司机")
    @ApiModelProperty("送货司机")
    private String deliveryDriver;

    /**
     * 司机联系方式
     */
    @JSONField(name = "司机联系方式")
    @ApiModelProperty("司机联系方式")
    private String driverPhone;

    /**
     * 司机证件号
     */
    @JSONField(name = "司机证件号")
    @ApiModelProperty("司机证件号")
    private String idCard;

    /**
     * 车辆类型
     */
    @JSONField(name = "车辆类型")
    @ApiModelProperty("车辆类型")
    private String carType;

    /**
     * 收货仓库编码
     */
    @JSONField(name = "收货仓库编码")
    @ApiModelProperty("收货仓库编码")
    private String whCode;

    /**
     * 收货仓库名称
     */
    @JSONField(name = "收货仓库名称")
    @ApiModelProperty("收货仓库名称")
    private String whName;

    /**
     * 月台号
     */
    @JSONField(name = "月台号")
    @ApiModelProperty("月台号")
    private String platformCode;

    /**
     * 月台名称
     */
    @JSONField(name = "月台名称")
    @ApiModelProperty("月台名称")
    private String platformName;

    /**
     * 单据状态 10-已录入,15-审核中,20-已生效,50-已完成
     */
    @JSONField(name = "单据状态")
    @ApiModelProperty("单据状态 10-已录入,15-审核中,20-已生效,50-已完成")
    private String planStatus;

    /**
     * 主单备注
     */
    @JSONField(name = "主单备注")
    @ApiModelProperty("主单备注")
    private String mainRemarks;

    /**
     * 主单业务状态 10-正常 20-已关闭
     */
    @JSONField(name = "主单业务状态 10-正常 20-已关闭")
    @ApiModelProperty("主单业务状态 10-正常 20-已关闭")
    private String mainBusinessStatus;

    /**
     * 收货月台
     */
    @JSONField(name = "收货月台")
    @ApiModelProperty("收货月台")
    private String receiveDock;

}
