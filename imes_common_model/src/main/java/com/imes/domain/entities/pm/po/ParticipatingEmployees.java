package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<参与员工>>
 * @company 捷创智能技术有限公司
 * @create 2020-10-26 17:28
 */
@Data
public class ParticipatingEmployees {
    private String id;
    private String code;
    private String name;
    private String task;
    @TableField(fill = FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;
}
