package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMoveOrder implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 移库单号	
     */	
	@ApiModelProperty("移库单号")	
    private String moCode;	
	
    /**	
     * 单据类型（库内移库单、调拨单）	
     */	
	@ApiModelProperty("单据类型（库内移库单、调拨单）")	
    private Integer orderType;	
	
    /**	
     * 单据来源（手工建单、第三方系统）	
     */	
	@ApiModelProperty("单据来源（手工建单、第三方系统）")	
    private Integer source;	
	
    /**	
     * 第三方单据号	
     */	
	@ApiModelProperty("第三方单据号")	
    private String thirdOrderCode;	
	
    /**	
     * 单据状态（未完成、已审核、已完成、已取消）	
     */	
	@ApiModelProperty("单据状态（未完成、已审核、已完成、已取消）")	
    private Integer status;	
	
    /**	
     * 移出仓库	
     */	
	@ApiModelProperty("移出仓库")	
    private String fromWhCode;	
	
    /**	
     * 移入仓库	
     */	
	@ApiModelProperty("移入仓库")	
    private String toWhCode;	
	
    /**	
     * 关联入库单号	
     */	
	@ApiModelProperty("关联入库单号")	
    private String storageInCode;	
	
    /**	
     * 关联出库单号	
     */	
	@ApiModelProperty("关联出库单号")	
    private String storageOutCode;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    private String userCode;	
	
//    private String materialCode;
//
//
//    private String materialName;
//
//
//    private String materialMarker;
//
//    private String specification;
//
//    private String batch;
//
//    private String outBinCode;
//
//    private String inBinCode;
//
//    private BigDecimal orderQty;
//
//    private BigDecimal availableQty;
//
//    private String unit;
//
    private String userName;
//
    private String applyName;
	
    /**	
     * 移出仓库编码	
     */	
	@ApiModelProperty("移出仓库编码")	
    private String fromCompanyCode;	
	
    /**	
     * 移出仓库名称	
     */	
	@ApiModelProperty("移出仓库名称")	
    private String fromCompanyName;	
    /**	
     * 移入仓库名称	
     */	
	@ApiModelProperty("移入仓库名称")	
    private String toCompanyCode;	
    /**	
     * 移入仓库名称	
     */	
	@ApiModelProperty("移入仓库名称")	
    private String toCompanyName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 流程id	
     */	
	@ApiModelProperty("流程id")	
    private String activityId;	
	
    private List<WmsMoveOrderItem> itemList;	
	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 调拨确认状态	
     */	
	@ApiModelProperty("调拨确认状态")	
    private Integer confirmStatus;	
	
    /**	
     * 调拨确认时间	
     */	
	@ApiModelProperty("调拨确认时间")	
    private Date confirmOn;	
	
    /**	
     * 调拨确认人	
     */	
	@ApiModelProperty("调拨确认人")	
    private String confirmBy;	
	
    /**	
     * 调拨审核工作流最终处理用户	
     */	
	@ApiModelProperty("调拨审核工作流最终处理用户")	
    private String auditUserCode;	
	
    /**	
     * 调出仓库名称	
     */	
	@ApiModelProperty("调出仓库名称")	
    private String fromWhName;	
	
    /**	
     * 调入仓库名称	
     */	
	@ApiModelProperty("调入仓库名称")	
    private String toWhName;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
	
    /**	
     * 审核按钮显示	
     */	
	@ApiModelProperty("审核按钮显示")	
    private boolean showAuditBtn;	
	
    private String skuCode;	
	
    private String boxNo;	
	
    /*****工作流信息*******/	
	
    private String taskId;	
	
    private String taskType;	
	
    private String businessKey;	
	
    private String processInstanceName;	
	
    /**	
     * 区分PC端还是移动端审核 1:PC端 2:移动端	
     */	
	@ApiModelProperty("区分PC端还是移动端审核 1:PC端 2:移动端")	
    private String flag;	
	
    /**	
     * 移动端审核调入库位是否可以下拉选择	
     */	
	@ApiModelProperty("移动端审核调入库位是否可以下拉选择")	
    private boolean showSelect;	
	
    /**	
     * 包装数量	
     */	
	@ApiModelProperty("包装数量")	
    private BigDecimal packQty;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
}	
