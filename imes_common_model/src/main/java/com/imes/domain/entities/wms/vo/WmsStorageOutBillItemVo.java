package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPickTask;	
import com.imes.domain.entities.wms.WmsStorageOutBillItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageOutBillItemVo extends WmsStorageOutBillItem {	
	
    /**	
     * 仓库号编码	
     */	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    /**	
     * 仓库号编码	
     */	
    @ApiModelProperty(value = "仓库号名称")	
    private String whName;	
	
    /**	
     * 存储区编码	
     */	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    /**	
     * 仓位编码	
     */	
    @ApiModelProperty(value = "仓位编码")	
    private String binCode;	
	
    /**	
     * 存储单元	
     */	
    @ApiModelProperty(value = "存储单元")	
    private String cellCode;	
	
    /**	
     * 批次号	
     */	
    @ApiModelProperty(value = "批次号")	
    private String batch;	
	
    /**	
     * 拣货数量	
     */	
    @ApiModelProperty(value = "拣货数量")	
    private BigDecimal qty;	
	
    /**	
     * 批次号	
     */	
    @ApiModelProperty(value = "条形码")	
    private String barCode;	
	
	
    /**	
     * 前端提交 序列号（SN码）	
     */	
	@ApiModelProperty("前端提交 序列号（SN码）")	
    private List<WmsBusTaskHistoryVo> serialNumbers;	
	
    private List<WmsPickTask> taskList;	
	
}	
