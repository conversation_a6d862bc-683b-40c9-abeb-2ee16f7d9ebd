package com.imes.domain.entities.pm.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<部门员工树>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-30 9:43
 */
@ApiModel("部门员工树")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeptTreeVo {
    private String code;
    private String label;
    private String pCode;
    private String departType;
    //是否部门1是0否
    private String isDept;
    // 是否离职
    private Boolean resign;
    // 是否分配账号
    private Boolean allocation;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<DeptTreeVo> children;
}
