package com.imes.domain.entities.crm.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.google.common.collect.Maps;
import com.imes.domain.entities.crm.enums.ApprovalStatusEnum;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * (Contract)表实体类
 *
 * <AUTHOR>
 * @since 2022-09-08 13:53:37
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_contract", resultMap = "BaseResultMap")
public class Contract extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -19032301766674966L;
    //id
    private String id;
    //编号
    @TableField(fill = FieldFill.INSERT, condition = SqlCondition.LIKE)
    private String code;
    //名称
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    //类型
    private String type;
    //合同开始日期
    private LocalDate startDate;
    //合同结束日期
    private LocalDate endDate;
    //金额
    private BigDecimal amount;
    //客户id
    private String customerId;
    //客户名称
    @TableField(exist = false)
    private String customerName;
    //商机id
    private String businessId;
    //商机名称
    @TableField(exist = false)
    private String businessName;
    //所属企业
    private String enterprise;
    //联系人id
    private String contactId;
    //联系人姓名
    @TableField(exist = false)
    private String contactName;
    //客户签署人
    private String customerSignatory;
    //客户签署人姓名
    @TableField(exist = false)
    private String customerSignatoryName;
    //公司签署人
    private String companySignatory;
    //公司签署人姓名
    @TableField(exist = false)
    private String companySignatoryName;
    //付款方式
    private String payMethod;
    //备注
    private String remarks;
    //审核状态（0：待审核 , 1：审核通过，2：审批中）
    private ApprovalStatusEnum approvalStatus;
    /**
     * 自定义字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }
}

