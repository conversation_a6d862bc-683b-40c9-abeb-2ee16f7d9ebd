package com.imes.domain.entities.po.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 采购订单明细变更单(PoPurchaseDetailStandardChange)实体类
 *
 * <AUTHOR>
 * @since 2023-08-12 11:50:23
 */
@Data
@ApiModel("采购订单变更单明细")
public class PoPurchaseDetailStandardChange implements Serializable {
    private static final long serialVersionUID = 892582617093025976L;

    private String id;

    @ApiModelProperty(value = "主单Id")
    private String mainId;

    @ApiModelProperty(value = "子订单号")
    private String sdNo;

    @ApiModelProperty(value = "子订单号")
    private String changeType;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "材质")
    private String quality;

    @ApiModelProperty(value = "型号")
    private String materialMarker;

    @ApiModelProperty(value = "源单行号")
    private String lineNo;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "原采购数量")
    private BigDecimal oldQty;

    @ApiModelProperty(value = "采购单位")
    private String unit;

    @ApiModelProperty(value = "基础单位")
    private String baseUnit;

    @ApiModelProperty(value = "基础单位数量")
    private BigDecimal baseUnitQty;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @TableField(updateStrategy = FieldStrategy.IGNORED)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "原备注")
    private String oldRemarks;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "原单价")
    private BigDecimal oldSinglePrice;

    @ApiModelProperty(value = "净价")
    private BigDecimal netPrice;

    @ApiModelProperty(value = "折扣方式")
    private String discountType;

    @ApiModelProperty(value = "折扣额")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "不税额金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "价税合计(总金额)")
    private BigDecimal allPrice;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "到货时间")
    private Date deliveryDate;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "原到货时间")
    private Date oldDeliveryDate;

    @ApiModelProperty(value = "源单类型")
    private String requestDocType;

    @ApiModelProperty(value = "源单单号")
    private String requestDocCode;

    @ApiModelProperty(value = "源单申请部门编码")
    private String requestDocDepCode;

    @ApiModelProperty(value = "源单申请部门名称")
    private String requestDocDepName;

    @ApiModelProperty(value = "源单申请人工号")
    private String requestDocPersonCode;

    @ApiModelProperty(value = "源单申请人名称")
    private String requestDocPersonName;

    @ApiModelProperty(value = "税率")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal taxRate;

    @ApiModelProperty(value = "税率编码")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String taxCode;

    @ApiModelProperty(value = "原税率")
    private BigDecimal oldTaxRate;

    @ApiModelProperty(value = "税率编码")
    private String oldTaxCode;

    @ApiModelProperty(value = "折扣率")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal discountRate;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    @ApiModelProperty(value = "辅助属性")
    private String skuCode;

    @ApiModelProperty(value = "原辅助属性")
    private String oldSkuCode;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

}

