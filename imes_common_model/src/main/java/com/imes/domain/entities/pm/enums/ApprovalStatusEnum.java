package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.imes.domain.entities.crm.enums.BusinessClosingEnum;
import org.apache.ibatis.type.Alias;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<审批状态枚举>>
 * @company 捷创智能技术有限公司
 * @create 2022-04-06 10:39
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Alias("perApprovalStatusEnum")
public enum ApprovalStatusEnum {

    NOT_APPROVED(0,"未审批"),
    APPROVED(1,"已审批"),
    UNDER_APPROVED(2,"审批中"),
    REJECT(3,"驳回");

    @EnumValue
    private final Integer approvalStatus;

    private final String approvalName;

    ApprovalStatusEnum(Integer approvalStatus, String approvalName) {
        this.approvalStatus = approvalStatus;
        this.approvalName = approvalName;
    }

    public Integer getApprovalStatus() {
        return approvalStatus;
    }

    public String getApprovalName() {
        return approvalName;
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static BusinessClosingEnum des(final JsonNode jsonNode) {
        int offState = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("approvalStatus")).map(JsonNode::asInt).orElse(-1);
        if(offState == -1){
            return null;
        }
        return BusinessClosingEnum.match(offState);
    }

    public static ApprovalStatusEnum match(Integer approvalStatus) {
        return Arrays.stream(ApprovalStatusEnum.values())
                .filter(e -> e.getApprovalStatus().equals(approvalStatus))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("审批状态错误"));
    }

}
