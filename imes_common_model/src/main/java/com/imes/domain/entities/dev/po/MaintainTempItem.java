package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@ApiModel(value = "保养模板保养项表")
@Data
public class MaintainTempItem implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 模板id
     */
    @ApiModelProperty(value = "模板编码")
    private String tempNo;

    /**
     * 设备代码
     */
    @ApiModelProperty(value = "设备编码")
    private String devNo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdOn;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createdBy;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedOn;

    /**
     * 保养项信息编码
     */
    @ApiModelProperty(value = "保养项信息编码")
    private String itemInfoNo;


    private static final long serialVersionUID = 1L;
}