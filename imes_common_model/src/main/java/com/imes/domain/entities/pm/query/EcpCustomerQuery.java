package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《客户信息》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-28 14:45
 */
@ApiModel("《客户信息》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpCustomerQuery extends BaseQuery {
    private String guid;
    private String sUser;
    private LocalDateTime createTime;
    private LocalDate createTime_s;
    private LocalDate createTime_e;
    private Boolean deleted;
}
