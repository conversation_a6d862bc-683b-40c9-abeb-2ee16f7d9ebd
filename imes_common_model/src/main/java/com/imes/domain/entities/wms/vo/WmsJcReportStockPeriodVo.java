package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportStockPeriodVo {	
	
	
    /**	
     * 入库日期	
     */	
    @ApiModelProperty("入库日期")	
    private Date inboundDate;	
	
    /**	
     * 库存数量	
     */	
    @ApiModelProperty("库存数量")	
    private BigDecimal qty;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal amount;	
	
    /**	
     * 未出库区间	
     */	
    @ApiModelProperty("未出库区间")	
    private String period;	
	
}	
