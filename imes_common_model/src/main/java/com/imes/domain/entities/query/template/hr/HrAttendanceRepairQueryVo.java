package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr考勤补签卡记录表(HrAttendanceRepair)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-26 15:38:27	
 */	
	
@Data	
@ApiModel("考勤补签卡管理")	
@QueryModel(name = "0788",	
        remark = "考勤补签卡管理",	
        alias = "hr_attendance_repair",	
        searchApi = "/api/hr/attendance/extend/repair/query")	
public class HrAttendanceRepairQueryVo extends BaseModel {	
    private static final long serialVersionUID = 848395228932209478L;	
	
    /**	
     * 员工编号	
     */	
	@ApiModelProperty("员工编号")	
    @QueryField(name = "员工编号" )	
    private String userCode;	
    /**	
     * 考勤日期	
     */	
	@ApiModelProperty("考勤日期")	
    @QueryField(name = "考勤日期", type = Type.Date)	
    private String attendanceDays;	
    /**	
     * 补卡类型	
     */	
	@ApiModelProperty("补卡类型")	
    @QueryField(name = "补卡类型" )	
    private String type;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 名称	
     */	
	@ApiModelProperty("员工名称")	
    @QueryField(name = "员工名称")	
    private String userName;	
	
}	
	
