package com.imes.domain.entities.qms.vo;

import com.imes.domain.entities.qms.po.AqlInfo;
import com.imes.domain.entities.qms.po.AqlScheme;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsWorkExceptionAddDataVo implements Serializable {
	
    private String  projactInfo;
    private String  productNo;
    private String  productName;
    private String  productShortName;
    private String  build;
    private String  partType;
    private String  orderNo;
    private String  orderId;
    private Integer ing;
    private Integer  finish;
    private List<String> ingIdList;
    private List<String> finishIdList;


    private static final long serialVersionUID = 1L;	
}	
