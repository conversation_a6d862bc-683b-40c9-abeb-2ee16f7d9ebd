package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<完成率计算方式>>
 * @company 捷创智能技术有限公司
 * @create 2022-11-02 14:01
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum CalculationEnum {
    AUTO(0, "自动计算"),
    MANUAL(1, "手动计算");
    @EnumValue
    public final Integer calculation;
    public final String calculationName;

    CalculationEnum(Integer calculation, String calculationName) {
        this.calculation = calculation;
        this.calculationName = calculationName;
    }

    public static CalculationEnum match(Integer calculation) {
        return Arrays.stream(CalculationEnum.values())
                .filter(e -> e.getCalculation().equals(calculation))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("计算方式错误"));
    }

    public Integer getCalculation() {
        return calculation;
    }

    public String getCalculationName() {
        return calculationName;
    }


    @Override
    public String toString() {
        return this.getCalculationName();
    }
}
