package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "仓库拆装箱单明细表")	
public class WmsPackingItem implements Serializable {	
	
    private static final long serialVersionUID = -68434360814987039L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "装箱单号")	
    private String packNo;	
	
    @ApiModelProperty(value = "关联明细单号")	
    private String receiptItemCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "型号")	
    private String modelMarker;	
	
    @ApiModelProperty(value = "箱数")	
    private Integer boxNum;	
	
    @ApiModelProperty(value = "产品批次")	
    private String batch;	
	
    @ApiModelProperty(value = "基础单位")	
    private String primaryUnitCode;	
	
    @ApiModelProperty(value = "关联明细单数量（基本数量）")	
    private BigDecimal receiptItemQty;	
	
    @ApiModelProperty(value = "已装箱数量（基本单位）")	
    private BigDecimal qty;	
	
    @ApiModelProperty(value = "sku编码")	
    private String skuCode;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "备注")	
    private String remarks;	
	
    @ApiModelProperty(value = "自定义字段")	
    private String custom;	
	
    @ApiModelProperty(value = "包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "散装数量")	
    private BigDecimal unBoxQty;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
}	
