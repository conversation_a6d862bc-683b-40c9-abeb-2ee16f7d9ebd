package com.imes.domain.entities.ppc.po;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel(value = "生产巡视计划")
public class PpcPatrolTaskTable implements Serializable {

    private static final long serialVersionUID = 551785327127084769L;

    private String id;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "任务单号")
    private String taskNo;

    @ApiModelProperty(value = "head编码")
    private String itemCode;

    @ApiModelProperty(value = "head名称")
    private String itemName;

    @ApiModelProperty(value = "值")
    private String itemValue;

    @ApiModelProperty(value = "字段类型")
    private String fieldType;

    @ApiModelProperty(value = "下拉类型")
    private String optionType;

    @ApiModelProperty(value = "下拉选项")
    private String optionValue;

    @ApiModelProperty(value = "上级head编码")
    private String parentCode;

    @ApiModelProperty(value = "数据层级")
    private Integer level;

    @ApiModelProperty(value = "显示顺序")
    private Integer showOrder;

    @ApiModelProperty(value = "表单编码")
    private String tableCode;

    @ApiModelProperty(value = "实时点位")
    private String realTimePoint;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private Date createdOn;

    @ApiModelProperty(value = "更新时间")
    private Date updatedOn;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 下拉选项
     */
    private List<Map<String, Object>> option;
}