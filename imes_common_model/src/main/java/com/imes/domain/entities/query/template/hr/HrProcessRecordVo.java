package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("员工异动")
@QueryModel(name = "0666", remark = "员工异动", alias = "process", searchApi = "/api/hr/process/query")	
    public class HrProcessRecordVo  extends BaseModel {	
    /**	
     * 用户编码	
     */	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码")	
    private String userCode;	
    /**	
     * 部门编码	
     */	
	@ApiModelProperty("部门编码")	
    @QueryField(name = "部门编码")	
    private String deptCode;	
    /**	
     * 异动类型	
     */	
	@ApiModelProperty("异动类型")	
    @QueryField(name = "异动类型")	
    private String type;	
    /**	
     * 异动描述	
     */	
	@ApiModelProperty("异动描述")	
    @QueryField(name = "异动描述")	
    private String describes;	
    /**	
     * 生效时间	
     */     	
	@ApiModelProperty("生效时间")	
    @QueryField(name = "生效时间", type = Type.Date)	
    private String effectTime;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称")	
    private String userName;	
	@ApiModelProperty("部门名称")	
    @QueryField(name = "部门名称")	
    private String deptName;	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称")	
    private String jobsName;	
	
	
	
	
}	
