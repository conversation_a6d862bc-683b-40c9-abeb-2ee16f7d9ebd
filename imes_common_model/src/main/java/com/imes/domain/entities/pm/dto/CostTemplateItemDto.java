package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (CostTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2021-06-21 17:03:00
 */
@ApiModel("成本模板项实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CostTemplateItemDto  implements Serializable {
    private static final long serialVersionUID = -99749438963675240L;
    public interface AddGroup {
    }

    public interface UpdateGroup {
    }
    /**
    * id
    */
    @ApiModelProperty("id")
    private String id;
    /**
     * 模板id
     */
    @ApiModelProperty("模板id")
    private String templateId;
    /**
     * 税收种类编号
     */
    @ApiModelProperty("税收种类编号")
    private String code;
    /**
    * 内容
    */
    @ApiModelProperty("内容")
    private String content;
    /**
    * 描述
    */
    @ApiModelProperty("描述")
    private String description;
    /**
    * 预估金额
    */
    @ApiModelProperty("预估金额")
    private BigDecimal estimatedMoney;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;

}