package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;

import java.util.Arrays;
import java.util.Optional;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum BugState {
    NOT_PROCESSED("notProcessed", "未处理"),
    PROCESSING("processing", "修复中"),
    FIXED("fixed", "已修复"),
    CLOSED("closed", "关闭"),
    VERIFIED("verified", "已验证"),
    REJECTED("rejected", "已拒绝"),
    REOPEN("reopen", "重新打开");
    @EnumValue
    public final String state;
    public final String stateName;

    public static BugState match(String state) {
        return Arrays.stream(BugState.values())
                .filter(e -> e.getState().equals(state))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("状态错误"));
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static BugState des(final JsonNode jsonNode) {
        String type = jsonNode.isTextual() ? jsonNode.asText() : Optional.ofNullable(jsonNode.get("state")).map(JsonNode::asText).orElse(null);
        if (type == null) {
            throw new IllegalArgumentException("状态错误");
        }
        return match(type);
    }

    BugState(String state, String stateName) {
        this.state = state;
        this.stateName = stateName;
    }

    public String getState() {
        return state;
    }

    public String getStateName() {
        return stateName;
    }
}
