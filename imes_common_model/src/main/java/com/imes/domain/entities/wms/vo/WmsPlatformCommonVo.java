package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.util.List;	
	
//用于月台相关的公用类	
@Data	
public class WmsPlatformCommonVo {	
    //唯一键	
    private String uniqueCode;	
    //排序号	
    private String sort;	
    //日期	
    private String registerDate;	
    //仓库	
    private String warehouseCode;	
    //仓库	
    private String warehouseName;	
    //月台号	
    private String platformCode;	
    //月台名称	
    private String platformName;	
    //预约数	
    private int yyNum;	
    //预约情况	
    private String status;	
    //预约情况	
    private String statusName;	
    //预约情况	
    private List<String> yyStatusLists;	
}	
