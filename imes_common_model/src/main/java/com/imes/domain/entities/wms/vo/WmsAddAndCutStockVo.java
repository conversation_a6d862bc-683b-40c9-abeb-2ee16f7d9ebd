package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.ppc.po.Promaterial;
import com.imes.domain.entities.system.wms.SysWmsBin;
import com.imes.domain.entities.wms.WmsStockBin;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data	
public class WmsAddAndCutStockVo {	
	
    /**	
     * 扣除库存需要传入的扣除库位库存列表	
     */	
	@ApiModelProperty("扣除库存需要传入的扣除库位库存列表")
    private List<WmsStockBin> stockBinList;
	
    /**	
     * 出入库物料对象	
     */	
	@ApiModelProperty("出入库物料对象")	
    private Promaterial material;	
	
    /**	
     * 增加库存需要传入的库位信息	
     */	
	@ApiModelProperty("增加库存需要传入的库位信息")	
    private SysWmsBin wmsBin;	
	
    /**	
     * 增加库存需要传入的批次信息	
     */	
	@ApiModelProperty("增加库存需要传入的批次信息")	
    private String batch;	
	
    /**	
     * 加减库存的基本数量	
     */	
	@ApiModelProperty("加减库存的基本数量")	
    private BigDecimal qty;	
	
    /**	
     * 加减库存的包装数量	
     */	
	@ApiModelProperty("加减库存的包装数量")	
    private BigDecimal packQty;	
	
    /**	
     * 加减库存包装单位	
     */	
	@ApiModelProperty("加减库存包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 增加库存仓库编码	
     */	
	@ApiModelProperty("增加库存仓库编码")	
    private String whCode;	
	
    /**	
     * 增加库存仓库名称	
     */	
	@ApiModelProperty("增加库存仓库名称")	
    private String whName;	
	
    /**	
     * 增加减少库存关联单号	
     */	
	@ApiModelProperty("增加减少库存关联单号")	
    private String receiptCode;	
	
    /**	
     * 增加减少库存类型	
     */	
	@ApiModelProperty("增加减少库存类型")	
    private String receiptType;	
	
    /**	
     * 历史总库数量	
     */	
	@ApiModelProperty("历史总库数量")	
    private BigDecimal oldWhQty;	
	
    /**	
     * 扣减增加库存类型	
     */	
	@ApiModelProperty("扣减增加库存类型")	
    private String optionType;	
	
    /**	
     * 库存单价	
     */	
	@ApiModelProperty("库存单价")	
    private BigDecimal unitPrice;	
	
	
    /**	
     * 增加库存的物料辅助属性	
     */	
	@ApiModelProperty("增加库存的物料辅助属性")	
    private String sku;	
	
	
    /**	
     * 增加库存的箱号	
     */	
	@ApiModelProperty("增加库存的箱号")	
    private String boxNo;	
	
    /**	
     * 供应商批次	
     */	
	@ApiModelProperty("供应商批次")	
    private String batchSupplier;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    private Date productionDate;	
	
    /**	
     * 失效日期	
     */	
	@ApiModelProperty("失效日期")	
    private Date failureDate;	
	
}	
