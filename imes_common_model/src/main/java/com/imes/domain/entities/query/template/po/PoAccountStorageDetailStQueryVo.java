package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
@ApiModel("锁铜退入库单明细高级查询")	
@QueryModel(	
        name = "1133-st-storageDetail",	
        remark = "锁铜退入库单明细高级查询",	
        alias = "a",	
        searchApi = "/api/po/poAccountVerify/stQueryList")	
public class PoAccountStorageDetailStQueryVo extends BaseModel {	
	
	@ApiModelProperty("唯一识别号")	
    @QueryField(name = "唯一识别号", show = false)	
    private String id;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
	@ApiModelProperty("单据类型")	
    @QueryField(name = "单据类型", show = false, type = Type.Select, option = {"in", "入库单", "return", "退库单", "discount", "贴现折让"}, query = false)	
    private String detailType;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号", order = OrderBy.DESC, required = true)	
    private String soNo;	
	
	@ApiModelProperty("采购单行号")	
    @QueryField(name = "采购单行号", order = OrderBy.ASC)	
    private String sdNo;	
	
	@ApiModelProperty("对账单号")	
    @QueryField(name = "对账单号", show = false)	
    private String verifyNo;	
	
	@ApiModelProperty("客户编码")	
    @QueryField(name = "客户编码", show = false, alias = "b")	
    private String customerCode;	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称", show = false, alias = "b")	
    private String customerName;	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种", alias = "b", show = false, dictOption = "CUSTOMER_CURRENCY", type = Type.MultiSelect)	
    private String currency;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("物料规格型号")	
    @QueryField(name = "物料规格型号")	
    private String specification;	
	
	@ApiModelProperty("入库/退库单号")	
    @QueryField(name = "入库/退库单号")	
    private String storageNo;	
	
    //@QueryField(name = "单号")	
    private String relationReceiveNo;	
	
	@ApiModelProperty("批次号")	
    @QueryField(name = "批次号")	
    private String cbatch;	
	
	@ApiModelProperty("到货时间")	
    @QueryField(name = "到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String cbatchDate;	
	
	@ApiModelProperty("入库/退库日期")	
    @QueryField(name = "入库/退库日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String storageTime;	
	
	@ApiModelProperty("入库/退库数量")	
    @QueryField(name = "入库/退库数量", type = Type.Number)	
    private String allStorageNum;	
	
	@ApiModelProperty("本次对账数量")	
    @QueryField(name = "本次对账数量", type = Type.Number)	
    private String storageNum;	
	
	@ApiModelProperty("剩余数量")	
    @QueryField(name = "剩余数量", type = Type.Number)	
    private String syQty;	
	
/*    @QueryField(name = "已对账数量", alias = ".ifnull(md.qty, 0)", sort = false, type = Type.Number)	
    private String usedNum;	
	
	@ApiModelProperty("未对账数量")	
    @QueryField(name = "未对账数量",show = false, alias = ".(a.storage_num - ifnull(md.qty, 0))", sort = false, type = Type.Number)	
    private String remainNum;*/	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", type = Type.MultiSelect, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("取价类型")	
    @QueryField(name = "取价类型", type = Type.MultiSelect, option = {"1", "大宗物料", "0", "普通", "2", "锁铜"})	
    private String isLargeMaterial;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(a.tax_rate, 2), 0) * 100)")	
    private String taxRate;	
	
	@ApiModelProperty("实际结算系数(δ)")	
    @QueryField(name = "实际结算系数(δ)", type = Type.Number)	
    private String settlementRatio;	
	
	@ApiModelProperty("市铜未税单价")	
    @QueryField(name = "市铜未税单价", type = Type.Number, format = "0.000000")	
    private String oldUnIncludePrice;	
	
	@ApiModelProperty("市铜结算金额")	
    @QueryField(name = "市铜结算金额",type = Type.Number)	
    private String stjsPrice;	
	
	@ApiModelProperty("实际未税结算单价")	
    @QueryField(name = "实际未税结算单价", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("实际结算含税单价")	
    @QueryField(name = "实际结算含税单价", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("实际结算税额")	
    @QueryField(name = "实际结算税额", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("实际结算未税金额")	
    @QueryField(name = "实际结算未税金额", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("实际结算含税总额")	
    @QueryField(name = "实际结算含税总额", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("对账状态")	
    @QueryField(name = "对账状态", dictOption = "SCS_ACCOUNT_STATUS", type = Type.MultiSelect, required = true, show = false)	
    private String accountStatus;	
	
	@ApiModelProperty("开票状态")	
    @QueryField(name = "开票状态", dictOption = "SCS_ACCOUNT_INVOICE_STATUS", type = Type.MultiSelect, show = false)	
    private String invoiceStatus;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
	@ApiModelProperty("业务类型")	
    @QueryField(name = "业务类型", show = false, logic = Logic.Eq)	
    private String cbustype;	
	
    private String unit;	
	
    private String irdrowno;	
	
    private String icost;	
	
    private String imoney;	
	
    private String itaxprice;	
	
    private String isum;	
	
    private String linkId;	
	
    private String isForInvoice;	
	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型", type = Type.MultiSelect, dictOption = "U8_PURCHASE_TYPE", show = false)	
    private String cptcode;	
	
    //颜色	
    private String storageNumColor;	
}	
