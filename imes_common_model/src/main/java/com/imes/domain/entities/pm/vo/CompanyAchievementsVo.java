package com.imes.domain.entities.pm.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<公司业绩>>
 * @company 捷创智能技术有限公司
 * @create 2021-07-06 14:45
 */
@ApiModel("公司业绩")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CompanyAchievementsVo {
    @ApiModelProperty("公司名称")
    private String companyName;
    @ApiModelProperty("金额")
    private BigDecimal money;
}
