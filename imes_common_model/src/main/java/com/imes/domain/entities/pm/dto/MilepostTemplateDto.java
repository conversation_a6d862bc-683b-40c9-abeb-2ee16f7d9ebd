package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProMilepostTemplate)实体类
 *
 * <AUTHOR>
 * @since 2020-10-23 14:15:52
 */
@ApiModel("里程碑模板实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class MilepostTemplateDto  implements Serializable {
    private static final long serialVersionUID = -66419819955449259L;
    public interface AddGroup {
    }

    public interface UpdateGroup {
    }

    /**
     * id
     */
    @ApiModelProperty("id")
    @NotNull(message = "里程碑模板id不能为空", groups = UpdateGroup.class)
    @Null(message = "里程碑模板id必须为空", groups = AddGroup.class)
    private String id;
    /**
     * 父id
     */
    @ApiModelProperty("父id")
    private String pid;
    /**
     * 模板名称
     */
    @ApiModelProperty("模板名称")
    @NotEmpty(message = "模板名称不能为空",groups = AddGroup.class)
    private String name;
    /**
     * 创建人id
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 创建人姓名
     */
    @ApiModelProperty("创建人姓名")
    private String createdByName;
    /**
     * 修改人id
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 是否为默认里程碑模板
     */
    @ApiModelProperty("是否为默认里程碑模板")
    @Null(message = "无法设置默认里程碑模板",groups = AddGroup.class)
    private Boolean isDefault;
    /**
     * 首选默认里程碑模板
     */
    @ApiModelProperty("首选默认里程碑模板")
    @Null(message = "无法设置首选里程碑模板",groups = AddGroup.class)
    private Boolean preferred;
    /**
     * 子里程碑模板
     */
    @ApiModelProperty("子里程碑模板")
    private List<MilepostTemplateDto> children;
    /**
     * 版本号
     */
    @ApiModelProperty("版本号")
    private Long version;
}