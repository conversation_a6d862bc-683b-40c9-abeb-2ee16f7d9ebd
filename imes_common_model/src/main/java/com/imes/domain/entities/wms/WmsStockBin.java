package com.imes.domain.entities.wms;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockBin implements Serializable {	
	
    private static final long serialVersionUID = 709897219098888165L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "库存主表主键")	
    private String stockCode;	
	
    @ApiModelProperty(value = "工厂编码")	
    private String ftyCode;	
	
    @ApiModelProperty(value = "工厂名称")	
    private String ftyName;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "批次")	
    private String batch;	
	
    @ApiModelProperty(value = "供应商编码")	
    private String supplierCode;	
	
    @ApiModelProperty(value = "供应商名称")	
    private String supplierName;	
	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "存储区名称")	
    private String areaName;	
	
    @ApiModelProperty(value = "仓位")	
    private String binCode;	
	
    @ApiModelProperty(value = "仓位名称")	
    private String binName;	
	
    @ApiModelProperty(value = "在库库存数量")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty(value = "可用库存")	
    private BigDecimal availableQty;	
	
    @ApiModelProperty(value = "锁定库存（是指领料时预分配的库存）")	
    private BigDecimal lockQty;	
	
    @ApiModelProperty(value = "残次品数量")	
    private BigDecimal defectiveQty;	
	
    @ApiModelProperty(value = "维修品数量")	
    private BigDecimal repairQty;	
	
    @ApiModelProperty(value = "单位编码")	
    private String unit;	
	
    @ApiModelProperty(value = "单位名称")	
    @TableField(exist = false)	
    private String unitName;	
	
    @ApiModelProperty(value = "辅助单位")	
    private String auxiliaryUnit;	
	
    @ApiModelProperty(value = "辅助单位名称")	
    @TableField(exist = false)	
    private String auxiliaryUnitName;	
	
    @ApiModelProperty(value = "辅助数量")	
    private BigDecimal auxiliaryQty;	
	
    @ApiModelProperty(value = "包装规格")	
    private String packCode;	
	
    @ApiModelProperty(value = "规格数量")	
    private BigDecimal packNum;	
	
    @ApiModelProperty(value = "包装件数量")	
    private Integer packStoreNum;	
	
    @ApiModelProperty(value = "包装件单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "包装件单位名称")	
    @TableField(exist = false)	
    private String packCodeUnitName;	
	
    @ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如盘点，移库，补货等作业而锁定的库存")	
    private Integer lockStatus;	
	
    @ApiModelProperty(value = "最近入库")	
    private Date inboundLately;	
	
    @ApiModelProperty(value = "最近出库")	
    private Date outboundLately;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "库存单价")	
    private BigDecimal unitPrice;	
	
    @ApiModelProperty(value = "废品数量")	
    private BigDecimal wasteQty;	
	
    @ApiModelProperty(value = "包装在库库存")	
    private BigDecimal packOnhandQty;	
	
    @ApiModelProperty(value = "包装可用库存")	
    private BigDecimal packAvailableQty;	
	
    //总库存数量	
    private BigDecimal totalStock;	
	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private int precisionDigit;	
	
    private int packPrecisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private int carryMode;	
	
    private String isBatch;

    /**
     * 时间戳
     */
    @ApiModelProperty("时间戳")
    private String timeStamp;

    private String isSku;	
	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 生产日期	
     */	
    @ApiModelProperty(value = "生产日期")	
    private Date productionDate;	
	
    /**	
     * 失效日期	
     */	
    @ApiModelProperty(value = "失效日期")	
    private Date failureDate;	
	
    /**	
     * 供应商批次	
     */	
    @ApiModelProperty(value = "供应商批次")	
    private String batchSupplier;	
	
    /**	
     * 需出库数量	
     */	
	@ApiModelProperty("需出库数量")	
    private BigDecimal needQty;	
	
    private BigDecimal pickedQty;	
	
    private int autoGenerateSerialNum;	
	
    /**	
     * 是否锁库	
     */	
	@ApiModelProperty("是否锁库")	
    private Boolean isLock;	
	
    private WmsStockLock lock;	
	
    private String isEnableMinusInventory;	
	
    private int packApplyQtyPrecision;	
	
    private int packCarryMode;	
	
    /**	
     * 盘点数量	
     */	
	@ApiModelProperty("盘点数量")	
    private BigDecimal existQty;	
	
    /**	
     * 差异数量	
     */	
	@ApiModelProperty("差异数量")	
    private BigDecimal differenceQty;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;

    @ApiModelProperty("质保期")
    private BigDecimal qualityPeriod;
}	
