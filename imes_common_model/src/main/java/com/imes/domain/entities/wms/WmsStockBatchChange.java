package com.imes.domain.entities.wms;	
	
import java.util.Date;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
	
@Data	
@ApiModel(value = "库存批次调整单")	
public class WmsStockBatchChange implements Serializable {	
	
    private static final long serialVersionUID = -40815966454993925L;	
	
@ApiModelProperty(value = "主键")	
private String id;	
	
@ApiModelProperty(value = "批次调整单号")	
private String batchChangeNo;	
	
@ApiModelProperty(value = "申请员工工号")	
private String applyUserCode;	
	
@ApiModelProperty(value = "申请员工名称")	
private String applyUserName;	
	
@ApiModelProperty(value = "申请日期")	
private Date applyOn;	
	
@ApiModelProperty(value = "单据状态（10-已录入；20-已生效；50-已完成）")	
private String orderStatus;	
	
@ApiModelProperty(value = "备注")	
private String remark;	
	
@ApiModelProperty(value = "第三方系统单号")	
private String thirdOrderCode;	
	
@ApiModelProperty(value = "创建时间")	
private Date createOn;	
	
@ApiModelProperty(value = "创建人")	
private String createBy;	
	
@ApiModelProperty(value = "更新时间")	
private Date updateOn;	
	
@ApiModelProperty(value = "更新人")	
private String updateBy;	
}	
