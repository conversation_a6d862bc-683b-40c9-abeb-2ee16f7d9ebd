package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
public class WmsInboundOrOutboundData {	
	
    @ApiModelProperty(value = "时间")	
    private String createOn;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "入库数量")	
    private BigDecimal inQty;	
	
    @ApiModelProperty(value = "出库数量")	
    private BigDecimal outQty;	
	
	
}	
