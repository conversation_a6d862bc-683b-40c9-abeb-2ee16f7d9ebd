package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (PerEvaluationTargetItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-11 15:26:51
 */
@ApiModel("考评目标项实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationTargetItemDto implements Serializable {
    private static final long serialVersionUID = -89913551136380801L;
    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;
    /**
     * 考评目标id
     */
    @ApiModelProperty("考评目标id")
    private String evaluationTargetId;
    /**
     * 考评人工号
     */
    @ApiModelProperty("考评人工号")
    private String assessorCode;
    /**
     * 考评人姓名
     */
    @ApiModelProperty("考评人姓名")
    private String assessorName;
    /**
     * 指标名称
     */
    @ApiModelProperty("指标名称")
    private String name;
    /**
     * 指标类型
     */
    @ApiModelProperty("指标类型")
    private Integer type;
    /**
     * 计算规则
     */
    @ApiModelProperty("计算规则")
    private String rules;
    /**
     * 目标
     */
    @ApiModelProperty("目标")
    private String target;
    /**
     * 考评结果
     */
    @ApiModelProperty("考评结果")
    private String actual;
    /**
     * 自评
     */
    @ApiModelProperty("自评")
    private String selfEvaluation;
    /**
     * 领导评
     */
    @ApiModelProperty("领导评")
    private String leadershipEvaluation;
    /**
     * 最高分
     */
    @ApiModelProperty("最高分")
    private Integer highestScore;
    /**
     * 分值
     */
    @ApiModelProperty("分值")
    private Integer score;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;

}