package com.imes.domain.entities.query.template.crm;

import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import com.imes.domain.entities.query.model.base.Type;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@ApiModel("商机管理")
@QueryModel(name = "0451",
        remark = "商机管理",
        searchApi = "/crm/business/queryBusinessQueryVoList")
public class CrmBusinessQueryVo extends BaseModel {

    /**
     * id
     */

    private String id;
    /**
     * 编号
     */
    @QueryField(name = "商机编号")
    private String code;
    /**
     * 商机名称
     */
    @QueryField(name = "商机名称")
    private String subject;
    /**
     * 商机类型
     */
    @QueryField(name = "商机类型")
    private String type;

    /**
     * 星级
     */
    @QueryField(name = "星级")
    private String level;
    /**
     * 发现日期
     */
    @QueryField(name = "发现日期")
    private LocalDate findDate;
    /**
     * 预计收入
     */
    @QueryField(name = "预计收入")
    private BigDecimal estimatedIncome;
    /**
     * 预计签订日期
     */
    @QueryField(name = "预计签订日期")
    private LocalDate estimatedSigningDate;
    /**
     * 赢单率
     */
    @QueryField(name = "赢单率")
    private Integer winningRate;
    /**
     * 竞争品牌
     */
    @QueryField(name = "竞争品牌")
    private String compete;
    /**
     * 地点
     */
    @QueryField(name = "地点")
    private String address;
    /**
     * 所属企业
     */
    @QueryField(name = "所属企业")
    private String enterprise;
//    /**
//     * 销售阶段
//     */
//    @QueryField(name = "")
//    private String stageId;
    /**
     * 销售阶段
     */
    @QueryField(name = "销售阶段")
    private String stage;

    /**
     * 协助单位
     */
    @QueryField(name = "协助单位")
    private String assistance;
    /**
     * 销售员/客服
     */
    @QueryField(name = "销售员")
    private String salesperson;
    /**
     * 销售工程师
     */
    @QueryField(name = "销售工程师")
    private String salesEngineer;

    /**
     * 销售主管
     */
    @QueryField(name = "销售主管")
    private String salesSupervisor;

    /**
     * 销售总监
     */
    @QueryField(name = "销售总监")
    private String salesDirector;

    /**
     * 客户
     */
    @QueryField(name = "客户")
    private String customer;

    /**
     * 客户类型
     */
    @QueryField(name = "客户类型")
    private String customerType;
    /**
     * 客户行业
     */
    @QueryField(name = "客户行业")
    private String industry;
    /**
     * 联系人工号
     */
    @QueryField(name = "联系人工号")
    private String contacts;
    /**
     * 备注
     */
    @QueryField(name = "备注")
    private String remarks;
    /**
     * 审核状态（0：待审核 , 1：审核通过，2：审批中）
     */
    // private Integer approvalStatus;
    @QueryField(name = "审核状态")
    private String approvalStatus;
    /**
     * 关闭状态：0=未关闭、1=正式单、2=试用单、3=丢单
     */
    @QueryField(name = "关闭状态")
    private String offState;
    /**
     * 竞争者Id
     */
    @QueryField(name = "竞争者Id")
    private String closingCompetitor;

    /**
     * 关闭原因
     */
    @QueryField(name = "关闭原因")
    private String closingReason;
    /**
     * 关闭备注
     */
    @QueryField(name = "关闭备注")
    private String closeNotes;
    /**
     * 关闭时间
     */
    @QueryField(name = "关闭时间", type = Type.Date)
    private String closingTime;

    /**
     * 国家
     */
    @QueryField(name = "国家")
    private String country;
    /**
     * 省
     */
    @QueryField(name = "省")
    private String province;
    /**
     * 市
     */
    @QueryField(name = "市")
    private String city;
    /**
     * 区
     */
    @QueryField(name = "区")
    private String area;
    /**
     * 镇
     */
    @QueryField(name = "镇")
    private String town;
    /**
     * 等级
     */
    @QueryField(name = "等级")
    private String grade;
    /**
     * 急迫性
     */
    @QueryField(name = "急迫性")
    private String urgency;
    /**
     * 管道id
     */
    @QueryField(name = "管道id")
    private String pipelineId;

    @QueryField(name = "审核人")
    private String initiator;
}
