package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageInBill;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInBillVo extends WmsStorageInBill {	

    List<WmsStorageInBillItemVo> items;	

    int flag;	
	
    /**	
     * 线边仓库编码	
     */	
	@ApiModelProperty("线边仓库编码")
    private String storageAreaCode;	
	
	
    /**	
     * 线边仓库名称	
     */	
	@ApiModelProperty("线边仓库名称")
    private String storageAreaName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")
    private String applyUserCode;	
	
    /**	
     * 申请人姓名	
     */	
	@ApiModelProperty("申请人姓名")
    private String applyUserName;	
	
    /**	
     * 申请人部门编码	
     */	
	@ApiModelProperty("申请人部门编码")
    private String applyDepartCode;	
	
    /**	
     * 申请人部门名称	
     */	
	@ApiModelProperty("申请人部门名称")
    private String applyDepartName;	
	
    /**	
     * 公司编码	
     */	
	@ApiModelProperty("公司编码")
    private String companyCode;	
	
    /**	
     * 公司名称	
     */	
	@ApiModelProperty("公司名称")
    private String companyName;	
	
    /**	
     * 调拨审核工作流最终处理用户	
     */	
	@ApiModelProperty("调拨审核工作流最终处理用户")	
    private String auditUserCode;	
	
    /**	
     * 区分PC端还是移动端审核 1:PC端 2:移动端	
     */	
	@ApiModelProperty("区分PC端还是移动端审核 1:PC端 2:移动端")	
    private String sumbitFromflag;	
	
    /**	
     * 移动端审核调入库位是否可以下拉选择	
     */	
	@ApiModelProperty("移动端审核调入库位是否可以下拉选择")	
    private boolean showSelect;	
	
    /**	
     * 入库单主键	
     */	
	@ApiModelProperty("入库单主键")	
    private String inboundId;	
	
}	
