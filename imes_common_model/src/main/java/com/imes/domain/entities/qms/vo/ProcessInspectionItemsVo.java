package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.ppc.po.PpcProcessBadCode;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.ProcessInspection;	
import com.imes.domain.entities.qms.po.ProcessInspectionDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class ProcessInspectionItemsVo implements Serializable {	
	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    List<ProcessInspectionDetail> detail;	
	
//    /**	
//     * 不良项	
//     */	
//    List<PpcProcessBadCode> badCodes;	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    int inspectionNum;	
	
    /**	
     * 检验类型	
     */	
	@ApiModelProperty("检验类型")	
    String inspectType;	
	
    /**	
     * aql值	
     */	
	@ApiModelProperty("aql值")	
    String aqlCode;	
	
    /**	
     * 过检合格率	
     */	
	@ApiModelProperty("过检合格率")	
    String passRatio;	
    /**	
     * 质检样本管理	
     */	
	@ApiModelProperty("质检样本管理")	
    String qmsSampleManager;	
    /**	
     * 数据字典是否开启不良项 。1启用0禁用	
     */	
	@ApiModelProperty("数据字典是否开启不良项 。1启用0禁用")	
    String lableFlag;	
	
    /**	
     * 真正的检验数量	
     */	
	@ApiModelProperty("真正的检验数量")	
    Integer reallyInspectionNum;	
	
    /**	
     * 检验抽样方式1AQL标准抽样,2比率抽样3定量抽样	
     */	
	@ApiModelProperty("检验抽样方式1AQL标准抽样,2比率抽样3定量抽样")	
    String inspectionSamplingMethod;	
	
    /**	
     * 检验判定方式1AQL判定2合格率判定3主观判定	
     */	
	@ApiModelProperty("检验判定方式1AQL判定2合格率判定3主观判定")	
    String inspectionJudgmentMethod;	
	
    private static final long serialVersionUID = 1L;	
}	
