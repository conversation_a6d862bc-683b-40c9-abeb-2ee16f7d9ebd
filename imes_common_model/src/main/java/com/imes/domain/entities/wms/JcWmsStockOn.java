package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class JcWmsStockOn implements Serializable {	
	
    /**	
     * 	
     */	
    @ApiModelProperty("主键")	
    private String id;	
	
    /**	
     * 第三方系统在途库存主键	
     */	
    @ApiModelProperty("第三方系统在途库存主键")	
    private String thirdOrderCode;	
	
    /**	
     * 存货公司编码	
     */	
    @ApiModelProperty("存货公司编码")	
    private String stockCompanyCode;	
	
    /**	
     * 存货公司名称	
     */	
    @ApiModelProperty("存货公司名称")	
    private String stockCompanyName;	
	
    /**	
     * 采购公司编码	
     */	
    @ApiModelProperty("采购公司编码")	
    private String purchaseCompanyCode;	
	
    /**	
     * 采购公司名称	
     */	
    @ApiModelProperty("采购公司名称")	
    private String purchaseCompanyName;	
	
    /**	
     * 销售合同单号	
     */	
    @ApiModelProperty("销售合同单号")	
    private String salesContractCode;	
	
    /**	
     * 采购合同单号	
     */	
    @ApiModelProperty("采购合同单号")	
    private String purchaseContractCode;	
	
    /**	
     * 申请合同单号	
     */	
    @ApiModelProperty("申请合同单号")	
    private String applyContractCode;	
	
    /**	
     * 订单号	
     */	
    @ApiModelProperty("订单号")	
    private String orderCode;	
	
    /**	
     * RA系统号	
     */	
    @ApiModelProperty("RA系统号")	
    private String raCode;	
	
    /**	
     * PSR No./RMA	
     */	
    @ApiModelProperty("PSR No./RMA")	
    private String psrCode;	
	
    /**	
     * 存货仓库编码	
     */	
    @ApiModelProperty("存货仓库编码")	
    private String whCode;	
	
    /**	
     * 存货仓库名称	
     */	
    @ApiModelProperty("存货仓库名称")	
    private String whName;	
	
    /**	
     * 库位编码	
     */	
    @ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 库存类型编码	
     */	
    @ApiModelProperty("库存类型编码")	
    private String stockTypeCode;	
	
    /**	
     * 库存类型名称	
     */	
    @ApiModelProperty("库存类型名称")	
    private String stockTypeName;	
	
    /**	
     * 销售员工号	
     */	
    @ApiModelProperty("销售员工号")	
    private String salesUserCode;	
	
    /**	
     * 销售员名称	
     */	
    @ApiModelProperty("销售员名称")	
    private String salesUserName;	
	
    /**	
     * 销售部门编码	
     */	
    @ApiModelProperty("销售部门编码")	
    private String salesDeptCode;	
	
    /**	
     * 销售部门名称	
     */	
    @ApiModelProperty("销售部门名称")	
    private String salesDeptName;	
	
    /**	
     * 采购员工号	
     */	
    @ApiModelProperty("采购员工号")	
    private String purchaseUserCode;	
	
    /**	
     * 采购员名称	
     */	
    @ApiModelProperty("采购员名称")	
    private String purchaseUserName;	
	
    /**	
     * 采购部门编码	
     */	
    @ApiModelProperty("采购部门编码")	
    private String purchaseDeptCode;	
	
    /**	
     * 采购部门名称	
     */	
    @ApiModelProperty("采购部门名称")	
    private String purchaseDeptName;	
	
    /**	
     * 客户编码	
     */	
    @ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
    @ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 供应商编码	
     */	
    @ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
    @ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 品牌	
     */	
    @ApiModelProperty("品牌")	
    private String brand;	
	
    /**	
     * 物料编码	
     */	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料型号	
     */	
    @ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料名称	
     */	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 规格	
     */	
    @ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 系列	
     */	
    @ApiModelProperty("系列")	
    private String series;	
	
    /**	
     * 单位	
     */	
    @ApiModelProperty("单位")	
    private String primaryUnit;	
	
    /**	
     * 库存说明	
     */	
    @ApiModelProperty("库存说明")	
    private String stockDesc;	
	
    /**	
     * 可用数量	
     */	
    @ApiModelProperty("可用数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 库存数量	
     */	
    @ApiModelProperty("库存数量")	
    private BigDecimal onHandQty;	
	
    /**	
     * 采购单价	
     */	
    @ApiModelProperty("采购单价")	
    private BigDecimal purchasePrice;	
	
    /**	
     * 可用库存金额	
     */	
    @ApiModelProperty("可用库存金额")	
    private BigDecimal availablePrice;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal onHandPrice;	
	
    /**	
     * 预计到货日期	
     */	
    @ApiModelProperty("预计到货日期")	
    private Date dueArrivalDate;	
	
    /**	
     * 状态（1：未入库，2：已入库）	
     */	
    @ApiModelProperty("状态（1：未入库，2：已入库）")	
    private String stockStatus;	
	
    /**	
     * 创建时间	
     */	
    @ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
    @ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
    @ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
    @ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
