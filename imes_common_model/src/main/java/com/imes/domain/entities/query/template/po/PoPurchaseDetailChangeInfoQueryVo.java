package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购订单回复记录明细")	
@QueryModel(	
        name = "1201-detail",	
        remark = "采购订单回复记录明细",	
        alias = "po_purchase_detail_change",	
        searchApi = "/po/poPurchaseDetailChange/selectByMainId"	
)	
public class PoPurchaseDetailChangeInfoQueryVo extends BaseModel {	
	
	@ApiModelProperty("主id")	
    @QueryField(name = "主id", logic = Logic.Eq, show = false)	
    private String mainId;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号")	
    private String sdNo;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
/*	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String materialMarker;	
*/	
	
	@ApiModelProperty("图号")	
    @QueryField(name = "图号")	
    private String dwgNo;	
	
	@ApiModelProperty("物料旧图号")	
    @QueryField(name = "物料旧图号")	
    private String oldDwgNo;	
	
	@ApiModelProperty("品牌")	
    @QueryField(name = "品牌")	
    private String brand;	
	
	@ApiModelProperty("标准件/非标件")	
    @QueryField(name = "标准件/非标件")	
    private String standardPart;	
	
	@ApiModelProperty("U8计划到货时间")	
    @QueryField(name = "U8计划到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String estimatedDeliveryDate;	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", type = Type.Number, format = "0.00")	
    private String singlePrice;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量")	
    private String purchaseQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String unit;	
	
	@ApiModelProperty("折扣率")	
    @QueryField(name = "折扣率", format = "{}%")	
    private String discountRate;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", type = Type.Select, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(po_purchase_detail_change.tax_rate, 2), 0) * 100)", format = "{}%")	
    private String taxRate;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税总额")	
    @QueryField(name = "价税总额", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("累计到货数量")	
    @QueryField(name = "累计到货数量")	
    private String arrivalQty;	
	
	@ApiModelProperty("回复最晚到货时间")	
    @QueryField(name = "回复最晚到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String requestedDeliveryDate;	
	
	@ApiModelProperty("需求回复数量")	
    @QueryField(name = "需求回复数量")	
    private String replyQty;	
	
	@ApiModelProperty("物料刻印号")	
    @QueryField(name = "物料刻印号")	
    private String engravingNumber;	
	
	@ApiModelProperty("行版本")	
    @QueryField(name = "行版本")	
    private String detailVer;	
	
   /* @QueryField(name = "行单据状态", type = Type.MultiSelect, option = {"10","采购已提交","20","采购已生效","30","供应商拒绝"})	
    private String status;	
*/	
	@ApiModelProperty("行锁定状态")	
    @QueryField(name = "行锁定状态",type = Type.MultiSelect, option = {"10","未锁定","20","已锁定"})	
    private String lockupStatus;	
/*	
	@ApiModelProperty("推送供应商状态")	
    @QueryField(name = "推送供应商状态",type = Type.MultiSelect, dictOption = "PO_PUSH_STATUS")	
    private String supplierStatus;*/	
	
	@ApiModelProperty("行业务状态")	
    @QueryField(name = "行业务状态", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE")	
    private String businessStatus;	
	
	@ApiModelProperty("产品类型")	
    @QueryField(name = "产品类型", type = Type.Select, treeKey = {"typeCode", "parentCode", "0"}, sqlOption = "select type_code as value, type_name as label, type_code as typeCode, parent_type_code as parentCode from sys_material_type order by type_code")	
    private String materialTypeCode;	
	
	@ApiModelProperty("是否发运拆分")	
    @QueryField(name = "是否发运拆分", sort = false, option = {"1", "拆分", "0", "正常"}, type = Type.Select, alias = ".(if(ifnull((select count(1) from po_purchase_detail_item_change g where g.main_id = po_purchase_detail_change.id group by version order by version desc limit 1),0)>1,'1','0'))")	
    private String forwardingSplit;	
	
	
/*    @QueryField(name = "数量状态", type = Type.MultiSelect, dictOption = "PO_QTY_STATUS", alias = ".(if(ifnull(po_purchase_detail.reply_qty, '4') != '4', if((po_purchase_detail.reply_qty - po_purchase_detail.purchase_qty) > 0, '2',if((po_purchase_detail.reply_qty - po_purchase_detail.purchase_qty) >= 0, '1', '3')),'4'))")	
    private String qtyStatus;*/	
	
	@ApiModelProperty("时间状态")	
    @QueryField(name = "时间状态", type = Type.MultiSelect, dictOption = "PO_TIME_STATUS", alias = ".(if(ifnull(po_purchase_detail_change.requested_delivery_date, '4') != '4', if((TIMESTAMPDIFF(SECOND, po_purchase_detail_change.requested_delivery_date, ifnull(po_purchase_detail_change.estimated_delivery_date, now()))) > 0, '3',if((TIMESTAMPDIFF(SECOND, po_purchase_detail_change.requested_delivery_date,ifnull(po_purchase_detail_change.estimated_delivery_date, now()))) >= 0, '1', '2')),'4'))")	
    private String timeStatus;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
}	
