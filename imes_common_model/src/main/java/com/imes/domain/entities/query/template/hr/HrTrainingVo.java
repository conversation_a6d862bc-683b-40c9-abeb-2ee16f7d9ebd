package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("培训计划信息")	
@QueryModel(	
        name = "0667",	
        remark = "培训计划信息",	
        alias = "hr_training",	
        searchApi = "/api/hr/training/query")	
    public class HrTrainingVo extends BaseModel {	
	
	@ApiModelProperty("编号")	
    @QueryField(name = "编号")	
    private String id;	
    /**	
     * 培训名称	
     */	
	@ApiModelProperty("培训名称")	
    @QueryField(name = "培训名称")	
    private String name;	
    /**	
     * 讲师编号	
     */	
	@ApiModelProperty("讲师编号")	
    @QueryField(name = "讲师编号")	
    private String userCode;	
    /**	
     * 讲师名称	
     */	
	@ApiModelProperty("讲师名称")	
    @QueryField(name = "讲师名称")	
    private String userName;	
    /**	
     * 位置	
     */	
	@ApiModelProperty("位置")	
    @QueryField(name = "位置", show = false)	
    private String location;	
    /**	
     * 联系电话	
     */	
	@ApiModelProperty("联系电话")	
    @QueryField(name = "联系电话", show = false)	
    private String phone;	
    /**	
     * 介绍	
     */	
	@ApiModelProperty("介绍")	
    @QueryField(name = "介绍", show = false)	
    private String introduction;	
    /**	
     * 类型	
     */	
	@ApiModelProperty("类型")	
    @QueryField(name = "类型")	
//    private TrainTypeEnum type;	
    private String type;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
    /**	
     * 培训方式	
     */	
//    @QueryField(name = "培训方式", type = Type.MultiSelect, dictOption = "TRAINING_METHODS")	
    private Integer methods;	
    /**	
     * 部门名称	
     */	
	@ApiModelProperty("部门名称")	
    @QueryField(name = "部门名称")	
    private String deptName;	
    /**	
     * 部门编码	
     */	
	@ApiModelProperty("部门编码")	
    @QueryField(name = "部门编码")	
    private String deptCode;	
    /**	
     * 培训时间	
     */	
	@ApiModelProperty("培训时间")	
    @QueryField(name = "培训时间", type = Type.Date)	
    private String trainingTime;	
    /**	
     * 结束时间	
     */	
	@ApiModelProperty("结束时间")	
    @QueryField(name = "结束时间", type = Type.Date)	
    private String endTime;	
	
    private Integer userFlag;	
	
    /**	
     * 场所编码	
     */	
    private String siteCode;	
    /**	
     * 所需设备	
     */	
    private String equipment;	
	
	
}	
