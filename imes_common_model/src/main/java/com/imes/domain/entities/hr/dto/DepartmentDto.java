package com.imes.domain.entities.hr.dto;

import com.imes.domain.entities.hr.po.JobsDept;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 组织表(Department)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-29 14:57:51
 */
@Data
@ApiModel("组织目录表")
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentDto implements Serializable {

    private static final long serialVersionUID = 3876243308898255937L;
    private String id;
    /**
     * 部门编号
     */
    private String departCode;
    /**
     * 部门名称
     */
    private String departName;
    /**
     * 上级部门
     */
    private String parentCode;
    /**
     * 所属公司
     */
    private String company;
    /**
     * 部门分类
     */
    private String departType;
    /**
     * 部门排序
     */
    private Integer sort;
    /**
     * 部门来源 0mom系统;1钉钉
     */
    private String departSource;
    /**
     * 部门负责人(废弃)
     */
    private String managerUserCode;
    /**
     * 创建时间
     */
    private Date createOn;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    private Date updateOn;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 部门编码转换用
     */
    private String remarks;
    /**
     * 部门数的深度
     */
    private Integer level;
    /**
     * 部门下总员工人数
     */
    private Integer employeeTotal  = 0;
    /**
     * 当前部门员工人数
     */
    private Integer employeeCount = 0;
    /**
     * 部门下编制总数
     */
    private Integer jobsTotal = 0;
    /**
     * 部门岗位数
     */
    private Integer jobsCount = 0;
    /**
     * 子目录字段
     */
    private List<DepartmentDto> childs;
    /**
     * 部门下的岗位
     */
    private List<JobsDept> jobsDepts;
    /**
     * 部门下空缺人数
     */
    private Integer vacanciesCount = 0;
    /**
     * 超编数量
     */
    private Integer overJobsCount = 0;
    /**
     * 部门负责人名称
     */
    private String managerUserName;

    private String value;

    private String label;

    /**
     * 子目录字段
     */
    private List<DepartmentDto> children;

}

