package com.imes.domain.entities.pm.dto.ex;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.Alias;

import java.time.LocalDateTime;

@EqualsAndHashCode(callSuper = true)
@Data
@Alias("pro_baseDto")
public class BaseDto extends PageDto {
    /**
     * id
     */
    //@JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String id;
    /**
     * 创建时间
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String createdBy;
    /**
     * 修改人
     */
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private String updatedBy;
    /**
     * 逻辑删除
     */
    // @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    // private Integer deleted;
    /**
     * 审核状态（0：待审核 , 1：审核通过，2：审批中）
     */
    // private Integer approvalStatus;
    // @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    // private ApprovalStatusEnum approvalStatus;
}
