package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.io.Serializable;
import java.time.LocalTime;
import java.time.YearMonth;
import java.util.List;

/**
 * (CrmBusinessFollow)实体类
 *
 * <AUTHOR>
 * @since 2022-03-01 17:03:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_business_follow", resultMap = "BaseResultMap")
public class BusinessFollow extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 774242483794710930L;
    /**
    * id
    */
    private String id;
    /**
    * 商机id
    */
    private String businessId;
    /**
     * 商机名称
     */
    @TableField(exist = false)
    private String businessSubject;
    /**
    * 阶段id
    */
    private String stageId;
    /**
     * 阶段名称
     */
    @TableField(exist = false)
    private String stageName;
    /**
    * 预期签订日期
    */
    private LocalDate estimatedSigningDate;
    /**
    * 预期收益
    */
    private BigDecimal estimatedIncome;
    /**
    * 赢单率
    */
    private Integer winningRate;
    /**
     * 用户工号
     */
    private String userCode;
    /**
     * 用户姓名
     */
    @TableField(exist = false)
    private String userName;
    /**
    * 跟进方式
    */
    private String type;
    /**
    * 报工日期
    */
    private LocalDate reportDate;
    /**
     * 年月
     */
    @TableField(exist = false)
    private YearMonth yearMonth;
    /**
    * 地点
    */
    private String address;
    /**
    * 开始时间
    */
    private LocalTime startTime;
    /**
     * 结束时间
     */
    private LocalTime endTime;
    /**
     * 时间范围
     */
    @TableField(exist = false)
    private List<LocalTime> timeFrames;
    /**
    * 工时
    */
    private BigDecimal hours;
    /**
    * 总结
    */
    private String content;
    /**
    * 备注
    */
    private String remarks;

}