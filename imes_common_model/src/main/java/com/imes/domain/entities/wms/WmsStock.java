package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "总库存实体类")	
public class WmsStock implements Serializable {	
	
    private static final long serialVersionUID = 323392034617677760L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "工厂编码")	
    private String ftyCode;	
	
    @ApiModelProperty(value = "工厂名称")	
    private String ftyName;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "品牌")	
    private String brand;	
	
    @ApiModelProperty(value = "系列")	
    private String series;	
	
    @ApiModelProperty(value = "在库库存")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty(value = "可用库存")	
    private BigDecimal availableQty;	
	
    @ApiModelProperty(value = "锁定库存（是指领料单或发货单预分配的库存）")	
    private BigDecimal lockQty;	
	
    @ApiModelProperty(value = "采购申请数量")	
    private BigDecimal poRequestQty;	
	
    @ApiModelProperty(value = "采购在途数量")	
    private BigDecimal poUnderWayQty;	
	
    @ApiModelProperty(value = "调拨在途数量")	
    private BigDecimal allocatingQty;	
	
    @ApiModelProperty(value = "退货在途数量")	
    private BigDecimal returningQty;	
	
    @ApiModelProperty(value = "生产中数量")	
    private BigDecimal producingQty;	
	
    @ApiModelProperty(value = "残次品数量")	
    private BigDecimal defectiveQty;	
	
    @ApiModelProperty(value = "维修品数量")	
    private BigDecimal repairQty;	
	
    @ApiModelProperty(value = "总数量")	
    private BigDecimal totalQty;	
	
    @ApiModelProperty(value = "单位")	
    private String unit;	
	
    @ApiModelProperty(value = "辅助单位")	
    private String auxiliaryUnit;	
	
    @ApiModelProperty(value = "辅助数量")	
    private BigDecimal auxiliaryQty;	
	
    @ApiModelProperty(value = "锁定状态（未锁定，锁定）由于某些操作，如盘点，移库，补货等作业而锁定的库存")	
    private Integer lockStatus;	
	
    @ApiModelProperty(value = "最近入库")	
    private Date inboundLately;	
	
    @ApiModelProperty(value = "最近出库")	
    private Date outboundLately;	
	
    @ApiModelProperty(value = "优先级")	
    private Integer priority;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "废品数量")	
    private BigDecimal wasteQty;	
	
    @ApiModelProperty(value = "安全库存")	
    private BigDecimal safeInventory;	
	
    @ApiModelProperty(value = "小数精度位数")	
    private Byte precisionDigit;	
	
    @ApiModelProperty(value = "进位方式")	
    private Byte carryMode;	
	
    @ApiModelProperty(value = "物料类型")	
    private String materialTypeCode;	
	
    @ApiModelProperty(value = "是否启用批次")	
    private String isBatch;	
	
    @ApiModelProperty(value = "是否启用序列号")	
    private String isSerial;	
	
    @ApiModelProperty(value = "默认库位")	
    private String defaultWhCode;	
}	
