package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.pm.enums.ApprovalStatusEnum;
import com.imes.domain.entities.pm.typeHandler.ListTypeHandler;
import com.imes.domain.entities.pm.typeHandler.WalletListTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * (PerEvaluationScheme)实体类
 *
 * <AUTHOR>
 * @since 2022-01-06 17:05:12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_scheme", resultMap = "BaseResultMap")
public class EvaluationScheme implements Serializable {
    private static final long serialVersionUID = 796219717044769005L;
    /**
     * id
     */
    private String id;
    /**
     * 父id
     */
    private String pid;
    /**
     * 0=考评方案分类；1=考评模板方案
     */
    private Integer type;
    /**
     * 名称
     */
    private String name;
    /**
     * 是否为项目方案（1：项目方案 0：其他）
     */
    private Boolean project;
    /**
     * 考评周期
     */
    private Integer cycle;
    /**
     * 考评人
     */
    private String assessor;
    /**
     * 流程模型
     */
    private String modeler;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 被考评人
     */
    @TableField(typeHandler = ListTypeHandler.class)
    private List<String> evaluatedPerson;
    /**
     * 考评指标
     */
    @TableField(exist = false)
    private List<EvaluationSchemeIndex> indexItems;
    /**
     * 考评等级
     */
     @TableField(typeHandler = WalletListTypeHandler.class)
    private List<EvaluationLevel> levels;
    /**
     * 子绩效考评方案
     */
    @TableField(exist = false)
    private List<EvaluationScheme> children;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 显示等级
     */
    private Boolean grade;
    /**
     * 审批状态
     */
    private ApprovalStatusEnum approvalStatus;
}