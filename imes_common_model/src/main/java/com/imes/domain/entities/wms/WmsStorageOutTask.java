package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageOutTask implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 入库任务号	
     */	
	@ApiModelProperty("入库任务号")	
    private String storageOutTaskCode;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String storageOutCode;	
	
    /**	
     * 入库任务编码	
     */	
	@ApiModelProperty("入库任务编码")	
    private String storageOutItemCode;	
	
    /**	
     * 任务状态（未关闭，已关闭）	
     */	
	@ApiModelProperty("任务状态（未关闭，已关闭）")	
    private String status;	
	
    /**	
     * 配货状态（还未拣货，正拣货，完成拣货）	
     */	
	@ApiModelProperty("配货状态（还未拣货，正拣货，完成拣货）")	
    private Integer pickStatus;	
	
    /**	
     * 任务开始时间	
     */	
	@ApiModelProperty("任务开始时间")	
    private Date startTime;	
	
    /**	
     * 任务结束时间	
     */	
	@ApiModelProperty("任务结束时间")	
    private Date endTime;	
	
    /**	
     * 任务单物料数量	
     */	
	@ApiModelProperty("任务单物料数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 本次任务需出库数量	
     */	
	@ApiModelProperty("本次任务需出库数量")	
    private BigDecimal theInventoryQty;	
	
    /**	
     * 本任务已出库数量	
     */	
	@ApiModelProperty("本任务已出库数量")	
    private BigDecimal inventoryQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
