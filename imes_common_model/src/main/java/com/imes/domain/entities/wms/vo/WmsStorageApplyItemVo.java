package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsStorageApplyItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsStorageApplyItemVo extends WmsStorageApplyItem {	
    private String receiptCode;	
	
    private BigDecimal outQty;	
	
    private String receiptType;	
	
    private BigDecimal availableQty;	
	
    private Byte carryMode;	
	
    private Byte precisionDigit;	
}	
