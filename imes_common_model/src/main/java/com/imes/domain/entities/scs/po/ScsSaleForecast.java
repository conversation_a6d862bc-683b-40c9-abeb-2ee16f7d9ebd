package com.imes.domain.entities.scs.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel(value = "销售预测计划")
public class ScsSaleForecast implements Serializable {

    private static final long serialVersionUID = -59027716807621999L;

    private String id;

    @ApiModelProperty(value = "需求批次号")
    private String demandForecastNo;

    @ApiModelProperty(value = "需求版本")
    private String demandVersion;

    @ApiModelProperty(value = "客户编码")
    private String customerCode;

    @ApiModelProperty(value = "客户名称")
    private String customerName;

    @ApiModelProperty(value = "需求联系人")
    private String customerLinkMan;

    @ApiModelProperty(value = "需求发布日期")
    private Date publishDate;
    //需求预测日期
    private Date forecastDate;

    @ApiModelProperty(value = "需求回复截止日期")
    private Date responseDeadline;

    @ApiModelProperty(value = "回复时间")
    private Date responseDate;

    @ApiModelProperty(value = "回复人姓名")
    private String responsePersonName;


    @ApiModelProperty(value = "单据状态10已录入，20审核中，30生效待接收，40已接收,50已回复采购方")
    private String status;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private String refuseReason;

    private String supplierRemarks;

    private int version;
}