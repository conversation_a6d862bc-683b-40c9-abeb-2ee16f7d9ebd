package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.PatrolPublish;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.ProcessTask;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class PatrolInspectionNewInfoVo implements Serializable {	
	
    private PatrolInspectionVo main;	
	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    Map<String, PatrolDetailBadVo> detail;	
	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 报工信息	
     */	
	@ApiModelProperty("报工信息")	
    List<Map> info;	
	
    /**	
     * 合并条目	
     */	
	@ApiModelProperty("合并条目")	
    List<String> orderIds;	
	
    /**	
     * 删除数组	
     */	
	@ApiModelProperty("删除数组")	
    List<String> inspectionNos;	
    private static final long serialVersionUID = 1L;	
}	
