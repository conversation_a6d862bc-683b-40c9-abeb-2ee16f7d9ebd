package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("价目管理")	
@QueryModel(	
        name = "1171",	
        remark = "价目管理",	
        alias = "a",	
        searchApi = "/api/po/price/queryList")	
public class PoPriceQueryVo extends BaseModel {	
	
    private String id;	
	
	@ApiModelProperty("定价时间")	
    @QueryField(name = "定价时间", type = Type.Date, order = OrderBy.DESC, alias = "a.effect_time")	
    private String priceTime;	
	
	@ApiModelProperty("生效时间")	
    @QueryField(name = "生效时间", type = Type.Date, alias = "b", format = "yyyy-MM-dd")	
    private String effectTime;	
	
	@ApiModelProperty("失效时间")	
    @QueryField(name = "失效时间", type = Type.Date, alias = "b", format = "yyyy-MM-dd")	
    private String uneffectTime;	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, option = {"10", "录入", "20", "审核中", "30", "审核通过"})	
    private String status;	
	
	@ApiModelProperty("定价单号")	
    @QueryField(name = "定价单号")	
    private String priceNo;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "b")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "b")	
    private String materialName;	
	
    /*@QueryField(name = "规格型号", alias = "b")	
    private String specification;*/	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号", alias = "b")	
    private String materialMarker;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "b")	
    private String unit;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = "b", format = "{}%")	
    private String taxRate;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价", alias = "b", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", alias = "b", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "b", type = Type.Select, option = {"0", "是", "1", "否"})	
    private String isIncludeTax;	
	
	@ApiModelProperty("生效状态")	
    @QueryField(name = "生效状态", alias = "b", show = false, value = "1")	
    private String effectStatus;	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种", type = Type.Select, dictOption = "CUSTOMER_CURRENCY")	
    private String currency;	
	
	@ApiModelProperty("定价员")	
    @QueryField(name = "定价员")	
    private String madeUserName;	
	
	@ApiModelProperty("定价单名称")	
    @QueryField(name = "定价单名称")	
    private String priceName;	
	
	@ApiModelProperty("供应商")	
    @QueryField(name = "供应商")	
    private String supplierName;	
	
	@ApiModelProperty("定价类型")	
    @QueryField(name = "定价类型", type = Type.Select, option = {"1", "采购"}, value = "1", query = false)	
    private String priceType;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
}	
