package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.util.List;	
@Data	
public class WmsMonthInAndOutData {	
    /**	
     * 入库	
     */	
	@ApiModelProperty("入库")	
    private List<WmsMonthInboundAndOutboundData> inboundList;	
	
    /**	
     * 出库	
     */	
	@ApiModelProperty("出库")	
    private List<WmsMonthInboundAndOutboundData> outboundList;	
}	
