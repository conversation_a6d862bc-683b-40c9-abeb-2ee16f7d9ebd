package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsAllotReturnOrder;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsAllotReturnOrderAndItemVo extends WmsAllotReturnOrder {	
	
    @ApiModelProperty(value = "调拨退货明细单号")	
    private String atoItemCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "退货数量")	
    private BigDecimal orderQty;	
	
    @ApiModelProperty(value = "主单位")	
    private String unit;	
	
    @ApiModelProperty(value = "批次号")	
    private String batch;	
	
    @ApiModelProperty(value = "退货库位")	
    private String outBinCode;	
	
    @ApiModelProperty(value = "接收库位")	
    private String inBinCode;	
	
    @ApiModelProperty(value = "包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "包装数量")	
    private BigDecimal packQty;	
	
    @ApiModelProperty(value = "可退数量")	
    private BigDecimal returnQty;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal availableQty;

    @ApiModelProperty(value = "明细id")
    private String detailId;
}	
