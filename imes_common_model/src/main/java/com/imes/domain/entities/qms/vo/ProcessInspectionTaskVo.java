package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.ProcessInspection;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
public class ProcessInspectionTaskVo extends ProcessInspection implements Serializable {	
	
    /**	
     * 过检合格率	
     */	
	@ApiModelProperty("过检合格率")	
    private String taskInspectionCode;	
	
    private static final long serialVersionUID = 1L;	
	
	
}	
