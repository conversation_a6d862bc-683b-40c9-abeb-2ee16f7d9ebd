package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (PerEvaluationTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2021-11-10 10:15:40
 */
@ApiModel("考评模板项实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationTemplateItemDto implements Serializable {
    private static final long serialVersionUID = 890302171427946518L;
    /**
    * id
    */
    @ApiModelProperty("id")
    private String id;
    /**
    * 模板id
    */
    @ApiModelProperty("模板id")
    private String templateId;
    /**
    * 指标名称
    */
    @ApiModelProperty("指标名称")
    private String name;
    /**
    * 指标类型
    */
    @ApiModelProperty("指标类型")
    private Integer type;
    /**
    * 计算规则
    */
    @ApiModelProperty("计算规则")
    private String rules;
    /**
    * 目标
    */
    @ApiModelProperty("目标")
    private String target;
    /**
    * 分值
    */
    @ApiModelProperty("分值")
    private Integer score;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;

}