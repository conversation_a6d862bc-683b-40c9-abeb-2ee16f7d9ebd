package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "保养计划表")
@Data
public class MaintainPlan implements Serializable {
    /**
     * 主键
     */
    private String id;

    /**
     * 计划编码
     */
    @ApiModelProperty(hidden = true)
    private String planNo;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称", dataType = "String")
    @NotBlank(message = "计划名称不能为空！")
    private String planName;

    @ApiModelProperty(value = "创建人")
    private String createdBy;
    @ApiModelProperty(value = "创建时间")
    private Date createdOn;
    @ApiModelProperty(value = "更新时间")
    private Date updatedOn;
    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    /**
     * 计划执行人
     */
    @ApiModelProperty(value = "执行班组")
    private String executor;

    /**
     * 计划上次执行时间
     */
    @ApiModelProperty(hidden = true)
    private Date lastExecutionTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 执行状态(1：未开始，2：执行中，3：已完成)
     */
    @ApiModelProperty(value = "状态：10-录入中；15-已驳回；20-审核中；30-审核通过待启动；35-执行中；40-已完成；45-已暂停；50-已停止", dataType = "String")
    private Integer status;

    /**
     * 执行频率
     */
    @ApiModelProperty(value = "执行频率周期", dataType = "String")
    private String frequently;

    @ApiModelProperty(value = "计划开始时间")
    private java.util.Date startTime;

    @ApiModelProperty(value = "计划结束时间")
    private java.util.Date endTime;

    /**
     * 是否按顺序执行
     */
    @ApiModelProperty(value = "是否按照顺序", dataType = "Integer")
    private Integer isSequential;

    /**
     * 是否需要实时数据
     */
    @ApiModelProperty(value = "是否需要现场数据0. 否，1是" , dataType = "Integer")
    private Integer isRealtimeData;

    /**
     * 是否需要现场图片
     */
    @ApiModelProperty(value = "是否上传现场图片", dataType = "Integer")
    private Integer isPhoto;

    /**
     * 模板编码
     */
    @ApiModelProperty(value = "模板编码")
    private String tempNo;

    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String tempName;

    /**
     * 计划最后生成时间
     */
    @ApiModelProperty(hidden = true)
    private Date lastPlanGenerationTime;

    /**
     * 有效期（在该有效期内必须完成）
     */
    @ApiModelProperty(value = "有效期", dataType = "Integer")
    private Integer validityTime;

    /**
     * 下次计划开始时间
     */
    @ApiModelProperty(value = "下次任务生成时间", dataType = "Date")
    private Date nextStartTime;

    /**
     * 日历编码
     */
    @ApiModelProperty(value = "日历编码", dataType = "String")
    private String calendarCode;

    /**
     * 指派的部门下所有维修员
     */
    @ApiModelProperty(value = "指派的部门下所有员工")
    @Transient
    private List<Object> userNamesInDepartment;

    private static final long serialVersionUID = 1L;

    /**
     * 间隔
     * */
    @ApiModelProperty(value = "间隔数", dataType = "Integer")
    private Integer gapNum;

    /**
     * 间隔类型
     * */
    @ApiModelProperty(value = "间隔类型", dataType = "String")
    private String cycleCategory;

    /**
     * 审核意见
     * */
    @ApiModelProperty(hidden = true)
    private String auditOpinion;

    /**
     * 提交时间
     * */
    @ApiModelProperty(value = "提交时间", dataType = "Date")
    private Date commitTime;

    /**
     * 审核时间
     * */
    @ApiModelProperty(value = "审核时间", dataType = "Date")
    private Date approvalTime;

    /**
     * 审核人
     * */
    @ApiModelProperty(hidden = true)
    private String approvalUser;

    /**
     * 审核人编码
     * */
    @ApiModelProperty(hidden = true)
    private String approvalUserCode;

    /**
     * 流程id
     * */
    @ApiModelProperty(value = "流程id")
    private String activitiId;

    /**
     * 审核标识
     * */
    @ApiModelProperty(value = "审核标识")
    private String auditMark;


    /**
     * 任务模式
     * */
    @ApiModelProperty(value = "任务模式；0-认领；1-派发", dataType = "String")
    private String taskModel;

    @ApiModelProperty(value = "完成数量")
    @Transient
    private Integer doneNum;

}