package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.scs.po.ScsSaleDetailItem;
import com.imes.domain.entities.scs.po.ScsSaleForecastDetail;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ScsUpdateToPo {
    //主表的id
    private String id;
    //客户编码
    private String customerCode;
    //客户编码+name集合
    private String customerCodeName;
    //供应商平台机构编码
    private String orgPlatfromCode;
    //明细对象
    private List<UpdateListVo> updateLists;
    //需求预测回复List
    private List<ScsSaleForecastDetailVo> scsSaleForecastDetails;
    //需求回复时间
    private String responseDate;
    //需求回复人
    private String responsePersonName;
    //回复客户备注
    private String supplierRemarks;
    //消息
    private String sendMsg;
    //拒绝原因
    private String refuseRemarks;
}
