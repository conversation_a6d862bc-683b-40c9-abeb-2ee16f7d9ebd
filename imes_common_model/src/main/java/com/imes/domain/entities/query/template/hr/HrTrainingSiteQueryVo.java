package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr培训场所(HrTrainingSite)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-03-13 10:28:30	
 */	
@Data	
@ApiModel("员工培训场所")	
@QueryModel(name = "0684",	
        remark = "员工培训场所",	
        alias = "hr_training_site",	
        searchApi = "/api/hr/training/extend/site/query")	
public class HrTrainingSiteQueryVo extends BaseModel {	
	
    private String id;	
    /**	
     * 场所编号	
     */	
	@ApiModelProperty("场所编号")	
    @QueryField(name = "场所编号")	
    private String siteCode;	
    /**	
     * 场地名称	
     */	
	@ApiModelProperty("场地名称")	
    @QueryField(name = "场地名称")	
    private String name;	
    /**	
     * 场地地址	
     */	
	@ApiModelProperty("场地地址")	
    @QueryField(name = "场地地址")	
    private String address;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, dictOption = "HR_SITE_STATUS")	
    private String status;	
	
}	
	
