package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
	
@Data	
@ApiModel("采购申请变更单明细")	
@QueryModel(	
        name = "1212-detial",	
        remark = "采购申请变更单明细",	
        alias = {"po_purchase_request_detail_change"},	
        searchApi = "/api/po/poPurchaseRequestDetailChange/queryList")	
public class PoPurchaseRequestDetailChangeSearchVo extends BaseModel {	
	
	@ApiModelProperty("申请单号")	
    @QueryField(name = "申请单号")	
    private String requestNo;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号")	
    private String lineNo;	
	
	@ApiModelProperty("原申请数量")	
    @QueryField(name = "原申请数量", type = Type.Number)	
    private String oldRequestQty;	
	
	@ApiModelProperty("原申请到货日期")	
    @QueryField(name = "原申请到货日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String oldRequestArriveDate;	
	
	@ApiModelProperty("原备注")	
    @QueryField(name = "原备注")	
    private String oldRemarks;	
	
	@ApiModelProperty("原辅助属性")	
    @QueryField(name = "原辅助属性")	
    private String oldSkuCode;	
	
	@ApiModelProperty("变更类型0-无变更 1-新增 2-修改 3-删除")	
    @QueryField(name = "变更类型0-无变更 1-新增 2-修改 3-删除")	
    private String changeType;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称",alias = "ppc_material")	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格",alias = "ppc_material")	
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号",alias = "ppc_material.material_marker")	
    private String modelNumber;	
	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", type = Type.Number)	
    private String requestQty;	
	
	@ApiModelProperty("申请数量单位")	
    @QueryField(name = "申请数量单位")	
    private String unit;	
	
	@ApiModelProperty("关联数量")	
    @QueryField(name = "关联数量", type = Type.Number)	
    private String relationQty;	
	
	@ApiModelProperty("业务状态 10-正常 20-已关闭")	
    @QueryField(name = "业务状态 10-正常 20-已关闭")	
    private String businessStatus;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位")	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", type = Type.Number)	
    private String baseUnitQty;	
	
	@ApiModelProperty("申请到货日期")	
    @QueryField(name = "申请到货日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String requestArriveDate;	
	
	@ApiModelProperty("来源单据类型")	
    @QueryField(name = "来源单据类型")	
    private String businessSource;	
	
	@ApiModelProperty("来源单据号")	
    @QueryField(name = "来源单据号")	
    private String businessNo;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号")	
    private String businessLineNo;	
	
	@ApiModelProperty("来源方式")	
    @QueryField(name = "来源方式")	
    private String businessWay;	
	
	@ApiModelProperty("需求运算号")	
    @QueryField(name = "需求运算号")	
    private String calcNo;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性")	
    private String skuCode;	
	
	@ApiModelProperty("成品辅助属性")	
    @QueryField(name = "成品辅助属性")	
    private String productSkuCode;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人")	
    private String createBy;	
	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String updateOn;	
	
	@ApiModelProperty("更新人")	
    @QueryField(name = "更新人")	
    private String updateBy;	
	
    private String remarks;	
	
	@ApiModelProperty("源申请部门编码-名称")	
    @QueryField(name = "源申请部门编码-名称")	
    private String businessDepartmentName;	
	
	@ApiModelProperty("源申请人编码-名称")	
    @QueryField(name = "源申请人编码-名称")	
    private String businessUserName;	
	
	@ApiModelProperty("自定义字段")	
    @QueryField(name = "自定义字段")	
    private String custom;	
	
	@ApiModelProperty("采购申请变更单id")	
    @QueryField(name = "采购申请变更单id")	
    private String mainId;	
	
    private String  requestQtyPrecision;	
	
    private BigDecimal availableQty;	
	
    private String relationUnit;	
	
    private BigDecimal remainQty;	
	
    //单位名称	
    private String unitName;	
	
    // 基础单位名称	
    private String baseUnitName;	
}	
	
