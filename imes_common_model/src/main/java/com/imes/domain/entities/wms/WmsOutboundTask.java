package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "出库任务主表")	
public class WmsOutboundTask implements Serializable {	
	
    private static final long serialVersionUID = 286132659327291883L;	
	
@ApiModelProperty(value = "主键")	
private String id;	
	
@ApiModelProperty(value = "出库任务单号")	
private String outboundCode;	
	
@ApiModelProperty(value = "出库任务类型(1:领料出库；2:销售出库)")	
private String orderType;	
	
@ApiModelProperty(value = "出库方式：1：空托盘出库；2：拣货出库；")	
private String outboundType;	
	
@ApiModelProperty(value = "出库单类型（1：出库计划）")	
private String receiptType;	
	
@ApiModelProperty(value = "关联的单据号")	
private String receiptCode;	
	
@ApiModelProperty(value = "单据状态（10-未下达；20-执行中；50-已完成；99-已删除）")	
private String orderStatus;	
	
@ApiModelProperty(value = "第三方系统单号")	
private String thirdOrderCode;	
	
@ApiModelProperty(value = "申请人")	
private String applyBy;	
	
@ApiModelProperty(value = "仓库编码")	
private String whCode;	
	
@ApiModelProperty(value = "库区编码")	
private String areaCode;	
	
@ApiModelProperty(value = "库位编码")	
private String binCode;	
	
@ApiModelProperty(value = "载具编码")	
private String cellCode;	
	
@ApiModelProperty(value = "线体编码")	
private String routeCode;	
	
@ApiModelProperty(value = "输送设备编码")	
private String conveyerCode;	
	
@ApiModelProperty(value = "堆垛机编码")	
private String stackerCode;	
	
@ApiModelProperty(value = "起始设备编码")	
private String startConveyerCode;	
	
@ApiModelProperty(value = "终点设备编码")	
private String endConveyerCode;	
	
@ApiModelProperty(value = "备注")	
private String remarks;	
	
@ApiModelProperty(value = "创建时间")	
private Date createOn;	
	
@ApiModelProperty(value = "创建人")	
private String createBy;	
	
@ApiModelProperty(value = "更新时间")	
private Date updateOn;	
	
@ApiModelProperty(value = "更新人")	
private String updateBy;	
	
@ApiModelProperty(value = "自定义字段")	
private Object custom;	
	
@ApiModelProperty(value = "流程id")	
private String activityId;	
}	
