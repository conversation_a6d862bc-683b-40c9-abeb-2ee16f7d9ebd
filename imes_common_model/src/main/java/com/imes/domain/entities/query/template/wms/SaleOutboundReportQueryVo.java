package com.imes.domain.entities.query.template.wms;

import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data	
@ApiModel("销售出库")	
@QueryModel(	
        name = "0430_report",
        remark = "销售出库-自定义报表",
        searchApi = "/wms/outStorage/findSaleOutboundList",	
        alias = {"wms_storage_out_bill", "wms_storage_out_bill_item","task"},	
        pushApi = PushApi.SaleOutboundQueryVo,	
        showMode = true,
        report = true)
public class SaleOutboundReportQueryVo extends BaseModel {
	
	@ApiModelProperty("出库单号")	
    @QueryField(name = "出库单号")
    @EditField
    private String storageOutCode;	
	
	@ApiModelProperty("明细单号")	
    @QueryField(name = "明细单号", alias = "wms_storage_out_bill_item")
    @EditField(show = false)
    private String storageOutItemCode;	
	
	@ApiModelProperty("出库类型")	
    @QueryField(name = "出库类型", type = Type.Select, option = {"1","销售出库"}, value = "1", query = false)
    @EditField(readonly = true)
    private String receiptType;	
	
  //  @QueryField(name = "关联单号")	
    private String receiptCode;	
	
  //  @QueryField(name = "销售订单号")	
    private String parentOrderCode;	
	
	@ApiModelProperty("detailId")	
    @QueryField(name="detailId",alias = "wms_storage_out_bill_item.id",show = false)
    @EditField(show = false)
    private String detailId;	
	
	@ApiModelProperty("关联上游单据行号")	
    @QueryField(name="关联上游单据行号",alias = "wms_storage_out_bill_item",show = false)
    @EditField(show = false)
    private String thirdItemCode;	
	
	@ApiModelProperty("出库日期")	
    @QueryField(name = "出库日期", type = Type.Date)
    @EditField(required = true)
    private String approveOn;	
	
//    @QueryField(name = "出库方式", type = Type.Select, dictOption = "WMS_BOXED_TYPE")	
//    private String purpose;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")
    @EditField(readonly = true)
    private String status;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "mat")
    @EditField(readonly = true)
    private String materialName;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "mat")
    @EditField(readonly = true)
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "mat")
    @EditField(readonly = true)
    private String specification;	
	
	@ApiModelProperty("申请基本数量")	
    @QueryField(name = "申请基本数量", alias = "wms_storage_out_bill_item", type = Type.Number)
    @EditField(readonly = true)
    private String orderQty;	
	
	@ApiModelProperty("出库基本数量")	
    @QueryField(name = "出库基本数量", alias = ".ifnull(wms_storage_out_bill_item.picked_qty, 0)", type = Type.Number)
    @EditField(readonly = true)
    private String pickedQty;	
	
	@ApiModelProperty("基本单位")	
    @QueryField(name = "基本单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String unit;
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String deliveryCode;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String receiptItemCode;	
	
	@ApiModelProperty("批次")	
    @QueryField(name = "批次", alias = "task")
    @EditField
    private String batch;	
	
	@ApiModelProperty("物料辅助属性")	
    @QueryField(name = "物料辅助属性", alias = "task")
    @EditField(readonly = true)
    private String skuCode;	
	
	@ApiModelProperty("申请出库数量")	
    @QueryField(name = "申请出库数量", alias = "wms_storage_out_bill_item", type = Type.Number)
    @EditField(readonly = true)
    private String packApplyQty;	
	
	@ApiModelProperty("实际出库数量")	
    @QueryField(name = "实际出库数量",  alias = ".ifnull(wms_storage_out_bill_item.pack_out_storage_qty, 0)", type = Type.Number)
    @EditField(required = true)
    private String packOutStorageQty;	
	
	@ApiModelProperty("库存单位")	
    @QueryField(name = "库存单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(readonly = true)
    private String packCodeUnit;
	
	@ApiModelProperty("申请部门")	
    @QueryField(name = "申请部门", alias = "u2.user_name")
    @EditField(show = false)
    private String applyDepartName;	
	
	@ApiModelProperty("申请人工号")	
    @QueryField(name = "申请人工号")
    @EditField
    private String applyBy;	
	
	@ApiModelProperty("申请人名称")	
    @QueryField(name = "申请人名称", alias = "u1.user_name")
    @EditField(show = false)
    private String applyName;	
	
	@ApiModelProperty("第三方系统单号")	
    @QueryField(name = "第三方系统单号")
    @EditField(show = false)
    private String thirdOrderCode;	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称", alias = "cus", level = Level.Main)
    @EditField(show = false)
    private String customerName;	
	
	@ApiModelProperty("退货单位")	
    @QueryField(name = "退货单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")
    @EditField(show = false)
    private String returnPackUnit;
	
	@ApiModelProperty("退货数量")
    @QueryField(name = "退货数量", alias = ".IFNULL(wms_storage_out_bill_item.return_pack_qty,0)")
    @EditField(show = false)
    private String returnPackQty;
	
	@ApiModelProperty("退货单价")	
    @QueryField(name = "退货单价", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String unitPrice;
	
	@ApiModelProperty("箱码")	
    @QueryField(name = "箱码", alias = "task")
    @EditField(readonly = true)
    private String boxNo;	
	
	@ApiModelProperty("创建日期")	
    @QueryField(name = "创建日期", type = Type.Date, order = OrderBy.DESC)
    @EditField(show = false)
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", alias = "pe_user")
    @EditField(show = false)
    private String userName;	
	
	@ApiModelProperty("销售单号")	
    @QueryField(name = "销售单号", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String saleCode;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "wms_storage_out_bill_item", dictOption = "PPC_INCLUDE_TAX", type = Type.Select)
    @EditField(readonly = true)
    private String includeTax;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String includePrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String unIncludePrice;	
	
	@ApiModelProperty("折扣方式")	
    @QueryField(name = "折扣方式", alias = "wms_storage_out_bill_item", dictOption = "SALE_DISCOUNT_TYPE", type = Type.Select)
    @EditField(readonly = true)
    private String discountType;	
	
	@ApiModelProperty("折扣率(%)")	
    @QueryField(name = "折扣率(%)", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String discountRate;	
	
	@ApiModelProperty("折扣额")	
    @QueryField(name = "折扣额", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String discountPrice;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = "wms_storage_out_bill_item", dictOption = "SALE_TAX_CODE", type = Type.Select)
    @EditField(readonly = true)
    private String taxCode;	
	
	@ApiModelProperty("净价")	
    @QueryField(name = "净价", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String netPrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", type = Type.Number, alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String allPrice;

    @ApiModelProperty("供应商")
    @QueryField(name = "供应商", alias = "wms_storage_out_bill_item")
    @EditField(readonly = true)
    private String supplierCode;

    @ApiModelProperty("申请时间")
    @QueryField(name = "申请时间", alias = "wms_storage_in_bill_item")
    @EditField(readonly = true)
    private String applyDate;

    @ApiModelProperty("仓库")
    @QueryField(name = "仓库")
    @EditField(required = true)
    private String whCode;

    @ApiModelProperty("库位")
    @QueryField(name = "库位", alias = "wms_storage_in_bill_item")
    @EditField(required = true)
    private String binCode;

}	
