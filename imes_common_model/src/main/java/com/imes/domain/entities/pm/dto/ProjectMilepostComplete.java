package com.imes.domain.entities.pm.dto;

import com.imes.domain.entities.pm.po.ProjectMilepost;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<导入已完成项目里程碑使用>>
 * @company 捷创智能技术有限公司
 * @create 2021-09-17 13:36
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel("导入已完成项目里程碑使用")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostComplete  extends ProjectMilepost {
    private String userCodes;
    private String hours;
    private String planHours;
}
