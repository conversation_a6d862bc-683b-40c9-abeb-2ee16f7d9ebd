package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.system.ppc.PpcRouteProcessFile;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsRouteInspectionDetailVo implements Serializable {	
    /**	
     * 	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 主表id	
     */	
	@ApiModelProperty("主表id")	
    private String mainId;	
	
    /**	
     * 质检项编码	
     */	
	@ApiModelProperty("质检项编码")	
    private String mainItemCode;	
	
    /**	
     * 质检项名称	
     */	
	@ApiModelProperty("质检项名称")	
    private String mainItemName;	
	
    /**	
     * 工艺路线编码	
     */	
	@ApiModelProperty("工艺路线编码")	
    private String routeCode;	
	
    /**	
     * 工序	
     */	
	@ApiModelProperty("工序")	
    private String processCode;	
	
    /**	
     * 质检项编码	
     */	
	@ApiModelProperty("质检项编码")	
    private String inspectCode;	
	
    /**	
     * 子项序号	
     */	
	@ApiModelProperty("子项序号")	
    private Integer itemIndex;	
	
    /**	
     * 产品名称	
     */	
	@ApiModelProperty("产品名称")	
    private String itemName;	
	
    /**	
     * 检测工具数据字典	
     */	
	@ApiModelProperty("检测工具数据字典")	
    private String toolType;	
	
    /**	
     * 判定方式1自动判定，2人工判定	
     */	
	@ApiModelProperty("判定方式1自动判定，2人工判定")	
    private String judgeMethod;	
	
    /**	
     * 上限	
     */	
	@ApiModelProperty("上限")	
    private BigDecimal upperLimit;	
	
    /**	
     * 下限	
     */	
	@ApiModelProperty("下限")	
    private BigDecimal lowerLimit;	
	
    /**	
     * 非理化标准	
     */	
	@ApiModelProperty("非理化标准")	
    private String standardDesc;	
	
    /**	
     * 实际参数	
     */	
	@ApiModelProperty("实际参数")	
    private String realityValue;	
	
    /**	
     * 是否合格	
     */	
	@ApiModelProperty("是否合格")	
    private String isQualified;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
