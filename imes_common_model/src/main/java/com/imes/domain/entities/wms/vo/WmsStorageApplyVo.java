package com.imes.domain.entities.wms.vo;

import io.swagger.annotations.ApiModel;
import com.imes.domain.entities.wms.WmsStorageApply;
import io.swagger.annotations.ApiModelProperty;
import com.imes.domain.entities.wms.WmsStorageApplyItem;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class WmsStorageApplyVo extends WmsStorageApply {

    /**
     * 物料编码
     */
    @ApiModelProperty("物料编码")
    private String materialCode;

    /**
     * 物料名称
     */
    @ApiModelProperty("物料名称")
    private String materialName;

    /**
     * 型号
     */
    @ApiModelProperty("型号")
    private String materialMarker;

    /**
     * 规格
     */
    @ApiModelProperty("规格")
    private String specification;

    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;

    /**
     * 单位
     */
    @ApiModelProperty("单位")
    private String unit;

    /**
     * 申请数量
     */
    @ApiModelProperty("申请数量")
    private BigDecimal orderQty;

    /**
     * 辅助属性
     */
    @ApiModelProperty("辅助属性")
    private String skuCode;

    /**
     * 明细单主键
     */
    @ApiModelProperty("明细单主键")
    private String detailId;

    @ApiModelProperty("库存数量")
    private BigDecimal availableQty;

    @ApiModelProperty("仓库")
    private String warehouseName;

    @ApiModelProperty("库位")
    private String binCode;

}	
