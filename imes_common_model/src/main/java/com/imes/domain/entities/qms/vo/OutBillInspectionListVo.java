package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.InBillInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.OutBillInspection;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class OutBillInspectionListVo implements Serializable {	
	
    /**	
     * 主信息	
     */	
	@ApiModelProperty("主信息")	
    OutBillInspection main;	
	
    /**	
     * 发货单号组	
     */	
	@ApiModelProperty("发货单号组")	
    String[] infos;	
	
    /**	
     * 报工单号组长度	
     */	
	@ApiModelProperty("报工单号组长度")	
    int length;	
    private static final long serialVersionUID = 1L;	
}	
