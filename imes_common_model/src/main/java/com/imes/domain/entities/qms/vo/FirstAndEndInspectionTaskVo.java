package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.FirstAndEndInspection;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
public class FirstAndEndInspectionTaskVo extends FirstAndEndInspection implements Serializable {	
	
	
    /**	
     * 检验单号	
     */	
	@ApiModelProperty("检验单号")	
    private String taskInspectionCode;	
	
    private static final long serialVersionUID = 1L;	
}	
