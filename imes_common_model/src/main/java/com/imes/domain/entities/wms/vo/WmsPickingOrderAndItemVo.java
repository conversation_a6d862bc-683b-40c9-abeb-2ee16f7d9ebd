package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPickingOrder;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsPickingOrderItem;	
import lombok.Data;	
	
import java.util.List;	
	
@Data	
public class WmsPickingOrderAndItemVo extends WmsPickingOrder {	
    /**	
     * 标识	
     * 0：保存	
	@ApiModelProperty("标识")	
     * 1：提交	
	@ApiModelProperty("0：保存")	
     */	
	@ApiModelProperty("1：提交")	
    private int flag;	
	
    private String pickNo;	
	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 领用人工号	
     */	
	@ApiModelProperty("领用人工号")	
    private String pickUserCode;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String receiptType;

    @ApiModelProperty("是否显示确认入库按钮")
    private boolean isShowBtn;

    @ApiModelProperty("是否自动入线边库")
    private boolean autoInbound;


}	
