package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsStorageOutBill;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageOutBillItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsOutStorageAndItemVo extends WmsStorageOutBill {	
    private List<WmsStorageOutBillItem> items;	
	
    private List<WmsOutStorageItemQtyVo> itemQtyVos;	
	
    //标识	
    private Integer flag;	
	
    /**	
     * 线边库编码	
     */	
	@ApiModelProperty("线边库编码")	
    private String storageAreaCode;	
	
    /**	
     * 线边库名称	
     */	
	@ApiModelProperty("线边库名称")	
    private String storageAreaName;	
	
    /**	
     * 申请人编码	
     */	
	@ApiModelProperty("申请人编码")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 领料人编码	
     */	
	@ApiModelProperty("领料人编码")	
    private String pickUserCode;	
	
    /**	
     * 领料人名称	
     */	
	@ApiModelProperty("领料人名称")	
    private String pickUserName;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领料申请类型	
     */	
	@ApiModelProperty("领料申请类型")	
    private String orderType;	
}	
