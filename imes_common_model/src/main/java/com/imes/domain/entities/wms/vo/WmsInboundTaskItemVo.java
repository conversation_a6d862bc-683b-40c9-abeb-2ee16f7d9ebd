package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsInboundTaskItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
public class WmsInboundTaskItemVo extends WmsInboundTaskItem {	
	
    private String cellCode;	
	
    /**	
     * 入库申请主单号	
     */	
	@ApiModelProperty("入库申请主单号")	
    private String applyCode;	
	
    /**	
     * 入库申请主单单据类型	
     */	
	@ApiModelProperty("入库申请主单单据类型")	
    private String applyOrderType;	
	
    /**	
     * 入库申请明细单号	
     */	
	@ApiModelProperty("入库申请明细单号")	
    private String applyItemCode;	
	
}	
