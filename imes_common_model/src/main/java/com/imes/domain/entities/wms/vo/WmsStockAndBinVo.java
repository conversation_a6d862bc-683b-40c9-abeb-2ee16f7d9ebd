package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("下午茶看板物料库存库位实体类")	
public class WmsStockAndBinVo  {	
	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    @ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty("库位")	
    private String binCode;	
	
    @ApiModelProperty("在库库存")	
    private BigDecimal onhandQty;	
	
    @ApiModelProperty("单位")	
    private String unit;	
}	
