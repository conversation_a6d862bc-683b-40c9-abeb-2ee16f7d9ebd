package com.imes.domain.entities.scs.vo;


import lombok.Data;

import java.math.BigDecimal;

@Data
public class ScSaccountVo {
    //1 入库单 2退库单
    private String type;
    //退库入库单id
    private String ids;
    //金额
    private BigDecimal singlePrice;
    //单价是否含税 0是 1否
    private String includeTax;

    private BigDecimal taxRate;

    private String remarks;

    private BigDecimal unIncludePrice;
    private BigDecimal includePrice;

    //给我系数和原有金额
    private BigDecimal settlementRatio;
    //
    private BigDecimal oldUnIncludePrice;

    private String priceId;
}
