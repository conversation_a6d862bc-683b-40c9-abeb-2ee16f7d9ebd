package com.imes.domain.entities.query.template.pm;

import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import com.imes.domain.entities.query.model.base.Type;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("项目里程碑")
@QueryModel(
        name = "01861",
        remark = "项目里程碑"
)
public class ProProjectMilepostQueryVo extends BaseModel {

    @ApiModelProperty("里程碑名称")
    @QueryField(name = "里程碑名称")
    private String text;

    @ApiModelProperty("状态")
    @QueryField(name = "状态", type = Type.MultiSelect, option = {"0", "未开始", "1", "进行中", "4", "已完成"})
    private String state;

    @ApiModelProperty("开始日期")
    @QueryField(name = "开始日期", type = Type.Date, format = "yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("结束日期")
    @QueryField(name = "结束日期", type = Type.Date, format = "yyyy-MM-dd")
    private String endDate;

}	
