package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsBusTaskHistory implements Serializable {	
    /**	
     * 主键	
     */	
    @JSONField(name = "主键")
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 业务主键	
     */	
	@ApiModelProperty("业务主键")	
    @JSONField(name = "业务主键")	
    private String busCode;	
	
    /**	
     * 1:入库单；2：出库单;3:到货单；	
     */	
	@ApiModelProperty("1:入库单；2：出库单;3:到货单；")	
    @JSONField(name = "1:入库单；2：出库单;3:到货单")	
    private Integer busType;	
	
    /**	
     * 物料备件序列号	
     */	
	@ApiModelProperty("物料备件序列号")	
    @JSONField(name = "物料备件序列号")	
    private String serialNo;	
	
    /**	
     * 创建时间	
     */	
    @JSONField(name = "创建时间")
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
    @JSONField(name = "创建人")
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 物料辅助属性	
     */	
	@ApiModelProperty("物料辅助属性")	
    @JSONField(name = "物料辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    @JSONField(name = "箱号")	
    private String boxNo;	
	
    private static final long serialVersionUID = 1L;	
}	
