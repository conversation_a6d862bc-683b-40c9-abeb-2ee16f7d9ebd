package com.imes.domain.entities.query.template.pc;

import com.imes.domain.entities.query.model.base.*;
import lombok.Data;
import io.swagger.annotations.ApiModelProperty;

@Data
@QueryModel(
        name = "",
        remark = "班组计件总表",
        searchApi = "/api/pc/ppcPieceTeamMonth/queryList")
public class PpcPieceTeamMonthSearchVo extends BaseModel {

    @ApiModelProperty(value = "计件月份yyyy-mm")
    @QueryField(name = "计件月份yyyy-mm")
    private String monthDate;

    @ApiModelProperty(value = "计件工段")
    @QueryField(name = "计件工段")
    private String bigProcessCode;

    @ApiModelProperty(value = "工段名称")
    @QueryField(name = "工段名称")
    private String bigProcessName;

    @ApiModelProperty(value = "班组编码")
    @QueryField(name = "班组编码")
    private String teamCode;

    @ApiModelProperty(value = "班组名称")
    @QueryField(name = "班组名称")
    private String teamName;

    @ApiModelProperty(value = "总金额")
    @QueryField(name = "总金额", type = Type.Number)
    private String totalMoney;

    @ApiModelProperty(value = "总数量")
    @QueryField(name = "总数量", type = Type.Number)
    private String qty;

    @ApiModelProperty(value = "总面积")
    @QueryField(name = "总面积", type = Type.Number)
    private String area;

    @ApiModelProperty(value = "进度1创建2个人3集体")
    @QueryField(name = "进度1创建2个人3集体")
    private String stage;

    @ApiModelProperty(value = "状态")
    @QueryField(name = "状态")
    private String status;

    @ApiModelProperty(value = "创建时间")
    @QueryField(name = "创建时间", type = Type.DateTime)
    private String createOn;

    @ApiModelProperty(value = "创建人")
    @QueryField(name = "创建人", type = Type.Select, sqlOption = "select user_code as value,user_name as label from pe_user ")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    @QueryField(name = "更新时间", type = Type.Date, format = "yyyy-MM-dd")
    private String updateOn;

    @ApiModelProperty(value = "更新人")
    @QueryField(name = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    @QueryField(name = "备注")
    private String remarks;


}

