package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import com.imes.domain.entities.query.plugin.imports.ImportRemark;
import lombok.Data;

@Data
@ImportModel(
        name = "库位信息导入模板",
        modelName = "0134")
public class WmsBinImportVo extends BaseModel {

    @ImportField(name = "仓库编码", required = true, remark = "必填\n参考【仓库信息】", maxLength = 40)
    private String whCode;

    @ImportField(name = "库区编码", required = true, remark = "必填\n参考【库区信息】", maxLength = 40)
    private String areaCode;

    @ImportField(name = "库位编码", required = true, remark = "必填", maxLength = 40)
    private String binCode;

    @ImportField(name = "库位名称", required = true, remark = "必填", maxLength = 40)
    private String binName;

    @ImportField(name = "库位类型", required = true, remark = "必填\n写入对应数字\n1:普通货位\n2:次品货位\n3:维修品货位\n4:虚拟货位", dictOption = "STORAGE_BIN_TYPE")
    private String binTypeCode;

    @ImportField(name = "禁用类型", remark = "写入对应数字\n1:可用\n2:禁用\n3:废弃")
    private String disableStatus;

    @ImportField(name = "列", isNumber = true, isInteger = true, remark = "必须是整数")
    private String kind;

    @ImportField(name = "层", isNumber = true, isInteger = true, remark = "必须是整数")
    private String layer;

    @ImportField(name = "长度(米)", isNumber = true, remark = "必须是数字", maxNumber = "99999999.99")
    private String length;

    @ImportField(name = "宽度(米)", isNumber = true, remark = "必须是数字", maxNumber = "99999999.99")
    private String width;

    @ImportField(name = "高度(米)", isNumber = true, remark = "必须是数字", maxNumber = "99999999.99")
    private String height;

    @ImportField(name = "最大托盘数量", isNumber = true, isInteger = true, remark = "必须是整数")
    private String storageCellMax;

    @ImportField(name = "最大承受重量(KG)", isNumber = true, remark = "必须是数字", maxNumber = "99999999.99")
    private String maxWeight;

    @ImportField(name = "最大容纳体积(m³)", isNumber = true, remark = "必须是数字", maxNumber = "99999999.99")
    private String maxVolume;

    @ImportField(name = "物料混合存放", isNumber = true, remark = "写入对应数字\n0:否\n1:是")
    private String materialMix;

    @ImportField(name = "批次混合", isNumber = true, remark = "写入对应数字\n0:否\n1:是")
    private String batchMix;

    @ImportField(name = "库存为0自动释放", isNumber = true, remark = "写入对应数字\n0:否\n1:是")
    private String autoRelease;

    @ImportField(name = "序号", isNumber = true, isInteger = true, remark = "必须是整数")
    private String sequence;
}
