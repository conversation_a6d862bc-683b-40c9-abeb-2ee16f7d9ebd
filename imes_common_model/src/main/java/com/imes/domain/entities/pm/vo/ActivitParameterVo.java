package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<审批提交参数>>
 * @company 捷创智能技术有限公司
 * @create 2021-05-08 14:14
 */
@ApiModel("工作流审批提交参数Vo")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ActivitParameterVo {
    @ApiModelProperty("业务")
    private String business;
    @ApiModelProperty("业务id")
    private String businessId;
    @ApiModelProperty("任务编号")
    private String taskId;
    @ApiModelProperty("任务类型")
    private String taskType;
    @ApiModelProperty("用户工号")
    private String userCode;
    @ApiModelProperty("状态")
    private String flag;
    @ApiModelProperty("备注")
    private String comment;
    @ApiModelProperty("额外参数")
    private Map<String,Object> variables;
}
