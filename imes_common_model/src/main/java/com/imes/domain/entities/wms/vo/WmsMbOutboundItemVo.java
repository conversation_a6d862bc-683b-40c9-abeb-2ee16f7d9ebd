package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsParts;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageOutBillItem;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
/**	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-03-17 14:53	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMbOutboundItemVo extends WmsStorageOutBillItem {	
	
    /**	
     * 关联单据主单编码	
     */	
	@ApiModelProperty("关联单据主单编码")	
    private String receiptCode;	
	
    /**	
     * 关联单据子单编码	
     */	
	@ApiModelProperty("关联单据子单编码")	
    private String thirdItemCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 物料主键	
     */	
	@ApiModelProperty("物料主键")	
    private String materialId;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 是否有序列号(0-否（缺省） 1-是)	
     */	
	@ApiModelProperty("是否有序列号(0-否（缺省） 1-是)")	
    private String isSerial;	
	
    /**	
     * 保修期	
     */	
	@ApiModelProperty("保修期")	
    private BigDecimal warrantyPeriod;	
	
    /**	
     * 保质期	
     */	
	@ApiModelProperty("保质期")	
    private BigDecimal qualityPeriod;	
	
    /**	
     * 库位库存	
     */	
	@ApiModelProperty("库位库存")	
    private List<WmsMbStockBinVo> stockBinVos;	
	
    /**	
     * 可出库备件	
     */	
	@ApiModelProperty("可出库备件")	
    private List<WmsParts> parts;	
	
	
    /***********页面控制所需参数*************/	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean active;	
	
    /**	
     * 当前明细是否被选择	
     */	
	@ApiModelProperty("当前明细是否被选择")	
    private Boolean show;	
	
    /**	
     * 扫码长度	
     */	
	@ApiModelProperty("扫码长度")	
    private Integer scanLength;	
	
    /**	
     * 物料条码	
     */	
	@ApiModelProperty("物料条码")	
    private String barCode;	
	
    /**	
     * SN码数量	
     */	
	@ApiModelProperty("SN码数量")	
    private BigDecimal serialQty;	
	
    /**	
     * 匹配库存数量	
     */	
	@ApiModelProperty("匹配库存数量")	
    private BigDecimal matchQty;	
	
    /**	
     * 可以用库存数量	
     */	
	@ApiModelProperty("可以用库存数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 总共已拣货数量数量	
     */	
	@ApiModelProperty("总共已拣货数量数量")	
    private BigDecimal totalPickedQty;	
	
    /**	
     * 是否需要修改物料条码	
     */	
	@ApiModelProperty("是否需要修改物料条码")	
    private Boolean updateBarCode;	
    /**	
     * 上架仓库	
     */	
	@ApiModelProperty("上架仓库")	
    private String whCode;	
	
    /**	
     * 上架仓库	
     */	
	@ApiModelProperty("上架仓库")	
    private String whName;	
	
    /**	
     * 上架库位	
     */	
	@ApiModelProperty("上架库位")	
    private String binName;	
	
    /**	
     * 明细单序号	
     */	
	@ApiModelProperty("明细单序号")	
    private String sdNo;	
	
    /**	
     * 工程名称	
     */	
	@ApiModelProperty("工程名称")	
    private String projectName;	
	
    /**	
     * 二级分类	
     */	
	@ApiModelProperty("二级分类")	
    private String purpose;	
	
    /**	
     * 库位库存	
     */	
	@ApiModelProperty("库位库存")	
    private List<WmsMbStockBinVo> btmList;	
	
    /**	
     * 前端提交 序列号（SN码）	
     */	
	@ApiModelProperty("前端提交 序列号（SN码）")	
    private List<String> serialNumbers;	
	
    /**	
     * 前端提交 序列号（SN码）	
     */	
	@ApiModelProperty("前端提交 序列号（SN码）")	
    private List<String> oldSerialNumbers;	
	
}	
