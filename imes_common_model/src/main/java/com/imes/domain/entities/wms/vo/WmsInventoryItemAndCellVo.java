package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
/**	
 * 盘点计划明细对象	
 */	
@Data	
public class WmsInventoryItemAndCellVo {	
	
    /**	
     * id	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 载具编码	
     */	
	@ApiModelProperty("载具编码")	
    private String cellCode;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 库存量	
     */	
	@ApiModelProperty("库存量")	
    private BigDecimal stockQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String primaryUnit;	
	
    /**	
     * 锁定状态	
     */	
	@ApiModelProperty("锁定状态")	
    private String lockStatus;	
	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
}	
