package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsOutboundTaskItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsOutboundTaskItemVo extends WmsOutboundTaskItem {	
	
    @ApiModelProperty(value = "出库任务主单主键")	
    private String taskId;	
	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "库区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "库区名称")	
    private String areaName;	
	
    @ApiModelProperty(value = "库位编码")	
    private String binCode;	
	
    @ApiModelProperty(value = "库位名称")	
    private String binName;	
	
    @ApiModelProperty(value = "载具编码")	
    private String cellCode;	
	
    @ApiModelProperty(value = "载具名称")	
    private String cellName;	
	
    @ApiModelProperty(value = "线体编码")	
    private String routeCode;	
	
    @ApiModelProperty(value = "线体名称")	
    private String routeName;	
	
    @ApiModelProperty(value = "出库输送线编码")	
    private String conveyerCode;	
	
    @ApiModelProperty(value = "出库输送线名称")	
    private String conveyerName;	
	
    @ApiModelProperty(value = "出库任务类型")	
    private String orderType;	
	
    @ApiModelProperty(value = "任务状态")	
    private String orderStatus;	
	
    @ApiModelProperty(value = "拣选出库单类型")	
    private String receiptType;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "规格")	
    private String specification;	
	
    @ApiModelProperty(value = "进位方式")	
    private Byte carryMode;	
	
    @ApiModelProperty(value = "精度")	
    private Byte precisionDigit;	
	
    @ApiModelProperty(value = "库存单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "库存数量")	
    private BigDecimal packAvailableQty;	
	
    @ApiModelProperty(value = "基本单位")	
    private String primaryUnit;	
	
    @ApiModelProperty(value = "基本库存数量")	
    private BigDecimal primaryAvailableQty;	
	
    @ApiModelProperty(value = "待拣货数量")	
    private BigDecimal needQty;	
	
    /**	
     * 出库计划主单号	
     */	
	@ApiModelProperty("出库计划主单号")	
    private String poCode;	
	
    /**	
     * 出库计划主单单据类型	
     */	
	@ApiModelProperty("出库计划主单单据类型")	
    private String poOrderType;	
	
    /**	
     * 出库计划明细单号	
     */	
	@ApiModelProperty("出库计划明细单号")	
    private String poItemCode;	
	
}	
