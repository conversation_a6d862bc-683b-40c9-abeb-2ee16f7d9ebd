package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * hr培训请假记录(HrTrainingLeave)实体类	
 *	
 * <AUTHOR> z	
 * @since 2022-11-29 16:30:51	
 */	
@Data	
@ApiModel("员工培训请假")	
@QueryModel(name = "0696",	
        remark = "员工培训请假",	
        alias = "leaves",	
        searchApi = "/api/hr/training/extend/leave/query")	
public class HrTrainingLeaveQueryVo extends BaseModel {	
	
    /**	
     * 请假人	
     */	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码")	
    private String userCode;	
    /**	
     * 请假课程	
     */	
	@ApiModelProperty("请假课程")	
    @QueryField(name = "请假课程")	
    private String trainingId;	
    /**	
     * 原因	
     */	
	@ApiModelProperty("原因")	
    @QueryField(name = "原因")	
    private String reason;	
    /**	
     * 发起时间	
     */	
	@ApiModelProperty("发起时间")	
    @QueryField(name = "发起时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String startTime;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称")	
    private String userName;	
	
	@ApiModelProperty("培训名称")	
    @QueryField(name = "培训名称")	
    private String trainingName;	
	
}	
	
