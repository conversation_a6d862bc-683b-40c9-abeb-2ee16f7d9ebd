package com.imes.domain.entities.dev.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 设备构造器
 *
 * <AUTHOR>
 */
@Data
@TableName(value = "dev_device_construct")
public class DeviceConstruct {

    @TableId
    private String id;

    /**
     * 名称
     */
    private String name;

    /**
     * 字段名
     */
    private String fieldName;

    /**
     * 物理量单位类型
     */
    private String unitType;

    /**
     * 物理量单位
     */
    private String unit;

    /**
     * 描述
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String description;

    /**
     * 数量
     */
    private Integer number;

    /**
     * HMI地址
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String hmiUrl;

    /**
     * 类型
     * 见{@link com.imes.common.constant.DeviceConstructType}
     */
    private String type;

    /**
     * 顶级id
     */
    private String topId;

    /**
     * 父级id
     */
    private String parentId;

    /**
     * 非必要, 先不使用
     */
    private Integer subNumber;

    /**
     * 详细内容, 先不使用
     */
    private String content;

    /**
     * 值
     */
    private String value;

    /**
     * 值类型
     */
    private String valueType;

    /**
     * 文件id
     */
    private String fileId;

    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件扩展名
     */
    private String fileExtName;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件上传人
     */
    private String fileUploadUserCode;

    /**
     * 文件上传时间
     */
    private Date fileUploadTime;

    /**
     * 点位
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String pointTag;

    /**
     * 库/表
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String pointSheet;

    /**
     * 公式
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String formula;

    /**
     * 公式开关
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Boolean switchFormula;

    /**
     * 数据模拟开关
     */
    private Boolean switchDataSimulation;

    /**
     * 上限值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String upperLimitValue;

    /**
     * 下限值
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String lowerLimitValue;

    /**
     * 函数发生器开关
     */
    private Boolean switchFunctionGenerator;

    /**
     * 函数发生器类型
     */
    private String functionGeneratorType;

    /**
     * 函数发生器公式
     */
    private String functionGeneratorFormula;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updateBy;
}
