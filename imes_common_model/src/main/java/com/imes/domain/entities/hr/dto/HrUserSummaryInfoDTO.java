package com.imes.domain.entities.hr.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class HrUserSummaryInfoDTO implements Serializable {

    private static final long serialVersionUID = 786253286663135066L;
    /**
     * 用户编号
     */
    private String userCode;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 手机
     */
    @Pattern(regexp = "^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\\d{8}$", message = "手机格式错误")
    private String mobile;
    /**
     * 上级领导
     */
    private String leaderUserCode;
}
