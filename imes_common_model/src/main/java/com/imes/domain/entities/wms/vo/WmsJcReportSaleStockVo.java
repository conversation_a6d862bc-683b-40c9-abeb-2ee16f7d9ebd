package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportSaleStockVo {	
	
	
    /**	
     * 销售员工号	
     */	
    @ApiModelProperty("销售员工号")	
    private String salesUserCode;	
	
    /**	
     * 销售员姓名	
     */	
    @ApiModelProperty("销售员姓名")	
    private String salesUserName;	
	
    /**	
     * 在库库存	
     */	
    @ApiModelProperty("在库库存")	
    private BigDecimal onHandQty;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal onHandPrice;	
	
}	
