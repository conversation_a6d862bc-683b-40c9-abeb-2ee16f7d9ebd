package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.pm.annotation.ChangeLog;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * (ProProjectMilepostEmployee)实体类
 *
 * <AUTHOR>
 * @since 2020-11-25 09:49:15
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_milepost_employee",resultMap = "BaseResultMap")
public class ProjectMilepostEmployee  implements Serializable {
    private static final long serialVersionUID = 463867663182856919L;
    /**
    * id
    */
    private String id;
    /**
    * 项目里程碑id
    */
    private String milepostId;
    /**
    * 参与人员id
    */
    private String employeeId;
    /**
     * 参与人姓名
     */
    @TableField(exist = false)
    private String employeeName;
    /**
     * 岗位
     */
    private String job;
    /**
    * 开始日期
    */
    private LocalDate startDate;
    /**
    * 结束日期
    */
    private LocalDate endDate;
    /**
    * 任务内容
    */
    @ChangeLog("任务内容")
    @TableField(condition = SqlCondition.LIKE)
    private String task;
    /**
     * 工时
     */
    @ChangeLog("工时")
    private BigDecimal workHour;
    /**
    * 加入时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime joinTime;
    /**
    * 创建日期
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改日期
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
}