package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
	
/**	
 * 仓库组装拆卸单(WmsAssembleOrder)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-08-07 15:32:21	
 */	
@Data	
public class WmsAssembleOrder implements Serializable {	
    private static final long serialVersionUID = -61743583528138280L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "装卸单号")	
    private String assembleNo;	
	
    @ApiModelProperty(value = "单据日期")	
    private Date orderDate;	
	
    @ApiModelProperty(value = "单据状态（10-已录入；20-已生效；50-已完成）")	
    private String orderStatus;	
	
    @ApiModelProperty(value = "备注")	
    private String remark;	
	
    @ApiModelProperty(value = "第三方系统单号")	
    private String thirdOrderCode;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "申请人工号")	
    private String applyUserCode;	
	
    @ApiModelProperty(value = "申请人名称")	
    private String applyUserName;	
	
}	
	
