package com.imes.domain.entities.hr.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组织表(Department)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-29 14:57:51
 */
@Data
@ApiModel("组织基础表")
@AllArgsConstructor
@NoArgsConstructor
public class DepartmentBaseInfoDTO {

    /**
     * 部门编号
     */
    private String departCode;
    /**
     * 部门名称
     */
    private String departName;

    private String hasChilds;

    /**
     * 子目录字段
     */
    private List<DepartmentBaseInfoDTO> children;

}

