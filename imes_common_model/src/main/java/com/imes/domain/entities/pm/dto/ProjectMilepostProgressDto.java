package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * (ProjectMilepostProgress)实体类
 *
 * <AUTHOR>
 * @since 2020-12-07 14:11:20
 */
@ApiModel("里程碑进度实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostProgressDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 308487428248907022L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("新阳guid保证不会重复同步")
    private String guid;
    @ApiModelProperty("总上报id")
    private String sumId;
    @ApiModelProperty("上报类型")
    private Boolean type;
    @ApiModelProperty("项目id")
    private String projectId;
    @ApiModelProperty("项目名称")
    private String projectName;
    @ApiModelProperty("里程碑id")
    @NotEmpty(message = "里程碑id不能为空")
    private String milepostId;
    @ApiModelProperty("里程碑名称")
    private String milepostName;
    @ApiModelProperty("上报id")
    private String reportId;
    @ApiModelProperty("参与人工号")
    private String employeeCode;
    @ApiModelProperty("参与人姓名")
    private String employeeName;
    @ApiModelProperty("上报日期")
    private LocalDate reportDate;
    @ApiModelProperty("工时")
    @DecimalMin(value = "0", message = "工时不能小于0")
    @DecimalMax(value = "24", message = "最大工时不能超过24小时")
    private BigDecimal workHour;
    @ApiModelProperty("进度")
    @NotNull(message = "程碑进度不能为空")
    @Max(value = 100, message = "进度不能超过100%")
    @Min(value = 1, message = "进度不能小于1%")
    private Integer progress;
    @ApiModelProperty("内容")
    private String content;
    @ApiModelProperty("备注")
    private String remarks;
    @ApiModelProperty("实际开始日期")
    private LocalDate actualStartDate;
    @ApiModelProperty("实际结束日期")
    private LocalDate actualFinishDate;
    @ApiModelProperty("完结")
    @Null(message = "完结必须为空")
    private Boolean complete;
    @ApiModelProperty("日期范围")
    private List<LocalDate> dates;
    @ApiModelProperty("推送人")
    private List<String> pushEmployeeCodes;
    /*======================================成本=======================================*/
    /**
     * '飞机费用'
     */
    private BigDecimal aircraftCost;
    /**
     * 飞机票数量
     */
    private Integer numberAirTickets;
    /**
     * 火车费用
     */
    private BigDecimal trainCost;
    /**
     * 火车票数量
     */
    private Integer numberTrainTickets;
    /**
     * 大巴费用
     */
    private BigDecimal busCost;
    /**
     * 大巴票数量
     */
    private Integer numberBusTickets;
    /**
     * 的士费用
     */
    private BigDecimal taxiCost;
    /**
     * 的士票数量
     */
    private Integer numberTaxiTickets;
    /**
     * 过路费用
     */
    private BigDecimal tollCost;
    /**
     * 过路票数量
     */
    private Integer numberTollTickets;
    /**
     * 停车费用
     */
    private BigDecimal parkingCost;
    /**
     * 停车票数量
     */
    private Integer numberParkingTickets;
    /**
     * 其他交通费用
     */
    private BigDecimal otherTrafficCost;
    /**
     * 其他交通票数量
     */
    private Integer numberOtherTrafficTickets;
    /**
     * 住宿费用
     */
    private BigDecimal stayCost;
    /**
     * 住宿费用票数量
     */
    private Integer numberStayTickets;
    /**
     * 招待费用
     */
    private BigDecimal entertainCost;
    /**
     * 招待费用票数量
     */
    private Integer numberEntertainTickets;
    /**
     * 招待人名
     */
    private String guests;
    /**
     * 招待人数
     */
    private Integer numberGuests;
    /**
     * 招待目的
     */
    private String guestsObjectives;
    /**
     * 差旅津贴
     */
    private BigDecimal travelAllowance;
    /**
     * 差旅津贴费用票数
     */
    private Integer numberAllowanceTickets;
    /**
     * 其他费用
     */
    private BigDecimal otherCost;
    /**
     * 其他费用票数量
     */
    private Integer numberOtherTickets;
    /**
     * 报销说明
     */
    private String reimbursementInstructions;
    /**
     * 补贴金额
     */
    private BigDecimal subsidy;
    /**
     * 经纬度
     */
    private String latlng;
    /**
     * 国家
     */
    private String nation;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String district;
    /**
     * 详细地址
     */
    private String detailAddress;
    /*======================================附件=======================================*/
    /**
     * 附件id
     */
    private List<String> fileIds;
}