package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
/**	
 * 生产领料明细	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-04-13 10:23	
 */	
@Data	
public class C_WmsOutboundItemVo {	
	
    /**	
     * 物料编码--必填	
     */	
	@ApiModelProperty("物料编码--必填")	
    private String materialCode;	
	
    /**	
     * 领料数量--必填	
     */	
	@ApiModelProperty("领料数量--必填")	
    private BigDecimal qty;	
	
    /**	
     * 主单位--必填	
     */	
	@ApiModelProperty("主单位--必填")	
    private String primaryUnit;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 包装规格编码	
     */	
	@ApiModelProperty("包装规格编码")	
    private String packCode;	
	
    /**	
     * 包装数量(10箱、10盒)	
     */	
	@ApiModelProperty("包装数量(10箱、10盒)")	
    private BigDecimal packQty;	
	
    /**	
     * 包装单位（箱、盒）	
     */	
	@ApiModelProperty("包装单位（箱、盒）")	
    private String packUnit;	
	
    /**	
     * 包装系数（一箱有多少个）	
     */	
	@ApiModelProperty("包装系数（一箱有多少个）")	
    private BigDecimal packNumber;	
	
    /**	
     * 销售单号	
     */	
	@ApiModelProperty("销售单号")	
    private String saleCode;	
	
    /**	
     * 行号	
     */	
	@ApiModelProperty("行号")	
    private String thirdItemCode;	
	
    private String whCode;	
	
    private String whName;	
	
    private String areaCode;	
	
    private String areaName;	
	
    private String binCode;	
	
    private String binName;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 发货单明细id	
     */	
	@ApiModelProperty("发货单明细id")	
    private String deliveryId;	
	
    /**	
     * 辅助属性字典编码拼接	
     */	
	@ApiModelProperty("辅助属性字典编码拼接")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 锁定数量	
     */	
	@ApiModelProperty("锁定数量")	
    private BigDecimal lockQty;	
	
    //产品编码(金蝶)	
    private String FParentMaterialId;	
	
    //生产订单编号(金蝶)	
    private String FMoBillNo;	
	
    private String FMoEntryId;	
	
    private String FPPBomBillNo;	
	
    private String FLot;	
	
    private String FEntrySrcBillNo;	
	
    //用料清单行号	
    private Integer FPPBomBillNoSeq;	
	
    private String FPPBomBillEntityId;	
	
    private String FPPBomBillId;	
	
    /**	
     * 金蝶订单类型 1领料 2补料	
     */	
	@ApiModelProperty("金蝶订单类型 1领料 2补料")	
    private String kdOrderType;	
	
    /**	
     * 金蝶单位	
     */	
	@ApiModelProperty("金蝶单位")	
    private String FUnit;	
	
}	
