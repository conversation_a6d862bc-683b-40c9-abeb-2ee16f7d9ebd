package com.imes.domain.entities.po.vo;

import com.imes.domain.entities.po.po.PoPrice;
import com.imes.domain.entities.po.po.PoPriceDetail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 价目信息保存包装类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PoPriceVo implements Serializable {

    private PoPrice poPrice;

    private String delIds;

    private List<PoPriceDetail> details;
}