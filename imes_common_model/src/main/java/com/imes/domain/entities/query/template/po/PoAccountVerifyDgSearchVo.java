package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.text.DecimalFormat;	
	
@Data	
@ApiModel("代管挂账对账确认")	
@QueryModel(	
        name = "1133-dg",	
        remark = "代管挂账对账确认",	
        alias = "a",	
        searchApi = "/api/po/poAccountVerify/queryList")	
public class PoAccountVerifyDgSearchVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
    private String id;	
	
	@ApiModelProperty("对账单号")	
    @QueryField(name = "对账单号")	
    private String verifyNo;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("对账开始日期")	
    @QueryField(name = "对账开始日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String startDate;	
	
	@ApiModelProperty("对账截止日期")	
    @QueryField(name = "对账截止日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String endDate;	
	
	@ApiModelProperty("对账金额")	
    @QueryField(name = "对账金额", type = Type.Number, format = "0.00")	
    private String totalMoney;	
	
    //@QueryField(name = "账款到账日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String moneyEndDate;	
	
	@ApiModelProperty("制单人")	
    @QueryField(name = "制单人")	
    private String madeUserName;	
	
	@ApiModelProperty("确认对账人")	
    @QueryField(name = "确认对账人")	
    private String verifyUserName;	
	
	@ApiModelProperty("确认对账时间")	
    @QueryField(name = "确认对账时间", type = Type.Date)	
    private String verifyTime;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", dictOption = "SALE_BUSINESS_CODE", type = Type.MultiSelect)	
    private String businessStatus;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", dictOption = "PO_ACCOUNT_STATUS", type = Type.MultiSelect)	
    private String status;	
	
	@ApiModelProperty("退回理由")	
    @QueryField(name = "退回理由")	
    private String refuseReason;	
	
	@ApiModelProperty("是否是代管挂账")	
    @QueryField(name = "是否是代管挂账", logic = Logic.Eq, show = false)	
    private String isAccount;	
	
	@ApiModelProperty("类型")	
    @QueryField(name = "类型", alias = "b", type = Type.Select, option = {"1", "入库单", "2", "退库单", "3", "代管挂账"})	
    private String type;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "b")	
    private String materialName;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "b")	
    private String materialCode;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号", alias = "b")	
    private String specification;	
	
	@ApiModelProperty("挂账单号")	
    @QueryField(name = "挂账单号", alias = "b")	
    private String storageNo;	
	
	@ApiModelProperty("批次号")	
    @QueryField(name = "批次号", alias = "b")	
    private String cbatch;	
	
	@ApiModelProperty("到货日期")	
    @QueryField(name = "到货日期", alias = "b", type = Type.Date, format = "yyyy-MM-dd")	
    private String cbatchDate;	
	
	@ApiModelProperty("挂账日期")	
    @QueryField(name = "挂账日期", alias = "b", type = Type.Date, format = "yyyy-MM-dd")	
    private String storageTime;	
	
	@ApiModelProperty("客户确认数量")	
    @QueryField(name = "客户确认数量", alias = "b", type = Type.Number)	
    private String allStorageNum;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "b")	
    private String unit;	
	
	@ApiModelProperty("本次对账数量")	
    @QueryField(name = "本次对账数量", alias = "b", type = Type.Number)	
    private String storageNum;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", alias = "b", type = Type.MultiSelect, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("取价类型")	
    @QueryField(name = "取价类型", alias = "b", type = Type.MultiSelect, option = {"1", "大宗物料", "0", "普通", "2", "锁铜"})	
    private String isLargeMaterial;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(b.tax_rate, 2), 0) * 100)", type = Type.Number)	
    private String taxRate;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价", alias = "b", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", alias = "b", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", alias = "b", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", alias = "b", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("含税总额")	
    @QueryField(name = "含税总额", alias = "b", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("对账状态")	
    @QueryField(name = "对账状态", alias = "b", dictOption = "SCS_ACCOUNT_STATUS", type = Type.MultiSelect)	
    private String accountStatus;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注", alias = "b")	
    private String remarks;	
	
    private String customerCode;	
	
    public static void main(String[] args) {	
        DecimalFormat decimalFormat = new DecimalFormat("0.00");	
        String scoreStr = decimalFormat.format(0.001);	
        System.out.println("scoreStr===" + scoreStr);	
    }	
}	
