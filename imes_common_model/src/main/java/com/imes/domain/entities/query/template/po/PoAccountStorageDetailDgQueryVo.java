package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
@ApiModel("代管挂账对账确认明细")	
@QueryModel(	
        name = "1133-storageDetailDg",	
        remark = "代管挂账对账确认明细",	
        alias = "a",	
        searchApi = "/api/po/poAccountStorageDetail/queryList")	
public class PoAccountStorageDetailDgQueryVo extends BaseModel {	
	
	@ApiModelProperty("唯一识别号")	
    @QueryField(name = "唯一识别号",show = false)	
    private String id;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
	@ApiModelProperty("挂账单号")	
    @QueryField(name = "挂账单号")	
    private String storageNo;	
	
	@ApiModelProperty("挂账单行号")	
    @QueryField(name = "挂账单行号")	
    private String irdrowno;	
	
	@ApiModelProperty("批次号")	
    @QueryField(name = "批次号")	
    private String cbatch;	
	
	@ApiModelProperty("到货日期")	
    @QueryField(name = "到货日期", type = Type.Date)	
    private String cbatchDate;	
	
	@ApiModelProperty("挂账日期")	
    @QueryField(name = "挂账日期", type = Type.Date)	
    private String storageTime;	
	
	@ApiModelProperty("供方确认数量")	
    @QueryField(name = "供方确认数量")	
    private String allStorageNum;	
	
	@ApiModelProperty("本次对账数量")	
    @QueryField(name = "本次对账数量")	
    private String storageNum;	
	
	@ApiModelProperty("剩余数量")	
    @QueryField(name = "剩余数量")	
    private String syQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String unit;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
	
	@ApiModelProperty("取价类型")	
    @QueryField(name = "取价类型", type = Type.MultiSelect, option = {"1", "大宗物料", "0", "普通", "2", "锁铜"})	
    private String isLargeMaterial;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", type = Type.MultiSelect, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(a.tax_rate, 2), 0) * 100)")	
    private String taxRate;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("含税总额")	
    @QueryField(name = "含税总额", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
}	
