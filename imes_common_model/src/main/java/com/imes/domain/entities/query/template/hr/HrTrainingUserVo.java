package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 培训记录表(TrainingUser)实体类	
 *	
 * <AUTHOR> z	
 * @since 2022-08-31 10:26:56	
 */	
@Data	
@ApiModel("培训评价模板")	
@QueryModel(	
        name = "0691",	
        remark = "培训评价模板",	
        alias = "hr_training_user",	
        searchApi = "/hr/training/extend/user/query")	
public class HrTrainingUserVo extends BaseModel {	
	
    private String id;	
    /**	
     * 培训id	
     */	
	@ApiModelProperty("培训id")	
    @QueryField(name = "培训id")	
    private String trainingId;	
    /**	
     * 学员编号	
     */	
	@ApiModelProperty("学员编号")	
    @QueryField(name = "学员编号")	
    private String userCode;	
    /**	
     * 学员名称	
     */	
	@ApiModelProperty("学员名称")	
    @QueryField(name = "学员名称")	
    private String userName;	
    /**	
     * 是否强制	
     */	
	@ApiModelProperty("是否强制")	
    @QueryField(name = "是否强制", type = Type.Select, option = {"0", "否", "1", "是"})	
    private String isMandatory;	
    /**	
     * 培训id	
     */	
	@ApiModelProperty("培训名称")	
    @QueryField(name = "培训名称")	
    private String trainingName;	
	
	@ApiModelProperty("点评状态")	
    @QueryField(name = "点评状态", type = Type.Select, option = {"0", "未开始", "1", "待评价", "2", "已完成"} )	
    private String status;	
	
    /**	
     * 学员-对课程的内容建议	
     */	
	@ApiModelProperty("内容建议")	
    @QueryField(name = "内容建议")	
    private String traineesEvaluation;	
    /**	
     * 学员-内容评分	
     */	
	@ApiModelProperty("内容评分")	
    @QueryField(name = "内容评分")	
    private String scoreContent;	
    /**	
     * 学员-案例评分	
     */	
	@ApiModelProperty("案例评分")	
    @QueryField(name = "案例评分")	
    private String scoreCase;	
    /**	
     * 学员-描述评分	
     */	
	@ApiModelProperty("描述评分")	
    @QueryField(name = "描述评分")	
    private String scoreDescribe;	
    /**	
     * 学员-沟通评分	
     */	
	@ApiModelProperty("沟通评分")	
    @QueryField(name = "沟通评分")	
    private String scoreCommunication;	
    /**	
     * 学员-外表评分	
     */	
	@ApiModelProperty("外表评分")	
    @QueryField(name = "外表评分")	
    private String scoreSurface;	
    /**	
     * 学员-内容建议	
     */	
	@ApiModelProperty("内容建议")	
    @QueryField(name = "内容建议")	
    private String adviceContent;	
    /**	
     * 学员-案例建议	
     */	
	@ApiModelProperty("案例建议")	
    @QueryField(name = "案例建议")	
    private String adviceCase;	
    /**	
     * 学员-描述建议	
     */	
	@ApiModelProperty("描述建议")	
    @QueryField(name = "描述建议")	
    private String adviceDescribe;	
    /**	
     * 学员-沟通建议	
     */	
	@ApiModelProperty("沟通建议")	
    @QueryField(name = "沟通建议")	
    private String adviceCommunication;	
    /**	
     * 学员-外表建议	
     */	
	@ApiModelProperty("外表建议")	
    @QueryField(name = "外表建议")	
    private String adviceSurface;	
    /**	
     * 学员-培训反馈ID	
     */	
	@ApiModelProperty("培训反馈ID")	
    @QueryField(name = "培训反馈ID", show = false)	
    private String feedbackId;	
}	
	
