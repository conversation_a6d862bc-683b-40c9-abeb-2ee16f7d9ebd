package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.InBillInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.InBillInspectionDetail;	
import com.imes.domain.entities.qms.po.OutBillInspection;	
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class OutBillInspectionInfoVo implements Serializable {	
	
    /**	
     * 主信息	
     */	
	@ApiModelProperty("主信息")	
    OutBillInspection main;	
    /**	
     * 明细	
     */	
	@ApiModelProperty("明细")	
    Map<String,List<OutBillInspectionDetail>> detail;	
	
    /**	
     * 关联文件	
     */	
	@ApiModelProperty("关联文件")	
    List<Map<String, Object>> processFile;	
	
    /**	
     * 采购信息	
     */	
	@ApiModelProperty("采购信息")	
    List<OutBillInspection> info;	
	
    private static final long serialVersionUID = 1L;	
}	
