package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
/**	
 * 岗位职等(HrJobsGrade)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-20 09:31:20	
 */	
@Data	
@ApiModel("岗位职等")	
@QueryModel(name = "0781",	
        remark = "岗位职等",	
        alias = "hr_jobs_grade",	
        searchApi = "/api/hr/position/grade/query")	
public class HrJobsGradeQueryVo extends BaseModel {	
	
    /**	
     * 级别	
     */	
	@ApiModelProperty("级别")	
    @QueryField(name = "级别" )	
    private String level;	
    /**	
     * 职务等级	
     */	
	@ApiModelProperty("职务等级")	
    @QueryField(name = "职务等级" )	
    private String jobsRank;	
    /**	
     * 职称	
     */	
	@ApiModelProperty("职称")	
    @QueryField(name = "职称" )	
    private String name;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人" )	
    private String createBy;	
	
	
}	
	
