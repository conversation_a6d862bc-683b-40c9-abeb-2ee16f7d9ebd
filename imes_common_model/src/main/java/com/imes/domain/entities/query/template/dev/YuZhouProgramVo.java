package com.imes.domain.entities.query.template.dev;		
		
import io.swagger.annotations.ApiModel;		
import com.imes.domain.entities.query.model.base.QueryField;		
import io.swagger.annotations.ApiModelProperty;		
import com.imes.domain.entities.query.model.base.BaseModel;		
import com.imes.domain.entities.query.model.base.QueryModel;		
import com.imes.domain.entities.query.model.base.Type;		
import lombok.Data;		
import lombok.experimental.Accessors;		
		
/**		
 * 宇洲，PC端，程序管理		
 */		
@Data		
@Accessors(chain = true)		
@ApiModel("宇洲，PC端，程序管理")		
@QueryModel(		
        name = "0712",		
        remark = "宇洲，PC端，程序管理",		
        searchApi = "/api/dev/devDevice/knife/program/page"		
)		
public class YuZhouProgramVo extends BaseModel {		
		
	@ApiModelProperty("排产日期")		
    @QueryField(name = "排产日期",alias = "wo")		
    private String createOn;		
	@ApiModelProperty("设备编码")		
    @QueryField(name = "设备编码",alias = "wo")		
    private String devCode;		
	@ApiModelProperty("设备名称")		
    @QueryField(name = "设备名称",alias = "wo")		
    private String devName;		
	@ApiModelProperty("下载状态")		
    @QueryField(name = "下载状态",alias = "wo")		
    private String downloadStatus;		
	@ApiModelProperty("派工单号")		
    @QueryField(name = "派工单号",alias = "wo")		
    private String woNo;		
	@ApiModelProperty("排产单号")		
    @QueryField(name = "排产单号",alias = "wo")		
    private String productionNo;		
    private String routeCode;		
	@ApiModelProperty("工序编码")		
    @QueryField(name = "工序编码",alias = "wo")		
    private String processCode;		
	@ApiModelProperty("工序名称")		
    @QueryField(name = "工序名称",alias = "wo")		
    private String processName;		
	@ApiModelProperty("物料编码")		
    @QueryField(name = "物料编码",alias = "wo")		
    private String materialCode;		
    // 物料		
	@ApiModelProperty("物料名")		
    @QueryField(name = "物料名",alias = "wo")		
    private String materialName;		
	@ApiModelProperty("设备文件")		
    @QueryField(name = "设备文件" ,alias = "df",show = false)		
    private String fileId;		
	@ApiModelProperty("设备文件名称")		
    @QueryField(name = "设备文件名称" ,alias = "pi")		
    private String programFileName;		
	@ApiModelProperty("文件下载结果")		
    @QueryField(name = "文件下载结果",query=false, type = Type.MultiSelect, option = {"-1", "失败", "0", "成功"})		
    private String downloadResult;		
		
    // 当前设备程序单		
    private String fileId2;		
		
}		
