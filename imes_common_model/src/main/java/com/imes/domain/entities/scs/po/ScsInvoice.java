package com.imes.domain.entities.scs.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 供应链销售-开票
 */
@Data
public class ScsInvoice implements Serializable {

    private static final long serialVersionUID = 824613178177042950L;

    private String id;

    /**
     * 开票流水号
     */
    private String invoiceNo;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 1蓝字发票（入），2红字发票（退）
     */
    private Integer blueRedType;

    /**
     * 发票日期
     */
    private Date invoiceDate;

    /**
     * 开票日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date issueDate;

    /**
     * 发票号码
     */
    private String invoiceCode;

    /**
     * 发票类型
     */
    private String invoiceType;

    /**
     * 币种
     */
    private String currency;

    /**
     * 支付方式  SCS_ACCOUNT_VERIFY_PAY_METHOD
     */
    private String payMethod;

    private String fileId;

    /**
     * 发票金额
     */
    private String totalMoney;

    /**
     * 制单人工号
     */
    private String madeUserCode;

    /**
     * 制单人名称
     */
    private String madeUserName;

    /**
     * 开票状态10已录入，20已提交，30开票完成
     */
    private String invoiceStatus;

    /**
     * 开票回复人
     */
    private String invoiceConfirmUserName;

    /**
     * 开票回复时间
     */
    private String invoiceConfirmTime;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remarks;

    private String refuseReason;

    /**
     * 税率（0~1）
     */
    private BigDecimal taxRate;

    /**
     * 税率编码
     */
    private String taxCode;

    /**
     * 销售对账单号
     */
    @TableField(exist = false)
    private String scsAccountVerifyNo;
    //实际金额
    private BigDecimal actualAmount;
    //汇率
    private BigDecimal cexchrate;
    //业务类型
    private String cbustype;
    //采购类型编码
    private String cptcode;
    //部门编码
    private String cdepcode;
    //业务员编码
    private String cpersoncode;
    //发票识别号
    private String chdefine1;
    //发票日期摘要
    private Date cdefine6;
    //收付款协议编码
    private String cvenpuomprotocol;
}