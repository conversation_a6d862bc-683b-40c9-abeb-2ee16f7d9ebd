package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsJcReportOnHandStockVo {	
	
	
    /**	
     * 品牌	
     */	
    @ApiModelProperty("品牌")	
    private String brand;	
	
    /**	
     * 物料型号	
     */	
    @ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 在库库存	
     */	
    @ApiModelProperty("在库库存")	
    private BigDecimal onHandQty;	
	
    /**	
     * 库存金额	
     */	
    @ApiModelProperty("库存金额")	
    private BigDecimal onHandPrice;	
	
    /**	
     * 入库日期	
     */	
    @ApiModelProperty("入库日期")	
    private String inBoundDate;	
	
}	
