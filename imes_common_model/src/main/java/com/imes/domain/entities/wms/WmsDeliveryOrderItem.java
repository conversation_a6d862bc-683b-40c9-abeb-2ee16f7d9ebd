package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.ppc.po.Promaterial;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import javax.persistence.Entity;	
import javax.persistence.Id;	
import javax.persistence.Table;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@Entity	
@Table(name="wms_delivery_order_item")	
public class WmsDeliveryOrderItem implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("主键")	
    @Id	
    private String id;
	
    /**	
     * 发货单主键	
     */	
	@ApiModelProperty("发货单主键")	
    private String doId;	
	
    /**	
     * 发货单号	
     */	
	@ApiModelProperty("发货单号")	
    private String doCode;	
	
    /**	
     * 发货明细单号	
     */	
	@ApiModelProperty("发货明细单号")	
    private String doItemCode;	
	
    /**	
     * 销售合同号	
     */	
	@ApiModelProperty("销售合同号")	
    private String salesContractCode;	
	
    /**	
     * 采购合同号	
     */	
	@ApiModelProperty("采购合同号")	
    private String purchaseContractCode;	
	
    /**	
     * 客户合同号	
     */	
	@ApiModelProperty("客户合同号")	
    private String customerContractCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 可用库存数量	
     */	
	@ApiModelProperty("可用库存数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 发货数量	
     */	
	@ApiModelProperty("发货数量")	
    private BigDecimal qty;	
	
    /**	
     * 已拣货数量	
     */	
	@ApiModelProperty("已拣货数量")	
    private BigDecimal pickedQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 总价	
     */	
	@ApiModelProperty("总价")	
    private BigDecimal totalPrice;	
	
    /**	
     * 价格单位	
     */	
	@ApiModelProperty("价格单位")	
    private String priceUnit;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    /**	
     * 第三方系统明细单唯一标识	
     */	
	@ApiModelProperty("第三方系统明细单唯一标识")	
    private String thirdItemCode;	
	
    /**	
     * 发货单价	
     */	
	@ApiModelProperty("发货单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 单个重量	
     */	
    @ApiModelProperty("单个重量")	
    private BigDecimal weight;	
	
    /**	
     * 总重量	
     */	
    @ApiModelProperty("总重量")	
    private BigDecimal totalWeight;	
	
    /**	
     * 重量单位	
     */	
    @ApiModelProperty("重量单位")	
    private String weightUnit;	
	
    /**	
     * 税额	
     */	
    @ApiModelProperty("税额")	
    private BigDecimal tax;	
	
    /**	
     * 库存说明	
     */	
    @ApiModelProperty(value = "库存说明")	
    private String stockDesc;	
	
    @ApiModelProperty(value = "订单号")	
    private String orderCode;	
	
    @ApiModelProperty(value = "入库单号")	
    private String billCode;	
	
    @ApiModelProperty(value = "入库时间")	
    private Date billDate;	
	
    @ApiModelProperty("辅助单位")	
    private String auxiliaryUnit;	
	
    @ApiModelProperty("换算比例")	
    private BigDecimal conversionRatio;	
	
    @ApiModelProperty("辅助数量")	
    private BigDecimal auxiliaryQty;	
	
    @ApiModelProperty("优先级")	
    private Integer priority;	
	
    @ApiModelProperty("关联明细单单号")	
    private String receiptItemCode;	
	
    @ApiModelProperty("客户物料编码")	
    private String customerMaterialCode;	
	
    @ApiModelProperty("供应商物料编码")	
    private String supplierMaterialCode;	
	
    @ApiModelProperty("物料规格")	
    private String specification;	
	
    private Promaterial promaterial;	
	
    private static final long serialVersionUID = 1L;	
}	
