package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("采购对账(入库单)")	
@QueryModel(	
        name = "1133-storageIn",	
        remark = "采购对账(入库单)",	
        alias = "a",	
        searchApi = "/api/po/poAccountStorageIn/queryList")	
public class PoAccountStorageInSearchVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
    private String id;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号", order = OrderBy.DESC)	
    private String soNo;	
	
	@ApiModelProperty("采购单行号")	
    @QueryField(name = "采购单行号")	
    private String sdNo;	
	
	@ApiModelProperty("对账单号")	
    @QueryField(name = "对账单号")	
    private String verifyNo;	
	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种", alias = "b", dictOption = "CUSTOMER_CURRENCY", type = Type.MultiSelect)	
    private String currency;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "c")	
    private String materialName;	
	
    /**	
     * 物料规格	
     */	
	@ApiModelProperty("物料规格")	
    @QueryField(name = "物料规格", alias = "c")	
    private String specification;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "c")	
    private String materialMarker;	
	
	
	@ApiModelProperty("入库单号")	
    @QueryField(name = "入库单号")	
    private String storageInNo;	
	
	@ApiModelProperty("关联单号")	
    @QueryField(name = "关联单号")	
    private String relationReceiveNo;	
	
	@ApiModelProperty("入库时间")	
    @QueryField(name = "入库时间", type = Type.Date)	
    private String storageInTime;	
	
	@ApiModelProperty("入库数量")	
    @QueryField(name = "入库数量")	
    private String storageNum;	
	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", type = Type.MultiSelect, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("取价类型")	
    @QueryField(name = "取价类型", type = Type.MultiSelect, option = {"1", "大宗物料", "0", "普通","2","锁铜"})	
    private String isLargeMaterial;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(a.tax_rate, 2), 0) * 100)")	
    private String taxRate;	
	
	@ApiModelProperty("物料单价")	
    @QueryField(name = "物料单价", type = Type.Number, format = "0.000000")	
    private String singlePrice;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税额金额")	
    @QueryField(name = "未税额金额", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("对账状态")	
    @QueryField(name = "对账状态", dictOption = "SCS_ACCOUNT_STATUS", type = Type.MultiSelect)	
    private String accountStatus;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
	
    private String unit;	
	
    private String irdrowno;	
    private String icost;	
    private String imoney;	
    private String itaxprice;	
    private String isum;	
	
    private String allStorageNum;	
	
}	
