package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsBorrowOrder implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 借货单号	
     */	
	@ApiModelProperty("借货单号")	
    private String boCode;	
	
    /**	
     * 订单类型	
     */	
	@ApiModelProperty("订单类型")	
    private Integer orderType;	
	
    /**	
     * 订单日期	
     */	
	@ApiModelProperty("订单日期")	
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")	
    private Date orderDate;	
	
    /**	
     * 借货仓库	
     */	
	@ApiModelProperty("借货仓库")	
    private String whCode;	
	
    /**	
     * 借用客户编码	
     */	
	@ApiModelProperty("借用客户编码")	
    private String toCustomerCode;	
	
    /**	
     * 借用客户名称	
     */	
	@ApiModelProperty("借用客户名称")	
    private String toCustomerName;	
	
    /**	
     * 借货单状态	
     */	
	@ApiModelProperty("借货单状态")	
    private Integer orderStatus;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    /**	
     * 借货明细单	
     */	
	@ApiModelProperty("借货明细单")	
    private List<WmsBorrowOrderItem> items;	
	
    /**	
     * 借用合同号	
     */	
	@ApiModelProperty("借用合同号")	
    private String borrowContractNo;	
	
    /**	
     * 销售部门编码	
     */	
	@ApiModelProperty("销售部门编码")	
    private String salesDepartmentCode;	
	
    /**	
     * 销售部门名称	
     */	
	@ApiModelProperty("销售部门名称")	
    private String salesDepartmentName;	
	
    /**	
     * 销售员工工号	
     */	
	@ApiModelProperty("销售员工工号")	
    private String salesPersonCode;	
	
    /**	
     * 销售员工名称	
     */	
	@ApiModelProperty("销售员工名称")	
    private String salesPersonName;	
	
    private static final long serialVersionUID = 1L;	
}	
