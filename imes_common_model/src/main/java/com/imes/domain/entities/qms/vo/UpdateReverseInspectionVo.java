package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class UpdateReverseInspectionVo {	
    //检验编号	
	@ApiModelProperty("检验编号")	
    public String inspectionCode;	
	
    //到货单号	
	@ApiModelProperty("到货单号")	
    public String arrivalOrder;	
	
    //到货数量	
	@ApiModelProperty("到货数量")	
    public BigDecimal arrivedQty;	
	
    //接收数量	
	@ApiModelProperty("接收数量")	
    public BigDecimal inboundQty;	
	
    //采购数量	
	@ApiModelProperty("采购数量")	
    public BigDecimal purchaseQty;	
	
    //到货明细单号	
	@ApiModelProperty("到货明细单号")	
    public String aoItemCode;	
	
    //批次	
	@ApiModelProperty("批次")	
    public String batch;	
	
    //检验单id（wms不用传给qms）	
	@ApiModelProperty("检验单id（wms不用传给qms）")	
    public String orderId;	
	
}	
