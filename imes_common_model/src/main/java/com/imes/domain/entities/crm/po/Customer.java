package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * (CrmCustomer)实体类
 *
 * <AUTHOR>
 * @since 2022-02-21 16:22:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_customer", resultMap = "BaseResultMap")
public class Customer extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 743485907505569373L;
    /**
    * id
    */
    private String id;
    /**
    * 客户编号
    */
    @TableField(fill = FieldFill.INSERT)
    private String code;
    /**
    * 客户名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 客户类型
    */
    private String type;
    /**
     * 客户来源
     */
    private String clueSource;
    /**
    * 客户所有人
    */
    private String userCode;
    /**
     * 客户可变通性
     */
    private String flexibility;
    /**
     * 客户关系
     */
    private String relationship;
    /**
     * 信息化
     */
    private String informatization;
    /**
     * 客户所有人-姓名
     */
    @TableField(exist = false)
    private String userName;
    /**
    * 等级
    */
    private String grade;
    /**
     * 主营产品
     */
    private String mainProduct;
    /**
     * 币种
     */
    private String currency;
    /**
    * 电话
    */
    private String phone;
    /**
    * 客户所在地
    */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> address;
    /**
     * 省份
     */
    @TableField(exist = false)
    private String province;
    /**
     * 详细地址
     */
    private String detailAddress;
    /**
     * 经纬度
     */
    private String latlng;
    /**
    * 传真
    */
    private String fax;
    /**
    * 网站
    */
    private String website;
    /**
    * 股票代码
    */
    private String stockCode;
    /**
    * 公司所有权
    */
    private String ownership;
    /**
    * 行业
    */
    private String industry;
    /**
     * 行业分类
     */
    private String industryClassify;
    /**
     * 行业代码
     */
    private String industryCode;
    /**
    * 员工人数
    */
    private String employeesNumber;
    /**
    * 年收入
    */
    private String annualIncome;
    /**
    * sic代码
    */
    private String sicCode;
    /**
    * 开票地址
    */
    private String billingAddress;
    /**
    * 发货地址
    */
    private String shippingAddress;
    /**
    * 备注
    */
    private String remarks;
    /**
     * 范围查询
     */
    @TableField(exist = false)
    private List<LocalDateTime> dates;

    /**
     * 自定义字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }
}