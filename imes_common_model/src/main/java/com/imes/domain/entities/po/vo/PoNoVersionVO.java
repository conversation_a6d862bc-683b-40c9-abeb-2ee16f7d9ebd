package com.imes.domain.entities.po.vo;

import lombok.Data;

import java.util.HashMap;
import java.util.List;

/**
 * @Description: 用于需求单批量变更状态接口
 * @Param:
 * @return:
 * @Author: cshu
 * @Date: 2022/3/31
 */
@Data
public class PoNoVersionVO {
    //需求批次号List
    private List<String> demandForecastNoList;
    //需求版本号List
    private List<String> demandVersionList;
    //物料编码(对应请求的时候 就是客户物料编码)
    private List<String> materialList;
    //平台机构编码
    private String orgPlatfromCode;
    //变更状态
    private String status;
    //所有需要变更状态的集合 no + demandVersion,materail,customer
    private HashMap<String, String> changeLists;
}
