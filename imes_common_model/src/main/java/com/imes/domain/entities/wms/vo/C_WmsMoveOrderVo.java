package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsMoveOrderVo {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 移库单号	
     */	
	@ApiModelProperty("移库单号")	
    private String moCode;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 单据类型（库内移库单、调拨单）	
     */	
	@ApiModelProperty("单据类型（库内移库单、调拨单）")	
    private Integer orderType;	
	
    /**	
     * 单据来源（手工建单、第三方系统）	
     */	
	@ApiModelProperty("单据来源（手工建单、第三方系统）")	
    private Integer source;	
	
    /**	
     * 第三方单据号	
     */	
	@ApiModelProperty("第三方单据号")	
    private String thirdOrderCode;	
	
    /**	
     * 单据状态（未完成、已审核、已完成、已取消）	
     */	
	@ApiModelProperty("单据状态（未完成、已审核、已完成、已取消）")	
    private Integer status;	
	
    /**	
     * 移出仓库	
     */	
	@ApiModelProperty("移出仓库")	
    private String fromWhCode;	
	
    /**	
     * 移入仓库	
     */	
	@ApiModelProperty("移入仓库")	
    private String toWhCode;	
	
    /**	
     * 关联入库单号	
     */	
	@ApiModelProperty("关联入库单号")	
    private String storageInCode;	
	
    /**	
     * 关联出库单号	
     */	
	@ApiModelProperty("关联出库单号")	
    private String storageOutCode;	
	
    /**	
     * 创建时间	
     */	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 移出仓库编码	
     */	
	@ApiModelProperty("移出仓库编码")	
    private String fromCompanyCode;	
	
    /**	
     * 移出仓库名称	
     */	
	@ApiModelProperty("移出仓库名称")	
    private String fromCompanyName;	
    /**	
     * 移入仓库名称	
     */	
	@ApiModelProperty("移入仓库名称")	
    private String toCompanyCode;	
    /**	
     * 移入仓库名称	
     */	
	@ApiModelProperty("移入仓库名称")	
    private String toCompanyName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 移出仓库名称	
     */	
	@ApiModelProperty("移出仓库名称")	
    private String fromWhName;	
	
    /**	
     * 移入仓库名称	
     */	
	@ApiModelProperty("移入仓库名称")	
    private String toWhName;	
	
    /**	
     * 移库单明细集合	
     */	
	@ApiModelProperty("移库单明细集合")	
    private List<C_WmsMoveOrderItemVo> list;	
	
    /**	
     * 亲亲食品移动类型	
     */	
	@ApiModelProperty("亲亲食品移动类型")	
    private String moveType;	
}	
