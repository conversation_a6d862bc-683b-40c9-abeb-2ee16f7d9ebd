package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 考勤组作息排版记录表(HrAttendanceGroupRule)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-25 10:20:55	
 */	
@Data	
@ApiModel("考勤组作息排版记录管理")	
@QueryModel(name = "0784",	
        remark = "考勤组作息排版记录管理",	
        alias = "hr_attendance_group_rule",	
        searchApi = "/api/hr/attendance/rule/group/rule/query")	
public class HrAttendanceGroupRuleQueryVo extends BaseModel {	
	
    /**	
     * 考勤组编码	
     */	
	@ApiModelProperty("考勤组编码")	
    @QueryField(name = "考勤组编码" )	
    private String groupCode;	
    /**	
     * 假日计划名称	
     */	
	@ApiModelProperty("假日计划名称")	
    @QueryField(name = "假日计划名称" )	
    private String holidayPlanName;	
    /**	
     * 班次编码	
     */	
	@ApiModelProperty("班次名称")	
    @QueryField(name = "班次名称" )	
    private String shiftName;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态" )	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
    /**	
     * 考情组名称	
     */	
	@ApiModelProperty("考情组名称")	
    @QueryField(name = "考情组名称" )	
    private String name;	
	
}	
	
