package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@ApiModel(value = "出库任务明细表")	
public class WmsOutboundTaskItem implements Serializable {	
	
    private static final long serialVersionUID = 281258845161402648L;	
	
@ApiModelProperty(value = "主键")	
private String id;	
	
@ApiModelProperty(value = "出库任务单号")	
private String outboundCode;	
	
@ApiModelProperty(value = "出库任务明细单号")	
private String outboundItemCode;	
	
@ApiModelProperty(value = "物料编码")	
private String materialCode;	
	
@ApiModelProperty(value = "辅助属性")	
private String skuCode;	
	
@ApiModelProperty(value = "箱码")	
private String boxNo;	
	
@ApiModelProperty(value = "库存单位")	
private String inventoryUnit;	
	
@ApiModelProperty(value = "出库单数量（包装单位）")	
private BigDecimal orderQty;	
	
@ApiModelProperty(value = "已出库数量（包装单位）")	
private BigDecimal pickQty;	
	
@ApiModelProperty(value = "基本单位")	
private String primaryUnit;	
	
@ApiModelProperty(value = "出库申请基本数量")	
private BigDecimal primaryApplyQty;	
	
@ApiModelProperty(value = "已出库基本数量")	
private BigDecimal primaryOutboundQty;	
	
@ApiModelProperty(value = "批次")	
private String batch;	
	
@ApiModelProperty(value = "供应商批次")	
private String batchSupplier;	
	
@ApiModelProperty(value = "含税单价")	
private BigDecimal includePrice;	
	
@ApiModelProperty(value = "不含税单价")	
private BigDecimal unIncludePrice;	
	
@ApiModelProperty(value = "生产日期")	
private Date productionDate;	
	
@ApiModelProperty(value = "失效日期")	
private Date failureDate;	
	
@ApiModelProperty(value = "关联单号")	
private String receiptCode;	
	
@ApiModelProperty(value = "关联明细单号")	
private String receiptItemCode;	
	
@ApiModelProperty(value = "库存状态（待检、合格、不合格）")	
private String stockStatus;	
	
@ApiModelProperty(value = "优先级")	
private Integer priority;	
	
@ApiModelProperty(value = "备注")	
private String itemRemarks;	
	
@ApiModelProperty(value = "自定义字段")	
private Object custom;	
	
@ApiModelProperty(value = "创建时间")	
private Date createOn;	
	
@ApiModelProperty(value = "创建人")	
private String createBy;	
	
@ApiModelProperty(value = "更新时间")	
private Date updateOn;	
	
@ApiModelProperty(value = "更新人")	
private String updateBy;	
}	
