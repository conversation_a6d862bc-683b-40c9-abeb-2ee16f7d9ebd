package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMoveOrderAndItem {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 移库单号	
     */	
	@ApiModelProperty("移库单号")	
    private String moCode;	
	
    /**	
     * 单据类型（库内移库单、调拨单）	
     */	
	@ApiModelProperty("单据类型（库内移库单、调拨单）")	
    private Integer orderType;	
	
    /**	
     * 单据来源（手工建单、第三方系统）	
     */	
	@ApiModelProperty("单据来源（手工建单、第三方系统）")	
    private Integer source;	
	
    /**	
     * 第三方单据号	
     */	
	@ApiModelProperty("第三方单据号")	
    private String thirdOrderCode;	
	
    /**	
     * 单据状态（未完成、已审核、已完成、已取消）	
     */	
	@ApiModelProperty("单据状态（未完成、已审核、已完成、已取消）")	
    private Integer status;	
	
    /**	
     * 移出仓库	
     */	
	@ApiModelProperty("移出仓库")	
    private String fromWhCode;	
	
    /**	
     * 移入仓库	
     */	
	@ApiModelProperty("移入仓库")	
    private String toWhCode;	
	
    /**	
     * 关联入库单号	
     */	
	@ApiModelProperty("关联入库单号")	
    private String storageInCode;	
	
    /**	
     * 关联出库单号	
     */	
	@ApiModelProperty("关联出库单号")	
    private String storageOutCode;	
	
    /**	
     * 创建时间	
     */	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 移动数量	
     */	
	@ApiModelProperty("移动数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 可移动数量	
     */	
	@ApiModelProperty("可移动数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 主单位	
     */	
	@ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 移库单id	
     */	
	@ApiModelProperty("移库单id")	
    private String moId;	
	
    private String moItemCode;	
}	
