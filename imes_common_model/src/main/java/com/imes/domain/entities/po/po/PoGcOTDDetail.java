package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "GC-OTD-明细表")
public class PoGcOTDDetail implements Serializable {

    private static final long serialVersionUID = -6483483384754391871L;
    private String id;

    @ApiModelProperty(value = "类型（1 订单交期 2 承诺交期）")
    private String type;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "订单行号")
    private String sdNo;

    @ApiModelProperty(value = "业务员判定")
    private String judgement;

    @ApiModelProperty(value = "修改原因")
    private String cause;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

}