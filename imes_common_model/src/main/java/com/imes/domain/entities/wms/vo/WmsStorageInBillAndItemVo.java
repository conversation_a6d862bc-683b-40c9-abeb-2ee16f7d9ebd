package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInBillAndItemVo implements Serializable {	
	
	@ApiModelProperty("id")	
    private String id;	
    /**	
     * 入库单编码	
     */	
	@ApiModelProperty("入库单编码")	
    private String storageInCode;	
	
    /**	
     * 单据来源（如从ERP获得，从MES获取，手工建单等）	
     */	
	@ApiModelProperty("单据来源（如从ERP获得，从MES获取，手工建单等）")	
    private Integer source;	
	
    /**	
     * 入库单类型	
     */	
	@ApiModelProperty("入库单类型")	
    private Integer receiptType;	
	
    /**	
     * 关联的单据号	
     */	
	@ApiModelProperty("关联的单据号")	
    private String receiptCode;	
	
    /**	
     * 过账日期	
     */	
	@ApiModelProperty("过账日期")	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date postingDate;	
	
    /**	
     * 单据状态（1-草稿；2-未完成；3-已完成）	
     */	
	@ApiModelProperty("单据状态（1-草稿；2-未完成；3-已完成）")	
    private Integer status;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 日期	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 入库明细单编号	
     */	
	@ApiModelProperty("入库明细单编号")	
    private String storageInItemCode;	
	
    /**	
     * 关联单据明细单号	
     */	
	@ApiModelProperty("关联单据明细单号")	
    private String receiptItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料描述	
     */	
	@ApiModelProperty("物料描述")	
    private String materialName;	
    /**	
     * 采购组织编码	
     */	
	@ApiModelProperty("采购组织编码")	
    private String departCode;	
	
    /**	
     * 采购组织描述	
     */	
	@ApiModelProperty("采购组织描述")	
    private String departName;	
	
    /**	
     * 入库单物料数量	
     */	
	@ApiModelProperty("入库单物料数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 已入库数量	
     */	
	@ApiModelProperty("已入库数量")	
    private BigDecimal inventoryQty;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")	
    private String ftyCode;	
    /**	
     * 仓库号编码	
     */	
	@ApiModelProperty("仓库号编码")	
    private String whCode;	
	
    /**	
     * 存储区编码	
     */	
	@ApiModelProperty("存储区编码")	
    private String areaCode;	
	
    /**	
     * 仓位	
     */	
	@ApiModelProperty("仓位")	
    private String binCode;	
	
    /**	
     * 入库申请人名称	
     */	
	@ApiModelProperty("入库申请人名称")	
    private String applyName;	
	
    /**	
     * 质检状态	
     */	
	@ApiModelProperty("质检状态")	
    private String inspectStatus;	
	
    /**	
     * 质检结果	
     */	
	@ApiModelProperty("质检结果")	
    private String inspectResult;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 申请人	
     */	
	@ApiModelProperty("申请人")	
    private String applyBy;	
	
    /**	
     * 时间	
     */	
	@ApiModelProperty("时间")	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date approveOn;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 操作员	
     */	
	@ApiModelProperty("操作员")	
    private String approveBy;	
	
    /**	
     * 日期	
     */	
	@ApiModelProperty("日期")	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date expectOn;	
    /**	
     * 供应商	
     */	
	@ApiModelProperty("供应商")	
    private String supplierCode;	
	
    /**	
     * 供应商	
     */	
	@ApiModelProperty("供应商")	
    private String supplierName;	
	
    /**	
     * 原料类型	
     */	
	@ApiModelProperty("原料类型")	
    private String category;	
	
    private String binName;	
	
    private String areaName;	
	
    private String whName;	
	
    private String custom;	
	
    private String theInventoryQty;	
	
    /**	
     * 物料辅助属性	
     */	
	@ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
}	
