package com.imes.domain.entities.pm.dto.ex;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class SortField {
    @TableField(exist = false)
    private List<OrderItem> sortFields;

    /*public List<OrderItem> getOrderItems() {
        if (CollectionUtils.isNotEmpty(sortFields)) {
            return Lists.newArrayList();
        }
        return sortFields.entrySet().stream().map(entry -> {
            OrderItem orderItem = new OrderItem();
            orderItem.setColumn(entry.getKey());
            orderItem.setAsc("asc".equals(entry.getValue()));
            return orderItem;
        }).collect(Collectors.toList());
    }

    public String getOrderString() {
        if (CollectionUtils.isNotEmpty(sortFields)) {
            return "";
        }
        return sortFields.entrySet().stream().map(entry -> {
            String value = entry.getValue();
            if ("asc".equals(value)) {
                value = "+";
            } else {
                value = "-";
            }
            return value + StringUtils.camelToUnderline(entry.getKey());
        }).collect(Collectors.joining(","));
    }*/

    // json字符串转换为SortField对象
    public static SortField parse(String json) {
        if (StringUtils.isBlank(json)) {
            return null;
        }
        SortField sortField = new SortField();
        sortField.setSortFields(Lists.newArrayList());
        return sortField;
    }
}
