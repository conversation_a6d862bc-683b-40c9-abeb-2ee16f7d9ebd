package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("发票管理(明细)")	
@QueryModel(	
        name = "1134-detail",	
        remark = "发票管理(明细)",	
        searchApi = "/po/invoice/queryDetailList")	
public class PoInvoiceDetailQueryVo extends BaseModel {	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
    private String id;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格")	
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String modelNumber;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量")	
    private String storageNum;	
	
	@ApiModelProperty("金额")	
    @QueryField(name = "金额", type = Type.Number, format = "0.000000")	
    private String allPrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, format = "0.000000")	
    private String taxRatePrice;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(a.tax_rate, 2), 0) * 100)", format = "%")	
    private String taxRate;	
	
	@ApiModelProperty("对账单号")	
    @QueryField(name = "对账单号")	
    private String verifyNo;	
	
	@ApiModelProperty("入库单号")	
    @QueryField(name = "入库单号")	
    private String storageInNo;	
	
	@ApiModelProperty("入库单行单号")	
    @QueryField(name = "入库单行单号", order = OrderBy.DESC)	
    private String irdrowno;	
	
	@ApiModelProperty("本币单价")	
    @QueryField(name = "本币单价", type = Type.Number, format = "0.00")	
    private String icost;	
	
	@ApiModelProperty("本币金额")	
    @QueryField(name = "本币金额", type = Type.Number, format = "0.000000")	
    private String imoney;	
	
	@ApiModelProperty("本币税额")	
    @QueryField(name = "本币税额", type = Type.Number, format = "0.000000")	
    private String itaxprice;	
	
	@ApiModelProperty("本币价税合计")	
    @QueryField(name = "本币价税合计", type = Type.Number, format = "0.00")	
    private String isum;	
	
	@ApiModelProperty("发票号")	
    @QueryField(name = "发票号", logic = Logic.Eq, show = false)	
    private String invoiceNo;	
}	
