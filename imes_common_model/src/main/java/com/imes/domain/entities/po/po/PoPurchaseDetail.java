package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "采购需求预测")
public class PoPurchaseDetail implements Serializable {

    private static final long serialVersionUID = 672127724240524511L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseQty;

    @ApiModelProperty(value = "到货数量")
    private BigDecimal arrivalQty;

    @ApiModelProperty(value = "总金额")
    private BigDecimal allPrice;

    @ApiModelProperty(value = "不含税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @ApiModelProperty(value = "税额")
    private BigDecimal taxRatePrice;

    @ApiModelProperty(value = "未税金额")
    private BigDecimal unTaxRatePrice;

    @ApiModelProperty(value = "折扣率")
    private BigDecimal discountRate;

    @ApiModelProperty(value = "需求回复数量")
    private BigDecimal replyQty;

    @ApiModelProperty(value = "物料行号")
    private String sdNo;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "物料型号")
    private String materialMarker;

    @ApiModelProperty(value = "图号")
    private String dwgNo;

    @ApiModelProperty(value = "产品大类")
    private String category;

    @ApiModelProperty(value = "产品类型")
    private String materialTypeCode;

    @ApiModelProperty(value = "基础单位")
    private String primaryUnit;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "预计交货日期")
    private Date estimatedDeliveryDate;

    @ApiModelProperty(value = "申请交货日期")
    private Date requestedDeliveryDate;

    @ApiModelProperty(value = "实际交货日期")
    private Date actualDeliveryDate;

    @ApiModelProperty(value = "行关闭人")
    private String closePerson;

    @ApiModelProperty(value = "业务状态")
    private String businessStatus;

    @ApiModelProperty(value = "旧图号")
    private String oldDwgNo;

    @ApiModelProperty(value = "项目编码")
    private String projectCode;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;
    //是否含税
    private String includeTax;
    //税率
    private BigDecimal taxRate;
    // 2023 -04 -16 增加 刻印号
    private String engravingNumber;
    //行状态
    private String status;
    //锁状态
    private String lockupStatus;
    //行版本
    private int detailVer;
    //推送状态
    private String supplierStatus;
    //品牌
    private String brand;
    //标准/非标件
    private String standardPart;
    //旧备注
    private String oldRemarks;
    //请购备注
    private String qgRemarks;
}