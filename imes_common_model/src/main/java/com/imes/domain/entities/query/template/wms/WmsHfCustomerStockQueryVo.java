package com.imes.domain.entities.query.template.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import lombok.Data;	
	
@Data	
@ApiModel("客户桶库存查询")	
@QueryModel(	
        name = "1196",	
        remark = "客户桶库存查询",	
        alias = "wms_customer_stock",	
        searchApi = "/wms/wmsCustomerStock/findHfCustomStockReport")	
public class WmsHfCustomerStockQueryVo extends BaseModel {	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称")	
    private String customerName;	
	
	@ApiModelProperty("桶物料编码")	
    @QueryField(name = "桶物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("桶物料名称")	
    @QueryField(name = "桶物料名称")	
    private String materialName;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号")	
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格")	
    private String specification;	
	
	@ApiModelProperty("期初数量")	
    @QueryField(name = "期初数量")	
    private String beginQty;	
	
	@ApiModelProperty("出库数量")	
    @QueryField(name = "出库数量")	
    private String outQty;	
	
	@ApiModelProperty("入库数量")	
    @QueryField(name = "入库数量")	
    private String inQty;	
	
	@ApiModelProperty("买断数量")	
    @QueryField(name = "买断数量")	
    private String buyoutQty;	
	
	@ApiModelProperty("结存数量")	
    @QueryField(name = "结存数量")	
    private String onhandQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位")	
    private String unit;	
	
}	
