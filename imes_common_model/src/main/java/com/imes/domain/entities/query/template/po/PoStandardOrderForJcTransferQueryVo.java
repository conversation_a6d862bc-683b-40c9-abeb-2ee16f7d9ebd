package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryField;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购到货调入采购单（标准采购模块总仓定制）")	
@QueryModel(	
        name = "0240-05-select",	
        remark = "采购到货调入采购单（标准采购模块总仓定制）",	
        searchApi = "/po/poPurchaseDetailStandard/queryJcPoAndDetailList",	
        alias = {"po_purchase_standard", "po_purchase_detail_standard"})	
public class PoStandardOrderForJcTransferQueryVo extends BaseModel {	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "po_purchase_detail_standard")	
    private String materialMarker;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "po_purchase_detail_standard")	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "po_purchase_detail_standard")	
    private String specification;	
	
	@ApiModelProperty("RA系统号")	
    @QueryField(name = "RA系统号")	
    private String docCode;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号")	
    private String purchaseNo;	
	
	@ApiModelProperty("订单号")	
    @QueryField(name = "订单号", alias = "po_purchase_detail_standard")	
    private String requestDocCode;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量", alias = "po_purchase_detail_standard")	
    private String qty;	
	
	@ApiModelProperty("已到货数量")	
    @QueryField(name = "已到货数量", alias = "po_purchase_detail_standard")	
    private String arriveQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "po_purchase_detail_standard")	
    private String unit;	
	
	@ApiModelProperty("库存说明")	
    @QueryField(name = "库存说明", alias = "po_purchase_detail_standard")	
    private String stockDesc;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("采购员")	
    @QueryField(name = "采购员")	
    private String demandUserName;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "POPURCHASE_STATSU", value = "30", query = false)	
    private String status;	
	
}	
