package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
/**	
 * (HrRecruitment)实体类	
 *	
 * <AUTHOR> z	
 * @since 2022-12-14 16:20:02	
 */	
@Data	
@ApiModel("招聘计划模板")	
@QueryModel(	
        name = "0693",	
        remark = "招聘计划模板",	
        alias = "hr_recruitment",	
        searchApi = "/api/hr/recruit/query")	
public class HrRecruitmentQueryVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
    /**	
     * 公司编码	
     */	
	@ApiModelProperty("公司编码")	
    @QueryField(name = "公司编码", type = Type.Date)	
    private String company;	
    /**	
     * 公司名称	
     */	
	@ApiModelProperty("公司名称")	
    @QueryField(name = "公司名称")	
    private String companyName;	
    /**	
     * 所属部门编码	
     */	
	@ApiModelProperty("所属部门编码")	
    @QueryField(name = "所属部门编码", show = false)	
    private String deptCode;	
    /**	
     * 岗位ID	
     */	
	@ApiModelProperty("岗位ID")	
    @QueryField(name = "岗位ID" )	
    private String jobsId;	
    /**	
     * 领导人编码	
     */	
	@ApiModelProperty("领导人编码")	
    @QueryField(name = "领导人编码" )	
    private String leaderUserCode;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.MultiSelect, dictOption = "HR_AUDIT_STATUS")	
    private String status;	
    /**	
     * 用户状态	
     */	
	@ApiModelProperty("用户状态")	
    @QueryField(name = "用户状态", type = Type.MultiSelect, dictOption = "HR_USER_STATUS")	
    private String userType;	
    /**	
     * 需求原因类型(性质) HR_RECRUIT_TYPE	
     */	
	@ApiModelProperty("需求原因类型")	
    @QueryField(name = "需求原因类型", type = Type.MultiSelect, dictOption = "HR_RECRUIT_TYPE" )	
    private String type;	
    /**	
     * 原因	
     */	
	@ApiModelProperty("原因")	
    @QueryField(name = "原因" )	
    private String reason;	
    /**	
     * 期望到岗时间	
     */	
	@ApiModelProperty("期望到岗时间")	
    @QueryField(name = "期望到岗时间", type = Type.Date)	
    private String expectTime;	
    /**	
     * 薪酬范围	
     */	
	@ApiModelProperty("薪酬范围")	
    @QueryField(name = "薪酬范围" )	
    private String remuneration;	
    /**	
     * 要求	
     */	
	@ApiModelProperty("要求")	
    @QueryField(name = "要求" )	
    private String requirements;	
    /**	
     * 是否能出差	
     */	
	@ApiModelProperty("是否能出差")	
    @QueryField(name = "是否能出差", type = Type.Select, option = {"0", "否", "1", "是"}, value = "0")	
    private String evection;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date,order = OrderBy.DESC)	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", show = false)	
    private String createBy;	
}	
	
