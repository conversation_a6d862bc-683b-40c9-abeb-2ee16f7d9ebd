package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalPlanApiVo {	
	
    /**	
     * 到货计划单号	
     */	
    @ApiModelProperty("到货计划单号")	
    private String planCode;	
	
    /**	
     * 到货计划类型(1:采购到货;2:销售退货到货)----必填项	
     */	
    @ApiModelProperty("到货计划类型：采购到货、销售退货到货")	
    private String orderType;	
	
    /**	
     * 操作人编码----必填项	
     */	
    @ApiModelProperty("操作人编码")	
    private String operatorCode;	
	
    /**	
     * 操作人姓名----必填项	
     */	
    @ApiModelProperty("操作人姓名")	
    private String operatorName;	
	
    /**	
     * 计划下单日期----必填项	
     */	
    @ApiModelProperty("计划下单日期")	
   // @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")	
    private String orderDate;	
	
    /**	
     * 预计到货日期----必填项	
     */	
    @ApiModelProperty("预计到货日期")	
   // @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")	
    private String dueArrivalDate;	
	
    /**	
     * 送货方式(101:物流快递;102:供应商货运;103:客户自提)----必填项	
     */	
    @ApiModelProperty("送货方式）")	
    private String logisticType;	
	
    /**	
     * 物流单号	
     */	
    @ApiModelProperty("物流单号")	
    private String logisticCode;	
	
    /**	
     * 物流公司编码	
     */	
    @ApiModelProperty("物流公司编码")	
    private String shipperCode;	
	
    /**	
     * 物流公司名称	
     */	
    @ApiModelProperty("物流公司名称")	
    private String shipperName;	
	
    /**	
     * 车牌号	
     */	
    @ApiModelProperty("车牌号")	
    private String licensePlate;	
	
    /**	
     * 送货司机	
     */	
    @ApiModelProperty("送货司机")	
    private String deliveryDriver;	
	
    /**	
     * 司机联系方式	
     */	
    @ApiModelProperty("司机联系方式")	
    private String driverPhone;	
	
    /**	
     * 司机证件号	
     */	
    @ApiModelProperty("司机证件号")	
    private String idCard;	
	
    /**	
     * 车辆类型	
     */	
    @ApiModelProperty("车辆类型")	
    private String carType;	
	
    /**	
     * 收货仓库编码	
     */	
    @ApiModelProperty("收货仓库编码")	
    private String whCode;	
	
    /**	
     * 收货仓库名称	
     */	
    @ApiModelProperty("收货仓库名称")	
    private String whName;	
	
    /**	
     * 月台号	
     */	
    @ApiModelProperty("月台号")	
    private String platformCode;	
	
    /**	
     * 月台名称	
     */	
    @ApiModelProperty("月台名称")	
    private String platformName;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 供应商编码	
     */	
    @ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
    @ApiModelProperty("供应商名称")	
    private String supplierName;	
	
	
    /**	
     * 销售订单单号	
     */	
    @ApiModelProperty("销售订单单号")	
    private String soNo;	
	
    /**	
     * 到货明细单	
     */	
	@ApiModelProperty("到货明细单")	
    private List<WmsArrivalPlanItemApiVo> itemVos;	
	
    /**	
     * 质量凭证	
     */	
	@ApiModelProperty("质量凭证")	
    private List<Map> fileList;	
	
    //预约时间段	
    private String timeInterval;	
	
    //用于判断是否有相同的发货计划，如果存在Id相同的先删除	
    private String thirdOrderCode;	
	
}	
