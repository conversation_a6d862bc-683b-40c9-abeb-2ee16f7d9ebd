package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import com.imes.domain.entities.query.plugin.imports.ImportRemark;
import lombok.Data;

@Data
@ImportModel(
        name = "仓库信息导入模板",
        modelName = "0027")
public class WmsWarehouseImportVo extends BaseModel {

    @ImportField(name = "部门编码", required = true, remark = "必填\n参考【组织管理】", maxLength = 40)
    private String departCode;

    @ImportField(name = "仓库编码", unique = true, required = true, remark = "必填", maxLength = 40)
    private String warehouseCode;

    @ImportField(name = "仓库名称", required = true, remark = "必填", maxLength = 50)
    private String warehouseName;

    @ImportField(name = "仓库类型", required = true, dictOption = "STORAGE_WH_TYPE", remark = "必填\n写入对应数字\n1:储存库\n2:流通库")
    private String warehouseType;

    @ImportField(name = "存储方式", required = true, dictOption = "WMS_STORAGE_MODE", remark = "必填\n写入对应数字\n1:平库\n2:立库\n默认1:平库")
    private String storageMode;

    @ImportField(name = "第三方编码", maxLength = 40)
    private String thirdBusCode;

    @ImportField(name = "联系电话", maxLength = 20)
    private String phone;

    @ImportField(name = "地址", maxLength = 40)
    private String address;

    @ImportField(name = "仓库描述", maxLength = 100)
    private String warehouseDescribe;

    @ImportField(name = "备注", maxLength = 100)
    private String remarks;

    @ImportField(name = "是否指定物料", isInteger = true, remark = "不必填\n写入对应数字\n0:否\n1:是")
    private String isLimitedMaterial;

    @ImportField(name = "是否启用负库存", isInteger = true, remark = "不必填\n写入对应数字\n0:否\n1:是")
    private String isEnableMinusInventory;
}
