package com.imes.domain.entities.pm.po;


import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (ProjectHistoryData)实体类
 *
 * <AUTHOR>
 * @since 2022-02-18 16:35:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_history_data", resultMap = "BaseResultMap")
public class ProjectHistoryData implements Serializable {


    private static final long serialVersionUID = -2821728330297083452L;
    /**
     * id
     */
    private String id;

    /**
     * 业务ID
     */
    private String businessId;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 系列
     */
    private String data;

    /**
     * 实体
     */
    private Object object;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改人名
     */
    @TableField(fill = FieldFill.INSERT)
    private String creator;
}
