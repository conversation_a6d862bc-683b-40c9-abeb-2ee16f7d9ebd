package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsScrapItemOrder;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.util.Date;	
	
@Data	
public class WmsScrapItemOrderVo extends WmsScrapItemOrder {	
	
    private Date applyOn;	
	
    private Integer orderType;	
	
    private Integer orderStatus;	
	
    private String applyDepartCode;	
	
    private String applyDepartName;	
	
    private String applyBy;	
	
    private String applyName;	
	
    private String purpose;	
	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    private String activityId;	
	
    /**	
     * 审核按钮显示	
     */	
	@ApiModelProperty("审核按钮显示")	
    private boolean showAuditBtn;	
	
    /*****工作流信息*******/	
	
    private String taskId;	
	
    private String taskType;	
	
    private String businessKey;	
	
    private String processInstanceName;

    private String detailId;
	
}	
