package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《合同》查询条件">>
 * @company 捷创智能技术有限公司
 * @create 2021-04-27 17:03
 */
@ApiModel("《合同》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class BargainQuery extends BaseQuery {
    @ApiModelProperty("单据guid值")
    private String guid;
    @ApiModelProperty("单据编码")
    private String nid;
    @ApiModelProperty("单据编号(合同号)")
    private String bid;
    @ApiModelProperty("父单据编号")
    private String pBid;
    @ApiModelProperty("制单人工号")
    private String bUser;
    @ApiModelProperty("制单部门路径")
    private String bDept;
    @ApiModelProperty("销售员工号")
    private String gUser;
    @ApiModelProperty("销售部门路径")
    private String gDept;
    @ApiModelProperty("客户编号")
    private String gClient;
    @ApiModelProperty("客户名称")
    private String gcName;
    @ApiModelProperty("备注")
    private String vBz;
    @ApiModelProperty("整单备注")
    private String vZdbz;
    /*@ApiModelProperty("创建日期")
    private LocalDateTime createTime;
    @ApiModelProperty("生效日期")
    private LocalDateTime updateTime;*/
    @ApiModelProperty("品牌")
    private String gBrand;
    @ApiModelProperty("客户型号")
    private String gIDC;
    @ApiModelProperty("型号")
    private String gID;
    @ApiModelProperty("规格")
    private String gModel;
    @ApiModelProperty("品名")
    private String gName;
    @ApiModelProperty("系列")
    private String gSerial;
    @ApiModelProperty("订货号")
    private String gIndex;
    @ApiModelProperty("单位")
    private String gUnit;
    @ApiModelProperty("物料编号")
    private String vWlBh;
    @ApiModelProperty("型号状态")
    private String vXhZt;
    @ApiModelProperty("型号类型(T/S)")
    private String vXhLx;
    @ApiModelProperty("统一物料编码")
    private String vEanUpc;
    @ApiModelProperty("PGC代码")
    private String vPgc;
    @ApiModelProperty("折扣代码")
    private String vZkDm;
    @ApiModelProperty("特价类型")
    private String vTjLx;
    @ApiModelProperty("EDI/NON-EDI")
    private String vEdi;
    @ApiModelProperty("客户BPID")
    private String bPID;
    @ApiModelProperty("客户地址")
    private String vAddress;
    @ApiModelProperty("联系人")
    private String vMan;
    @ApiModelProperty("联系人传真")
    private String vFax;
    @ApiModelProperty("联系人电话")
    private String vTel;
    @ApiModelProperty("销售类型编码")
    private String vXsLx;
    @ApiModelProperty("付款方式编码")
    private String vFkFs;
    @ApiModelProperty("客户合同号")
    private String vKhHth;
    @ApiModelProperty("RA主导")
    private String vRaZd;
    @ApiModelProperty("RA系统号")
    private String vRaXth;
    @ApiModelProperty("RA销售")
    private String vRaXs;
    @ApiModelProperty("合同交货期")
    private LocalDate dHthq;
    @ApiModelProperty("项目编码")
    private String vXmBm;
    @ApiModelProperty("项目名称")
    private String vXmMc;
    @ApiModelProperty("联系人地址")
    private String vAddressM;
    @ApiModelProperty("收货人")
    private String vManSh;
    @ApiModelProperty("收货人传真")
    private String vFaxSh;
    @ApiModelProperty("收货人电话")
    private String vTelSh;
    @ApiModelProperty("收货人地址")
    private String vAddressSh;
    @ApiModelProperty("合同货期")
    private String vHthq;
    @ApiModelProperty("商务工号")
    private String sUser;
    @ApiModelProperty("商务部门路径")
    private String sDept;
    @ApiModelProperty("商务员")
    private String sUserName;
    @ApiModelProperty("商务部门")
    private String sDeptName;
    @ApiModelProperty("商务确认状态")
    private String vQrZt;
    @ApiModelProperty("商务确认时间")
    private LocalDate dQrSj;
    @ApiModelProperty("商机编号")
    private String vSjBh;
    @ApiModelProperty("分销商BPID")
    private String pBPID;
    @ApiModelProperty("特价号码")
    private String vTjHm;
    @ApiModelProperty("特价名称")
    private String vTjMc;
    @ApiModelProperty("客户品名")
    private String gNameC;
    @ApiModelProperty("客户行业")
    private String trade;
    @ApiModelProperty("PA产品")
    private String vPaCp;
    @ApiModelProperty("打印模板")
    private String vDyMb;
    @ApiModelProperty("人工服务")
    private String vRgFw;
    @ApiModelProperty("客户物料编号")
    private String vWlBhC;
    @ApiModelProperty("条款是否修改")
    private String vTkSfXg;
    @ApiModelProperty("试/借开始日期")
    private LocalDate dSjKsSj;
    @ApiModelProperty("试/借结束日期")
    private LocalDate dSjJsSj;
    @ApiModelProperty("所属公司")
    private String cidName;
    @ApiModelProperty("制单人")
    private String bUserName;
    @ApiModelProperty("制单部门")
    private String bDeptName;
    @ApiModelProperty("销售员区域")
    private String gArea;
    @ApiModelProperty("销售员")
    private String gUserName;
    @ApiModelProperty("销售部门")
    private String gDeptName;
    @ApiModelProperty("销售类型")
    private String vXsLxName;
    @ApiModelProperty("付款方式")
    private String vFkFsName;
}
