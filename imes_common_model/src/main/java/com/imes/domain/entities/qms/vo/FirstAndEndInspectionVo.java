package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.FirstAndEndInspection;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.ProcessInspection;	
import com.imes.domain.entities.qms.po.QmsRouteInspectionDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class FirstAndEndInspectionVo extends FirstAndEndInspection {	
	
    /**	
     * 不良品数量	
     */	
	@ApiModelProperty("不良品数量")	
    private int badNum;	
    private static final long serialVersionUID = 1L;	
}	
