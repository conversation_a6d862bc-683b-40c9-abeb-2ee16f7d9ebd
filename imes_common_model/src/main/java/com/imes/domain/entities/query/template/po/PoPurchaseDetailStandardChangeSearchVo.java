package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
	
@Data	
@ApiModel("采购订单明细变更单")	
@QueryModel(	
        name = "1234-detail",	
        remark = "采购订单明细变更单",	
        alias = "a",	
        searchApi = "/api/po/poPurchaseDetailStandardChange/queryList")	
public class PoPurchaseDetailStandardChangeSearchVo extends BaseModel {	
	
	@ApiModelProperty("主单Id")	
    @QueryField(name = "主单Id")	
    private String mainId;	
	
	@ApiModelProperty("子订单号")	
    @QueryField(name = "子订单号")	
    private String sdNo;	
	
	@ApiModelProperty("变更类型")	
    @QueryField(name = "变更类型")	
    private String changeType;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称",alias = "ppc_material")	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格",alias = "ppc_material")	
    private String specification;	
	
	@ApiModelProperty("图号")	
    @QueryField(name = "图号",alias = "ppc_material")	
    private String dwgNo;	
	
	@ApiModelProperty("材质")	
    @QueryField(name = "材质",alias = "ppc_material")	
    private String quality;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号",alias = "ppc_material")	
    private String materialMarker;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号")	
    private String lineNo;	
	
	@ApiModelProperty("采购数量")	
    @QueryField(name = "采购数量", type=Type.Number)	
    private String qty;	
	
	@ApiModelProperty("原采购数量")	
    @QueryField(name = "原采购数量", type=Type.Number)	
    private String oldQty;	
	
	@ApiModelProperty("采购单位")	
    @QueryField(name = "采购单位")	
    private String unit;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位")	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", type=Type.Number)	
    private String baseUnitQty;	
	
   // @QueryField(name = "关联数量", type=Type.Number)	
    private BigDecimal relationQty;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", type=Type.Number)	
    private String arriveQty;	
	
/*    @QueryField(name = "退货不补货数量", type=Type.Number)	
    private String cancelQty;	
	
	@ApiModelProperty("退货补货数量")	
    @QueryField(name = "退货补货数量", type=Type.Number)	
    private String cancelReplenishQty;*/	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
	
	@ApiModelProperty("业务状态 10-正常 20-已关闭")	
    @QueryField(name = "业务状态 10-正常 20-已关闭")	
    private String businessStatus;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String createOn;	
	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人")	
    private String createBy;	
	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String updateOn;	
	
	@ApiModelProperty("更新人")	
    @QueryField(name = "更新人")	
    private String updateBy;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
	@ApiModelProperty("原备注")	
    @QueryField(name = "原备注")	
    private String oldRemarks;	
	
	@ApiModelProperty("单价")	
    @QueryField(name = "单价", type=Type.Number)	
    private String singlePrice;	
	
	@ApiModelProperty("原单价")	
    @QueryField(name = "原单价", type=Type.Number)	
    private String oldSinglePrice;	
	
	@ApiModelProperty("净价")	
    @QueryField(name = "净价", type=Type.Number)	
    private String netPrice;	
	
	@ApiModelProperty("折扣方式")	
    @QueryField(name = "折扣方式")	
    private String discountType;	
	
	@ApiModelProperty("折扣额")	
    @QueryField(name = "折扣额", type=Type.Number)	
    private String discountPrice;	
	
	@ApiModelProperty("不含税单价")	
    @QueryField(name = "不含税单价", type=Type.Number)	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type=Type.Number)	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type=Type.Number)	
    private String taxRatePrice;	
	
	@ApiModelProperty("不税额金额")	
    @QueryField(name = "不税额金额", type=Type.Number)	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计(总金额)")	
    @QueryField(name = "价税合计(总金额)", type=Type.Number)	
    private String allPrice;	
	
	@ApiModelProperty("到货时间")	
    @QueryField(name = "到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String deliveryDate;	
	
	@ApiModelProperty("原到货时间")	
    @QueryField(name = "原到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String oldDeliveryDate;	
	
	@ApiModelProperty("源单类型")	
    @QueryField(name = "源单类型")	
    private String requestDocType;	
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号")	
    private String requestDocCode;	
	
	@ApiModelProperty("源单申请部门编码")	
    @QueryField(name = "源单申请部门编码")	
    private String requestDocDepCode;	
	
	@ApiModelProperty("源单申请部门名称")	
    @QueryField(name = "源单申请部门名称", alias = "co_department.depart_name")	
    private String requestDocDepName;	
	
	@ApiModelProperty("源单申请人工号")	
    @QueryField(name = "源单申请人工号")	
    private String requestDocPersonCode;	
	
	@ApiModelProperty("源单申请人名称")	
    @QueryField(name = "源单申请人名称", alias = "pe_user.user_name")	
    private String requestDocPersonName;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", type=Type.Number)	
    private String taxRate;	
	
	@ApiModelProperty("税率编码")	
    @QueryField(name = "税率编码")	
    private String taxCode;	
	
	@ApiModelProperty("原税率")	
    @QueryField(name = "原税率", type=Type.Number)	
    private String oldTaxRate;	
	
	@ApiModelProperty("税率编码")	
    @QueryField(name = "税率编码")	
    private String oldTaxCode;	
	
	@ApiModelProperty("折扣率")	
    @QueryField(name = "折扣率", type=Type.Number)	
    private String discountRate;	
	
	@ApiModelProperty("是否含税 0是 1否")	
    @QueryField(name = "是否含税 0是 1否")	
    private String includeTax;	
	
	@ApiModelProperty("自定义字段")	
    @QueryField(name = "自定义字段")	
    private String custom;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性")	
    private String skuCode;	
	
	@ApiModelProperty("原辅助属性")	
    @QueryField(name = "原辅助属性")	
    private String oldSkuCode;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码")	
    private String supplierCode;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称", alias = "sys_supplier")	
    private String supplierName;	
	
    private String qtyPrecision;	
	
    private BigDecimal availableQty;	
	
    private String uniqueCode;	
	
    private String standardId;	
	
    private String unitName;	
	
    private String baseUnitName;	
	
}	
	
