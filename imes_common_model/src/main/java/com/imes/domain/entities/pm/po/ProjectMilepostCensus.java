package com.imes.domain.entities.pm.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目里程碑统计>>
 * @company 捷创智能技术有限公司
 * @create 2021-12-14 19:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostCensus {
    private String id;
    private String pid;
    private String text;
    private Integer sort;
    private String employeeCode;
    private String employeeName;
    private BigDecimal actualHours;
    private BigDecimal planHours;
    /**
     * '飞机费用'
     */
    private BigDecimal aircraftCost;
    /**
     * 火车费用
     */
    private BigDecimal trainCost;
    /**
     * 大巴费用
     */
    private BigDecimal busCost;
    /**
     * 的士费用
     */
    private BigDecimal taxiCost;
    /**
     * 过路费用
     */
    private BigDecimal tollCost;
    /**
     * 停车费用
     */
    private BigDecimal parkingCost;
    /**
     * 其他交通费用
     */
    private BigDecimal otherTrafficCost;
    /**
     * 住宿费用
     */
    private BigDecimal stayCost;
    /**
     * 招待费用
     */
    private BigDecimal entertainCost;
    /**
     * 差旅补助
     */
    private BigDecimal travelAllowance;
    /**
     * 其他费用
     */
    private BigDecimal otherCost;
    /**
     * 补贴金额
     */
    private BigDecimal subsidy;
}
