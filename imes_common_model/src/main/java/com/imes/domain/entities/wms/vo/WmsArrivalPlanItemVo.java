package com.imes.domain.entities.wms.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imes.domain.entities.wms.WmsArrivalPlanItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalPlanItemVo extends WmsArrivalPlanItem {	
	
    /**	
     * 到货计划单类型	
     */	
	@ApiModelProperty("到货计划单类型")	
    @JSONField(name = "到货计划单类型")	
    private String orderType;	
	
    /**	
     * 到货计划单状态	
     */	
	@ApiModelProperty("到货计划单状态")	
    @JSONField(name = "到货计划单状态")	
    private String planStatus;	
	
    /**	
     * 到货计划业务关闭状态	
     */	
	@ApiModelProperty("到货计划业务关闭状态")	
    @JSONField(name = "到货计划业务关闭状态")	
    private String businessStatus;	
	
    /**	
     * 预计到货时间	
     */	
	@ApiModelProperty("预计到货时间")	
    @JSONField(name = "预计到货时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")	
    private Date dueArrivalDate;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    @JSONField(name = "供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    @JSONField(name = "供应商名称")	
    private String supplierName;	
	
    @JSONField(name = "制造商编码")	
    @ApiModelProperty("制造商编码")	
    private String manufacturerCode;	
	
    @JSONField(name = "制造商名称")	
    @ApiModelProperty("制造商名称")	
    private String manufacturerName;	
	
    /**	
     * 计划申请人	
     */	
	@ApiModelProperty("计划申请人")	
    @JSONField(name = "计划申请人")	
    private String operatorCode;	
	
    /**	
     * 计划申请人	
     */	
	@ApiModelProperty("计划申请人")	
    @JSONField(name = "计划申请人")	
    private String operatorName;	
	
    /**	
     * 部门编码	
     */	
	@ApiModelProperty("部门编码")	
    @JSONField(name = "部门编码")	
    private String depCode;	
	
    /**	
     * 部门名称	
     */	
	@ApiModelProperty("部门名称")	
    @JSONField(name = "部门名称")	
    private String depName;	
	
    /**	
     * 车牌号	
     */	
	@ApiModelProperty("车牌号")	
    @JSONField(name = "车牌号")	
    private String licensePlate;	
	
    /**	
     * 送货司机	
     */	
	@ApiModelProperty("送货司机")	
    @JSONField(name = "送货司机")	
    private String deliveryDriver;	
	
    /**	
     * 司机联系方式	
     */	
	@ApiModelProperty("司机联系方式")	
    @JSONField(name = "司机联系方式")	
    private String driverPhone;	
	
    /**	
     * 司机证件号	
     */	
	@ApiModelProperty("司机证件号")	
    @JSONField(name = "司机证件号")	
    private String idCard;
	
    /**	
     * 车辆类型	
     */	
	@ApiModelProperty("车辆类型")	
    @JSONField(name = "车辆类型")	
    private String carType;	
	
    /**	
     * 收货仓库编码	
     */	
	@ApiModelProperty("收货仓库编码")	
    @JSONField(name = "收货仓库编码")	
    private String whCode;	
	
    /**	
     * 收货仓库名称	
     */	
	@ApiModelProperty("收货仓库名称")	
    @JSONField(name = "收货仓库名称")	
    private String whName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    @JSONField(name = "备注")	
    private String planRemarks;	
	
    /**	
     * 待到货数量(采购单位)	
     */	
	@ApiModelProperty("待到货数量(采购单位)")	
    @JSONField(name = "待到货数量")	
    private BigDecimal needQty;	
	
    /**	
     * 待到货数量(基本单位)	
     */	
	@ApiModelProperty("待到货数量(基本单位)")	
    @JSONField(name = "待到货数量")	
    private BigDecimal baseNeedQty;	
	
    /**	
     * 待到货数量(采购单位)	
     */	
	@ApiModelProperty("待到货数量(采购单位)")	
    @JSONField(name = "待到货数量")	
    private BigDecimal poNeedQty;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    @JSONField(name = "批次号")	
    private String batch;	
	
    /**	
     * 物流公司编码	
     */	
	@ApiModelProperty("物流公司编码")	
    @JSONField(name = "物流公司编码")	
    private String shipperCode;	
	
    /**	
     * 物流公司名称	
     */	
	@ApiModelProperty("物流公司名称")	
    @JSONField(name = "物流公司名称")	
    private String shipperName;	
	
    /**	
     * 物流单号	
     */	
	@ApiModelProperty("物流单号")	
    @JSONField(name = "物流单号")	
    private String logisticCode;	
	
    /**	
     * 默认存储仓库	
     */	
	@ApiModelProperty("默认存储仓库")	
    @JSONField(name = "默认存储仓库")	
    private String defaultWhCode;	
	
    /**	
     * 默认存储仓库	
     */	
	@ApiModelProperty("默认存储仓库")	
    @JSONField(name = "默认存储仓库")	
    private String defaultWhName;	
	
    /**	
     * 默认存储库位	
     */	
	@ApiModelProperty("默认存储库位")	
    @JSONField(name = "默认存储库位")	
    private String defaultBinCode;	
	
    /**	
     * 打印是否显示明细	
     */	
	@ApiModelProperty("打印是否显示明细")	
    @JSONField(name = "打印是否显示明细")	
    private Integer printShowItem;	
	
    /**	
     * 采购员工编码	
     */	
	@ApiModelProperty("采购员工编码")	
    @JSONField(name = "采购员工编码")	
    private String purchaserCode;	
	
    /**	
     * 采购员工姓名	
     */	
	@ApiModelProperty("采购员工姓名")	
    @JSONField(name = "采购员工姓名")	
    private String purchaserName;	
	
    /**	
     * 供应商批次号	
     */	
	@ApiModelProperty("供应商批次号")	
    @JSONField(name = "供应商批次号")	
    private String batchSupplier;	
	
    /**	
     * 到货数量	
     */	
	@ApiModelProperty("到货数量")	
    @JSONField(name = "到货数量")	
    private BigDecimal arrivalQty;	
	
    /**	
     * 是否启用批次管理	
     */	
	@ApiModelProperty("是否启用批次管理")	
    @JSONField(name = "是否启用批次管理")	
    private String isBatch;	
	
    /**	
     * 是否自动生成批次号	
     */	
	@ApiModelProperty("是否自动生成批次号")	
    @JSONField(name = "是否自动生成批次号")	
    private String autoBatchNo;	
	
    /**	
     * 是否有序列号(0-否（缺省） 1-是)	
     */	
	@ApiModelProperty("是否有序列号(0-否（缺省） 1-是)")	
    @JSONField(name = "是否有序列号")	
    private String isSerial;	
	
    /**	
     * 是否自动生成批次号	
     */	
	@ApiModelProperty("是否自动生成批次号")	
    @JSONField(name = "是否自动生成批次号")	
    private String autoSerialNo;	
	
    /**	
     * 单据来源	
     */	
	@ApiModelProperty("单据来源")	
    @JSONField(name = "单据来源")	
    private String source;	
	
    /**	
     * 销售订单明细单号	
     */	
	@ApiModelProperty("销售订单明细单号")	
    @JSONField(name = "销售订单明细单号")	
    private String soItemCode;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    @JSONField(name = "生产日期")	
    private String productionDateStr;	
	
    /**	
     * 失效日期	
     */	
	@ApiModelProperty("失效日期")	
    @JSONField(name = "失效日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")	
    private String failureDateStr;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    @JSONField(name = "进位方式")	
    private Byte carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    @JSONField(name = "小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    @JSONField(name = "包装单位")	
    private List<String> packUnitCodeList;	
	
    /**	
     * 是否启用箱码管理	
     */	
	@ApiModelProperty("是否启用箱码管理")	
    @JSONField(name = "是否启用箱码管理")	
    private String enableCaseNumber;	
	
    /**	
     * 最小装箱数量	
     */	
	@ApiModelProperty("最小装箱数量")	
    @JSONField(name = "最小装箱数量")	
    private BigDecimal minNumberOfBoxes;	
	
    /**	
     * 月台编码	
     */	
	@ApiModelProperty("月台编码")	
    @JSONField(name = "月台编码")	
    private String platformCode;	
	
    /**	
     * 月台名称	
     */	
	@ApiModelProperty("月台名称")	
    @JSONField(name = "月台名称")	
    private String platformName;	
	
	
    /**	
     * 预约月台	
     */	
	@ApiModelProperty("预约月台")	
    @JSONField(name = "预约月台")	
    private String receiveDock;	
	
    /**	
     * 是否赠品（1:是；0:否）	
     */	
	@ApiModelProperty("是否赠品（1:是；0:否）")	
    @JSONField(name = "是否赠品")	
    private String giftType;	
	
    /**	
     * 质保周期	
     */	
	@ApiModelProperty("质保周期")	
    @JSONField(name = "质保周期")	
    private BigDecimal qualityPeriod;	
	
    /**	
     * 质保周期单位	
     */	
	@ApiModelProperty("质保周期单位")	
    @JSONField(name = "质保周期单位")	
    private String qualityPeriodUnit;	
	
    /**	
     * 来料检验顺序1先到货后检验2先检验后到货	
     */	
	@ApiModelProperty("来料检验顺序1先到货后检验2先检验后到货")	
    @JSONField(name = "来料检验顺序")	
    private String incomingInspectionSequence;	
	
    /**	
     * 采购订单明细主键	
     */	
	@ApiModelProperty("采购订单明细主键")	
    @JSONField(name = "采购订单明细主键")	
    private String poItemId;	
	
    /**	
     * 物流类型（1：汽运；2：物流；3：快递）	
     */	
    @JSONField(name = "物流类型（1：汽运；2：物流；3：快递）")	
    @ApiModelProperty("物流类型（1：汽运；2：物流；3：快递）")	
    private String logisticType;	
	
    /**	
      * 物料是否启用质保期	
      */	
    @JSONField(name = "物料是否启用质保期")	
    @ApiModelProperty("物料是否启用质保期")	
    private String isQualityPeriod;	
	
    /**	
     * 箱码	
     */	
	@ApiModelProperty("箱码")	
    @JSONField(name = "箱码")	
    private List<WmsPackingBoxMaterialVo> boxMaterialList;	
	
    /**	
     * 单位名称	
     */	
	@ApiModelProperty("单位名称")	
    private String poMainUnitName;	
	
    /**	
     * 子单业务状态	
     */	
    @JSONField(name = "子单业务状态")	
    @ApiModelProperty("子单业务状态")	
    private String detailBusinessStatus;

    /**
     * 到货明细主键
     */
    @JSONField(name = "到货明细主键")
    @ApiModelProperty("到货明细主键")
    private String detailId;
}
