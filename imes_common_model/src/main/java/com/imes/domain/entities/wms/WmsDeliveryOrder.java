package com.imes.domain.entities.wms;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Entity
@Table(name = "wms_delivery_order")
public class WmsDeliveryOrder implements Serializable {
    /**
     *
     */
    @Id
    @ApiModelProperty("id")
    private String id;

    /**
     * 发货单号
     */
    @ApiModelProperty("发货单号")
    private String doCode;

    /**
     * 发货单类型：销售发货、直接发货、其他类型
     */
    @ApiModelProperty("发货单类型：销售发货、直接发货、其他类型")
    private String orderType;

    /**
     * 单据的来源：手工建单、第三方系统
     */
    @ApiModelProperty("单据的来源：手工建单、第三方系统")
    private String source;

    /**
     * 第三方系统发货单唯一标识
     */
    @ApiModelProperty("第三方系统发货单唯一标识")
    private String thirdOrderCode;

    /**
     * 发货单状态：未发货、已发货、已取消
     */
    @ApiModelProperty("发货单状态：未发货、已发货、已取消")
    private String orderStatus;

    /**
     * 拣货状态状态：未拣货、部分拣货、全部拣货
     */
    @ApiModelProperty("拣货状态状态：未拣货、部分拣货、全部拣货")
    private String pickedStatus;

    /**
     * 关联单据类型：采购单、销售单
     */
    @ApiModelProperty("关联单据类型：采购单、销售单")
    private String receiptType;

    /**
     * 关联单据单号
     */
    @ApiModelProperty("关联单据单号")
    private String receiptCode;

    /**
     * 客户编码
     */
    @ApiModelProperty("客户编码")
    private String customerCode;

    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String customerName;

    /**
     * 计划交期（预计发货日期）
     */
    @ApiModelProperty("计划交期（预计发货日期）")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date dueDate;

    /**
     * 实际发货日期
     */
    @ApiModelProperty("实际发货日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date deliveryDate;

    /**
     * 配送发送：普通快递、线下运输
     */
    @ApiModelProperty("配送发送：普通快递、线下运输")
    private String deliveryType;

    /**
     * 物流公司编码
     */
    @ApiModelProperty("物流公司编码")
    private String shipperCode;

    /**
     * 物流公司名称
     */
    @ApiModelProperty("物流公司名称")
    private String shipperName;

    /**
     * 物流单号
     */
    @ApiModelProperty("物流单号")
    private String logisticCode;

    /**
     * 发货仓库编码
     */
    @ApiModelProperty("发货仓库编码")
    private String whCode;

    /**
     * 发货仓库名称
     */
    @ApiModelProperty("发货仓库名称")
    private String whName;

    /**
     * 收货人
     */
    @ApiModelProperty("收货人")
    private String receiverName;

    /**
     * 收货人联系方式
     */
    @ApiModelProperty("收货人联系方式")
    private String receiverPhone;

    /**
     * 收货人地址
     */
    @ApiModelProperty("收货人地址")
    private String receiverAddress;

    /**
     * 发货箱数量
     */
    @ApiModelProperty("发货箱数量")
    private Integer boxCount;

    /**
     * 发货总件数
     */
    @ApiModelProperty("发货总件数")
    private BigDecimal totalCount;

    /**
     * 配送费用
     */
    @ApiModelProperty("配送费用")
    private BigDecimal deliveryCost;

    /**
     * 总重量
     */
    @ApiModelProperty("总重量")
    private BigDecimal weight;

    /**
     * 总体积
     */
    @ApiModelProperty("总体积")
    private BigDecimal volume;

    /**
     * 单据附件
     */
    @ApiModelProperty("单据附件")
    private String orderAttach;

    /**
     * 签收单附件
     */
    @ApiModelProperty("签收单附件")
    private String signAttach;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")
    private Date createOn;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")
    private Date updateOn;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;

    /**
     * 逻辑删除标识
     */
    @ApiModelProperty("逻辑删除标识")
    private Integer deleteStatus;

    /**
     * 发货开始日期
     */
    @ApiModelProperty("发货开始日期")
    @Transient
    private Date startDate;

    /**
     * 发货结束日期
     */
    @ApiModelProperty("发货结束日期")
    @Transient
    private Date endDate;

    @Transient
    private List<WmsDeliveryOrderItem> items;

    /**
     * 发货人编码
     */
    @ApiModelProperty("发货人编码")
    private String deliveryCode;

    /**
     * 发货人名称
     */
    @ApiModelProperty("发货人名称")
    private String deliveryName;

    /**
     * 发货备注
     */
    @ApiModelProperty("发货备注")
    private String deliveryRemarks;

    /**
     * 供应商编号
     */
    @ApiModelProperty("供应商编号")
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ApiModelProperty("供应商名称")
    private String supplierName;

    /**
     * 销售员工号
     */
    @ApiModelProperty("销售员工号")
    private String salesCode;

    /**
     * 销售员名称
     */
    @ApiModelProperty("销售员名称")
    private String salesName;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;

    /**
     * 发货部门编码
     */
    @ApiModelProperty("发货部门编码")
    private String departCode;

    /**
     * 发货部门名称
     */
    @ApiModelProperty("发货部门名称")
    private String departName;

    /**
     * 二维码图片
     */
    @ApiModelProperty("二维码图片")
    private String deliveryQrcodeId;

    /**
     * 新阳系统单据编码（nid）
     */
    @ApiModelProperty("新阳系统单据编码（nid）")
    private String ecpOrderCode;

    /**
     * 存货公司编码
     */
    @ApiModelProperty("存货公司编码")
    private String companyCode;

    /**
     * 存货公司名称
     */
    @ApiModelProperty("存货公司名称")
    private String companyName;

    /**
     * 销售类型编码
     */
    @ApiModelProperty("销售类型编码")
    private String saleTypeCode;

    /**
     * 销售类型名称
     */
    @ApiModelProperty("销售类型名称")
    private String saleTypeName;

    /**
     * 发货方式名称
     */
    @ApiModelProperty("发货方式名称")
    private String deliveryTypeName;

    /**
     * 出库类型
     */
    @ApiModelProperty("出库类型")
    private String outType;

    /**
     * 是否回签
     */
    @ApiModelProperty("是否回签")
    private String isSignBack;

    /**
     * 第三方系统业务编码
     */
    @ApiModelProperty("第三方系统业务编码")
    private String thirdBusCode;

    /**
     * 申请人工号
     */
    @ApiModelProperty("申请人工号")
    private String applicantUserCode;

    /**
     * 申请人姓名
     */
    @ApiModelProperty("申请人姓名")
    private String applicantUserName;

    /**
     * 退回备注
     */
    @ApiModelProperty("退回备注")
    private String rollBackRemarks;

    @ApiModelProperty("包装人")
    private String packer;

    /**
     * 出库状态（新阳系统同步传值使用）
     */
    @ApiModelProperty("出库状态（新阳系统同步传值使用）")
    private String outboundStatus;

    private static final long serialVersionUID = 1L;

}	
