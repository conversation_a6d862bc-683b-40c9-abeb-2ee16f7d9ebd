package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import com.fasterxml.jackson.annotation.JsonFormat;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStorageInTask implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 入库任务号	
     */	
	@ApiModelProperty("入库任务号")	
    private String storageInTaskCode;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String storageInCode;	
	
    /**	
     * 入库任务编码	
     */	
	@ApiModelProperty("入库任务编码")	
    private String storageInItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 任务状态（未关闭，已关闭）	
     */	
	@ApiModelProperty("任务状态（未关闭，已关闭）")	
    private Integer status;	
	
    /**	
     * 质检状态（还未质检，正在质检，完成质检）	
     */	
//    private Integer inspectStatus;
	
    /**	
     * 质检结构（未认定，合格，不合格）	
     */	
//    private Integer inspectResult;
	
    /**	
     * 上架状态（未上架，正在上架，完成上架）	
     */	
	@ApiModelProperty("上架状态（未上架，正在上架，完成上架）")	
    private String putawayStatus;	
	
    /**	
     * 任务开始时间	
     */	
	@ApiModelProperty("任务开始时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date startTime;	
	
    /**	
     * 任务结束时间	
     */	
	@ApiModelProperty("任务结束时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date endTime;	
	
    /**	
     * 任务单物料数量	
     */	
	@ApiModelProperty("任务单物料数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 本任务已入库数量	
     */	
	@ApiModelProperty("本任务已入库数量")	
    private BigDecimal inventoryQty;	
	
    /**	
     * 本次任务需入库数量	
     */	
	@ApiModelProperty("本次任务需入库数量")	
    private BigDecimal theInventoryQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
