package com.imes.domain.entities.scs.vo;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class ScsAccountSumSearchVo {
    /**
     * 创建时间
     */
    private String createOn;

    /**
     * 销售订单号
     */
    private String soNo;

    /**
     * 订单类型
     */
    private String planType;

    /**
     * 下单日期
     */
    private Date receiveDate;

    /**
     * 订单项次
     */
    private String sdNo;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 物料规格
     */
    private String specification;

    /**
     * 物料型号
     */
    private String modelNumber;

    /**
     * 采购数量
     */
    private BigDecimal qty;

    /**
     * 是否含税 0是1否
     */
    private String includeTax;

    /**
     * 税率
     */
    private BigDecimal taxRate;

    /**
     * 单价
     */
    private BigDecimal singlePrice;

    //发货数量
    private BigDecimal deliveryNum;

    //签收数量
    private BigDecimal receiveNum;

    //签退数量
    private BigDecimal returnNum;

    //签差异数量
    private BigDecimal signDiffNum;

    //入库数量
    private BigDecimal storageNum;

    //退库数量
    private BigDecimal storageReturnNum;

    //已对账数量
    private BigDecimal reconciledNum;

    //待对账数量
    private BigDecimal reconciledNeedNum;

    //待对账金额
    private BigDecimal reconciledNeedPrice;
}
