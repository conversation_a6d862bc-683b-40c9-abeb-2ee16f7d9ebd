package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import com.baomidou.mybatisplus.annotation.TableField;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
import io.swagger.annotations.ApiModelProperty;	
	
/**	
 * (WmsInventoryScheduleItem)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-12-27 18:12:11	
 */	
@Data	
public class WmsInventoryScheduleItem implements Serializable {	
    private static final long serialVersionUID = -22587298956515620L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "盘点任务号")	
    private String scheduleNo;	
	
    @ApiModelProperty(value = "盘点任务明细号")	
    private String scheduleItemNo;	
	
    @ApiModelProperty(value = "关联明细单号")	
    private String receiptItemCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    @TableField(exist = false)	
    private String materialName;	
	
    @ApiModelProperty(value = "生产批次号")	
    private String batch;	
	
    @ApiModelProperty(value = "原库存数量")	
    private BigDecimal stockQty;	
	
    @ApiModelProperty(value = "盘点记录数量")	
    private BigDecimal existQty;	
	
    @ApiModelProperty(value = "复盘数量")	
    private BigDecimal replayQty;	
	
    @ApiModelProperty(value = "库存单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "${column.comment}")	
    private String remarks;	
	
}	
	
