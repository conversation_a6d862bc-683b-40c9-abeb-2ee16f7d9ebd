package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.validation.constraints.NotBlank;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsStdInspectionDetailSeniorVo implements Serializable {	
    /**	
     *子项id	
     */	
	@ApiModelProperty("id")	
    private String id;	
    /**	
     * 工序编码	
     */	
	@ApiModelProperty("工序编码")	
    private String processCode;	
	
    /**	
     * 检验项	
     */	
	@ApiModelProperty("检验项")	
    private String inspectCode;	
    /**	
     * 检验项名称	
     */	
	@ApiModelProperty("检验项名称")	
    private String inspectName;	
	
    /**	
     * 文件ID	
     */	
	@ApiModelProperty("文件ID")	
    private String fileId;	
	
    /**	
     * 文件名称	
     */	
	@ApiModelProperty("文件名称")	
    private String fileName;	
	
    /**	
     * 子项序号	
     */	
	@ApiModelProperty("子项序号")	
    private Integer itemIndex;	
	
    /**	
     * 产品名称	
     */	
	@ApiModelProperty("产品名称")	
    @NotBlank(message = "子项名称不能为空")	
    private String itemName;	
	
    /**	
     * 检测工具数据字典	
     */	
	@ApiModelProperty("检测工具数据字典")	
    private String toolType;	
	
    /**	
     * 判定方式1自动判定，2人工判定	
     */	
	@ApiModelProperty("判定方式1自动判定，2人工判定")	
    private String judgeMethod;	
	
    /**	
     * 上限	
     */	
	@ApiModelProperty("上限")	
    private BigDecimal upperLimit;	
	
    /**	
     * 下限	
     */	
	@ApiModelProperty("下限")	
    private BigDecimal lowerLimit;	
	
    /**	
     * 非理化标准	
     */	
	@ApiModelProperty("非理化标准")	
    private String standardDesc;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private static final long serialVersionUID = 1L;	
}	
