package com.imes.domain.entities.scs.vo;

import com.imes.domain.entities.ppc.po.PpcSaleMain;
import com.imes.domain.entities.ppc.vo.PpcSaleDetailVo;
import com.imes.domain.entities.scs.po.ScsSaleMain;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class ScsSaleMainVo extends ScsSaleMain {

    private int qty;

    private String detailId;

    private String detailNo;

    private String ppNo;

    private String ppId;

    private String bomCode;
    private String bomVer;

    private String specification;

    private String detailRemarks;

    private Date actualEndDate;
    private String productInputSite;
    private String productOutputSite;

    //用于判断主单是否需要提示联动关闭下游单据
    private String checkIsNeed;

    //总金额
    private BigDecimal sumPrice;

    //总金额
    private BigDecimal allPrice;

    //最大到货日期
    private Date maxDeliveryDate;
    //最大发货日期
    private Date maxShipDate;
    //单据状态
    private String statusName;
    //业务单据状态
    private String businessStatusName;
    //发货类型
    private String docTypeName;
    //子单明细
    private List<ScsSaleDetailVo> scsSaleDetailLists;

    //更新标志 add update delete
    private String updateFlg;

}
