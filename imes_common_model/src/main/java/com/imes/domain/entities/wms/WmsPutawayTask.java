package com.imes.domain.entities.wms;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPutawayTask implements Serializable {	
    /**	
     * 主键	
     */
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 上架任务号	
     */	
	@ApiModelProperty("上架任务号")
    private String putAwayTaskCode;	
	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")
    private String storageInCode;	
	
    /**	
     * 入库明细单号	
     */	
	@ApiModelProperty("入库明细单号")
    private String storageInItemCode;	
	
    /**	
     * 入库任务编码	
     */	
	@ApiModelProperty("入库任务编码")
    private String storageInTaskCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")
    private String materialCode;	
	
    /**	
     * 物料描述	
     */	
	@ApiModelProperty("物料描述")
    private String materialName;	
	
    /**	
     * 工厂编码	
     */	
	@ApiModelProperty("工厂编码")
    private String ftyCode;	
	
    /**	
     * 仓库号编码	
     */	
	@ApiModelProperty("仓库号编码")
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")
    private String whName;	
	
    /**	
     * 存储区编码	
     */	
	@ApiModelProperty("存储区编码")
    private String areaCode;	
	
    /**	
     * 存储区名称	
     */	
	@ApiModelProperty("存储区名称")
    private String areaName;	
	
    /**	
     * 仓位编码	
     */	
	@ApiModelProperty("仓位编码")
    private String binCode;	
	
    /**	
     * 原仓位号（移库时可能用到）	
     */	
	@ApiModelProperty("原仓位号（移库时可能用到）")
    private String fromBinCode;	
	
    /**	
     * 已上架数量	
     */	
	@ApiModelProperty("已上架数量")
    private BigDecimal theInventoryQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")
    private String unit;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")
    private String batch;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")
    private String remark;	
	
    /**	
     * 创建时间	
     */
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 存储单元	
     */	
	@ApiModelProperty("存储单元")
    private String cellCode;	
	
    /**	
     * 本次上架数量	
     */	
	@ApiModelProperty("本次上架数量")
    private BigDecimal putQty;	
	
    /**	
     * 库存状态	
     */	
	@ApiModelProperty("库存状态")
    private String stockStatus;	
	
    @ApiModelProperty(value = "包装单位")
    private String packCodeUnit;	
	
    @ApiModelProperty(value = "包装入库数量")
    private BigDecimal packInStorageQty;	
	
    @ApiModelProperty(value = "物料辅助属性")
    private String skuCode;	
	
    @ApiModelProperty(value = "箱号")
    private String boxNo;

    @ApiModelProperty("生产日期")
    private Date productionDate;

    private static final long serialVersionUID = 1L;	
}	
