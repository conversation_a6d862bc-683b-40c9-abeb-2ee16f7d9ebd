package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsPlatformRegister implements Serializable {	
	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 预约日期	
     */	
	@ApiModelProperty("预约日期")	
    @JsonFormat(pattern = "yyyy-MM-dd")	
    private Date registerDate;	
	
    /**	
     * 月台号	
     */	
	@ApiModelProperty("月台号")	
    private String platformCode;	
	
    /**	
     * 预约时间段	
     */	
	@ApiModelProperty("预约时间段")	
    private String timeInterval;	
	
    /**	
     * 预约方式1正常预约2	
     */	
	@ApiModelProperty("预约方式1正常预约2")	
    private String registerType;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String warehouseCode;	
	
    /**	
     * 到货计划单号	
     */	
	@ApiModelProperty("到货计划单号")	
    private String aoPlanCode;	
	
    /**	
     * 当前状态10预约20到场30进场40出场50已取消	
     */	
	@ApiModelProperty("当前状态10预约20到场30进场40出场50已取消")	
    private String status;	
	
    /**	
     * 车牌号	
     */	
	@ApiModelProperty("车牌号")	
    private String vehicleNumber;	
	
    /**	
     * 司机名称	
     */	
	@ApiModelProperty("司机名称")	
    private String driverName;	
	
    /**	
     * 联系电话	
     */	
	@ApiModelProperty("联系电话")	
    private String contactPhone;	
	
    /**	
     * 驾驶证号码	
     */	
	@ApiModelProperty("驾驶证号码")	
    private String driverCard;	
	
    /**	
     * 预约操作时间	
     */	
	@ApiModelProperty("预约操作时间")	
    private Date registerTime;	
	
    /**	
     * 预约操作人	
     */	
	@ApiModelProperty("预约操作人")	
    private String registerPerson;	
	
    /**	
     * 到场时间	
     */	
	@ApiModelProperty("到场时间")	
    private Date arriveTime;	
	
    /**	
     * 排队控制时间	
     */	
	@ApiModelProperty("排队控制时间")	
    private Date sortTime;	
	
    /**	
     * 到场叫号	
     */	
	@ApiModelProperty("到场叫号")	
    private String arriveNumber;	
	
    /**	
     * 进场时间	
     */	
	@ApiModelProperty("进场时间")	
    private Date enterTime;	
	
    /**	
     * 出场时间	
     */	
	@ApiModelProperty("出场时间")	
    private Date exitTime;	
	
    /**	
     * 可入场时间	
     */	
	@ApiModelProperty("可入场时间")	
    private Date admissionTime;	
	
    /**	
     * 过号时间	
     */	
	@ApiModelProperty("过号时间")	
    private Date oversignTime;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    /**	
     * 来司事由	
     */	
	@ApiModelProperty("来司事由")	
    private String comeReason;	
	
    /**	
     * 运输	
     */	
	@ApiModelProperty("运输")	
    private String transport;	
	
    /**	
     * 客户	
     */	
	@ApiModelProperty("客户")	
    private String customerName;	
	
    private String custom;	
	
    private static final long serialVersionUID = 1L;	
}	
