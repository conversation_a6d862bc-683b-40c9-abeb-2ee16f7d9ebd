package com.imes.domain.entities.scs.po;

import java.util.Date;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "采购方公告主表")
public class ScsNotice implements Serializable {

    private static final long serialVersionUID = -98818102586566787L;

    private String id;

    @ApiModelProperty(value = "公告号")
    private String noticeNo;

    @ApiModelProperty(value = "公告标题")
    private String noticeTitle;

    @ApiModelProperty(value = "公告内容")
    private String content;

    @ApiModelProperty(value = "附件id")
    private String fileId;

    @ApiModelProperty(value = "公告类型名称")
    private String noticeTypeName;

    @ApiModelProperty(value = "1已阅，0未阅")
    private String readStatus;

    @ApiModelProperty(value = "发布企业名称")
    private String publishCompanyName;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    private String remarks;

    @ApiModelProperty(value = "搜索开始时间")
    private Date startTime;

    @ApiModelProperty(value = "搜索结束时间")
    private Date endTime;
}