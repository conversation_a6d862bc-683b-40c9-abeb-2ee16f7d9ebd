package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsInventoryTaskItem implements Serializable {	
    /**	
     * id	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 盘点单号	
     */	
	@ApiModelProperty("盘点单号")	
    private String inventoryCode;	
	
    /**	
     * 盘点明细单号	
     */	
	@ApiModelProperty("盘点明细单号")	
    private String inventoryItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 开始盘点时间	
     */	
	@ApiModelProperty("开始盘点时间")	
    private Date startTime;	
	
    /**	
     * 盘点结束时间	
     */	
	@ApiModelProperty("盘点结束时间")	
    private Date endTime;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 原库存数量	
     */	
	@ApiModelProperty("原库存数量")	
    private BigDecimal stockQty;	
	
    /**	
     * 盘点记录数量	
     */	
	@ApiModelProperty("盘点记录数量")	
    private BigDecimal existQty;	
	
    /**	
     * 复盘数量	
     */	
    @ApiModelProperty("复盘数量")	
    private BigDecimal replayQty;	
	
    /**	
     * 开封记录数量	
     */	
	@ApiModelProperty("开封记录数量")	
    private BigDecimal openedQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 误差量	
     */	
	@ApiModelProperty("误差量")	
    private String amountOfError;	
	
    /**	
     * 盈亏状态	
     */	
	@ApiModelProperty("盈亏状态")	
    private String profitAndLossStatus;	
    /**	
     * 生产批次号	
     */	
	@ApiModelProperty("生产批次号")	
    private String batch;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 状态	
     */	
    @ApiModelProperty("状态")	
    private String status;	
	
    /**	
     * 行号	
     */	
    @ApiModelProperty("行号")	
    private String lineCode;	
	
    /**	
     * 盘点人	
     */	
    @ApiModelProperty("盘点人")	
    private String inventoryBy;	
	
    /**	
     * 盘点时间	
     */	
    @ApiModelProperty("盘点时间")	
    private Date inventoryOn;	
	
    /**	
     * 复核人	
     */	
    @ApiModelProperty("复核人")	
    private String reviewBy;	
	
    /**	
     * 复核时间	
     */	
    @ApiModelProperty("复核时间")	
    private Date reviewOn;	
	
    /**	
     * 处理状态	
     */	
    @ApiModelProperty("处理状态")	
    private String handleStatus;	
	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    @ApiModelProperty("库区名称")	
    private String areaName;	
	
    @ApiModelProperty("库位名称")	
    private String binName;	
	
    @ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
    private String lockStatus;	
	
    private String brand;	
	
    private BigDecimal diffQty;	
	
    private String specification;	
	
    private String boxNo;	
	
    private String profitStatus;	
	
    /**	
     * 载具编码	
     */	
	@ApiModelProperty("载具编码")	
    private String cellCode;	
	
    /**	
     * 盘点物料明细编码	
     */	
	@ApiModelProperty("盘点物料明细编码")	
    private String inventoryMaterialItemCode;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    private static final long serialVersionUID = 1L;	
}	
