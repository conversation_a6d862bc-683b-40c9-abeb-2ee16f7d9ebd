package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

public enum StatusCategoryEnum {
    TO_DO("to_do","未开始"),

    IN_PROGRESS("in_progress","进行中"),

    DONE("done","已完成");

    @EnumValue
    @JsonValue
    public final String category;
    public final String categoryName;

    StatusCategoryEnum(String category, String categoryName) {
        this.category = category;
        this.categoryName = categoryName;
    }


    public String getCategory() {
        return category;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public static StatusCategoryEnum match(String category) {
        return Arrays.stream(StatusCategoryEnum.values())
                .filter(e -> e.getCategory().equals(category))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("状态类型错误"));
    }
}
