package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<工作流审批>>
 * @company 捷创智能技术有限公司
 * @create 2022-01-01 23:45
 */
@ApiModel("工作流审批")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class WorkflowSubmitVo {
    @NotBlank(message = "业务id不能为空")
    private String operationId;
    @NotNull
    private Boolean flag;
    private String userCode;
    @NotBlank
    private String taskType;
    @NotNull(message = "任务id不能为空")
    private String taskId;
    private String comment;
}
