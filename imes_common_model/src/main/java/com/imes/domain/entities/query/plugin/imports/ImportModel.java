package com.imes.domain.entities.query.plugin.imports;

import java.lang.annotation.*;

@Documented
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface ImportModel {

    /**
     * 模板名称
     * 例如：name = "这是一个导入模板的名称"
     * 注意：请保证唯一
     */
    String name();

    /**
     * 模板备注
     * 例如：remark = ImportRemark.COMMON_REMARK
     * 注意：一般情况下可不填，需需要自定义内容，可在ImportRemark创建然后引用
     */
    String remark() default ImportRemark.COMMON_REMARK;

    /**
     * 关联高级查询模型名
     * 例如：modelName = "0001"
     * 注意：若需要开启自定义字段，请设置模型名
     * 只写一个模型名且不指定层级，则自动按照层级一一对应匹配
     * 若想指定多个层级，例如：modelName = {"0001->1", "0002", "0003->0"}
     * 即主单绑定0001模型的明细1，明细1绑定0002模型的主单，明细2绑定0003模型的主单
     * 若只想绑定某一个层级，不绑定的层级可以置空，例如：modelName = {"", "", "0003->1"}
     * 即仅明细2绑定0003模型的明细1自定义字段
     */
    String[] modelName() default {};

    /**
     * 起始行数
     * 例如：index = 10
     * 注意：默认起始行数为第3行，一般情况下不需要设置
     * 若你使用了自定义的模板进行导入，可进行修改
     */
    int index() default 3;

    /**
     * 导入内容条数最大限制
     * 例如：maxLimit = 500
     * 注意：默认10000条，一般情况下不需要设置
     */
    int maxLimit() default 10000;

    /**
     * 是否是写入搜索数据
     * 例如：writeData = true
     * 注意：需要先实现高级查询功能
     */
    boolean writeData() default false;

    /**
     * 跳过版本检查
     * 例如：skipVersion = true
     */
    boolean skipVersion() default false;
}