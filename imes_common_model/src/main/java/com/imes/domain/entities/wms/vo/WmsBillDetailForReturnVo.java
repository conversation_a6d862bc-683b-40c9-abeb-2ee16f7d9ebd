package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsBillDetailForReturnVo {	
	
	
	@ApiModelProperty("id")	
    private String id;	
    /**	
     * 入库单号	
     */	
	@ApiModelProperty("入库单号")	
    private String inboundCode;	
	
    /**	
     * 入库明细单号	
     */	
	@ApiModelProperty("入库明细单号")	
    private String inboundItemCode;	
	
    /**	
     * 入库类型	
     */	
	@ApiModelProperty("入库类型")	
    private Integer receiptType;	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
    /**	
     * 入库数量	
     */	
	@ApiModelProperty("入库数量")	
    private BigDecimal packInStorageQty;	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
    /**	
     * 已退货单位	
     */	
	@ApiModelProperty("已退货单位")	
    private String returnPackUnit;	
    /**	
     * 已退货数量	
     */	
	@ApiModelProperty("已退货数量")	
    private BigDecimal returnPackQty;	
    /**	
     * 库存包装数量	
     */	
	@ApiModelProperty("库存包装数量")	
    private BigDecimal packAvailableQty;	
    /**	
     * 库存基本数量	
     */	
	@ApiModelProperty("库存基本数量")	
    private BigDecimal availableQty;	
    /**	
     * 库存基本单位	
     */	
	@ApiModelProperty("库存基本单位")	
    private String unit;	
	
    /**	
     * 入库日期	
     */	
	@ApiModelProperty("入库日期")	
    private String approveOn;	
	
    /**	
     * 可退货数量	
     */	
	@ApiModelProperty("可退货数量")	
    private BigDecimal allowReturnNum;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    private BigDecimal qty;	
	
    private String supplierCode;	
	
    private String supplierName;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Integer carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Integer precisionDigit;	
	
    /**	
     * 入库单价	
     */	
	@ApiModelProperty("入库单价")	
    private BigDecimal unitPrice;	
	
	
}	
