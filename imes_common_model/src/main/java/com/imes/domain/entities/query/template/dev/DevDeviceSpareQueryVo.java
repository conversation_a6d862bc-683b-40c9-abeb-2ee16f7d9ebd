package com.imes.domain.entities.query.template.dev;


import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.MsgAction;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "设备备件高级查询模型")
@Data
@QueryModel(
        name = "02272",
        remark = "设备备件",
        searchApi = "/dev/devSpares/findAll",
        msgAction = {MsgAction.SCHEDULED_TASK}
)
public class DevDeviceSpareQueryVo extends BaseModel {



    @ApiModelProperty(value = "设备编码")
    @QueryField(name = "设备编码", alias = "spare.dev_code")
    private String devCode;

    @ApiModelProperty(value = "设备名称")
    @QueryField(name = "设备名称", alias = "dev.dev_name")
    private String devName;

    @ApiModelProperty(value = "备件编码")
    @QueryField(name = "备件编码", alias = "spare.material_code")
    private String materialCode;

    @ApiModelProperty(value = "备件名称")
    @QueryField(name = "备件名称", alias = "material.material_name")
    private String materialName;


    @ApiModelProperty(value = "安装位置")
    @QueryField(name = "安装位置", alias = "install.install_site")
    private String installSite;

}
