package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (ProProjectEmployee)实体类
 *
 * <AUTHOR>
 * @since 2020-12-04 17:24:35
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_employee",resultMap = "BaseResultMap")
public class ProjectEmployee implements Serializable {
    private static final long serialVersionUID = 261979548401455399L;
    /**
    * id
    */
    private String id;
    /**
    * 项目id
    */
    private String projectId;
    /**
    * 参与人员工号
    */
    private String employeeCode;
    /**
    * 参与人员姓名
    */
    @TableField(condition = SqlCondition.LIKE)
    private String employeeName;
    /**
    * 加入时间
    */
    @TableField(fill = FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime joinTime;
    /**
    * 角色
    */
    private String role;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
    * 逻辑删除
    */
    private Boolean deleted;
    /**
    * 乐观锁
    */
    private Long version;

}