package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class JcWmsPackingList implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 装箱单号	
     */	
	@ApiModelProperty("装箱单号")	
    private String packingNo;	
	
    /**	
     * 单据类型（856）	
     */	
	@ApiModelProperty("单据类型（856）")	
    private String orderType;	
	
    /**	
     * 单据编码	
     */	
	@ApiModelProperty("单据编码")	
    private String orderCode;	
	
    /**	
     * 明细行号	
     */	
	@ApiModelProperty("明细行号")	
    private Integer lineNo;	
	
    /**	
     * 发货号	
     */	
	@ApiModelProperty("发货号")	
    private String deliveryCode;	
	
    /**	
     * 发货日期	
     */	
	@ApiModelProperty("发货日期")	
    private Date deliveryDate;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料条码	
     */	
	@ApiModelProperty("物料条码")	
    private String barCode;	
	
    /**	
     * 产品描述	
     */	
	@ApiModelProperty("产品描述")	
    private String materialDesc;	
	
    /**	
     * 供应商物料编码	
     */	
	@ApiModelProperty("供应商物料编码")	
    private String supplierMaterialCode;	
	
    /**	
     * wms采购单表订单号	
     */	
	@ApiModelProperty("wms采购单表订单号")	
    private String poCode;	
	
    /**	
     * wms采购单明细行号	
     */	
	@ApiModelProperty("wms采购单明细行号")	
    private String poItemCode;	
	
    /**	
     * wms采购单订单日期	
     */	
	@ApiModelProperty("wms采购单订单日期")	
    private Date poOrderDate;	
	
    /**	
     * 发货数量	
     */	
	@ApiModelProperty("发货数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 实际到货数量	
     */	
	@ApiModelProperty("实际到货数量")	
    private BigDecimal actualQty;	
	
    /**	
     * 单位	
     */	
	@ApiModelProperty("单位")	
    private String unit;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 纸箱数量	
     */	
	@ApiModelProperty("纸箱数量")	
    private BigDecimal packNum;	
	
    /**	
     * 纸箱类型	
     */	
	@ApiModelProperty("纸箱类型")	
    private String packType;	
	
    /**	
     * 毛重值	
     */	
	@ApiModelProperty("毛重值")	
    private BigDecimal roughWeightValue;	
	
    /**	
     * 毛重单位	
     */	
	@ApiModelProperty("毛重单位")	
    private String roughWeightUnit;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 已到货数量	
     */	
	@ApiModelProperty("已到货数量")	
    private BigDecimal arriveQty;	
	
    /**	
     * 新阳guId	
     */	
	@ApiModelProperty("新阳guId")	
    private String guId;	
	
    private static final long serialVersionUID = 1L;	
}	
