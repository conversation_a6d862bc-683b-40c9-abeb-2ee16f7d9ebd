package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsAllotOrderApiVo {	
	
	
    /**	
     * 关联主单号----必填项	
     */	
    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 调出仓库编码----必填项	
     */	
    @ApiModelProperty("调出仓库编码")	
    private String fromWhCode;	
	
    /**	
     * 调入仓库编码----必填项	
     */	
    @ApiModelProperty("调入仓库编码")	
    private String toWhCode;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 库存变更策略--1:变更调出和调入库存；2:变更调出库存；3:变更调入库存	
     */	
    @ApiModelProperty("库存变更策略")	
    private String stockStrategy;	
	
    /**	
     * 调拨明细单	
     */	
    @ApiModelProperty("调拨明细单")	
    private List<C_WmsAllotItemApiVo> items;	
	
    @ApiModelProperty("创建人")	
    private String createBy;	
	
    @ApiModelProperty("亲亲食品移动类型定制字段")	
    private String googMoveType;	
	
}	
