package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsAvailableStockVo {	
	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    @ApiModelProperty("可用库存数量")	
    private BigDecimal availableQty;	
}	
