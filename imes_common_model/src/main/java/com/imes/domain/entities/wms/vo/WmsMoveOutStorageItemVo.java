package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsBusTaskHistory;	
import com.imes.domain.entities.wms.WmsStockBin;	
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMoveOutStorageItemVo {	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 需出库数量	
     */	
	@ApiModelProperty("需出库数量")	
    private BigDecimal needQty;	
	
    /**	
     * 本次已出库数量	
     */	
	@ApiModelProperty("本次已出库数量")	
    private BigDecimal pickedQty;	
	
    /**	
     * 本单总共出库数量	
     */	
	@ApiModelProperty("本单总共出库数量")	
    private BigDecimal pickedOrderQty;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 库区编码	
     */	
	@ApiModelProperty("库区编码")	
    private String areaCode;	
	
    /**	
     * 库区名称	
     */	
	@ApiModelProperty("库区名称")	
    private String areaName;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 库位名称	
     */	
	@ApiModelProperty("库位名称")	
    private String binName;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 库存数量	
     */	
	@ApiModelProperty("库存数量")	
    private BigDecimal stockQty;	
	
    /**	
     * 是否开启批次管理	
     */	
	@ApiModelProperty("是否开启批次管理")	
    private String isBatch;	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String applyDepartCode;	
	
    /**	
     * 申请部门名称	
     */	
	@ApiModelProperty("申请部门名称")	
    private String applyDepartName;	
	
    /**	
     * 申请人工号	
     */	
	@ApiModelProperty("申请人工号")	
    private String applyUserCode;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;

    @ApiModelProperty("申请日期")
    private Date applyDate;
	
    /**	
     * 领用人工号	
     */	
	@ApiModelProperty("领用人工号")	
    private String pickUserCode;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 领用部门编码	
     */	
	@ApiModelProperty("领用部门编码")	
    private String pickDepartCode;	
	
    /**	
     * 领用部门名称	
     */	
	@ApiModelProperty("领用部门名称")	
    private String pickDepartName;	
	
    @ApiModelProperty("关联明细单单号")	
    private String receiptItemCode;	
	
    /**	
     * 出库单编码	
     */	
	@ApiModelProperty("出库单编码")	
    private String storageOutCode;	
	
    private String saleCode;	
	
    /**	
     * 出库明细单编号	
     */	
	@ApiModelProperty("出库明细单编号")	
    private String storageOutItemCode;	
	
    private BigDecimal packInventoryQty;	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    private String thirdItemCode;	
	
    private List<String> batchList;	
	
    private List<WmsStockBin> binList;	
	
    private List<WmsBusTaskHistory> historiesList;	
	
    private String packCodeUnit;	
	
    private BigDecimal packOnhandQty;	
	
    private BigDecimal packAvailableQty;	
	
    private String unit;	
	
    private String isFixedBatch;	
	
    private String isFixedWh;	
	
    private String skuCode;	
	
    private String boxNo;	
	
    private String  isSku;	
	
    private String newBoxNo;	
	
    @ApiModelProperty("是否启用负库存")	
    private String isEnableMinusInventory;	

    @ApiModelProperty("关联单据编码")
    private String receiptCode;

    private List<WmsPackCodeUnitAnUnitVo> anUnitVos;	
	
	
}	
