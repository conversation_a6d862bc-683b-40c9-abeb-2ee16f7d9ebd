package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目计划预览>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-22 10:56
 */
@ApiModel("项目")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlanPreviewVo {
    private List<LocalDate> months;
    private String projectId;
}
