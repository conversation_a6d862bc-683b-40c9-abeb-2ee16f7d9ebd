package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.persistence.Transient;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsParts implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 备件序列号	
     */	
	@ApiModelProperty("备件序列号")	
    private String serialNo;	
	
    /**	
     * 备件类型（1：新品，2：维修品）	
     */	
	@ApiModelProperty("备件类型（1：新品，2：维修品）")	
    private Integer type;	
	
    private String whCode;	
	
    private String whName;	
	
    /**	
     * 仓位	
     */	
	@ApiModelProperty("仓位")	
    private String binCode;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 开封状态（0：未开封，1：已开封）	
     */	
	@ApiModelProperty("开封状态（0：未开封，1：已开封）")	
    private Integer openedStatus;	
	
    /**	
     * 在库状态（2：已入库；4：已出库）	
     */	
	@ApiModelProperty("在库状态（2：已入库；4：已出库）")	
    private Integer stockStatus;	
	
    /**	
     * 入库上架时间	
     */	
	@ApiModelProperty("入库上架时间")	
    private Date putAwayTime;	
	
    /**	
     * 出库时间	
     */	
	@ApiModelProperty("出库时间")	
    private Date pickTime;	
	
    /**	
     * 出库次数	
     */	
	@ApiModelProperty("出库次数")	
    private Integer pickCount;	
	
    /**	
     * 质保到期时间	
     */	
	@ApiModelProperty("质保到期时间")	
    private Date periodTime;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private BigDecimal availableQty;	
	
    /**	
     * 物料辅助属性	
     */	
	@ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     *  单位	
     */	
	@ApiModelProperty(" 单位")	
    private String unit;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    @Transient	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    @Transient	
    private String materialMarker;	
	
    private static final long serialVersionUID = 1L;	
}	
