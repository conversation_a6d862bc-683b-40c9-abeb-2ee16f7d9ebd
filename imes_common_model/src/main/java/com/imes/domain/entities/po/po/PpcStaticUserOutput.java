package com.imes.domain.entities.po.po;

import java.io.Serializable;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 人均产量静态数据(PpcStaticUserOutput)实体类
 *
 * <AUTHOR>
 * @since 2025-03-05 12:37:19
 */
@Data
public class PpcStaticUserOutput implements Serializable {
    private static final long serialVersionUID = 377914049331454213L;

    private String id;

    @ApiModelProperty(value = "年份")
    private Integer year;

    @ApiModelProperty(value = "月份")
    private Integer month;

    @ApiModelProperty(value = "总产出")
    private BigDecimal outputSum;

    @ApiModelProperty(value = "人数")
    private BigDecimal userAll;

    @ApiModelProperty(value = "人均产出")
    private BigDecimal outputUser;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

}

