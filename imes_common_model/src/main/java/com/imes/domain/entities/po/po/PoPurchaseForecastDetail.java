package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "采购需求预测")
public class PoPurchaseForecastDetail implements Serializable {

    private static final long serialVersionUID = 306875058604900473L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    @ApiModelProperty(value = "需求日期")
    private Date demandDate;

    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQty;

    @ApiModelProperty(value = "回复数量")
    private BigDecimal replyQty;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    private int version;
}