package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目经理项目数据详情>>
 * @company 捷创智能技术有限公司
 * @create 2021-07-14 10:47
 */
@ApiModel("项目经理项目柱状图数据")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManagerEchartsVo {
    @ApiModelProperty("工号")
    private String userCode;
    @ApiModelProperty("姓名")
    private String userName;
    @ApiModelProperty("项目数")
    private Integer sum;
    @ApiModelProperty("总金额")
    private BigDecimal totalMoney;
    @ApiModelProperty("在执行数")
    private Integer exec;
    @ApiModelProperty("未开始数")
    private Integer notStar;
}
