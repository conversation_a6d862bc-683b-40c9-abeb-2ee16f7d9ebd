package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.ppc.po.Promaterial;
import com.imes.domain.entities.wms.WmsStockBin;
import com.imes.domain.entities.wms.WmsStockHistory;
import com.imes.domain.entities.wms.WmsStorageInBill;
import com.imes.domain.entities.wms.WmsStorageOutBill;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockBinVo extends WmsStockBin {	
	
    private static final long serialVersionUID = 1L;	
	
    /**	
     * 物料条码	
     */	
	@ApiModelProperty("物料条码")	
    private String barCode;	
	
    /**	
     * 本次减少库存数量	
     */	
	@ApiModelProperty("本次减少库存数量")	
    private BigDecimal theInventoryQty;	
	
    /**	
     * 上架时间	
     */	
	@ApiModelProperty("上架时间")	
    private Date putAwayDate;	
	
    /**	
     * 生产日期	
     */	
//    private Date productionDate;
	
    /**	
     * 失效日期	
     */	
//    private Date failureDate;
	
    /**	
     * 质保期	
     */	
	@ApiModelProperty("质保期")	
    private BigDecimal qcPeriod;	
	
    /**	
     * 逾期状态（1、未逾期;2、已逾期）	
     */	
	@ApiModelProperty("逾期状态（1、未逾期;2、已逾期）")	
    private Integer dueState;	
	
    /**	
     * 相差多少天	
     */	
	@ApiModelProperty("相差多少天")	
    private Integer differ;	
	
    /**	
     * 采购合同号	
     */	
	@ApiModelProperty("采购合同号")	
    private String poCode;	
	
    /**	
     * 销售合同号	
     */	
	@ApiModelProperty("销售合同号")	
    private String soCode;	
	
    /**	
     * 库存类型	
     */	
	@ApiModelProperty("库存类型")	
    private String stockType;	
	
    /**	
     * 关联单号（采购合同号、销售合同号、生产单号、领料单号、退货单号。。。。。。）	
     */	
	@ApiModelProperty("关联单号（采购合同号、销售合同号、生产单号、领料单号、退货单号。。。。。。）")	
    private String relatedOrderCode;	
	
    /**	
     * 关联单据类型	
     */	
	@ApiModelProperty("关联单据类型")	
    private String relatedOrderType;	
	
    /**	
     * 入库单主表	
     */	
	@ApiModelProperty("入库单主表")	
    private WmsStorageInBill inbound;	
	
    /**	
     * 出库单主表	
     */	
	@ApiModelProperty("出库单主表")	
    private WmsStorageOutBill outbound;	
	
    /**	
     * 物料	
     */	
	@ApiModelProperty("物料")	
    private Promaterial promaterial;	
	
    /**	
     * 订单数量	
     */	
	@ApiModelProperty("订单数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 库存单价	
     */	
	@ApiModelProperty("库存单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 所属公司编码	
     */	
	@ApiModelProperty("所属公司编码")	
    private String companyCode;	
	
    /**	
     * 所属公司名称	
     */	
	@ApiModelProperty("所属公司名称")	
    private String companyName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 质保期单位	
     */	
	@ApiModelProperty("质保期单位")	
    private String qualityPeriodUnit;	
	
    /**	
     * 仓库类型	
     */	
	@ApiModelProperty("仓库类型")	
    private String warehouseType;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 库区名称	
     */	
	@ApiModelProperty("库区名称")	
    private String areaName;	
	
    /**	
     * 库位名称	
     */	
	@ApiModelProperty("库位名称")	
    private String binName;	
	
    /**	
     * 报废包装数量	
     */	
	@ApiModelProperty("报废包装数量")	
    private BigDecimal wastPackQty;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String warehouseName;	
	
    @ApiModelProperty(value = "定批未出库数量")	
    private BigDecimal packNotOutQty;	
    //可分配数量	
    private BigDecimal allocableQty;	
	
    private String materialTypeCode;	
	
    private String isSerial;	
	
    /**	
     * 质保到期天数	
     */	
	@ApiModelProperty("质保到期天数")	
    private String diffDay;	
	
    private String custom;	
	
    /**	
     * 是否启用SN码	
     */	
	@ApiModelProperty("是否启用SN码")	
    private int autoGenerateSerialNum;	
	
	
    private int packAvailableQtyPrecision;	
	
    private int packOnhandQtyQtyPrecision;

    @ApiModelProperty("基本单位名称")
    private String unitName;

    @ApiModelProperty("库存单位名称")
    private String packCodeUnitName;

    /**
     * 时间戳
     */
    @ApiModelProperty("时间戳")
    private String oldTimeStamp;


    private WmsStockHistory stockHistory;

}	
