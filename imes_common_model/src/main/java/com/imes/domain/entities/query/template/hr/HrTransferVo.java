package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("员工调岗")
@QueryModel(name = "0669", remark = "员工调岗", alias = "hr_transfer", searchApi = "/api/hr/transfer/query")	
    public class HrTransferVo extends BaseModel {	
    /**	
     * 用户编码	
     */	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码")	
    private String userCode;	
    /**	
     * 调出部门	
     */	
	@ApiModelProperty("调出部门")	
    @QueryField(name = "调出部门")	
    private String calloutDept;	
    /**	
     * 调入部门	
     */	
	@ApiModelProperty("调入部门")	
    @QueryField(name = "调入部门")	
    private String callinDept;	
    /**	
     * 调出岗位	
     */	
	@ApiModelProperty("调出岗位")	
    @QueryField(name = "调出岗位")	
    private String calloutJobs;	
    /**	
     * 调入岗位	
     */	
	@ApiModelProperty("调入岗位")	
    @QueryField(name = "调入岗位")	
    private String callinJobs;	
    /**	
     * 生效时间	
     */	
	@ApiModelProperty("生效时间")	
    @QueryField(name = "生效时间", type = Type.Date)	
    private String effectTime;	
	
	
}	
