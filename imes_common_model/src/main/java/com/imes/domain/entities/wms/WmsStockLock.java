package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockLock implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 库存主表主键	
     */	
	@ApiModelProperty("库存主表主键")	
    private String stockId;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 锁定类型（销售预留、领料预留）	
     */	
	@ApiModelProperty("锁定类型（销售预留、领料预留）")	
    private String lockType;	
	
    /**	
     * 关联单据类型(销售单、领料单)	
     */	
	@ApiModelProperty("关联单据类型(销售单、领料单)")	
    private String receiptType;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 预留库存	
     */	
	@ApiModelProperty("预留库存")	
    private BigDecimal qty;	
	
    /**	
     * 借出库存	
     */	
	@ApiModelProperty("借出库存")	
    private BigDecimal borrowQty;	
	
    /**	
     * 主单位	
     */	
	@ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 包装数量	
     */	
	@ApiModelProperty("包装数量")	
    private BigDecimal packQty;	
	
    /**	
     * 包装单位	
     */	
	@ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 预留周期（天）	
     */	
	@ApiModelProperty("预留周期（天）")	
    private BigDecimal period;	
	
    /**	
     * 开始时间	
     */	
	@ApiModelProperty("开始时间")	
    private Date startTime;	
	
    /**	
     * 截止时间	
     */	
	@ApiModelProperty("截止时间")	
    private Date endTime;	
	
    /**	
     * 优先级	
     */	
	@ApiModelProperty("优先级")	
    private Integer priority;	
	
    /**	
     * 部门编码	
     */	
	@ApiModelProperty("部门编码")	
    private String departCode;	
	
    /**	
     * 部门名称	
     */	
	@ApiModelProperty("部门名称")	
    private String departName;	
	
    /**	
     * 单据状态	
     */	
	@ApiModelProperty("单据状态")	
    private String orderStatus;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 仓库类型	
     */	
	@ApiModelProperty("仓库类型")	
    private String warehouseType;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 库位名称	
     */	
	@ApiModelProperty("库位名称")	
    private String binName;	
	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    private BigDecimal availableQty;	
	
    private BigDecimal onhandQty;	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    private List<WmsStockLock> lockList;	
    private static final long serialVersionUID = 1L;	
}	
