package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("采购订单(变更单反选)")	
@QueryModel(	
        name = "1195-forBg",	
        remark = "采购订单(变更单反选)",	
        searchApi = "/api/po/poPurchaseStandard/queryList",	
        alias = {"po_purchase_standard", "po_purchase_detail_standard"},	
        customBind = "1195"	
)	
	
public class PoPurchaseStandardSearchVoForBg extends PoPurchaseStandardQueryVo {	
	
	@ApiModelProperty("主单单据状态")	
    @QueryField(name = "主单单据状态", alias = "po_purchase_standard", type = Type.MultiSelect, dictOption = "SALE_BILL_CODE", value = "30", query = false, required = true)	
    private String status;	
	
	@ApiModelProperty("主单业务状态")	
    @QueryField(name = "主单业务状态", alias = "po_purchase_standard", type = Type.MultiSelect, dictOption = "SALE_BUSINESS_CODE", value = "10", query = false, required = true)	
    private String businessStatus;	
	
	@ApiModelProperty("子订单业务状态")	
    @QueryField(name = "子订单业务状态", type = Type.MultiSelect, alias = "po_purchase_detail_standard.business_status", dictOption = "SALE_BUSINESS_CODE", value = "10", query = false, required = true)	
    private String detailBusinessStatus;	
	
}	
