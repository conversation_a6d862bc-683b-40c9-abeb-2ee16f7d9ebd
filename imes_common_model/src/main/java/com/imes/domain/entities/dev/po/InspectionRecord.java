/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.domain.entities.dev.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@ApiModel(value = "巡检任务表")
@Data
public class InspectionRecord implements Serializable {

	private static final long serialVersionUID = -8122575632964821001L;

	@Id
	private String id;
	@ApiModelProperty(value = "实际开始时间", dataType = "Date")
	private java.util.Date startTime;

	@ApiModelProperty(value = "实际结束时间")
	private java.util.Date endTime;


	@ApiModelProperty(value = "花费时间")
	private java.util.Date tokeTime;

	@ApiModelProperty(value = "执行人编码")
	private String executor;
	@ApiModelProperty(value = "执行人名称")
	private String executorName;

	@ApiModelProperty(value = "状态：10-执行中；20-已完成；30-强制完成", dataType = "Integer")
	private Integer status;
	/**
	 * 是否超期
	 * */
	@ApiModelProperty(value = "是否超期；0-正常，1-超期", dataType = "Integer")
	private Integer overTime;
	@ApiModelProperty(value = "备注")
	private String remarks;
	@ApiModelProperty(value = "创建人")
	private String createdBy;
	@ApiModelProperty(value = "创建时间")
	private Date createdOn;
	@ApiModelProperty(value = "更新时间")
	private Date updatedOn;
	@ApiModelProperty(value = "更新人")
	private String updatedBy;

	@ApiModelProperty(value = "是否以确认(0、未确认 1、已确认)", dataType = "Integer")
	private Integer confirmed;

	@ApiModelProperty(value = "计划名称", dataType = "String")
	private String planName;

	@ApiModelProperty(value = "巡检计划")
	private InspectionPlan plan;

	@ApiModelProperty(value = "计划id", dataType = "String")
	private String planId;

	/**
	 * 计划开始时间
	 */
	@ApiModelProperty(value = "计划开始时间", dataType = "Date")
	private Date planStartTime;

	/**
	 * 计划结束时间
	 */
	@ApiModelProperty(value = "计划结束时间", dataType = "Date")
	private Date planEndTime;

	/**
	 * 巡检单号
	 */
	@ApiModelProperty(value = "巡检单号", dataType = "String")
	private String inspectionRecordNo;

	/**
	 * 确认人
	 */
	@ApiModelProperty(value = "确认人编码", dataType = "String")
	private String confirmUser;

	/**
	 * 确认人姓名
	 */
	@ApiModelProperty(value = "确认人姓名", dataType = "String")
	private String confirmUsername;

	/**
	 * 确认时间
	 */
	@ApiModelProperty(value = "确认时间", dataType = "Date")
	private Date confirmTime;

	/**
	 * 巡检结果
	 */
	@ApiModelProperty(value = "结果：1-无异常；2-有异常", dataType = "Integer")
	private Integer result;

	/**
	 * 是否已全部上报
	 */
	@ApiModelProperty(value = "报修状态：0-未全部报修；1-已全部报修", dataType = "Integer")
	private Integer isReport;

	/**
	 * 是否推送消息 1已推送 0未推送
	 */
	@ApiModelProperty(value = "是否推送消息：0-未推送；1-已推送", dataType = "Integer")
	private Integer pushSign;

	/**
	 * 部门
	 */
	@ApiModelProperty(value = "部门编码", dataType = "String")
	private String departCode;

	@ApiModelProperty(value = "部门名称", dataType = "String")
	private String departName;

	/**
	 * 是否已认领  1已认领 0未认领
	 */
	@ApiModelProperty(value = "是否已认领  1已认领 0未认领", dataType = "Integer")
	private Integer claimSign;


	/**
	 * 超期原因
	 */
	@ApiModelProperty(value = "超期原因")
	private String overReason;

	/**
	 * 多个执行人  用于in查询
	 */
	@ApiModelProperty(value = "执行人集合")
	@Transient
	private List<String> executors;

	/**
	 * 多个状态  用于in查询
	 */
	@ApiModelProperty(value = "状态集合")
	@Transient
	private List<Integer> statusList;

	/**
	 * 指派的部门下所有人员
	 */
	@ApiModelProperty(value = "部门下的人员集合")
	@Transient
	private List<Object> inspectionNames;

	@ApiModelProperty(value = "设备集合")
	@Transient
	private List<DevInspectionRecordDev> recordDevList;

	/**
	 * 辅助执行人
	 * */
	@ApiModelProperty(value = "辅助执行人")
	@Transient
	private String minorName;

	/**
	 * 任务模式
	 * */
	@ApiModelProperty(value = "任务模式；0-认领；1-派发", dataType = "String")
	@Transient
	private String taskModel;

	/**
	 * 时限
	 * */
	@ApiModelProperty(value = "时限")
	@Transient
	private Long limitTime;

	/**
	 * 现场情况
	 * */
	@ApiModelProperty(value = "是否需要现场数据0. 否，1是" , dataType = "Integer")
	@Transient
	private Integer isRealtimeData;


	/**
	 * 是否上传图片
	 * */
	@ApiModelProperty(value = "是否上传图片")
	@Transient
	private Integer isPhoto;



}
