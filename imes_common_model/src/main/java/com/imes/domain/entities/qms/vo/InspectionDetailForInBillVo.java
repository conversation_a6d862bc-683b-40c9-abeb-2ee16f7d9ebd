package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.InspectionItemDetail;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class InspectionDetailForInBillVo extends InspectionItemDetail {	
    //选中标识，1为选中，2为未选中	
	@ApiModelProperty("选中标识，1为选中，2为未选中")	
    public String checkIdentity;	
}	
