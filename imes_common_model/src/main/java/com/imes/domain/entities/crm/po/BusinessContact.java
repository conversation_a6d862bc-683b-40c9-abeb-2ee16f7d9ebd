package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * (CrmBusinessCustomer)实体类
 *
 * <AUTHOR>
 * @since 2022-02-28 15:57:56
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_business_contact", resultMap = "BaseResultMap")
public class BusinessContact implements Serializable {
    private static final long serialVersionUID = 685196028063915500L;
    /**
    * id
    */
    private String id;
    /**
    * 商机id
    */
    private String businessId;
    /**
    * 联系人id
    */
    private String contactId;
    /**
     * 客户名称
     */
    @TableField(exist = false)
    private String customerName;
    /**
     * 联系人姓名
     */
    @TableField(exist = false)
    private String contactName;
    /**
     * 联系人手机
     */
    @TableField(exist = false)
    private String phone;
    /**
     * 联系人电话
     */
    @TableField(exist = false)
    private String telephone;
    /**
     * 联系人邮件
     */
    @TableField(exist = false)
    private String email;
    /**
    * 职位
    */
    private String position;
    /**
    * 备注
    */
    private String remark;
    /**
    * 加入时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime joinTime;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

}