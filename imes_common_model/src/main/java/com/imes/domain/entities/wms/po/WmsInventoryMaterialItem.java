package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
	
import com.baomidou.mybatisplus.annotation.TableField;	
import com.imes.domain.entities.wms.WmsInventoryTaskItem;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
import io.swagger.annotations.ApiModelProperty;	
	
/**	
 * (WmsInventoryMaterialItem)实体类	
 *	
 * <AUTHOR>	
 * @since 2024-03-06 10:25:37	
 */	
@Data	
public class WmsInventoryMaterialItem implements Serializable {	
    private static final long serialVersionUID = 663290408880969185L;	
	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty(value = "盘点单号")	
    private String inventoryCode;	
	
    @ApiModelProperty(value = "盘点明细单号")	
    private String inventoryMaterialItemCode;	
	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    @ApiModelProperty(value = "原库存数量")	
    private BigDecimal stockQty;	
	
    @ApiModelProperty(value = "盘点记录数量")	
    private BigDecimal existQty;	
	
    @ApiModelProperty(value = "单位")	
    private String unit;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    /**	
     * 复盘数量	
     */	
	@ApiModelProperty("复盘数量")	
    private BigDecimal replayQty;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 差异数量	
     */	
	@ApiModelProperty("差异数量")	
    @TableField(exist = false)	
    private BigDecimal diffQty;	
	
    @TableField(exist = false)	
    private String specification;	
	
    /**	
     * 盘点单明细	
     */	
	@ApiModelProperty("盘点单明细")	
    @TableField(exist = false)	
    private List<WmsInventoryTaskItem> items;	
}	
	
