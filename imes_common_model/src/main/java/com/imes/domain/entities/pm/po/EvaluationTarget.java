package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

/**
 * (PerEvaluationTarget)实体类
 *
 * <AUTHOR>
 * @since 2021-11-11 14:49:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_target",resultMap = "BaseResultMap")
public class EvaluationTarget implements Serializable {
    private static final long serialVersionUID = -40405603450224392L;
    /**
    * id
    */
    private String id;
    /**
    * 考评计划id
    */
    private String evaluationPlanId;
    /**
     * 考评指标
     */
    @TableField(exist = false)
    private List<EvaluationTargetItem> evaluationTargetItems;
    /**
    * 考评人工号
    */
    private String assessorCode;
    /**
     * 权重
     */
    private Integer weight;
    /**
     * 考评人姓名
     */
    @TableField(exist = false)
    private String assessorName;
    /**
     * 标题
     */
    private String title;
    /**
    * 考评类型（0：管理考评，1：项目考评）
    */
    private Integer type;
    /**
    * 项目id
    */
    private String projectId;
    /**
     * 方案id
     */
    private String schemeId;
    /**
     * 项目名称
     */
    @TableField(exist = false)
    private String projectName;
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
    * 创建人
    */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
    * 修改人
    */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
}