package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.StandardInspectionDetail;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class StandardInspectionSaveVo {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 产品名称	
     */	
	@ApiModelProperty("产品名称")	
    private String productName;	
	
    /**	
     * 版本号	
     */	
	@ApiModelProperty("版本号")	
    private String versionCode;	
	
    /**	
     * 检验文件	
     */	
	@ApiModelProperty("检验文件")	
    private String uploadFile;	
	
    /**	
     * 检验类型	
     */	
	@ApiModelProperty("检验类型")	
    private String inspectionType;	
	
    /**	
     * 检验项	
     */	
	@ApiModelProperty("检验项")	
    private String inspectionItems;	
	
    private List<StandardInspectionDetail> details;	
}	
