package com.imes.domain.entities.query.template.dev;		
		
import io.swagger.annotations.ApiModel;		
import com.baomidou.mybatisplus.annotation.TableField;		
import io.swagger.annotations.ApiModelProperty;		
import com.imes.domain.entities.query.model.base.QueryField;		
import com.imes.domain.entities.query.model.base.BaseModel;		
import com.imes.domain.entities.query.model.base.QueryModel;		
import lombok.Data;		
import lombok.experimental.Accessors;		
		
import java.util.Date;		
import java.util.List;		
		
@Data		
@Accessors(chain = true)		
@ApiModel("刀具寿命")		
@QueryModel(		
        name = "0716",		
        remark = "刀具寿命",		
        searchApi = "/api/dev/devDevice/knife/life/page",		
        alias = {"ppc_route_line", "ppc_route_process"		
                , "ppc_route_process_dev_step", "sys_ppc_line_process_device"		
                , "dev_device", "yuzhou_knife"})		
public class YuZhouKnifeLife extends BaseModel {		
		
	@ApiModelProperty("物料名")		
    @QueryField(name = "物料名",alias = "k")		
    private String materialName;		
	@ApiModelProperty("物料码")		
    @QueryField(name = "物料码",alias = "s")		
    private String materialCode;		
	@ApiModelProperty("物料规格")		
    @QueryField(name = "物料规格",alias = "k")		
    private String spec;		
	@ApiModelProperty("序列号")		
    @QueryField(name = "序列号",alias = "k")		
    private String sn;		
    private String ppNo;		
    // 产品名称		
    private String productName;		
    // 产品编码		
    private String productCode;		
    // 工序编码		
	@ApiModelProperty("工艺路线码")		
    @QueryField(name = "工艺路线码",alias = "p")		
    private String routeCode;		
    // 工序编码		
	@ApiModelProperty("工序码")		
    @QueryField(name = "工序码",alias = "p")		
    private String processCode;		
    private String processName;		
	@ApiModelProperty("工步号")		
    @QueryField(name = "工步号",alias = "s")		
    private String stepNo;		
    // 设备名称		
    private String devName;		
    private String devCode;		
    // 加工数量		
    private Integer num;		
    // 状态		
    private Integer status;		
    // 加工次数寿命，额定寿命		
	@ApiModelProperty("额定寿命")		
    @QueryField(name = "额定寿命",alias = "s")		
    private Integer life;		
    // 已用寿命		
	@ApiModelProperty("已用寿命")		
    @QueryField(name = "已用寿命",query = false)		
    private Integer usefulLife;		
    // 剩余寿命		
	@ApiModelProperty("剩余寿命")		
    @QueryField(name = "剩余寿命",query = false)		
    private Integer remainingLife;		
    // 剩余寿命状态		
	@ApiModelProperty("剩余寿命比率")		
    @QueryField(name = "剩余寿命比率",query = false)		
    private String remainingLifeRatio;		
    // 工序版本号		
    private String processVersion;		
    // plm工步号		
    private String stepContent;		
		
    // 工具字段		
    @TableField(exist = false)		
    private Date startDate;		
    @TableField(exist = false)		
    private Date endDate;		
    @TableField(exist = false)		
    private List<String> idList;		
    @TableField(exist = false)		
    private String statusEq;		
    @TableField(exist = false)		
    private List<String> statusIn;		
}		
