package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@ApiModel("人员项目里程碑工时")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilePostHoursVo {
    @ApiModelProperty("里程碑名称")
    private String milepostName;
    @ApiModelProperty("里程碑计划工时")
    private BigDecimal milepostPlannedWorkHours;
    @ApiModelProperty("里程碑实际工时")
    private BigDecimal milepostActualWorkHours;
}
