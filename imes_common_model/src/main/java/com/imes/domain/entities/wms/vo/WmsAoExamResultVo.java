package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsAoExamResultVo {	
	
    /**	
     * LIMS 化验任务类型	
     * 1:成品；2:生产过程样；3:采购到货；4:退货到货；5:复验；6:临期；7:抽检；8:发货；9:退料；10:成品复验	
     */	
    @ApiModelProperty(value = "LIMS 化验任务类型")	
    private String examiningCategory;	
	
    /**	
     * 1:采购到货；2:退货到货--必填项 (后续扩展可能还有销售出库化验)	
     */	
	@ApiModelProperty("1:采购到货；2:退货到货--必填项 (后续扩展可能还有销售出库化验)")	
    private String orderType;	
	
    /**	
     * 单号（到货单号）--必填项	
     */	
	@ApiModelProperty("单号（到货单号）--必填项")	
    private String orderCode;	
	
    /**	
     * 单号（到货明细单号）--必填项	
     */	
//    private String orderItemCode;
	
    /**	
     * 物料编码--必填项	
     */	
	@ApiModelProperty("物料编码--必填项")	
    private String materialCode;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 处理方式(10:正常；20:让步接收；30:退货；40:回融；50:销毁)--必填项	
     */	
	@ApiModelProperty("处理方式(10:正常；20:让步接收；30:退货；40:回融；50:销毁)--必填项")	
    private String handleResult;	
	
    /**	
     * 接收数量	
     */	
	@ApiModelProperty("接收数量")	
    private BigDecimal receiveQty;	
	
    /**	
     * 化验结果(1:合格；2:不合格)--必填项	
     */	
	@ApiModelProperty("化验结果(1:合格；2:不合格)--必填项")	
    private String examiningResult;	
	
    /**	
     * 化验单号	
     */	
	@ApiModelProperty("化验单号")	
    private String sampleRecordCode;	
	
    /**	
     * 操作入库人	
     */	
	@ApiModelProperty("操作入库人")	
    private String userCode;	
	
}	
