package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购对账差异")	
@QueryModel(	
        name = "1133-storageDetail",	
        remark = "采购对账差异",	
        alias = "a",	
        searchApi = "/api/po/poAccountStorageDetail/queryList")	
public class PoAccountStorageDetailQueryVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id",show = false)	
    private String id;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", order = OrderBy.DESC, show = false)	
    private String createOn;	
	
	@ApiModelProperty("入库类型")	
    @QueryField(name = "入库类型",show = false, type = Type.Select, option = {"in", "入库单", "return", "退库单"})	
    private String detailType;	
	
	@ApiModelProperty("采购单号")	
    @QueryField(name = "采购单号", order = OrderBy.DESC, required = true)	
    private String soNo;	
	
	@ApiModelProperty("采购单行号")	
    @QueryField(name = "采购单行号", order = OrderBy.ASC)	
    private String sdNo;	
	
	@ApiModelProperty("对账单号")	
    @QueryField(name = "对账单号",show = false)	
    private String verifyNo;	
	
//    @QueryField(name = "客户编码", alias = "b")	
//    private String customerCode;	
//	
//    @QueryField(name = "客户名称", alias = "b")	
//    private String customerName;	
//	
//    @QueryField(name = "币种", alias = "b", dictOption = "CUSTOMER_CURRENCY", type = Type.MultiSelect)	
//    private String currency;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称")	
    private String materialName;	
	
	@ApiModelProperty("物料规格型号")	
    @QueryField(name = "物料规格型号")	
    private String specification;	
	
	@ApiModelProperty("入库/退库单号")	
    @QueryField(name = "入库/退库单号")	
    private String storageNo;	
	
	@ApiModelProperty("关联单号")	
    @QueryField(name = "关联单号")	
    private String relationReceiveNo;	
	
	@ApiModelProperty("批次号")	
    @QueryField(name = "批次号")	
    private String cbatch;	
	
	@ApiModelProperty("到货时间")	
    @QueryField(name = "到货时间", type = Type.Date,format = "yyyy-MM-dd")	
    private String cbatchDate;	
	
	@ApiModelProperty("入库/退库时间")	
    @QueryField(name = "入库/退库时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String storageTime;	
	
	@ApiModelProperty("入库/退库数量")	
    @QueryField(name = "入库/退库数量")	
    private String allStorageNum;	
	
	@ApiModelProperty("本次对账数量")	
    @QueryField(name = "本次对账数量")	
    private String storageNum;	
	
	@ApiModelProperty("剩余数量")	
    @QueryField(name = "剩余数量")	
    private String syQty;	
	
	@ApiModelProperty("是否含税")	
    @QueryField(name = "是否含税", type = Type.MultiSelect, option = {"0", "是", "1", "否"})	
    private String includeTax;	
	
	@ApiModelProperty("取价类型")	
    @QueryField(name = "取价类型", type = Type.MultiSelect, option = {"1", "大宗物料", "0", "普通", "2", "锁铜"})	
    private String isLargeMaterial;	
	
	@ApiModelProperty("税率(%)")	
    @QueryField(name = "税率(%)", alias = ".(ifnull(TRUNCATE(a.tax_rate, 2), 0) * 100)")	
    private String taxRate;	
	
	@ApiModelProperty("未税单价")	
    @QueryField(name = "未税单价", type = Type.Number, format = "0.000000")	
    private String unIncludePrice;	
	
	@ApiModelProperty("含税单价")	
    @QueryField(name = "含税单价", type = Type.Number, format = "0.000000")	
    private String includePrice;	
	
	@ApiModelProperty("税额")	
    @QueryField(name = "税额", type = Type.Number, format = "0.00")	
    private String taxRatePrice;	
	
	@ApiModelProperty("未税金额")	
    @QueryField(name = "未税金额", type = Type.Number, format = "0.00")	
    private String unTaxRatePrice;	
	
	@ApiModelProperty("价税合计")	
    @QueryField(name = "价税合计", type = Type.Number, format = "0.00")	
    private String allPrice;	
	
	@ApiModelProperty("对账状态")	
    @QueryField(name = "对账状态", dictOption = "SCS_ACCOUNT_STATUS", type = Type.MultiSelect,show = false)	
    private String accountStatus;	
	
	@ApiModelProperty("开票状态")	
    @QueryField(name = "开票状态", dictOption = "SCS_ACCOUNT_INVOICE_STATUS", type = Type.MultiSelect, show = false)	
    private String invoiceStatus;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    private String remarks;	
	
    private String unit;	
	
    private String irdrowno;	
	
    private String icost;	
	
    private String imoney;	
	
    private String itaxprice;	
	
    private String isum;	
    //颜色	
    private String storageNumColor;	
}	
