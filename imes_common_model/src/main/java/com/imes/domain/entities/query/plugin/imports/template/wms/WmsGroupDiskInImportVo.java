package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(
        name = "组盘入库导入模板",
        modelName = "1351")
public class WmsGroupDiskInImportVo extends BaseModel {
    @ImportField(name = "仓库编码", required = true, remark = "必填\n参考【仓库信息】", maxLength = 255)
    private String whCode;

    @ImportField(name = "物料编码", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialCode;

    @ImportField(name = "辅助属性", maxLength = 255, remark = "参考【物料基本信息】")
    private String skuCode;

    @ImportField(name = "批次", maxLength = 255, remark = "参考【物料基本信息】")
    private String batch;

    @ImportField(name = "规格", maxLength = 255, remark = "参考【物料基本信息】")
    private String specification;

    @ImportField(name = "计划数量", required = true, isNumber = true, remark = "必填", minNumber = "1", maxNumber = "999999999.999999")
    private String packApplyQty;

    @ImportField(name = "供应商编码", maxLength = 255, remark = "参考【供应商管理】")
    private String supplierCode;

    @ImportField(name = "生产日期", maxLength = 255,date = "yyyy-MM-dd")
    private String productionDate;

    @ImportField(name = "备注", maxLength = 255, remark = "备注")
    private String remark;
}
