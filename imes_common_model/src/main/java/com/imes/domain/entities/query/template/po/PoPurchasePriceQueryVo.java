package com.imes.domain.entities.query.template.po;	
	
import com.imes.domain.entities.query.model.base.*;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
/**	
 * @author: 武新宇	
 * @version: v1.0	
 * @Package: com.imes.domain.entities.system.query.template	
 * @description:	
 * @date:2023/5/19 - 13:15	
 */	
@Data	
@QueryModel(	
        name = "1204",	
        remark = "采购价目",	
        searchApi = "/api/po/poPurchasePrice/queryList",	
        alias = {"po_purchase_price", "po_purchase_price_detail"},
        authTenant = true,
        resume = "priceNo",
        openapi = true,
        auth = Auth.ALL,
        showMode = true)	
@ApiModel(value = "采购价目表高级查询VO")	
public class PoPurchasePriceQueryVo extends BaseModel {	
	
    /**	
     * 主订单id	
     */	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
    /**	
     * 详情id	
     */	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", show = false, alias = "po_purchase_price_detail")	
    private String detailId;	
	
    /**	
     * 定价单号	
     */	
    @QueryField(name = "定价单号")	
    @ApiModelProperty(value = "定价单号")
    @EditField(readonly = true)
    private String priceNo;	
	
    /**	
     * 定价单名称	
     */	
    @QueryField(name = "定价单名称")	
    @ApiModelProperty(value = "定价单名称")
    @EditField(required = true)
    private String priceName;	
	
    /**	
     * 币种	
     */	
    @QueryField(name = "币种", type = Type.MultiSelect, sqlOption = "select ccy_no as value,ccy_name as label from sys_currency")	
    @ApiModelProperty(value = "币种(详见币种配置管理)")
    @EditField(required = true)
    private String currency;	
	
    /**	
     * 供应商编码	
     */	
    @QueryField(name = "供应商编码")	
    @ApiModelProperty(value = "供应商编码")
    @EditField(readonly = true)
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
    @QueryField(name = "供应商名称", alias = "sys_supplier",level = Level.Main)	
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
	
    /**	
     * 价目类型 1采购	
     */	
    @QueryField(name = "价目类型" , type = Type.Select, dictOption = "PO_PURCHASE_PRICE_TYPE")
    @ApiModelProperty(value = "价目类型(数据字典PO_PURCHASE_PRICE_TYPE)")
    @EditField(required = true)
    private String priceType;	
	
    /**	
     * 定价员工号	
     */	
    @QueryField(name = "定价员工号")	
    @ApiModelProperty(value = "定价员工号")
    @EditField(readonly = true)
    private String madeUserCode;	
	
    /**	
     * 定价员名称	
     */	
    @QueryField(name = "定价员名称", alias = "pe_user.user_name",level = Level.Main)	
    @ApiModelProperty(value = "定价员名称")
    @EditField(required = true)
    private String madeUserName;	
	
    /**	
     * 状态10录入20审核中30审核通过	
     */	
    @QueryField(name = "状态", type = Type.MultiSelect, option = {"10", "已录入", "20", "审核中", "30", "已审核"} )
    @ApiModelProperty(value = "状态(10-录入，20-审核中，30-审核通过)")
    @EditField(readonly = true)
    private String status;	
	
    /**	
     * 是否启用1启用0禁用	
     */	
    @QueryField(name = "是否启用", type = Type.MultiSelect, dictOption = "PO_PURCHASE_PRICE_ISENABLE")	
    @ApiModelProperty(value = "是否启用(数据字典PO_PURCHASE_PRICE_ISENABLE)")
    @EditField(readonly = true)
    private String isEnable;	
	
    /**	
     * 创建时间	
     */	
    @QueryField(name = "创建时间", order = OrderBy.DESC, type = Type.DateTime)
    @ApiModelProperty(value = "创建时间")
    @EditField(readonly = true)
    private String createOn;	
	
    /**	
     * 创建人	
     */	
    @QueryField(name = "创建人",sqlOption = "select user_name as label,user_code as value from pe_user",level = Level.Main,alias = ".(select user_name from pe_user where pe_user.user_code = po_purchase_price.create_by limit 1)")	
    @ApiModelProperty(value = "创建人")
    @EditField(show = false)
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
    @QueryField(name = "更新时间", type = Type.DateTime)
    @ApiModelProperty(value = "更新时间")
    @EditField(show = false)
    private String updateOn;	
	
    /**	
     * 更新人	
     */	
    @QueryField(name = "更新人")	
    @ApiModelProperty(value = "更新人")
    @EditField(show = false)
    private String updateBy;	
	
    /**	
     * 备注	
     */	
    @QueryField(name = "备注")	
    @ApiModelProperty(value = "备注")
    private String remarks;	
	
	
    /**	
     * 物料编码	
     */	
    @QueryField(name = "物料编码" ,alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "物料编码")
    @EditField(required = true)
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
    @QueryField(name = "物料名称" ,alias = "ppc_material")	
    @ApiModelProperty(value = "物料名称")
    @EditField(readonly = true)
    private String materialName;	
	
    /**	
     * 规格	
     */	
    @QueryField(name = "规格" ,alias = "ppc_material")	
    @ApiModelProperty(value = "规格")
    @EditField(readonly = true)
    private String specification;	
	
    /**	
     * 型号	
     */	
    @QueryField(name = "型号" ,alias = "ppc_material")	
    @ApiModelProperty(value = "型号")
    @EditField(readonly = true)
    private String materialMarker;	
	
    /**	
     * 单位	
     */	
    @QueryField(name = "单位" ,alias = "po_purchase_price_detail", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    @ApiModelProperty(value = "单位(详见标准单位管理)")
    @EditField(required = true)
    private String unit;	
	
    /**	
     * 单价	
     */	
    @QueryField(name = "单价" ,alias = "po_purchase_price_detail", type = Type.Number)	
    @ApiModelProperty(value = "单价")
    @EditField(required = true)
    private String singlePrice;	
	
    /**	
     * 是否含税 1是 0否	
     */	
    @QueryField(name = "是否含税" , type = Type.MultiSelect, option = {"0","是","1","否"},alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "是否含税(0-是，1-否)")
    @EditField(required = true)
    private String isIncludeTax;	
	
    /**	
     * 税率编码	
     */	
    @QueryField(name = "税率" ,alias = "po_purchase_price_detail", dictOption = "SALE_TAX_CODE", type = Type.MultiSelect)	
    @ApiModelProperty(value = "税率编码(数据字典SALE_TAX_CODE)")	
    private String taxCode;	
	
    /**	
     * 税率	
     */	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率" ,alias = "po_purchase_price_detail",show = false)	
    private String taxRate;	
	
    /**	
     * 未税单价	
     */	
    @QueryField(name = "不含税单价" ,alias = "po_purchase_price_detail", type = Type.Number)
    @ApiModelProperty(value = "不含税单价")
    private String unIncludePrice;	
	
    /**	
     * 含税单价	
     */	
    @QueryField(name = "含税单价" ,alias = "po_purchase_price_detail", type = Type.Number)	
    @ApiModelProperty(value = "含税单价")	
    private String includePrice;	
	
    /**	
     * 价格生效日期	
     */	
    @QueryField(name = "生效日期", type = Type.Date, format = "yyyy-MM-dd" ,alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "生效日期")
    @EditField(required = true)
    private String effectTime;	
	
    /**	
     * 价格失效日期	
     */	
    @QueryField(name = "失效日期", type = Type.Date, format = "yyyy-MM-dd" ,alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "失效日期")
    @EditField(required = true)
    private String uneffectTime;	
	
    /**	
     * 定价数量	
     */	
    @QueryField(name = "定价数量" ,alias = "po_purchase_price_detail", type = Type.Number)	
    @ApiModelProperty(value = "定价数量")	
    private String batchQty;	
	
    /**	
     * 数量阶梯从	
     */	
    @QueryField(name = "数量阶梯从" ,alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "数量阶梯从")	
    private String startQty;	
	
    /**	
     * 数量阶梯至	
     */	
    @QueryField(name = "数量阶梯至" ,alias = "po_purchase_price_detail")	
    @ApiModelProperty(value = "数量阶梯至")	
    private String endQty;	
	
    /**	
     * 子单备注	
     */	
    @QueryField(name = "子单备注" ,alias = "po_purchase_price_detail.remarks")	
    @ApiModelProperty(value = "子单备注")	
    private String detailRemarks;	
	
    /**	
     * 状态10录入20审核中30审核通过	
     */	
    @QueryField(name = "子单状态", type = Type.MultiSelect, option = {"10", "已录入", "20", "审核中", "30", "已审核"}, alias = "po_purchase_price_detail.status")
    @ApiModelProperty(value = "子单状态(10-录入,20-审核中,30-审核通过)")	
    private String detailStatus;	
	
    private Integer singlePricePrecision;	
	
}	
