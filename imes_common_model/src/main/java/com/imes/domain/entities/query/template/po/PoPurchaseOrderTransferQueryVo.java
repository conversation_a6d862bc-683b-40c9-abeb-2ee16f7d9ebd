package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
	
@Data	
@ApiModel("采购到货调入采购单（标准采购模块）")	
@QueryModel(	
        name = "1195-forwms",	
        remark = "采购到货调入采购单（标准采购模块）",	
        searchApi = "/po/poPurchaseDetailStandard/queryPoAndDetailList",	
        customBind = "1195",	
        exclude = {View.Export},	
        alias = {"po_purchase_standard", "po_purchase_detail_standard"}	
)	
public class PoPurchaseOrderTransferQueryVo extends PoPurchaseStandardQueryVo {	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "POPURCHASE_STATSU", value = "30", query = false, required = true)	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS", value = "10",query=false,required = true)	
    private String businessStatus;	
	
	@ApiModelProperty("行业务状态")	
    @QueryField(name = "行业务状态", type = Type.MultiSelect, alias = "po_purchase_detail_standard.business_status",value = "10",dictOption = "SALE_BUSINESS_CODE",query=false,required = true)	
    private String detailBusinessStatus;	
	
	@ApiModelProperty("到货数量")	
    @QueryField(name = "到货数量", alias = ".(IFNULL(po_purchase_detail_standard.arrive_qty, 0))")	
    private String arriveQty;	
	
	@ApiModelProperty("退货数量")	
    @QueryField(name = "退货数量", alias = ".(IFNULL(po_purchase_detail_standard.cancel_qty, 0))")	
    private String cancelQty;	
	
	@ApiModelProperty("退货补货数量")	
    @QueryField(name = "退货补货数量", alias = ".(ifnull(po_purchase_detail_standard.cancel_replenish_qty,0))")	
    private String cancelReplenishQty;	
}	
