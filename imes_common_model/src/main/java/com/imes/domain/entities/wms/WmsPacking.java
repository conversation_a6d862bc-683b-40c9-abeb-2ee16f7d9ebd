package com.imes.domain.entities.wms;	
	
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@ApiModel(value = "仓库拆装箱单主表")	
public class WmsPacking implements Serializable {	
	
    private static final long serialVersionUID = 738493768459969088L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "装箱单号")	
    private String packNo;	
	
    @ApiModelProperty(value = "单据类型（1:装箱单；2:拆箱单）")	
    private String orderType;	
	
    @ApiModelProperty(value = "来源单据类型（0:无；1:销售订单；2:发货单；3：采购到货单；4:销售退货到货单；5:生产领料申请；6:备料领料申请；7:备件领用申请）")	
    private String receiptType;	
	
    @ApiModelProperty(value = "关联的单据号")	
    private String receiptCode;	
	
    @ApiModelProperty(value = "客户编码")	
    private String customerCode;	
	
    @ApiModelProperty(value = "客户名称")	
    private String customerName;	
	
    @ApiModelProperty(value = "装箱单状态（10：已录入；20:已生效；50:已完成）")	
    private String orderStatus;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    @ApiModelProperty(value = "备注")	
    private String remarks;	
	
    @ApiModelProperty(value = "自定义字段")	
    private String custom;

    @ApiModelProperty(value = "自定义字段")
    @TableField(exist = false)
    private String userName;
	
    private List<WmsPackingItem> wmsPackingItems;	
	
    private List<WmsPackingBox> wmsPackingBoxes;	
	
    private List<WmsPackingBoxMaterial> bulkPackingBoxMaterials;	
}	
