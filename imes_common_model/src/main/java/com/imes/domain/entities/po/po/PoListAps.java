package com.imes.domain.entities.po.po;


import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class PoListAps {
    private String id;
    private String poNo;
    private Date flagDate;
    private String supplierCode;
    private String supplierName;
    private String sdNo;
    private String materialCode;
    private String materialName;
    /**
     * 需求数量
     */
    private BigDecimal purchaseQty;

    /**
     * 回复数量
     */
    private BigDecimal replyQty;

    /**
     * U8计划到货时间
     */
    private Date estimatedDeliveryDate;

    /**
     * 供应商回复最晚到货时间
     */
    private Date requestedDeliveryDate;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;
}
