package com.imes.domain.entities.pm.vo;

import com.imes.domain.entities.pm.enums.BugState;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel("解决方案")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SolutionVo {
    private String id;
    private BugState state;
    private String content;
    private List<String> fileIds;
}
