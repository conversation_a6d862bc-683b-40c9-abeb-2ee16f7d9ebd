package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * 人员考勤档案表(HrAttendanceUserRule)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-04-25 17:31:52	
 */	
@Data	
@ApiModel("人员考勤档案记录管理")	
@QueryModel(name = "0784",	
        remark = "人员考勤档案记录管理",	
        alias = "hr_attendance_user_rule",	
        searchApi = "/api/hr/attendance/rule/group/rule/query")	
public class HrAttendanceUserRuleQueryVo {	
	
	@ApiModelProperty("用户编码")	
    @QueryField(name = "用户编码" )	
    private String userCode;	
    /**	
     * 是否打卡上班	
     */	
	@ApiModelProperty("是否打卡上班")	
    @QueryField(name = "是否打卡上班" )	
    private String clockIn;	
    /**	
     * 自动排班	
     */	
	@ApiModelProperty("自动排班")	
    @QueryField(name = "自动排班" )	
    private String autoSetting;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date)	
    private String createOn;	
	
	@ApiModelProperty("日历配置名称")	
    @QueryField(name = "日历配置名称" )	
    private String holidayPlanName;	
	
	@ApiModelProperty("班次名称")	
    @QueryField(name = "班次名称" )	
    private String shiftName;	
	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称" )	
    private String userName;	
	
	
	
}	
	
