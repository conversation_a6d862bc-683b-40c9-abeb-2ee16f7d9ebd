package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.validation.constraints.NotBlank;	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsRouteInspectionDetailSeniorVo extends QmsStdInspectionDetailSeniorVo implements Serializable {	
    /**	
     *工艺路线编码	
     */	
    private String routeCode;	
	
    private static final long serialVersionUID = 1L;	
}	
