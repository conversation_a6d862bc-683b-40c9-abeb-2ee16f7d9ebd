package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("GC-OTD-年度表")	
@QueryModel(	
        name = "1308",	
        remark = "GC-OTD-年度表",	
        searchApi = "/api/po/poPurchase/queryPoOTDAnnualVo",	
        alias = "summary",	
        exclude = { View.Column, View.Template}	
)	
public class PoOTDAnnualVo extends BaseModel {	
	
	
	@ApiModelProperty("类型(1为订单，2为承诺)")	
    @QueryField(name = "类型(1为订单，2为承诺)", show = false, query = false)	
    private String type;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码", show = false)	
    private String supplierCode;	
	
	@ApiModelProperty("年")	
    @QueryField(name = "年", show = false)	
    private String year;	
	
	@ApiModelProperty("10月")	
    @QueryField(name = "10月" )	
    private String month1;	
	
	@ApiModelProperty("11月")	
    @QueryField(name = "11月" )	
    private String month2;	
	
	@ApiModelProperty("12月")	
    @QueryField(name = "12月" )	
    private String month3;	
	
	@ApiModelProperty("Q1平均值")	
    @QueryField(name = "Q1平均值" )	
    private String quarter1;	
	
	@ApiModelProperty("1月")	
    @QueryField(name = "1月" )	
    private String month4;	
	
	@ApiModelProperty("2月")	
    @QueryField(name = "2月" )	
    private String month5;	
	
	@ApiModelProperty("3月")	
    @QueryField(name = "3月" )	
    private String month6;	
	
	@ApiModelProperty("Q2平均值")	
    @QueryField(name = "Q2平均值" )	
    private String quarter2;	
	
	@ApiModelProperty("4月")	
    @QueryField(name = "4月" )	
    private String month7;	
	
	@ApiModelProperty("5月")	
    @QueryField(name = "5月" )	
    private String month8;	
	
	@ApiModelProperty("6月")	
    @QueryField(name = "6月" )	
    private String month9;	
	
	@ApiModelProperty("Q3平均值")	
    @QueryField(name = "Q3平均值" )	
    private String quarter3;	
	
	@ApiModelProperty("7月")	
    @QueryField(name = "7月" )	
    private String month10;	
	
	@ApiModelProperty("8月")	
    @QueryField(name = "8月" )	
    private String month11;	
	
	@ApiModelProperty("9月")	
    @QueryField(name = "9月" )	
    private String month12;	
	
	@ApiModelProperty("Q4平均值")	
    @QueryField(name = "Q4平均值" )	
    private String quarter4;	
	
	
}	
