package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsAllotItemApiVo {	
	
    /**	
     * 物料编码----必填项	
     */	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
    @ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 调拨数量----必填项	
     */	
    @ApiModelProperty("调拨数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 主单位----必填项	
     */	
    @ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 批次号	
     */	
    @ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 调出库位----必填项	
     */	
    @ApiModelProperty("调出库位")	
    private String outBinCode;	
	
    /**	
     * 调入库位----必填项	
     */	
    @ApiModelProperty("调入库位")	
    private String inBinCode;	
	
    @ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty("包装数量")	
    private BigDecimal packQty;	
	
    @ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 生产日期	
     */	
    @ApiModelProperty("生产日期")	
    private Date productionDate;	
    /**	
     * 到期时间	
     */	
    @ApiModelProperty("到期时间")	
    private Date failureDate;	
    /**	
     * 质保天数	
     */	
    @ApiModelProperty("质保天数")	
    private BigDecimal qcPeriod;	
	
    /**	
     * 供应商编码	
     */	
    @ApiModelProperty("供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	
    @ApiModelProperty("供应商名称")	
    private String supplierName;	
	
    @ApiModelProperty("亲亲食品货物移动类型")	
    private String moveType;	
	
}	
