package com.imes.domain.entities.qms.vo;

import com.imes.domain.entities.qms.po.QmsWorkException;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class QmsWorkInspectOrderStatusVo implements Serializable {
    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 	
     */	
	@ApiModelProperty("工单ID")
    private String orderId;
	
    /**	
     * 工序	
     */	
	@ApiModelProperty("是否在进行")
    private boolean isIng;
	

}	
