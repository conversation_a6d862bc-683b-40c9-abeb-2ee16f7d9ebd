package com.imes.domain.entities.query.template.dev;		
		
import io.swagger.annotations.ApiModel;		
import com.baomidou.mybatisplus.annotation.TableField;		
import io.swagger.annotations.ApiModelProperty;		
import com.baomidou.mybatisplus.annotation.TableId;		
import com.imes.domain.entities.query.model.base.BaseModel;		
import com.imes.domain.entities.query.model.base.QueryField;		
import com.imes.domain.entities.query.model.base.QueryModel;		
import lombok.Data;		
import lombok.experimental.Accessors;		
		
import javax.validation.constraints.NotNull;		
import java.math.BigDecimal;		
import java.util.Date;		
import java.util.List;		
		
@Data		
@Accessors(chain = true)		
@ApiModel("刀具使用记录")		
@QueryModel(		
        name = "0711",		
        remark = "刀具使用记录",		
        searchApi = "/api/dev/devDevice/installknife/page",		
        alias = {"yuzhou_install_knife"})		
public class YuZhouInstallKnife extends BaseModel {		
	@ApiModelProperty("id")		
    @QueryField(name = "id")		
    private String id;		
    @NotNull(message = "物料为空")		
	@ApiModelProperty("物料名")		
    @QueryField(name = "物料名")		
    private String materialName;		
    @NotNull(message = "物料为空")		
	@ApiModelProperty("物料编码")		
    @QueryField(name = "物料编码")		
    private String materialCode;		
    @NotNull(message = "规格为空")		
	@ApiModelProperty("规格")		
    @QueryField(name = "规格")		
    private String spec;		
    @NotNull(message = "序列号（SN）为空")		
	@ApiModelProperty("序列号")		
    @QueryField(name = "序列号")		
    private String sn;		
    @NotNull(message = "派工单号为空")		
	@ApiModelProperty("派工单号")		
    @QueryField(name = "派工单号")		
    private String woNo;		
    @NotNull(message = "工艺路线编码为空")		
	@ApiModelProperty("工艺路线编码")		
    @QueryField(name = "工艺路线编码")		
    private String routeCode;		
    // 产品名称		
    @NotNull(message = "产品名称为空")		
	@ApiModelProperty("产品名称")		
    @QueryField(name = "产品名称")		
    private String materialName2;		
    // 产品编码		
    @NotNull(message = "产品编码为空")		
	@ApiModelProperty("产品编码")		
    @QueryField(name = "产品编码")		
    private String materialCode2;		
    // 工序编码		
    @NotNull(message = "工序编码为空")		
	@ApiModelProperty("工序编码")		
    @QueryField(name = "工序编码")		
    private String processCode;		
    @NotNull(message = "工序名称为空")		
	@ApiModelProperty("工序名称")		
    @QueryField(name = "工序名称")		
    private String processName;		
    @NotNull(message = "工步号为空")		
	@ApiModelProperty("工步号")		
    @QueryField(name = "工步号")		
    private String stepNo;		
    // 工步描述，刀号		
    @NotNull(message = "刀号为空")		
	@ApiModelProperty("刀号")		
    @QueryField(name = "刀号")		
    private String stepContent;		
    // 设备名称		
    @NotNull(message = "派工单未选择设备")		
	@ApiModelProperty("设备名")		
    @QueryField(name = "设备名")		
    private String devName;		
    @NotNull(message = "派工单未选择设备")		
	@ApiModelProperty("设备码")		
    @QueryField(name = "设备码")		
    private String devCode;		
    // 加工数量		
	@ApiModelProperty("数量")		
    @QueryField(name = "数量")		
    @NotNull(message = "数量为空")		
    private BigDecimal num;		
    // 对刀时间		
	@ApiModelProperty("对刀时间")		
    @QueryField(name = "对刀时间")		
    private Date selectDate;		
    // 装刀时间		
	@ApiModelProperty("装刀时间")		
    @QueryField(name = "装刀时间")		
    private Date installDate;		
    // 卸刀时间		
	@ApiModelProperty("卸刀时间")		
    @QueryField(name = "卸刀时间")		
    private Date uninstallDate;		
    // 卸刀原因		
	@ApiModelProperty("换刀原因")		
    @QueryField(name = "换刀原因")		
    private String reason;		
    // 操作人		
    //@QueryField(name = "操作人")		
    private String operator;		
	@ApiModelProperty("创建时间")		
    @QueryField(name = "创建时间")		
    private Date createOn;		
	@ApiModelProperty("修改时间")		
    @QueryField(name = "修改时间")		
    private Date updateOn;		
    // 状态		
	@ApiModelProperty("状态")		
    @QueryField(name = "状态")		
    private String status;		
	@ApiModelProperty("操作人")		
    @QueryField(name = "操作人")		
    @TableField(exist = false)		
    private String userName;		
    // 刀补文件ID		
    private String fileId;		
		
		
    // 工具字段		
    @TableField(exist = false)		
    private Date startDate;		
    @TableField(exist = false)		
    private Date endDate;		
    @TableField(exist = false)		
    private List<String> idList;		
    @TableField(exist = false)		
    private String statusEq;		
    @TableField(exist = false)		
    private List<String> statusIn;		
    @TableField(exist = false)		
    private List<String> routeCodeList;		
    @TableField(exist = false)		
    private List<String> processList;		
    @TableField(exist = false)		
    private List<String> materialCodeList;		
    @TableField(exist = false)		
    private List<String> ppList;		
    @TableField(exist = false)		
    private String bomCode;		
    @TableField(exist = false)		
    private String bomVer;		
    @TableField(exist = false)		
    private String ppNo;		
    @TableField(exist = false)		
    private String life;		
    @TableField(exist = false)		
    private Integer orderNo;		
    @TableField(exist = false)		
    private Integer processNo;		
    @TableField(exist = false)		
    private String remarks;		
		
    public void setId(String id) {		
        this.id = id;		
    }		
}		
