package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("采购预警")	
@QueryModel(	
        name = "1202",	
        alias = "t",	
        searchApi = "/api/po/poPurchase/queryPurchaseWarningList",	
        remark = "采购预警")	
public class PurchaseWarningQueryVo extends BaseModel {	
	
	@ApiModelProperty("年")	
    @QueryField(name = "年", show = false)	
    private String orderYear;	
	
	@ApiModelProperty("月")	
    @QueryField(name = "月", show = false)	
    private String orderMonth;	
	
	@ApiModelProperty("供应商")	
    @QueryField(name = "供应商")	
    private String supplierName;	
	
	@ApiModelProperty("供应商编号")	
    @QueryField(name = "供应商编号")	
    private String supplierCode;	
	
	@ApiModelProperty("订单号")	
    @QueryField(name = "订单号")	
    private String purchaseNo;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号")	
    private String sdNo;	
	
	@ApiModelProperty("发运行号")	
    @QueryField(name = "发运行号")	
    private String itemNo;	
	
	@ApiModelProperty("存货编码")	
    @QueryField(name = "存货编码")	
    private String materialCode;	
	
	@ApiModelProperty("存货名称")	
    @QueryField(name = "存货名称")	
    private String materialName;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量")	
    private String purchaseQty;	
	
	@ApiModelProperty("发运数量")	
    @QueryField(name = "发运数量")	
    private String qty;	
	
	@ApiModelProperty("累计到货数量")	
    @QueryField(name = "累计到货数量",alias = ".ifnull(t1.arrival_qty,ifnull( t2.arrival_qty, 0 ))")	
    private String arrivalQty;	
	
	@ApiModelProperty("回复到货日期")	
    @QueryField(name = "回复到货日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String requestedDeliveryDate;	
	
	@ApiModelProperty("预警提前期（天）")	
    @QueryField(name = "预警提前期（天）")	
    private String warningAdvanceDay;	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, option = {"1", "正常", "0", "预警", "-1", "逾期"})	
    private String status;	
	
	
	
	
}	
