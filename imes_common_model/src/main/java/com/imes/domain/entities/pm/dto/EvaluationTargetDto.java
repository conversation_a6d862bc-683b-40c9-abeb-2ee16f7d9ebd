package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (PerEvaluationTarget)实体类
 *
 * <AUTHOR>
 * @since 2021-11-11 14:49:18
 */
@ApiModel("考评目标实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EvaluationTargetDto implements Serializable {
    private static final long serialVersionUID = -40405603450224392L;
    /**
     * id
     */
    @ApiModelProperty("id")
    private String id;
    /**
     * 考评计划id
     */
    @ApiModelProperty("考评计划id")
    private String evaluationPlanId;
    /**
     * 考评指标
     */
    @ApiModelProperty("考评指标")
    private List<EvaluationTargetItemDto> evaluationTargetItems;
    /**
     * 考评人工号
     */
    @ApiModelProperty("考评人工号")
    private String assessorCode;
    /**
     * 权重
     */
    @ApiModelProperty("权重")
    private Integer weight;
    /**
     * 考评人姓名
     */
    @ApiModelProperty("考评人姓名")
    private String assessorName;
    /**
     * 标题
     */
    @ApiModelProperty("标题")
    private String title;
    /**
     * 考评类型（0：管理考评，1：项目考评）
     */
    @ApiModelProperty("考评类型（0：管理考评，1：项目考评）")
    private Integer type;
    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private String projectId;
    /**
     * 方案id
     */
    @ApiModelProperty("方案id")
    private String schemeId;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
     * 修改人
     */
    @ApiModelProperty("修改人")
    private String updatedBy;
}