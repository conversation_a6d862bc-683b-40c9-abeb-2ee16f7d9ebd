package com.imes.domain.entities.wms;	
	
import java.util.Date;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import javax.validation.constraints.NotBlank;	
import java.io.Serializable;	
	
@Data	
@ApiModel(value = "文档角色表")	
public class WmsRole implements Serializable {	
	
    private static final long serialVersionUID = 615639586322141851L;	
	
    @ApiModelProperty(value = "主键ID")	
    private String id;	
	
    @ApiModelProperty(value = "角色编码")	
    @NotBlank(message = "角色编码不能为空")	
    private String wmsRoleCode;	
	
    @ApiModelProperty(value = "说明")	
    @NotBlank(message = "角色说明不能为空")	
    private String wmsRoleDesc;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
    private String code;	
	
    private String userName;	
	
    private String whCode;	
}	
