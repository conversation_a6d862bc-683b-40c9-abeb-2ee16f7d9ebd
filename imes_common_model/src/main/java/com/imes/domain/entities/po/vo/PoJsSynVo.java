package com.imes.domain.entities.po.vo;

import com.imes.domain.entities.scs.vo.ScsSaleMainVo;
import lombok.Data;

import java.util.List;

@Data
public class PoJsSynVo {
    //只变更采购订单业务状态用
    private List<String> purchaseNos;
    private String closePerson;
    private String closeTime;
    //判断明细行是关闭还是反关闭
    private String closeType;
    //实际金额
    private String actualAmount;
    //数据变更用
    private PoPurchaseVO poPurchaseVO;
    //开票单号集合
    private List<String> invoiceNos;
    //明细
    private List<PoJsDetailVo> invoiceDetail;

    //是否通过 0拒绝 1通过
    private String type;
    //拒绝理由
    private String refuseReason;
    //自身的orgPlatformCode
    private String orgPlatformCode;
    //采购订单号用于反审核删除单
    private String poPurchaseNo;
    //采购订单号用于行关闭
    private String poNo;
    //采购订单行号
    private List<String> sdNos;
    //开始日期
    private String beginDate;
    //供应商编码
    private String supplierCode;
    //
    private String companyName;

    private String sendMsg;
    //查询时间区间
    private String beginTime;

    private String endTime;
    //业务标记 10 更新供应商的加锁状态
    private String busType;
    //唯一键
    private String uniqueCode;
    //用于删除
    private String id;
    //需求版本号
    private String versionNo;

    private int pageSize;
    private int pageNum;
    //发货单号
    private String deliveryNo;
}
