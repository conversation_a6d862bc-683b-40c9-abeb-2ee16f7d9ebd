package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsStockBin;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsStockBinVos extends WmsStockBin {	
    private List<Map<String,Object>> mapList;	
    private String onhandQtys;	
    private String status;	
	
    private String category;	
	
    private BigDecimal totalQty;	
}	
