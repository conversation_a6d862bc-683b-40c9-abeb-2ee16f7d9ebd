package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
/**	
 * 生产领料明细	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-04-13 10:23	
 */	
@Data	
public class WmsOutboundItemVo {	
	
    /**	
     * 物料编码--必填	
     */	
	@ApiModelProperty("物料编码--必填")	
    private String materialCode;	
	
    /**	
     * 领料数量--必填	
     */	
	@ApiModelProperty("领料数量--必填")	
    private BigDecimal qty;	
	
    /**	
     * 主单位--必填	
     */	
	@ApiModelProperty("主单位--必填")	
    private String primaryUnit;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 包装规格编码	
     */	
	@ApiModelProperty("包装规格编码")	
    private String packCode;	
	
    /**	
     * 包装数量(10箱、10盒)	
     */	
	@ApiModelProperty("包装数量(10箱、10盒)")	
    private BigDecimal packQty;	
	
    /**	
     * 包装单位（箱、盒）	
     */	
	@ApiModelProperty("包装单位（箱、盒）")	
    private String packUnit;	
	
    /**	
     * 包装系数（一箱有多少个）	
     */	
	@ApiModelProperty("包装系数（一箱有多少个）")	
    private BigDecimal packNumber;	
	
    /**	
     * 销售单号	
     */	
	@ApiModelProperty("销售单号")	
    private String saleCode;	
	
    /**	
     * 定批批号	
     */	
	@ApiModelProperty("定批批号")	
    private String thirdItemCode;	
	
    private String whCode;	
	
    private String whName;	
	
    private String areaCode;	
	
    private String areaName;	
	
    private String binCode;	
	
    private String binName;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 发货单明细id	
     */	
	@ApiModelProperty("发货单明细id")	
    private String deliveryId;	
	
    /**	
     * 辅助属性字典编码拼接	
     */	
	@ApiModelProperty("辅助属性字典编码拼接")	
    private String skuCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 锁定数量	
     */	
	@ApiModelProperty("锁定数量")	
    private BigDecimal lockQty;	
	
    /**	
     * 投料单号	
     */	
	@ApiModelProperty("投料单号")	
    private String outCode;	
	
    /**	
     * 生产投料投料单号	
     */	
	@ApiModelProperty("生产投料投料单号")	
    private String ppcOutCode;	
	
    /**	
     * 发货单单号	
     */	
	@ApiModelProperty("发货单单号")	
    private String deliveryCode;	
	
    /**	
     * 行号	
     */	
	@ApiModelProperty("行号")	
    private String receiptItemCode;	
	
    /**	
     * 基本申请数量	
     */	
	@ApiModelProperty("基本申请数量")	
    private BigDecimal primaryQty;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 申请人编码	
     */	
	@ApiModelProperty("申请人编码")	
    private String workerCode;	
	
    /**	
     * 申请人	
     */	
	@ApiModelProperty("申请人")	
    private String workerName;	
	
	
    /**	
     * 领料单号	
     */	
	@ApiModelProperty("领料单号")	
    private String receiptCode;	
	
    /**	
     * 预计领料日期	
     */	
	@ApiModelProperty("预计领料日期")	
    private Date expectDate;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
	
    /**	
     * 申请部门编码	
     */	
	@ApiModelProperty("申请部门编码")	
    private String departCode;	
	
    /**	
     * 申请部门	
     */	
	@ApiModelProperty("申请部门")	
    private String departName;	
	
	
    /**	
     * 关联数量	
     */	
	@ApiModelProperty("关联数量")	
    private BigDecimal relationWmsQty;	
	
	
    /**	
     * 计划领料量	
     */	
	@ApiModelProperty("计划领料量")	
    private BigDecimal pickQty;	
	
	
    /**	
     * 是否允许负库存	
     */	
	@ApiModelProperty("是否允许负库存")	
    private String isEnableMinusInventory;	
	
}	
