package com.imes.domain.entities.hr.enmus;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.ibatis.type.Alias;

import java.util.Arrays;
import java.util.Optional;

/**
 * 用户证件状态
 *
 * <AUTHOR> z
 * @since 2022-06-20 15:44:39
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
@Alias("userIDEnum")
public enum UserIDEnum {

    IDCARD(0, "居民身份证"),
    AOMENGPAss(1, "港澳台居民居住证"),
    HONGKONGPASS(2, "港澳居民来往内地通行证"),
    MAINLANDPASS(3, "台湾居民来往内地通行证"),
    PASSPORT(5, "护照");

    @EnumValue
    private final Integer status;
    // @JsonValue
    private final String statusName;

    UserIDEnum(Integer status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }


    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static UserIDEnum des(final JsonNode jsonNode) {
        int status = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("status")).map(JsonNode::asInt).orElse(-1);
        if (status == -1) {
            return null;
        }
        return UserIDEnum.match(status);
    }

    public static UserIDEnum match(Integer status) {
        return Arrays.stream(UserIDEnum.values())
                .filter(e -> e.getStatus().equals(status))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("用户证件状态错误"));
    }


    public static UserIDEnum getStatus(String values) {
        return Arrays.stream(UserIDEnum.values())
                .filter(e -> e.getStatusName().equals(values))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("用户证件状态错误"));
    }

}
