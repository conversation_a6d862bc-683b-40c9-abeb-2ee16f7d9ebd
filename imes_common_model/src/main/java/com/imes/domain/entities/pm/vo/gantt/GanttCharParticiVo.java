package com.imes.domain.entities.pm.vo.gantt;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<参与人员>>
 * @company 捷创智能技术有限公司
 * @create 2021-02-25 15:20
 */
@ApiModel("甘特图参与人VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GanttCharParticiVo {
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("姓名")
    private String text;
    @ApiModelProperty("父id")
    private String parent;
    // @ApiModelProperty("默认")
    // private Boolean Default;
}
