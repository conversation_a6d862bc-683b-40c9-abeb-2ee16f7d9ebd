package com.imes.domain.entities.wms;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
import com.alibaba.fastjson.annotation.JSONField;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel(value = "到货任务实体类")	
public class WmsArrivalTask implements Serializable {	
    /**	
     * 主键	
     */	
    @ApiModelProperty(value = "id")	
    private String id;	
	
    /**	
     * 任务单号	
     */	
    @ApiModelProperty(value = "任务单号")	
    private String taskCode;	
	
    /**	
     * 到货单号	
     */	
    @ApiModelProperty(value = "到货单号")	
    private String aoCode;	
	
    /**	
     * 到货单明细号	
     */	
    @ApiModelProperty(value = "到货单明细号")	
    private String aoItemCode;	
	
    /**	
     * 第三方系统明细单唯一标识	
     */	
    @ApiModelProperty(value = "第三方系统明细单唯一标识")	
    private String thirdItemCode;	
	
    /**	
     * 第三方系统单号	
     */	
    @ApiModelProperty(value = "第三方系统单号")	
    private String raSysCode;	
	
    /**	
     * 关联单号	
     */	
    @ApiModelProperty(value = "关联单号")	
    private String receiptCode;	
	
    /**	
     * 物料编码	
     */	
    @ApiModelProperty(value = "物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
    @ApiModelProperty(value = "物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
    @ApiModelProperty(value = "物料型号")	
    private String materialMarker;	
	
    /**	
     * 物料品牌	
     */	
    @ApiModelProperty(value = "物料型品牌")	
    private String brand;	
	
    /**	
     * 物料条码	
     */	
    @ApiModelProperty(value = "物料条码")	
    private String barCode;	
	
    /**	
     * 批次号	
     */	
    @ApiModelProperty(value = "批次号")	
    private String batch;	
	
    /**	
     * 到货批次	
     */	
	@ApiModelProperty("到货批次")	
    private String batchSupplier;	
	
    /**	
     * 生产日期	
     */	
	@ApiModelProperty("生产日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date productionDate;	
	
    /**	
     * 仓库编码	
     */	
    @ApiModelProperty(value = "仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    /**	
     * 库区编码	
     */	
    @ApiModelProperty(value = "库区编码")	
    private String areaCode;	
	
    /**	
     * 库区名称	
     */	
    @ApiModelProperty(value = "库区名称")	
    private String areaName;	
	
    /**	
     * 库位编码	
     */	
    @ApiModelProperty(value = "库位编码")	
    private String binCode;	
	
    /**	
     * 库位名称	
     */	
    @ApiModelProperty(value = "库位名称")	
    private String binName;	
	
    /**	
     * 主单位	
     */	
    @ApiModelProperty(value = "主单位")	
    private String unit;	
	
    /**	
     * 采购数量	
     */	
    @ApiModelProperty(value = "采购数量")	
    private BigDecimal purchaseQty;	
	
    /**	
     * 已接收数量	
     */	
    @ApiModelProperty(value = "已接收数量")	
    private BigDecimal receivedQty;	
	
    /**	
     * 已到货数量	
     */	
    @ApiModelProperty(value = "已到货数量")	
    private BigDecimal arrivalQty;	
	
    /**	
     * 已上架数量	
     */	
    @ApiModelProperty(value = "已上架数量")	
    private BigDecimal putQty;	
	
    /**	
     * 辅助单位	
     */	
    @ApiModelProperty(value = "辅助单位")	
    private String auxiliaryUnit;	
	
    /**	
     * 辅助数量	
     */	
    @ApiModelProperty(value = "辅助数量")	
    private BigDecimal auxiliaryQty;	
	
    /**	
     * 换算比例	
     */	
    @ApiModelProperty(value = "换算比例")	
    private BigDecimal conversionRatio;	
	
    /**	
     * 采购单价	
     */	
    @ApiModelProperty(value = "采购单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 车牌号	
     */	
    @ApiModelProperty(value = "车牌号")	
    private String licensePlate;	
	
    /**	
     * 送货司机	
     */	
    @ApiModelProperty(value = "送货司机")	
    private String deliveryDriver;	
	
    /**	
     * 司机联系方式	
     */	
    @ApiModelProperty(value = "司机联系方式")	
    private String driverPhone;	
	
    /**	
     * 收货人工号	
     */	
    @ApiModelProperty(value = "收货人工号")	
    private String receiverCode;	
	
    /**	
     * 收货人名称	
     */	
    @ApiModelProperty(value = "收货人名称")	
    private String receiverName;	
	
    /**	
     * 到货时间	
     */	
    @ApiModelProperty(value = "到货时间")	
    private Date arrivalDate;	
	
    /**	
     * 核检状态（10：未核检；20：已核检）	
     */	
    @ApiModelProperty(value = "核检状态（10：未核检；20：已核检）")	
    private String qualityStatus;	
	
    /**	
     * 处置方式（10：预入库；20：拒收）	
     */	
    @ApiModelProperty(value = "处置方式（10：预入库；20：拒收）")	
    private String storageType;	
	
    /**	
     * 任务状态（10：未完成上架；20：部分完成上架；30：全部完成上架）	
     */	
    @ApiModelProperty(value = "任务状态（10：未完成上架；20：部分完成上架；30：全部完成上架）")	
    private String taskStatus;	
	
    /**	
     * 入库单号	
     */	
    @ApiModelProperty(value = "入库单号")	
    private String inboundCode;	
	
    /**	
     * 入库数量	
     */	
    @ApiModelProperty(value = "入库数量")	
    private BigDecimal inboundQty;	
	
    /**	
     * 包装单位	
     */	
    @ApiModelProperty(value = "包装单位")	
    private String packCodeUnit;	
	
    /**	
     * 包装上架数量	
     */	
    @ApiModelProperty(value = "包装上架数量")	
    private BigDecimal packPutQty;	
	
    /**	
     * 包装入库数量	
     */	
    @ApiModelProperty(value = "包装入库数量")	
    private BigDecimal packInboundQty;	
	
	
	
    /**	
     * 入库状态（10：未入库；20：部分入库；30：全部入库）	
     */	
    @ApiModelProperty(value = "入库状态（10：未入库；20：部分入库；30：全部入库）")	
    private String inboundStatus;	
	
    /**	
     * 库存说明	
     */	
    @ApiModelProperty(value = "库存说明")	
    private String stockDesc;	
	
    /**	
     * 所属公司编码	
     */	
    @ApiModelProperty(value = "所属公司编码")	
    private String companyCode;	
	
    /**	
     * 所属公司名称	
     */	
    @ApiModelProperty(value = "所属公司名称")	
    private String companyName;	
	
    /**	
     * 客户编码	
     */	
    @ApiModelProperty(value = "客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
    @ApiModelProperty(value = "客户名称")	
    private String customerName;	
	
    /**	
     * 销售合同号	
     */	
    @ApiModelProperty(value = "销售合同号")	
    private String salesContractCode;	
	
    /**	
     * 备注	
     */	
    @ApiModelProperty(value = "备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    /**	
     * 修改时间	
     */	
    @ApiModelProperty(value = "修改时间")	
    private Date updateOn;	
	
    /**	
     * 修改人	
     */	
    @ApiModelProperty(value = "修改人")	
    private String updateBy;	
	
    @JSONField(name = "箱号")	
    @ApiModelProperty("箱号")	
    private String boxNo;	
	
    @ApiModelProperty("自定义字段")	
    @JSONField(name = "自定义字段")	
    private String custom;	
	
    private static final long serialVersionUID = 1L;	
}	
