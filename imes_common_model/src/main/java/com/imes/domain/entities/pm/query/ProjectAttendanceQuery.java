package com.imes.domain.entities.pm.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * (ProjectAttendance)实体类
 *
 * <AUTHOR>
 * @since 2021-05-10 14:31:20
 */
@ApiModel("《打卡签到》条件查询")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectAttendanceQuery implements Serializable {
    private static final long serialVersionUID = -41797779299367817L;
    /**
    * 人员姓名
    */
    @ApiModelProperty("人员姓名")
    private String employeeName;
    /**
    * 项目名称
    */
    @ApiModelProperty("项目名称")
    private String projectName;
    /**
    * 经纬度
    */
    @ApiModelProperty("经纬度")
    private String latlng;
    /**
    * 备注
    */
    @ApiModelProperty("备注")
    private String remarks;
    /**
    * 创建人
    */
    @ApiModelProperty("创建人")
    private String createdBy;
    /**
    * 修改人
    */
    @ApiModelProperty("修改人")
    private String updatedBy;
    /**
    * 创建时间
    */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @ApiModelProperty("修改时间")
    private LocalDateTime updateTime;
    /**
    * 审核状态（0：待审核 , 1：审核通过，2：审批中, 3：已驳回）
    */
    @ApiModelProperty("审核状态")
    private Integer approvalStatus;
}