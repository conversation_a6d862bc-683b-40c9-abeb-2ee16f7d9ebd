package com.imes.domain.entities.pm.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目里程碑参与人预览>>
 * @company 捷创智能技术有限公司
 * @create 2021-12-17 13:06
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostParticipantsDes {
    /**
     * 工号
     */
    private String employeeCode;
    /**
     * 姓名
     */
    private String employeeName;
    /**
     * 计划工时
     */
    private BigDecimal planHours;
    /**
     * 实际工时
     */
    private BigDecimal actualHours;
    /**
     * 差旅费
     */
    private BigDecimal travelExpenses;
}
