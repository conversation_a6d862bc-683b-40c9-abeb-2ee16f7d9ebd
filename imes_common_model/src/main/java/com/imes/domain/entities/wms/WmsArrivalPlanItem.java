package com.imes.domain.entities.wms;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalPlanItem implements Serializable {	
    /**	
     *	
     */	
    @JSONField(name = "主键")	
    @ApiModelProperty("主键")	
    private String id;	
	
    /**	
     * 到货计划明细单号	
     */	
    @JSONField(name = "到货计划明细单号")	
    @ApiModelProperty("到货计划明细单号")	
    private String aoPlanItemCode;	
	
    /**	
     * 到货计划主表id	
     */	
    @JSONField(name = "到货计划主表id")	
    @ApiModelProperty("到货计划主表id")	
    private String aoPlanId;	
	
    /**	
     * 到货计划主表业务单号	
     */	
    @JSONField(name = "到货计划主表业务单号")	
    @ApiModelProperty("到货计划主表业务单号")	
    private String aoPlanCode;	
	
    /**	
     * 关联单号	
     */	
    @JSONField(name = "关联单号")	
    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 关联明细单号	
     */	
    @JSONField(name = "关联明细单号")	
    @ApiModelProperty("关联明细单号")	
    private String receiptItemCode;	
	
    /**	
     * 采购单号	
     */	
    @JSONField(name = "采购单号")	
    @ApiModelProperty("采购单号")	
    private String poCode;	
	
    /**	
     * 采购明细单号	
     */	
    @JSONField(name = "采购明细单号")	
    @ApiModelProperty("采购明细单号")	
    private String poItemCode;	
	
    /**	
     * 物料编码	
     */	
    @JSONField(name = "物料编码")	
    @ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
    @JSONField(name = "物料名称")	
    @ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 规格	
     */	
    @JSONField(name = "规格")	
    @ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 型号	
     */	
    @JSONField(name = "型号")	
    @ApiModelProperty("型号")	
    private String materialMarker;	
	
    /**	
     * 单据数量(采购单位)	
     */	
    @JSONField(name = "单据数量")	
    @ApiModelProperty("单据数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 已到货数量(采购单位)	
     */	
    @JSONField(name = "已到货数量")	
    @ApiModelProperty("已到货数量")	
    private BigDecimal arrivedQty;	
	
    /**	
     * 到货状态（10：未到货；20：部分到货；30：全部到货）	
     */	
    @JSONField(name = "到货状态（10：未到货；20：部分到货；30：全部到货）")	
    @ApiModelProperty("到货状态（10：未到货；20：部分到货；30：全部到货）")	
    private String arrivalStatus;	
	
    /**	
     * 基本单位	
     */	
    @JSONField(name = "基本单位")	
    @ApiModelProperty("基本单位")	
    private String primaryUnit;	
	
    /**	
     * 采购单位	
     */	
    @JSONField(name = "采购单位")	
    @ApiModelProperty("采购单位")	
    private String poMainUnit;	
	
    /**	
     * 采购含税单价	
     */	
    @JSONField(name = "采购含税单价")	
    @ApiModelProperty("采购含税单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * 采购未税单价	
     */	
    @JSONField(name = "采购未税单价")	
    @ApiModelProperty("采购未税单价")	
    private BigDecimal unIncludePrice;	
	
    /**	
     * 物料总金额	
     */	
    @JSONField(name = "物料总金额")	
    @ApiModelProperty("物料总金额")	
    private BigDecimal amount;	
	
    /**	
     * 所属公司编码	
     */	
    @JSONField(name = "所属公司编码")	
    @ApiModelProperty("所属公司编码")	
    private String companyCode;	
	
    /**	
     * 所属公司名称	
     */	
    @JSONField(name = "所属公司名称")	
    @ApiModelProperty("所属公司名称")	
    private String companyName;	
	
    /**	
     * 辅助属性	
     */	
    @JSONField(name = "辅助属性")	
    @ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    /**	
     * 优先级	
     */	
    @JSONField(name = "优先级")	
    @ApiModelProperty("优先级")	
    private Integer priority;	
	
    /**	
     * 创建时间	
     */	
    @JSONField(name = "创建时间")	
    @ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
    @JSONField(name = "创建人")	
    @ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
    @JSONField(name = "更新时间")	
    @ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
    @JSONField(name = "更新人")	
    @ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 备注	
     */	
    @JSONField(name = "备注")	
    @ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 业务状态 10-正常 20-已关闭	
     */	
    @JSONField(name = "业务状态 10-正常 20-已关闭")	
    @ApiModelProperty("业务状态 10-正常 20-已关闭")	
    private String businessStatus;	
	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    @JSONField(name = "进位方式")	
    private Byte carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    @JSONField(name = "小数精度位数")	
    private Byte precisionDigit;	
	
    @ApiModelProperty("自定义字段")	
    @JSONField(name = "自定义字段")	
    private String custom;	
	
    private static final long serialVersionUID = 1L;	
}	
