package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目工时汇总条件查询>>
 * @company 捷创智能技术有限公司
 * @create 2021-06-30 10:20
 */
@ApiModel("《项目工时汇总》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ManHourSummaryQuery {
    @ApiModelProperty("日期范围")
    private List<LocalDate> dates;
    @ApiModelProperty("项目名称")
    private String projectName;
    @ApiModelProperty("项目经理")
    private String projectManager;
}
