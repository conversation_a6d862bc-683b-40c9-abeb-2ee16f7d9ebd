package com.imes.domain.entities.pm.query;

import com.imes.domain.entities.pm.po.DataScope;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@ApiModel("《问题》查询条件")
@Data
public class DataScopeQuery extends BaseQuery {
    private String id;
    private String userCode;

    public DataScope toPo() {
        DataScope dataScope = new DataScope();
        dataScope.setId(id);
        dataScope.setUserCode(userCode);
        return dataScope;
    }
}
