package com.imes.domain.entities.wei.po;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WeiDevice implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 所属区域编码
     */
    private String areaCode;

    /**
     * 设备编码
     */
    private String devCode;

    /**
     * 设备名称
     */
    private String devName;

    /**
     * 型号
     */
    private String modelNumber;

    /**
     * 设备种类,01-特种设备，02生产设备......详见DEVICE_TYPE
     */
    private String devCategory;

    /**
     * 制造厂商
     */
    private String madeFactory;

    /**
     * 制造时间
     */
    private Date madeTime;

    /**
     * 制造编号
     */
    private String madeCode;

    /**
     * 设备重量
     */
    private BigDecimal devWeight;

    /**
     * 投用时间
     */
    private Date useTime;

    /**
     * 安装地点
     */
    private String installAddress;

    /**
     * 报警编码
     */
    private String warningCode;

    /**
     * ABC分类，数据值A,B,C
     */
    private String abctype;

    /**
     * 设备在厂状态，1在用，2待用，3停用
     */
    private String devUseStatus;

    /**
     * 设备运行状态，1运行，0停机
     */
    private String devRunStatus;

    /**
     * 设备特性
     */
    private String devFeatures;

    /**
     * 设备图片
     */
    private String devPicId;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 备注
     */
    private String remarks;

    private static final long serialVersionUID = 1L;
}