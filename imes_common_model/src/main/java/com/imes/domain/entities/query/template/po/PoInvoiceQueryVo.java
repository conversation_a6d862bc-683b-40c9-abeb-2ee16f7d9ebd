package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("采购发票")	
@QueryModel(	
        name = "1134",	
        remark = "采购发票",	
        alias = "pi",	
        searchApi = "/po/invoice/queryList"	
)	
public class PoInvoiceQueryVo extends BaseModel {	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("发票日期")	
    @QueryField(name = "发票日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String invoiceDate;	
	
	@ApiModelProperty("发票号码")	
    @QueryField(name = "发票号码")	
    private String invoiceCode;	
	
	@ApiModelProperty("发票金额")	
    @QueryField(name = "发票金额", type = Type.Number, format = "0.00")	
    private String totalMoney;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", alias = ".(ifnull(TRUNCATE(pi.tax_rate, 2), 0) * 100)", format = "{}%", level = Level.Main,show = false)	
    private String taxRate;	
	
	@ApiModelProperty("税率")	
    @QueryField(name = "税率", dictOption = "SALE_TAX_CODE",type = Type.MultiSelect)	
    private String taxCode;	
	
	@ApiModelProperty("业务类型")	
    @QueryField(name = "业务类型")	
    private String cbustype;	
	
	@ApiModelProperty("发票状态")	
    @QueryField(name = "发票状态", type = Type.MultiSelect, dictOption = "PO_INVOICE_STATUS")	
    private String invoiceStatus;	
	
	@ApiModelProperty("业务员")	
    @QueryField(name = "业务员", type = Type.Select, sqlOption = "select user_name as label,user_code as value from pe_user")	
    private String cpersoncode;	
	
	@ApiModelProperty("推送ERP状态")	
    @QueryField(name = "推送ERP状态", type = Type.Select, option = {"0", "未推送", "1", "已推送"})	
    private String erpStatus;	
	
	@ApiModelProperty("发票类型")	
    @QueryField(name = "发票类型", type = Type.Select, dictOption = "PO_INVOICE_TYPE")	
    private String invoiceType;	
	
	@ApiModelProperty("驳回原因")	
    @QueryField(name = "驳回原因")	
    private String refuseReason;	
	
	@ApiModelProperty("发票日期摘要")	
    @QueryField(name = "发票日期摘要", type = Type.Date, format = "yyyy-MM-dd")	
    private String cdefine6;	
	
	@ApiModelProperty("币种")	
    @QueryField(name = "币种", type = Type.Select, dictOption = "CUSTOMER_CURRENCY")	
    private String currency;	
	
	@ApiModelProperty("开票时间")	
    @QueryField(name = "开票时间", type = Type.Date, order = OrderBy.DESC, format = "yyyy-MM-dd")	
    private String createOn;	
	
	@ApiModelProperty("红蓝发票")	
    @QueryField(name = "红蓝发票", type = Type.Select, option = {"1", "蓝字发票（入）", "2", "红字发票（退）"})	
    private Integer blueRedType;	
	
	@ApiModelProperty("开票申请人")	
    @QueryField(name = "开票申请人")	
    private String madeUserName;	
	
	@ApiModelProperty("开票日期")	
    @QueryField(name = "开票日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String issueDate;	
	
	@ApiModelProperty("发票识别号")	
    @QueryField(name = "发票识别号")	
    private String chdefine1;	
	
	@ApiModelProperty("采购类型")	
    @QueryField(name = "采购类型", type = Type.Select, dictOption = "U8_PURCHASE_TYPE")	
    private String cptcode;	
	
	@ApiModelProperty("业务员部门")	
    @QueryField(name = "业务员部门", type = Type.Select, sqlOption = "select depart_name label, depart_code value from co_department where depart_type='019'")	
    private String cdepcode;	
	
	@ApiModelProperty("开票流水号")	
    @QueryField(name = "开票流水号")	
    private String invoiceNo;	
	
	@ApiModelProperty("汇率")	
    @QueryField(name = "汇率")	
    private String cexchrate;	
	
	@ApiModelProperty("收付款协议编码")	
    @QueryField(name = "收付款协议编码")	
    private String cvenpuomprotocol;	
	
    /*@QueryField(name = "支付方式", type = Type.Select, dictOption = "SCS_ACCOUNT_VERIFY_PAY_METHOD")	
    private String payMethod;*/	
	
    //@QueryField(name = "开票确认人")	
    //private String invoiceConfirmUserName;	
	
    //@QueryField(name = "开票确认时间", type = Type.Date)	
    //private String invoiceConfirmTime;	
	
    //@QueryField(name = "备注")	
    //private String remarks;	
	
    //@QueryField(name = "发票日期摘要", type = Type.Date, format = "yyyy-MM-dd")	
    //private String cdefine6;	
	
	@ApiModelProperty("fileId")	
    @QueryField(name = "fileId", show = false)	
    private String fileId;	
}	
