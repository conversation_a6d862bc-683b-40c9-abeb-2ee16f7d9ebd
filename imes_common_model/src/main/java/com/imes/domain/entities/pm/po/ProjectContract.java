package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * (ProjectContract)实体类
 *
 * <AUTHOR>
 * @since 2021-04-29 15:35:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_project_contract", resultMap = "BaseResultMap")
public class ProjectContract implements Serializable {
    private static final long serialVersionUID = -40537986378943342L;

    /**
     * id
     */
    private String id;

    /**
     * 项目id
     */
    private String projectId;

    /**
     * 合同号
     */
    private String contractId;

    /**
     * 客户合同号
     */
    private String customerContractId;

    /**
     * 合同金额
     */
    private BigDecimal contractAmount;

    /**
     * 开票金额
     */
    private BigDecimal invoicedAmount;

    /**
     * 到款金额
     */
    private BigDecimal amountReceived;

    /**
     * 合同来源
     */
    private String source;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;

}