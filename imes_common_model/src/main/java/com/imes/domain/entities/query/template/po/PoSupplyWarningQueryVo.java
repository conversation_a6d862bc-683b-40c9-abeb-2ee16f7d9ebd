package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("发运预警")	
@QueryModel(	
        name = "1203",	
        alias = "t",	
        searchApi = "/api/scs/scsSaleMain/querySaleWarningList",	
        remark = "发运预警")	
public class PoSupplyWarningQueryVo extends BaseModel {	
	
	@ApiModelProperty("年")	
    @QueryField(name = "年", order = OrderBy.DESC, show = false)	
    private String year;	
	
	@ApiModelProperty("月")	
    @QueryField(name = "月", order = OrderBy.DESC, show = false)	
    private String month;	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称")	
    private String customerName;	
	
	@ApiModelProperty("订单号")	
    @QueryField(name = "订单号", order = OrderBy.DESC)	
    private String soNo;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号", order = OrderBy.ASC)	
    private String sdNo;	
	
	@ApiModelProperty("发运行号")	
    @QueryField(name = "发运行号", order = OrderBy.ASC)	
    private String itemSdNo;	
	
	@ApiModelProperty("存货编码")	
    @QueryField(name = "存货编码")	
    private String materialCode;	
	
	@ApiModelProperty("存货名称")	
    @QueryField(name = "存货名称")	
    private String materialName;	
	
	@ApiModelProperty("规格型号")	
    @QueryField(name = "规格型号")	
    private String specification;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量")	
    private String qty;	
	
	@ApiModelProperty("发运数量")	
    @QueryField(name = "发运数量")	
    private String itemQty;	
	
	@ApiModelProperty("累计发货数量")	
    @QueryField(name = "累计发货数量")	
    private String sendedQty;	
	
	@ApiModelProperty("回复到货日期")	
    @QueryField(name = "回复到货日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String requestedDeliveryDate;	
	
	@ApiModelProperty("预警提前期（天）")	
    @QueryField(name = "预警提前期（天）")	
    private String warningDay;	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, option = {"1", "正常", "0", "预警", "-1", "逾期"})	
    private String status;	
	
	
}	
