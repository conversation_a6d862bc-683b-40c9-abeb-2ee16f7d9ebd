package com.imes.domain.entities.wms;	
	
import com.imes.domain.entities.wms.vo.WmsBillDetailForReturnVo;	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import io.swagger.annotations.ApiOperation;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.persistence.Transient;	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("领料申请实体类")	
public class WmsPickingOrder implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    @ApiModelProperty("领料单号")	
    private String poCode;	
	
    @ApiModelProperty("单据类型 1：生产领用")	
    private String orderType;	
	
    @ApiModelProperty("单据来源：手工建单、第三方系统")	
    private String source;	
	
    @ApiModelProperty("第三方系统发货单号")	
    private String thirdOrderCode;	
	
    @ApiModelProperty("单据状态:未完成、已审核、已完成、已取消")	
    private String orderStatus;	
	
    @ApiModelProperty("出库状态:未出库、部分出库、全部出库")	
    private String pickedStatus;	
	
    @ApiModelProperty("关联单据单号")	
    private String receiptCode;	
	
    @ApiModelProperty("所属公司编码")	
    private String companyCode;	
	
    @ApiModelProperty("所属公司名称")	
    private String companyName;	
	
    @ApiModelProperty("仓库编码")	
    private String whCode;	
	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    @ApiModelProperty("销售合同号")	
    private String salesContractCode;	
	
    @ApiModelProperty("客户合同号")	
    private String customerContractCode;	
	
    @ApiModelProperty("客户编码")	
    private String customerCode;	
	
    @ApiModelProperty("客户名称")	
    private String customerName;	
	
    @ApiModelProperty("项目编码")	
    private String projectCode;	
	
    @ApiModelProperty("项目名称")	
    private String projectName;	
	
    @ApiModelProperty("领用用途")	
    private String purpose;	
	
    @ApiModelProperty("实际领用日期")	
    private Date pickingDate;	
	
    @ApiModelProperty("备注")	
    private String remarks;	
	
    @ApiModelProperty("创建时间")	
    private Date createOn;	
	
    @ApiModelProperty("创建人")	
    private String createBy;	
	
    @ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty("更新人")	
    private String updateBy;	
	
    @ApiModelProperty("第三方单据编码")	
    private String thirdBusCode;	
	
    @ApiModelProperty("是否入线边库")	
    private int isInStorage;	
	
    @ApiModelProperty("退货原因")	
    private String reason;	
	
    @ApiModelProperty("流程ID")	
    private String activityId;	
	
    @ApiModelProperty("自定义字段")	
    private String custom;	
	
    @ApiModelProperty("班组编码")	
    private String teamCode;	
	
    @ApiModelProperty("班组名称")	
    private String teamName;	
	
    @ApiModelProperty("车间编码")	
    private String workshopCode;	
	
    @ApiModelProperty("车间名称")	
    private String workshopName;	
	
    @ApiModelProperty("是否定批")	
    private String isLimitedBatch;	
	
    @ApiModelProperty("销售订单号")	
    private String saleNo;	
	
    @ApiModelProperty("关联单类型")	
    private String receiptType;	
	
    @ApiModelProperty("开始日期")	
    private Date startDate;	
	
    @ApiModelProperty("结束日期")	
    private Date endDate;	
	
    @Transient	
    private List<WmsPickingOrderItem> items;	
	
    private List<WmsPickingOrderItem> detailLists;	
	
    @Transient	
    private List<WmsBillDetailForReturnVo> purchaseReturnItems;	
	
    private static final long serialVersionUID = 1L;	
}	
