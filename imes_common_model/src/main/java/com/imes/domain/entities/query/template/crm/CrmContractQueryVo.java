package com.imes.domain.entities.query.template.crm;

import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import com.imes.domain.entities.query.model.base.Type;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("合同")
@QueryModel(name = "0461",
        remark = "合同",
        searchApi = "/crm/contract/queryContractQueryVo")
public class CrmContractQueryVo extends BaseModel {
    //id
    @QueryField(name = "id")
    private String id;

    @QueryField(name = "合同编码")
    private String code;
    //名称
    @QueryField(name = "合同名称")
    private String name;
    //类型
    @QueryField(name = "类型")
    private String type;
    //合同开始日期
    @QueryField(name = "开始日期", type = Type.Date)
    private String startDate;
    //合同结束日期
    @QueryField(name = "结束日期", type = Type.Date)
    private String endDate;
    //金额
    @QueryField(name = "金额", type = Type.Number)
    private String amount;
    //客户id
    @QueryField(name = "客户id", show = false)
    private String customerId;
    //客户名称
    @QueryField(name = "客户")
    private String customerName;
    //商机id
    @QueryField(name = "商机id", show = false)
    private String businessId;
    //商机名称
    @QueryField(name = "商机名称")
    private String businessName;
    //所属企业
    @QueryField(name = "所属企业")
    private String enterprise;
    //联系人id
    @QueryField(name = "联系人id", show = false)
    private String contactId;
    //联系人姓名
    @QueryField(name = "联系人")
    private String contactName;
    //客户签署人
    @QueryField(name = "客户签署人编码", show = false)
    private String customerSignatory;
    //客户签署人姓名
    @QueryField(name = "客户签署人")
    private String customerSignatoryName;
    //公司签署人
    @QueryField(name = "公司签署人编码")
    private String companySignatory;
    //公司签署人姓名
    @QueryField(name = "公司签署人")
    private String companySignatoryName;
    //备注
    @QueryField(name = "备注")
    private String remarks;
    //审核状态（0：待审核 , 1：审核通过，2：审批中）
    // @JsonIgnore
    @QueryField(name = "审核状态")
    private String approvalStatus;

    //付款方式
    @QueryField(name = "付款方式")
    private String payMethod;

    @QueryField(name = "审核人")
    private String leaderUserCode;

    @QueryField(name = "是否小于百分之五十")
    private String discount;

    @QueryField(name = "系统默认审核人", flowableApprovalUser = true, show = false)
    private List<String> userCodes;
}
