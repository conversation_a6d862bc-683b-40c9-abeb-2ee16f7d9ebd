package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalOrder implements Serializable {	
    /**	
     * 	
     */
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    /**	
     * 到货通知单号	
     */
    @ApiModelProperty(value = "到货通知单号")	
    private String aoCode;	
	
    /**	
     * 到货类型：采购到货、销售退货	
     */
    @ApiModelProperty(value = "到货类型")	
    private String orderType;	
	
    /**	
     * 单据的来源：手工建单、第三方系统	
     */
    @ApiModelProperty(value = "单据的来源")	
    private String source;	
	
    /**	
     * 第三方系统发货单号	
     */
    @ApiModelProperty(value = "第三方系统发货单号")	
    private String thirdOrderCode;	
	
    /**	
     * 关联单据类型：采购单、退货单	
     */
    @ApiModelProperty(value = "关联单据类型")	
    private String receiptType;	
	
    /**	
     * 关联单号	
     */
    @ApiModelProperty(value = "关联单号")	
    private String receiptCode;	
	
    /**	
     * 收货仓库	
     */
    @ApiModelProperty(value = "收货仓库")	
    private String whCode;	
	
    /**	
     * 收货仓库名称	
     */	

    @ApiModelProperty(value = "收货仓库名称")	
    private String whName;	
	
    /**	
     * 到货时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")

    @ApiModelProperty(value = "到货时间")	
    private Date arrivalTime;	
	
    /**	
     * 供应商编码	
     */	

    @ApiModelProperty(value = "供应商编码")	
    private String supplierCode;	
	
    /**	
     * 供应商名称	
     */	

    @ApiModelProperty(value = "供应商名称")	
    private String supplierName;	
	
    /**	
     * 客户编码	
     */	

    @ApiModelProperty(value = "客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	

    @ApiModelProperty(value = "客户名称")	
    private String customerName;	
	
    /**	
     * 车牌号	
     */	

    @ApiModelProperty(value = "车牌号")	
    private String licensePlate;	
	
    /**	
     * 物流公司编码	
     */
    @ApiModelProperty(value = "物流公司编码")	
    private String shipperCode;	
	
    /**	
     * 物流公司名称	
     */	

    @ApiModelProperty(value = "物流公司名称")	
    private String shipperName;	
	
    /**	
     * 物流单号	
     */	

    @ApiModelProperty(value = "物流单号")	
    private String logisticCode;	
	
    /**	
     * 送货司机	
     */	

    @ApiModelProperty(value = "送货司机")	
    private String deliveryDriver;	
	
    /**	
     * 司机联系方式	
     */
    @ApiModelProperty(value = "司机联系方式")	
    private String driverPhone;	
	
    /**	
     * 收货员	
     */	

    @ApiModelProperty(value = "收货员")	
    private String receiver;	
	
    /**	
     * 收货员名称	
     */	

    @ApiModelProperty(value = "收货员名称")	
    private String receiverName;	
	
    /**	
     * 质检员	
     */	

    @ApiModelProperty(value = "质检员")
    private String inspector;	
	
    /**	
     * 质检员名称	
     */	

    @ApiModelProperty(value = "质检员名称")	
    private String inspectorName;	
	
    /**	
     * 审核人	
     */	

    @ApiModelProperty(value = "审核人")	
    private String approver;	
	
    /**	
     * 审核人名称	
     */	

    @ApiModelProperty(value = "审核人名称")	
    private String approverName;	
	
    /**	
     * 到货单状态（未完成、已审核、已完成、已取消）	
     */	

    @ApiModelProperty("到货单状态")	
    private String orderStatus;	
	
    /**	
     * 质检状态（未质检、已质检）	
     */	

    @ApiModelProperty("质检状态")	
    private String qualityStatus;	
	
    /**	
     * 入库状态（未入库、入库完成、部分入库、拒绝入库）	
     */	

    @ApiModelProperty("入库状态")	
    private String storageStatus;	
	
    /**	
     * 库存状态（正常、维修更换、新件退货、残次品退货、过期新件、过期残次品）	
     */	
    @ApiModelProperty("库存状态")
    private String stockStatus;	
	
    /**	
     * 创建时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	

    @ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
//    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	

    @ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")
    private String deleteStatus;	
	
    /**	
     * 备注	
     */
    @ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 总数量	
     */	
    @ApiModelProperty("总数量")
    private BigDecimal totalCount;	
    /**	
     * 总金额	
     */	
    @ApiModelProperty("总金额")
    private BigDecimal totalAmount;	
	
    /**	
     * 发送状态（未发送、已发送）	
     */	
    @ApiModelProperty("发送状态")	

    private String sendStatus;	
	
    /**	
     * 所属公司编码	
     */	
    @ApiModelProperty("所属公司编码")	

    private String companyCode;	
	
    /**	
     * 所属公司名称	
     */	
    @ApiModelProperty("所属公司名称")
    private String companyName;	
	
    /**	
     * 所属部门编号	
     */	
    @ApiModelProperty("所属部门编号")
    private String depCode;	
	
    /**	
     * 所属部门名称	
     */	
    @ApiModelProperty("所属部门名称")
    private String depName;	
	
    /**	
     * 司机证件号	
     */	
    @ApiModelProperty("司机证件号")
    private String idCard;
	
    /**	
     * 车辆类型	
     */	
    @ApiModelProperty("车辆类型")
    private String carType;	
	
    @ApiModelProperty("供应商单号")
    private String customerOrderCode;	
	
    /**	
     * 物流类型	
     */	
    @ApiModelProperty("物流类型")
    private String logisticType;	
	
    @ApiModelProperty("自定义字段")
    private String custom;	
	
    /**	
     * 到货明细单	
     */
    @ApiModelProperty("到货明细单")	
    private List<WmsArrivalOrderItem> items;	
	
    private static final long serialVersionUID = 1L;	
}	
