package com.imes.domain.entities.scs.vo;

import lombok.Data;

import java.util.List;

/*
* {
	"data": {
		"doctype": "VMICurStock",
		"pageno": "1",
		"pagesize": "100",
		"total_page": "1",
		"total_rows": "2",
		"rows": [{
			"RowNumber": "1",
			"materialCode": "1999000070",
			"materialName": "密封接线柱",
			"specification": "EBC00064B,EBC00064D,EBC00277A",
			"materialMarker": "EBC00064B,EBC00064D,EBC00277A",
			"unit": "件",
			"onhandQty": "1.000000",
			"lastInTime": "2023-03-15T00:00:00",
			"lastOutTime": "2023-03-15T00:00:00"
		}, {
			"RowNumber": "2",
			"materialCode": "1999000070",
			"materialName": "密封接线柱",
			"specification": "EBC00064B,EBC00064D,EBC00277A",
			"materialMarker": "EBC00064B,EBC00064D,EBC00277A",
			"unit": "件",
			"onhandQty": "10.000000",
			"lastInTime": "2023-03-15T00:00:00",
			"lastOutTime": "2023-03-15T00:00:00"
		}]
	},
	"code": "0",
	"errmsg": ""
}
* */
@Data
public class VmiResultVo {
    //
    private String doctype;
    private String pageno;
    private String pagesize;
    private String total_page;
    private String total_rows;
    private List<VmiRowsVo> rows;
}
