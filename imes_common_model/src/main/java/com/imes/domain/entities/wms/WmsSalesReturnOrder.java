package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.vo.WmsSalesReturnOrderItemVo;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import javax.persistence.Transient;	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsSalesReturnOrder implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 销售退货单	
     */	
	@ApiModelProperty("销售退货单")	
    private String stoCode;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String orderType;	
	
    /**	
     * 单据的来源：手工建单、第三方系统	
     */	
	@ApiModelProperty("单据的来源：手工建单、第三方系统")	
    private String source;	
	
    /**	
     * 第三方系统销售退货单号	
     */	
	@ApiModelProperty("第三方系统销售退货单号")	
    private String thirdOrderCode;	
	
    /**	
     * 发货单状态：（未完成、已审核、已完成、已取消）	
     */	
	@ApiModelProperty("发货单状态：（未完成、已审核、已完成、已取消）")	
    private String orderStatus;	
	
    /**	
     * 关联单据单号（销售单）	
     */	
	@ApiModelProperty("关联单据单号（销售单）")	
    private String receiptCode;	
	
    /**	
     * 出库单号	
     */	
	@ApiModelProperty("出库单号")	
    private String outboundCode;	
	
    /**	
     * 出库日期	
     */	
	@ApiModelProperty("出库日期")	
    private Date outDate;	
	
    /**	
     * 所属公司编码	
     */	
	@ApiModelProperty("所属公司编码")	
    private String companyCode;	
	
    /**	
     * 所属公司名称	
     */	
	@ApiModelProperty("所属公司名称")	
    private String companyName;	
	
    /**	
     * 退货仓库编码	
     */	
	@ApiModelProperty("退货仓库编码")	
    private String whCode;	
	
    /**	
     * 退货仓库名称	
     */	
	@ApiModelProperty("退货仓库名称")	
    private String whName;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 退货说明	
     */	
	@ApiModelProperty("退货说明")	
    private String describes;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    private String deptCode;	
    private String deptName;	
	
    private String applyUserCode;	
    private String applyUserName;	
	
    private String reason;	
	
    private String receiverPhone;	
    private String receiverName;	
    private String receiverAddress;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    @Transient	
    private List<WmsSalesReturnOrderItem> items;	
	
    @Transient	
    private List<WmsSalesReturnOrderItemVo> saleItems;	
	
	
    private static final long serialVersionUID = 1L;	
}	
