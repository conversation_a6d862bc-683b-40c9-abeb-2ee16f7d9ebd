package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsLogisticsHistory implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 发货单主键	
     */	
	@ApiModelProperty("发货单主键")	
    private String doId;	
	
    /**	
     * 发货单号	
     */	
	@ApiModelProperty("发货单号")	
    private String doCode;	
	
    /**	
     * 物流单号	
     */	
	@ApiModelProperty("物流单号")	
    private String logisticCode;	
	
    /**	
     * 物流公司编码	
     */	
	@ApiModelProperty("物流公司编码")	
    private String shipperCode;	
	
    /**	
     * 物流公司名称	
     */	
	@ApiModelProperty("物流公司名称")	
    private String shipperName;	
	
    /**	
     * 增值物流状态： 1-已揽收， 2-在途中， 201-到达派件城市， 202-派件中， 211-已放入快递柜或驿站， 3-已签收， 311-已取出快递柜或驿站， 4-问题件， 401-发货无信息， 402-超时未签收， 403-超时未更新， 404-拒收（退件）， 412-快递柜或驿站超时未取	
     */	
	@ApiModelProperty("增值物流状态： 1-已揽收， 2-在途中， 201-到达派件城市， 202-派件中， 211-已放入快递柜或驿站， 3-已签收， 311-已取出快递柜或驿站， 4-问题件， 401-发货无信息， 402-超时未签收， 403-超时未更新， 404-拒收（退件）， 412-快递柜或驿站超时未取")	
    private String state;	
	
    /**	
     * 所在城市	
     */	
	@ApiModelProperty("所在城市")	
    private String location;	
	
    /**	
     * 时间	
     */	
	@ApiModelProperty("时间")	
    private Date acceptTime;	
	
    /**	
     * 描述	
     */	
	@ApiModelProperty("描述")	
    private String acceptStation;	
	
    /**	
     * 备注	
     */	
	@ApiModelProperty("备注")	
    private String remark;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    private static final long serialVersionUID = 1L;	
}	
