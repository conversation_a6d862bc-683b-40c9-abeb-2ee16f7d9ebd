package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "江森采购变更单-回复表")
public class PoPurchaseDetailItemChange implements Serializable {

    private static final long serialVersionUID = 777955085321799892L;

    private String id;

    @ApiModelProperty(value = "关联id")
    private String mainId;

    private String sdNo;

    @ApiModelProperty(value = "数量")
    private BigDecimal qty;

    @ApiModelProperty(value = "计划到货时间")
    private Date requestedDeliveryDate;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "回复版本号")
    private Integer version;
}