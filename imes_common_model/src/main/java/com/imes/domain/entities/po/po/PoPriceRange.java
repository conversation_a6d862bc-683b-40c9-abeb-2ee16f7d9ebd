package com.imes.domain.entities.po.po;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购取价方式(PoPriceRange)实体类
 *
 * <AUTHOR>
 * @since 2023-11-07 10:05:32
 */
@Data
public class PoPriceRange implements Serializable {
    private static final long serialVersionUID = 925525899398238613L;

    /**
     * id
     */
    private String id;

    /**
     * 取价编码
     */
    private String rangeNo;

    /**
     * 未取到价格允许人工输入
     */
    private String limitInput;

    /**
     * 价格修改类型
     */
    private String editType;

    /**
     * 说明
     */
    private String remarks;

    /**
     * 允许价格为0
     */
    private String limitPriceZero;

    /**
     * 修改百分比
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Integer editPercent;

    /**
     * 创建时间
     */
    private Date createOn;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateOn;

    /**
     * 更新人
     */
    private String updateBy;

}

