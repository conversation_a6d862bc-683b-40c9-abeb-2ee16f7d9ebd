package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
import java.util.List;	
	
/**	
 * 生产入库明细	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-04-13 10:23	
 */	
@Data	
public class WmsInboundItemVo {	
	
    /**	
     * 物料编码--必填	
     */	
	@ApiModelProperty("物料编码--必填")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
	
    /**	
     * 入库数量--必填	
     */	
	@ApiModelProperty("入库数量--必填")	
    private BigDecimal qty;	
	
    /**	
     * 主单位--必填	
     */	
	@ApiModelProperty("主单位--必填")	
    private String primaryUnit;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 包装规格编码	
     */	
	@ApiModelProperty("包装规格编码")	
    private String packCode;	
	
    /**	
     * 包装数量(10箱、10盒)	
     */	
	@ApiModelProperty("包装数量(10箱、10盒)")	
    private Integer packQty;	
	
    /**	
     * 包装单位（箱、盒）	
     */	
	@ApiModelProperty("包装单位（箱、盒）")	
    private String packUnit;	
	
    /**	
     * 包装系数（一箱有多少个）换算比例	
     */	
	@ApiModelProperty("包装系数（一箱有多少个）换算比例")	
    private BigDecimal packNumber;	
	
    /**	
     * 单价	
     */	
	@ApiModelProperty("单价")	
    private BigDecimal unitPrice;	
	
    /**	
     * SN码列表	
     */	
	@ApiModelProperty("SN码列表")	
    private List<String> snNoList;	
	
    /**	
     * 包装申请数量	
     */	
	@ApiModelProperty("包装申请数量")	
    private BigDecimal packApplyQty;	
	
    /**	
     * 预计开工日期(YYYY-MM-DD)	
     */	
	@ApiModelProperty("预计开工日期(YYYY-MM-DD)")	
    private String planStartDate;	
	
    /**	
     * 预计完工日期(YYYY-MM-DD)	
     */	
	@ApiModelProperty("预计完工日期(YYYY-MM-DD)")	
    private String planEndDate;	
	
    /**	
     * 第三方系统单据类型	
     */	
	@ApiModelProperty("第三方系统单据类型")	
    private String thirdOrderType;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 客户名称	
     */	
	@ApiModelProperty("客户名称")	
    private String customerName;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 销售订单行号	
     */	
	@ApiModelProperty("销售订单行号")	
    private String sdNo;	
	
    /**	
     * 完成数量	
     */	
	@ApiModelProperty("完成数量")	
    private BigDecimal finishQty;	
	
    private String baseUnit;	
	
    private BigDecimal baseUnitQty;	
	
	
    /**	
     * 入库仓库	
     */	
	@ApiModelProperty("入库仓库")	
    private String whCode;	
	
    /**	
     * 入库仓库名称	
     */	
	@ApiModelProperty("入库仓库名称")	
    private String whName;	
	
    /**	
     * 仓库类型	
     */	
	@ApiModelProperty("仓库类型")	
    private String warehouseType;	
	
    /**	
     * 库位类型	
     */	
	@ApiModelProperty("库位类型")	
    private String binCode;	
	
    /**	
     * 报工投料清单	
     */	
	@ApiModelProperty("报工投料清单")	
    private List<WmsWorkReleaseVo> workVoList;	
	
    /**	
     * 行号	
     */	
	@ApiModelProperty("行号")	
    private String receiptItemCode;	
	
}	
