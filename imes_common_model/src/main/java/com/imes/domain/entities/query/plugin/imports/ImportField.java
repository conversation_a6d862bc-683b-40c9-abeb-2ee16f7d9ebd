package com.imes.domain.entities.query.plugin.imports;

import com.imes.domain.entities.query.model.base.Level;

import java.lang.annotation.*;

@Documented
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ImportField {

    /**
     * 字段名称
     * 例如：name = "客户名称"
     */
    String name();

    /**
     * 字段样例值
     * 例如：demo = "张三"
     */
    String demo() default "";

    /**
     * 字段默认值
     * 例如：value = "1"
     * 注意：当导入内容为空时，则默认为此内容
     */
    String value() default "";

    /**
     * 字段备注
     * 例如：remark = "巴拉拉小魔仙变身！"
     */
    String remark() default "";

    /**
     * 必填
     * 例如：required = true
     */
    boolean required() default false;

    /**
     * 固定长度
     * 例如：length = 100
     */
    int length() default -999;

    /**
     * 最小长度
     * 例如：minLength = 100
     * 注意：此项是检查长度，若检查数字大小请使用minNumber/maxNumber
     */
    int minLength() default -999;

    /**
     * 最大长度
     * 例如：maxLength = 100
     * 注意：此项是检查长度，若检查数字大小请使用minNumber/maxNumber
     */
    int maxLength() default -999;

    /**
     * 是否Excel内唯一
     * 例如：unique = true
     */
    boolean unique() default false;

    /**
     * 检查是否是数组字典内选项
     * 例如：option = {"1", "0"}
     */
    String[] option() default {};

    /**
     * 检查是否是系统字典内选项
     * 例如：dictOption = "CUSTOMER_CURRENCY"
     */
    String dictOption() default "";

    /**
     * 检查是否是SQL字典内选项
     * 传入对象必须是返回类型为List<Map<String,Object>>的SQL语句
     * 且显示名必须为label，值名必须为value
     * 例如：sqlOption = "select name as label,id as value from table"
     */
    String sqlOption() default "";

    /**
     * 检查是否是接口字典内选项
     * 例如：apiOption = "/api/sys/dict/fetch?type=1"
     */
    String apiOption() default "";

    /**
     * 检查是否是数字类型
     * 例如：isNumber = true
     */
    boolean isNumber() default false;

    /**
     * 检查是否是整数类型
     * 例如：isInteger = true
     * 注意：若设置此项，是否是数字（isNumber）可不填，会自动检查
     */
    boolean isInteger() default false;

    /**
     * 最小值
     * 例如：minNumber = "0.0"
     * 注意：若设置此项，是否是数字（isNumber）可不填，会自动检查
     */
    String minNumber() default "";

    /**
     * 最大值
     * 例如：maxNumber = "9999.99"
     * 注意：若设置此项，是否是数字（isNumber）可不填，会自动检查
     */
    String maxNumber() default "";

    /**
     * 检查是否是邮箱格式
     * 例如：isEmail = true
     */
    boolean isEmail() default false;

    /**
     * 检查是否是手机号格式
     * 例如：isMobile = true
     */
    boolean isMobile() default false;

    /**
     * 验证是否为座机号码+手机号码
     * 例如：isPhone = true
     */
    boolean isPhone() default false;

    /**
     * 标记字段所属层级
     * 例如：level = 1
     * 注意：默认是黄色，如果是明细字段，会使用橘色进行标记
     */
    int level() default Level.Main;

    /**
     * 检查是否是日期格式
     * 例如：date = "yyyy-MM-dd"
     * 注意：请传入需要校验的日期格式
     */
    String date() default "";

    /**
     * 检查是否是统一社会信用编号格式
     * 例如：isCreditCode = true
     */
    boolean isCreditCode() default false;
}
