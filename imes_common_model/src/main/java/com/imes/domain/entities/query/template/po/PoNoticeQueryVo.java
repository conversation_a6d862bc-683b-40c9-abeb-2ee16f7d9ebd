package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
@ApiModel("公告管理")	
@QueryModel(	
        name = "1166",	
        remark = "公告管理",	
        alias = "a",	
        searchSql = SearchSql.PoNoticeQueryVo	
)	
public class PoNoticeQueryVo extends BaseModel {	
    private String id;	
	
	@ApiModelProperty("公告号")	
    @QueryField(name = "公告号")	
    private String noticeNo;	
	
	@ApiModelProperty("公告标题")	
    @QueryField(name = "公告标题")	
    private String noticeTitle;	
	
	@ApiModelProperty("公告内容")	
    @QueryField(name = "公告内容")	
    private String content;	
	
    private String fileId;	
	
	@ApiModelProperty("公告类型")	
    @QueryField(name = "公告类型",dictOption = "PO_NOTICE_TYPE",type = Type.Select)	
    private String noticeType;	
	
	@ApiModelProperty("公告发送状态")	
    @QueryField(name = "公告发送状态",dictOption = "PO_NOTICE_STATUS",type = Type.MultiSelect)	
    private String noticeStatus;	
	
	@ApiModelProperty("发布企业名称")	
    @QueryField(name = "发布企业名称")	
    private String publishCompanyName;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间" ,type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
	
    private String createBy;	
	
	
    private Date updateOn;	
	
	
    private String updateBy;	
	
    private String remarks;	
	
//    @QueryField(name = "供应商编码", alias = "b")	
//    private String supplierCode;	
//	
//    @QueryField(name = "供应商名称", alias = "b")	
//    private String supplierName;	
//	
//    @QueryField(name = "公告发送状态", alias = "b" ,dictOption= "PO_NOTICE_SUPPLIER_STATUS",type = Type.MultiSelect)	
//    private String status;	
//	
//    @QueryField(name = "发送时间", alias = "b",type = Type.Date)	
//    private Date sendTime;	
//	
//    @QueryField(name = "发送失败信息", alias = "b")	
//    private String sendInfo;	
}	
