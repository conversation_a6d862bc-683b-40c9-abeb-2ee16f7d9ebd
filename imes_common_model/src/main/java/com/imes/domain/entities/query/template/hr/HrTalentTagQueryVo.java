package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * 人才库标签表(HrTalentTag)实体类	
 *	
 * <AUTHOR>	
 * @since 2023-04-14 13:39:27	
 */	
@Data	
@ApiModel("人才标签库")	
@QueryModel(name = "0706",	
        remark = "人才标签库",	
        alias = "hr_talent_tag",	
        searchApi = "/api/hr/talent/tag/query")	
public class HrTalentTagQueryVo extends BaseModel {	
    /**	
     * 编码	
     */	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
    /**	
     * 标签名称	
     */	
	@ApiModelProperty("标签名称")	
    @QueryField(name = "标签名称")	
    private String name;	
    /**	
     * 人才标签描述	
     */	
	@ApiModelProperty("人才标签描述")	
    @QueryField(name = "人才标签描述")	
    private String description;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private Date createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", show = false)	
    private String createBy;	
	
    private String poolId;	
	
}	
	
