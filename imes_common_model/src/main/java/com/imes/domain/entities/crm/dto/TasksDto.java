package com.imes.domain.entities.crm.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.imes.domain.entities.crm.dto.ex.BaseDto;
import com.imes.domain.entities.crm.enums.BusinessTypeEnum;
import com.imes.domain.entities.crm.enums.TaskPriorityEnum;
import com.imes.domain.entities.crm.enums.TaskStageEnum;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 任务管理(任务实体类)
 * <AUTHOR>
 * @Date 2022/3/9 13:32
 */

@ApiModel("任务管理(任务实体类)")
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TasksDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 7529194668826837690L;
    /**
     * id
     */
    private String id;
    /**
     * 任务主题
     */
    // @NotBlank(message = "任务主题不能为空")
    private String subject;
    /**
     * 跟进方式
     */
    @NotBlank(message = "跟进方式不能为空")
    private String way;
    /**
     * 业务类型
     */
    // @Dict(value = "CRM_BUSINESS_TYPE")
    @NotNull(message = "业务类型不能为空")
    private BusinessTypeEnum businessType;
    /**
     * 业务id
     */
    @NotBlank(message = "业务不能为空")
    private String businessId;
    /**
     * 业务名称
     */
    private String businessName;
    /**
     * 二级业务名称
     */
    private String relationName;
    /**
     * 二级业务id
     */
    private String relationId;
    /**
     * 任务执行人
     */
    private String userCode;
    /**
     * 执行人姓名
     */
    private String userName;
    /**
     * 任务状态（0：未启动、1：进行中、2：已完成、3：已取消’、4：推迟）
     */
    // @NotNull(message = "任务状态不能为空")
    private TaskStageEnum state;
    /**
     * 开始时间
     */
    // @NotNull(message = "开始时间不能为空")
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    /**
     * 时间范围查询
     */
    //@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private List<LocalDateTime> times;
    /**
     * 优先级（0：低、1：正常、2：高、3：紧急）
     */
    // @NotNull(message = "优先级不能为空")
    private TaskPriorityEnum priority;
    /**
     * 是否提醒 （0：不提醒、1：提醒）
     */
    private Boolean reminder;
    /**
     * 提醒间隔 （小时）
     */
    private Integer intervalTime;
    /**
     * 下次提醒时间
     */
    private LocalDateTime reminderTime;
    /**
     * 内容
     */
    @NotBlank(message = "内容不能为空")
    private String content;
    /**
     * 备注
     */
    private String remarks;
    /**
     * 自定义字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }
    /*======================================成本=======================================*/
    /**
     * '飞机费用'
     */
    private BigDecimal aircraftCost;
    /**
     * 飞机票数量
     */
    private Integer numberAirTickets;
    /**
     * 火车费用
     */
    private BigDecimal trainCost;
    /**
     * 火车票数量
     */
    private Integer numberTrainTickets;
    /**
     * 大巴费用
     */
    private BigDecimal busCost;
    /**
     * 大巴票数量
     */
    private Integer numberBusTickets;
    /**
     * 的士费用
     */
    private BigDecimal taxiCost;
    /**
     * 的士票数量
     */
    private Integer numberTaxiTickets;
    /**
     * 过路费用
     */
    private BigDecimal tollCost;
    /**
     * 过路票数量
     */
    private Integer numberTollTickets;
    /**
     * 停车费用
     */
    private BigDecimal parkingCost;
    /**
     * 停车票数量
     */
    private Integer numberParkingTickets;
    /**
     * 其他交通费用
     */
    private BigDecimal otherTrafficCost;
    /**
     * 其他交通票数量
     */
    private Integer numberOtherTrafficTickets;
    /**
     * 住宿费用
     */
    private BigDecimal stayCost;
    /**
     * 住宿费用票数量
     */
    private Integer numberStayTickets;
    /**
     * 招待费用
     */
    private BigDecimal entertainCost;
    /**
     * 招待费用票数量
     */
    private Integer numberEntertainTickets;
    /**
     * 招待人名
     */
    private String guests;
    /**
     * 招待人数
     */
    private Integer numberGuests;
    /**
     * 招待目的
     */
    private String guestsObjectives;
    /**
     * 差旅补助
     */
    private BigDecimal travelAllowance;
    /**
     * 差旅补助费用票数
     */
    private Integer numberAllowanceTickets;
    /**
     * 其他费用
     */
    private BigDecimal otherCost;
    /**
     * 其他费用票数量
     */
    private Integer numberOtherTickets;
    /**
     * 报销说明
     */
    private String reimbursementInstructions;
    /**
     * 补贴金额
     */
    private BigDecimal subsidy;
    /**
     * 经纬度
     */
    private String latlng;
    /**
     * 国家
     */
    private String nation;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区县
     */
    private String district;
    /**
     * 详细地址
     */
    private String detailAddress;
}
