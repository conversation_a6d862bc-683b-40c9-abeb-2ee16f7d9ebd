package com.imes.domain.entities.crm.po;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.imes.domain.entities.crm.po.ex.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * (CrmContact)实体类
 *
 * <AUTHOR>
 * @since 2022-02-25 09:14:21
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "crm_contact", resultMap = "BaseResultMap")
public class Contact extends BaseEntity implements Serializable {
    private static final long serialVersionUID = -19032601766674966L;
    /**
    * id
    */
    private String id;
    /**
    * 姓名
    */
    @TableField(condition = SqlCondition.LIKE)
    private String name;
    /**
    * 称呼：先生、夫人、女生 等
    */
    private String callName;
    /**
    * 客户id
    */
    private String customerId;
    /**
     * 客户姓名
     */
    @TableField(exist = false)
    private String customerName;
    /**
     * 线索来源
     */
    private String clueSource;
    /**
    * 部门
    */
    private String dept;
    /**
    * 职位/职称
    */
    private String position;
    /**
    * 邮箱
    */
    private String email;
    /**
    * 其他邮箱
    */
    private String otherEmail;
    /**
    * 电话
    */
    private String telephone;
    /**
    * 其他电话
    */
    private String otherTelephone;
    /**
    * 手机
    */
    private String phone;
    /**
    * 其他手机
    */
    private String otherPhone;
    /**
    * 住宅电话
    */
    private String residentialTelephone;
    /**
     * 联系人关系
     */
    private String relationship;
    /**
    * 传真
    */
    private String fax;
    /**
    * 生日
    */
    private LocalDate birthday;
    /**
     * 激活
     */
    private Boolean activity;
    /**
    * 地址
    */
    private String address;
    /**
    * 备注
    */
    private String remarks;
    /**
     * 联系人所属人(查询条件)
     */
    @TableField(exist = false)
    private String owner;
    /**
     * 自定义字段
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }
}