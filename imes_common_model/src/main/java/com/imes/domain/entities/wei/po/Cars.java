package com.imes.domain.entities.wei.po;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

@Entity
@Table(name = "wei_cars")
public class Cars implements Serializable {

    @Id
    private String id;
    private String truckNo;
    private String truckType;
    private Double tare;
    private String driver;
    private java.util.Date createdOn;
    private String createdBy;
    private java.util.Date updatedOn;
    private String updatedBy;
    private String remarks;
    private Double toleranceRatio;

    public void setId(String value) {
        this.id = value;
    }
    public String getId() {
       return this.id;
    }
    public void setTruckNo(String value) {
        this.truckNo = value;
    }
    public String getTruckNo() {
       return this.truckNo;
    }
    public void setTruckType(String value) {
        this.truckType = value;
    }
    public String getTruckType() {
       return this.truckType;
    }
    public void setTare(Double value) {
        this.tare = value;
    }
    public Double getTare() {
       return this.tare;
    }
    public void setDriver(String value) {
        this.driver = value;
    }
    public String getDriver() {
       return this.driver;
    }
    public void setCreatedOn(java.util.Date value) {
        this.createdOn = value;
    }
    public java.util.Date getCreatedOn() {
       return this.createdOn;
    }
    public void setCreatedBy(String value) {
        this.createdBy = value;
    }
    public String getCreatedBy() {
       return this.createdBy;
    }
    public void setUpdatedOn(java.util.Date value) {
        this.updatedOn = value;
    }
    public java.util.Date getUpdatedOn() {
       return this.updatedOn;
    }
    public void setUpdatedBy(String value) {
        this.updatedBy = value;
    }
    public String getUpdatedBy() {
       return this.updatedBy;
    }
    public void setRemarks(String value) {
        this.remarks = value;
    }
    public String getRemarks() {
       return this.remarks;
    }
    public void setToleranceRatio(Double value){this.toleranceRatio = value;}
    public Double getToleranceRatio(){
        return this.toleranceRatio;
    }
}
