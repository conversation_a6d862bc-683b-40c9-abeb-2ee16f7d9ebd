package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsStorageInBill;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
/**	
 * 移动端入库任务列表	
 * <AUTHOR>	
 * @version 1.0	
 * @date 2021-03-17 14:30	
 */	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsMbInboundVo extends WmsStorageInBill {	
	
    /**	
     * 到货单入库状态	
     */	
	@ApiModelProperty("到货单入库状态")	
    private Integer storageStatus;	
	
    /**	
     * 到货日期	
     */	
	@ApiModelProperty("到货日期")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date arrivalTime;	
	
    /**	
     * 仓库号	
     */	
	@ApiModelProperty("仓库号")	
    private String whCode;	
	
    /**	
     * 总到货数量	
     */	
	@ApiModelProperty("总到货数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 已入库数量	
     */	
	@ApiModelProperty("已入库数量")	
    private BigDecimal inventoryQty;	
	
    /**	
     * 入库单明细	
     */	
	@ApiModelProperty("入库单明细")	
    private List<WmsMbInboundItemVo> items;	
	
    /**	
     * 搜索关键字	
     */	
	@ApiModelProperty("搜索关键字")	
    private String searchKey;	
	
    private Integer pageNum;	
	
    private Integer pageSize;	
	
    private List<String> statusArr;	
	
}	
