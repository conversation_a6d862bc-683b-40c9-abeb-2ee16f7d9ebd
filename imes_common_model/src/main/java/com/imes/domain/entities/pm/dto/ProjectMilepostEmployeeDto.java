package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * (ProProjectMilepostEmployee)实体类
 *
 * <AUTHOR>
 * @since 2020-11-25 09:49:15
 */
@ApiModel("里程碑参与人实体类")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostEmployeeDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 463867663182852915L;
    @ApiModelProperty("id")
    private String id;
    @ApiModelProperty("里程碑id")
    //@NotBlank(message = "里程碑id不能为空")
    private String milepostId;
    @ApiModelProperty("参与人员id")
    //@NotBlank(message = "参与人不能为空")
    private String employeeId;
    @ApiModelProperty("参与人员姓名")
    private String employeeName;
    @ApiModelProperty("岗位")
    private String job;
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    @ApiModelProperty("结束日期")
    private LocalDate endDate;
    @ApiModelProperty("任务内容")
    //@NotEmpty(message = "任务内容不能为空")
    private String task;
    @ApiModelProperty("工时")
    //@NotNull(message = "工时不能为空")
    private BigDecimal workHour;
    @ApiModelProperty("加入时间")
    private LocalDateTime joinTime;
}