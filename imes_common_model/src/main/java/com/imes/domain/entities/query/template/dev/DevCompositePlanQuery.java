package com.imes.domain.entities.query.template.dev;

import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@ApiModel(value = "点润巡保计划综合查询高级查询模型")
@Data
@QueryModel(
        name = "11880",
        remark = "点润巡保计划",
        searchApi = "/dev/report/compositePlanQuery",
        msgAction = {MsgAction.APPROVAL}
)
public class DevCompositePlanQuery {
    @ApiModelProperty(value = "计划id")
    @QueryField(name = "计划id", show = false)
    private String id;

    @ApiModelProperty(value = "计划分类：1-点检；2-巡检；3-润滑；4-保养")
    @QueryField(name = "计划分类", order = OrderBy.ASC, type = Type.Select, option = {"1", "点检", "2", "巡检", "3", "润滑", "4", "保养"})
    private String type;

    @ApiModelProperty(value = "计划名称")
    @QueryField(name = "计划名称")
    private String planName;
}
