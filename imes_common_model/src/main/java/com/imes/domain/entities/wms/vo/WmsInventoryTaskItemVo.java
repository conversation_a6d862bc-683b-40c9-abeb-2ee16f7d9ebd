package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsInventoryTaskItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
import java.util.Map;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsInventoryTaskItemVo extends WmsInventoryTaskItem {	
    private List<Map<String,Object>> mapList;	
    private String stockQtys;	
    private String existQtys;	
    private Byte carryMode;	
    private Byte precisionDigit;	
}	
