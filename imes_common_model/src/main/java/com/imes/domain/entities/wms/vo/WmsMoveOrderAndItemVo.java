package com.imes.domain.entities.wms.vo;

import com.imes.domain.entities.wms.WmsMoveOrder;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class WmsMoveOrderAndItemVo extends WmsMoveOrder {

    @ApiModelProperty(value = "明细单主键")
    private String detailId;

    @ApiModelProperty(value = "明细单号")
    private String moItemCode;

    @ApiModelProperty("物料编码")
    private String materialCode;

    @ApiModelProperty("物料名称")
    private String materialName;

    @ApiModelProperty("物料型号")
    private String materialMarker;

    @ApiModelProperty("批次")
    private String batch;

    @ApiModelProperty("调拨数量")
    private BigDecimal orderQty;

    @ApiModelProperty("已退回数量")
    private BigDecimal returnQty;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("调出仓库")
    private String fromWhName;

    /**
     * 下架库位
     */
    @ApiModelProperty("调出库位")
    private String outBinCode;

    @ApiModelProperty("调入仓库")
    private String toWhName;

    /**
     * 上架库位
     */
    @ApiModelProperty("调入库位")
    private String inBinCode;

    @ApiModelProperty("申请人")
    private String applyName;

    @ApiModelProperty("包装单位")
    private String packCodeUnit;

    @ApiModelProperty("包装数量")
    private BigDecimal packQty;

    @ApiModelProperty("库存数量")
    private BigDecimal stockQty;

    @ApiModelProperty("进位方式")
    private Byte carryMode;

    @ApiModelProperty("小数精度位数")
    private Byte orderQtyPrecision;

    @ApiModelProperty("审核人")
    private String auditName;

    @ApiModelProperty("调拨确认状态")
    private Integer confirmStatus;

    @ApiModelProperty("调拨确认时间")
    private Date confirmOn;

    @ApiModelProperty("调拨确认人")
    private String confirmName;

    @ApiModelProperty("是否启用负库存")
    private String isEnableMinusInventory;

    @ApiModelProperty("规格")
    private String specification;
}	
