package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目立项信息>>
 * @company 捷创智能技术有限公司
 * @create 2021-06-25 11:11
 */
@ApiModel("《项目立项信息》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class ProjectqdQuery extends BaseQuery {
    @ApiModelProperty("行唯一值")
    private String guid;
    @ApiModelProperty("立项单据编码")
    private String nid;
    @ApiModelProperty("立项单号")
    private String bid;
    @ApiModelProperty("销售合同行唯一值")
    private String htGuid;
    @ApiModelProperty("说明")
    private String remark;
    @ApiModelProperty("销售合同行号")
    private Integer htIid;
    @ApiModelProperty("销售合同单号")
    private Integer gtBid;
    @ApiModelProperty("品牌")
    private Integer gBrand;

}
