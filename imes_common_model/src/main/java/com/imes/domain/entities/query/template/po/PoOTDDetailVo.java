package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("订单-OTD-明细表")	
@QueryModel(	
        name = "1304",	
        remark = "订单-OTD-明细表",	
        searchApi = "/api/po/poPurchase/queryPoOTDDetailVo",	
        alias = "po_purchase_detail"	
)	
public class PoOTDDetailVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
	@ApiModelProperty("年")	
    @QueryField(name = "年", order = OrderBy.DESC)	
    private String year;	
	
	@ApiModelProperty("月")	
    @QueryField(name = "月", order = OrderBy.DESC)	
    private String month;	
	
	@ApiModelProperty("供应商名称")	
    @QueryField(name = "供应商名称")	
    private String supplierName;	
	
	@ApiModelProperty("供应商编码")	
    @QueryField(name = "供应商编码",order = OrderBy.ASC)	
    private String supplierCode;	
	
	@ApiModelProperty("订单号")	
    @QueryField(name = "订单号",order = OrderBy.ASC)	
    private String purchaseNo;	
	
	@ApiModelProperty("订单行号")	
    @QueryField(name = "订单行号",order = OrderBy.ASC)	
    private String sdNo;	
	
	@ApiModelProperty("存货编码")	
    @QueryField(name = "存货编码")	
    private String materialCode;	
	
	@ApiModelProperty("存货名称")	
    @QueryField(name = "存货名称")	
    private String materialName;	
	
	@ApiModelProperty("存货型号")	
    @QueryField(name = "存货型号")	
    private String specification;	
	
	@ApiModelProperty("数量")	
    @QueryField(name = "数量")	
    private String purchaseQty;	
	
	@ApiModelProperty("计划到货时间")	
    @QueryField(name = "计划到货时间", type = Type.Date, format = "yyyy-MM-dd")	
    private String estimatedDeliveryDate;	
	
	@ApiModelProperty("累计到货数量")	
    @QueryField(name = "累计到货数量")	
    private String arrivalQty;	
	
	@ApiModelProperty("最晚到货日期")	
    @QueryField(name = "最晚到货日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String arrivalTime;	
	
	@ApiModelProperty("准时到货率")	
    @QueryField(name = "准时到货率")	
    private String arrivalRate;	
	
	@ApiModelProperty("业务员判定")	
    @QueryField(name = "业务员判定")	
    private String judgement;	
	
	@ApiModelProperty("修改原因")	
    @QueryField(name = "修改原因")	
    private String cause;	
	
	@ApiModelProperty("类型(1为订单，2为承诺)")	
    @QueryField(name = "类型(1为订单，2为承诺)",show = false,query = false)	
    private String type;	
}	
