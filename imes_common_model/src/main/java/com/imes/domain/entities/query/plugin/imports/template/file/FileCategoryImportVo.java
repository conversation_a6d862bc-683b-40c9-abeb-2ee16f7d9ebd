package com.imes.domain.entities.query.plugin.imports.template.file;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import com.imes.domain.entities.query.plugin.imports.ImportRemark;
import lombok.Data;

@Data
@ImportModel(name = "文档类目导入模板", remark = ImportRemark.FileCategoryImportVo)
public class FileCategoryImportVo extends BaseModel {

    @ImportField(name = "类目名称", required = true, remark = "必填", maxLength = 50)
    private String name;

    @ImportField(name = "父级类目路径", maxLength = 255, remark = "以英文/作为层级区分，确定导入类目所属的层级情况\n不填默认为一级类目")
    private String allCategoryName;

    @ImportField(name = "档案号", maxLength = 30)
    private String archivesNumber;

    @ImportField(name = "授权类型", required = true, remark = "必填\n写入对应数字\n0:公开文档\n1:受限文档\n一级类目必须是受限文档")
    private String authorizationLevel;

    @ImportField(name = "保管时间(月)", isNumber = true, isInteger = true, maxNumber = "9999", remark = "不必填\n整数不填默认永久")
    private String keepingTime;

    @ImportField(name = "文件类型", remark = "不必填\n参考数据字典【FILE_UPLOAD_TYPE】")
    private String fileType;

    @ImportField(name = "备注", remark = "不必填", maxLength = 255)
    private String remark;
}
