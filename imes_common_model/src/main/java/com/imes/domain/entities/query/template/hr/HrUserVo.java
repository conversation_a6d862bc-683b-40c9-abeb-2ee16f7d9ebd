package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.List;	
	
@Data	
@ApiModel("员工信息")	
@QueryModel(name = "0655",	
        remark = "员工信息",	
        alias = "hr_user",	
        searchApi = "/api/hr/user/query")	
public class HrUserVo extends BaseModel {	
	
    /**	
     * 用户编号	
     */	
	@ApiModelProperty("用户编号")	
    @QueryField(name = "用户编号")	
    private String userCode;	
    /**	
     * 用户名称	
     */	
	@ApiModelProperty("用户名称")	
    @QueryField(name = "用户名称")	
    private String userName;	
    /**	
     * 性别（0：男；1：女）	
     */	
	@ApiModelProperty("性别")	
    @QueryField(name = "性别", type = Type.Select, option = {"0", "男", "1", "女"})	
    private String sex;	
    /**	
     * 手机	
     */	
	@ApiModelProperty("手机")	
    @QueryField(name = "手机")	
    private String mobile;	
    /**	
     * 已有用户编码	
     */	
	@ApiModelProperty("已有用户编码")	
    @QueryField(name = "已有用户编码")	
    private String peUsercode;	
    /**	
     * 上级领导	
     */	
	@ApiModelProperty("上级领导")	
    @QueryField(name = "上级领导")	
    private String leaderUserCode;	
    /**	
     * 是否在职	
     */	
	@ApiModelProperty("是否在职")	
    @QueryField(name = "是否在职")	
    private String isWorker;	
    /**	
     * 邮箱	
     */	
	@ApiModelProperty("邮箱")	
    @QueryField(name = "邮箱")	
    private String email;	
    /**	
     * 毕业院校	
     */	
	@ApiModelProperty("毕业院校")	
    @QueryField(name = "毕业院校")	
    private String institutions;	
    /**	
     * 毕业年份	
     */	
	@ApiModelProperty("毕业年份")	
    @QueryField(name = "毕业年份")	
    private String institutionsDate;	
    /**	
     * 学历	
     */	
	@ApiModelProperty("学历")	
    @QueryField(name = "学历")	
    private String qualifications;	
    /**	
     * 身份证	
     */	
	@ApiModelProperty("证件类型")	
    @QueryField(name = "证件类型")	
    private String idType;	
    /**	
     * 身份证	
     */	
	@ApiModelProperty("证件号")	
    @QueryField(name = "证件号")	
    private String idNumber;	
    /**	
     * 名族	
     */	
	@ApiModelProperty("名族")	
    @QueryField(name = "名族")	
    private String nation;	
    /**	
     * 籍贯省	
     */	
	@ApiModelProperty("籍贯省")	
    @QueryField(name = "籍贯省")	
    private String province;	
    /**	
     * 户籍所在地	
     */	
	@ApiModelProperty("户籍所在地")	
    @QueryField(name = "户籍所在地")	
    private String domicilePlace;	
    /**	
     * 户口性质 0农村 1城镇	
     */	
	@ApiModelProperty("户口性质")	
    @QueryField(name = "户口性质")	
    private String domicileType;	
    /**	
     * 是否已婚	
     */	
	@ApiModelProperty("是否已婚")	
    @QueryField(name = "是否已婚")	
	
    private String relatives;	
    /**	
     * 政治面貌	
     */	
	@ApiModelProperty("政治面貌")	
    @QueryField(name = "政治面貌")	
    private String politicsStatus;	
    /**	
     * 现居住地	
     */	
	@ApiModelProperty("现居住地")	
    @QueryField(name = "现居住地")	
    private String residence;	
    /**	
     * 所属银行	
     */	
	@ApiModelProperty("所属银行")	
    @QueryField(name = "所属银行")	
    private String bank;	
    /**	
     * 银行卡号	
     */	
	@ApiModelProperty("银行卡号")	
    @QueryField(name = "银行卡号")	
    private String bankCode;	
    /**	
     * 外语等级证书	
     */	
	@ApiModelProperty("外语等级证书")	
    @QueryField(name = "外语等级证书")	
    private String englishLevel;	
    /**	
     * 职称证书	
     */	
	@ApiModelProperty("职称证书")	
    @QueryField(name = "职称证书")	
    private String credential;	
    /**	
     * 健康状况	
     */	
	@ApiModelProperty("健康状况")	
    @QueryField(name = "健康状况")	
    private String health;	
    /**	
     * 招聘来源	
     */	
	@ApiModelProperty("招聘来源")	
    @QueryField(name = "招聘来源")	
    private String recruidSource;	
    /**	
     * 能否出差	
     */	
	@ApiModelProperty("能否出差")	
    @QueryField(name = "能否出差")	
    private String evection;	
    /**	
     * 入职时间	
     */	
	@ApiModelProperty("入职时间")	
    @QueryField(name = "入职时间", type = Type.Date)	
    private String entryTime;	
    /**	
     * 转正时间	
     */	
	@ApiModelProperty("转正时间")	
    @QueryField(name = "转正时间", type = Type.Date)	
    private String regularTime;	
    /**	
     * 出生日期	
     */	
	@ApiModelProperty("出生日期")	
    @QueryField(name = "出生日期", type = Type.Date)	
    private String birthday;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.MultiSelect, option = {"0", "在职", "1", "离职", "2", "试用", "3", "实习", "4", "待审核"})	
    private String status;	
	
    /**	
     * 用户编号	
     */	
    private String departmantCode;	
	
    private String trainingId;	
	
    private String deptName;	
	
    private String jobsName;	
	
    private List<String> deptCodeList;	
}	
