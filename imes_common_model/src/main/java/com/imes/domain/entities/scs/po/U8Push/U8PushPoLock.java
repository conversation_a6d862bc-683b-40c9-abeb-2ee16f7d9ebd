package com.imes.domain.entities.scs.po.U8Push;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


@Data
@ApiModel(value = "U8采购订单加锁解锁")
public class U8PushPoLock implements Serializable {

    @ApiModelProperty(value = "采购单号")
    private String docno;

    @ApiModelProperty(value = "业务类型")
    private String cbustype;

    @ApiModelProperty(value = "数据类型")
    private String doctype;

    @ApiModelProperty(value = "操作人")
    private String operator;

    private List<U8PushPoLockRows> rows;

}
