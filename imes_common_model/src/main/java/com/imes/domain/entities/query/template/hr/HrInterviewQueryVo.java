package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 面试管理(HrInterview)	
 *	
 * <AUTHOR> z	
 * @since 2022-12-20 16:12:51	
 */	
@Data	
@ApiModel("面试管理")	
@QueryModel(name = "0686",	
            remark = "面试管理",	
            alias = "interview",	
            searchApi = "/api/hr/interview/query")	
public class HrInterviewQueryVo  extends BaseModel  {	
	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称" )	
    private String id;	
    /**	
     * 姓名	
     */	
	@ApiModelProperty("姓名")	
    @QueryField(name = "姓名" )	
    private String name;	
    /**	
     * 岗位编码	
     */	
	@ApiModelProperty("岗位编码")	
    @QueryField(name = "岗位编码", show = false )	
    private String jobsId;	
    /**	
     * 面试时间	
     */	
	@ApiModelProperty("面试时间")	
    @QueryField(name = "面试时间", type = Type.Date)	
    private String interviewTime;	
    /**	
     * 预约时间	
     */	
	@ApiModelProperty("预约时间")	
    @QueryField(name = "预约时间", type = Type.Date)	
    private String appointmentTime;	
    /**	
     * 负责人	
     */	
	@ApiModelProperty("负责人")	
    @QueryField(name = "负责人", show = false )	
    private String responsibleUser;	
    /**	
     * 录用意向	
     */	
	@ApiModelProperty("录用意向")	
    @QueryField(name = "录用意向" )	
    private String intention;	
    /**	
     * 面试类型	
     */	
	@ApiModelProperty("面试类型")	
    @QueryField(name = "面试类型", type = Type.Select, dictOption = "HR_INTERVIEW_TYPE")	
    private String type;	
    /**	
     * 面试状态	
     */	
	@ApiModelProperty("面试状态")	
    @QueryField(name = "面试状态", type = Type.Select, dictOption = "HR_INTERVIEW_STATUS")	
    private String status;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, format = "yyyy-MM-dd HH:mm:ss")	
    private String createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", show = false  )	
    private String createBy;	
	
	@ApiModelProperty("岗位名称")	
    @QueryField(name = "岗位名称", show = false )	
    private String jobsName;	
	@ApiModelProperty("负责人名称")	
    @QueryField(name = "负责人名称", show = false )	
    private String responsibleUserName;	
}	
	
