package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.AllArgsConstructor;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class FirstFinalQualityBadCountReportVO implements Serializable {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 检验编号	
     */	
	@ApiModelProperty("检验编号")	
    private String inspectionCode;	
	
    /**	
     * 检验时间	
     */	
	@ApiModelProperty("检验时间")	
    private Date inspectionTime;	
	
    /**	
     * 检验人	
     */	
	@ApiModelProperty("检验人")	
    private String inspector;	
	
    /**	
     * 检验数量	
     */	
	@ApiModelProperty("检验数量")	
    private int inspectionNum;	
	
    /**	
     * 检验合格数	
     */	
	@ApiModelProperty("检验合格数")	
    private int inspectionGoodQty;	
	
    /**	
     * 检验不合格数	
     */	
	@ApiModelProperty("检验不合格数")	
    private int inspectionBadQty;	
	
    /**	
     * 检验是否合格	
     */	
	@ApiModelProperty("检验是否合格")	
    private String isQualified;	
	
    /**	
     * 排产单号	
     */	
	@ApiModelProperty("排产单号")	
    private String pcNo;	
	
    /**	
     * 生产单号	
     */	
	@ApiModelProperty("生产单号")	
    private String ppNo;	
	
	
    private static final long serialVersionUID = 1L;	
}	
