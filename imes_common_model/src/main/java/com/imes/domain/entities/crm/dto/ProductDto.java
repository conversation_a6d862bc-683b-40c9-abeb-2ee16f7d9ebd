package com.imes.domain.entities.crm.dto;

import com.fasterxml.jackson.annotation.JsonAnyGetter;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import com.imes.domain.entities.crm.dto.ex.BaseDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

/**
 * (CrmProduct)实体类
 *
 * <AUTHOR>
 * @since 2022-02-11 16:57:03
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ProductDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 964861104208101345L;
    /**
    * id
    */
    private String id;
    /**
    * 产品编号
    */
    @NotBlank(message = "产品编号不能为空")
    @Pattern(regexp = "^\\w+$",message = "产品编号只能由英文、数字、下划线组成")
    private String code;
    /**
    * 产品名称
    */
    @NotBlank(message = "产品名称不能为空")
    private String name;
    /**
    * 是否启用(1:启用，0:禁用)
    */
    private Boolean enable;
    /**
    * 分类
    */
    @NotBlank(message = "产品分类不能为空")
    private String classify;
    /**
    * 销售开始日期
    */
    private LocalDate salesStartDate;
    /**
    * 销售结束日期
    */
    private LocalDate salesEndDate;
    /**
     * 停售
     */
    private Boolean stopSale;
    /**
    * 支持开始日期
    */
    private LocalDate supportStartDate;
    /**
    * 支持结束日期
    */
    private LocalDate supportEndDate;
    /**
     * 停止支持
     */
    private Boolean stopSupport;
    /**
    * 价格
    */
    @Max(value = 99999999, message = "价格不能超过99999999")
    @Min(value = 0, message = "价格不能小于0")
    private BigDecimal price;
    /**
    * 佣金比例
    */
    @Max(value = 100, message = "佣金比例不能大于100")
    @Min(value = 0, message = "佣金比例不能小于0")
    private Integer commissionRate;
    /**
    * 税率
    */
    @Max(value = 99, message = "税率不能大于99")
    @Min(value = 0, message = "税率不能小于0")
    private BigDecimal taxRate;
    /**
    * 单位
    */
    private String unit;
    /**
    * 库存
    */
    @Max(value = 99999999, message = "库存不能超过99999999")
    @Min(value = 0, message = "库存不能小于0")
    private Integer inventoryQuantity;
    /**
    * 备注
    */
    private String remarks;
    /**
     * 商机id
     */
    private String businessId;
    /**
     * 自定义字段
     */
    private Map<String, Object> custom = new HashMap<>();
    @JsonAnyGetter
    public Map<String, Object> getCustom() {
        return custom;
    }
    @JsonAnySetter
    public void setCustom(String name, Object value) {
        this.custom.put(name, value);
    }

}