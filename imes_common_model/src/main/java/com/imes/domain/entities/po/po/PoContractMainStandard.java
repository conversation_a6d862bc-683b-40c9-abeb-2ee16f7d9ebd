package com.imes.domain.entities.po.po;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;

/**
 * 采购合同(PoContractMainStandard)实体类
 *
 * <AUTHOR>
 * @since 2023-11-14 10:10:55
 */
@Data
@ApiModel(value = "采购合同主单")
public class PoContractMainStandard implements Serializable {
    private static final long serialVersionUID = 492673389357245019L;

    private String id;

    @ApiModelProperty(value = "合同编号")
    private String soNo;

    @ApiModelProperty(value = "合同名称")
    private String soName;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "采购部门编码")
    private String demandDepCode;

    @ApiModelProperty(value = "采购部门")
    private String demandDepName;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商")
    private String supplierName;

    @ApiModelProperty(value = "客户联系人")
    private String customerLinkPerson;

    @ApiModelProperty(value = "客户联系人姓名")
    private String customerLinkPersonName;

    @ApiModelProperty(value = "客户联系人方式")
    private String customerLinkPhone;

    @ApiModelProperty(value = "采购员编码")
    private String salesPersonCode;

    @ApiModelProperty(value = "采购员")
    private String salesPersonName;

    @ApiModelProperty(value = "有效开始日期")
    private Date beginDate;

    @ApiModelProperty(value = "有效截止日期")
    private Date endDate;

    @ApiModelProperty(value = "合同有效天数")
    private Integer periodTime;

    @ApiModelProperty(value = "单据状态 10-已录入,20-审核中,30-已审核")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

}

