package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;


@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum EvaluationCycleEnum {
    JANUARY(1, "一月"),
    FEBRUARY(2, "二月"),
    MARCH(3, "三月"),
    APRIL(4, "四月"),
    MAY(5, "五月"),
    JUNE(6, "六月"),
    JULY(7, "七月"),
    AUGUST(8, "八月"),
    SEPTEMBER(9, "九月"),
    OCTOBER(10, "十月"),
    NOVEMBER(11, "十一月"),
    DECEMBER(12, "十二月"),
    FIRST_QUARTER(13, "第一季度"),
    SECOND_QUARTER(14, "第二季度"),
    THIRD_QUARTER(15, "第三季度"),
    FOURTH_QUARTER(16, "第四季度"),
    FIRST_HALF_YEAR(17, "上半年"),
    SECOND_HALF_YEAR(18, "下半年"),
    ANNUAL(19, "全年");

    @EnumValue
    private final Integer cycle;
    private final String cycleStr;

    EvaluationCycleEnum(Integer cycle, String cycleStr) {
        this.cycle = cycle;
        this.cycleStr = cycleStr;
    }

    public Integer getCycle() {
        return cycle;
    }

    public String getCycleStr() {
        return cycleStr;
    }

    /**
     * 枚举反序列话调用该方法
     *
     * @param jsonNode
     * @return
     */
    @JsonCreator
    public static EvaluationCycleEnum des(final JsonNode jsonNode) {
        int cycle = jsonNode.isInt() ? jsonNode.asInt() : Optional.ofNullable(jsonNode.get("cycle")).map(JsonNode::asInt).orElse(-1);
        if (cycle == -1) {
            return null;
        }
        return EvaluationCycleEnum.match(cycle);
    }

    public static EvaluationCycleEnum match(Integer cycle) {
        return Arrays.stream(EvaluationCycleEnum.values())
                .filter(e -> e.getCycle().equals(cycle))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("考评周期错误"));
    }

    public static List<EvaluationCycleEnum> getByPeriod(PeriodEnum periodEnum) {
        // 月度
        if (periodEnum == PeriodEnum.MONTHLY) {
            return Lists.newArrayList(
                    EvaluationCycleEnum.JANUARY,
                    EvaluationCycleEnum.FEBRUARY,
                    EvaluationCycleEnum.MARCH,
                    EvaluationCycleEnum.APRIL,
                    EvaluationCycleEnum.MAY,
                    EvaluationCycleEnum.JUNE,
                    EvaluationCycleEnum.JULY,
                    EvaluationCycleEnum.AUGUST,
                    EvaluationCycleEnum.SEPTEMBER,
                    EvaluationCycleEnum.OCTOBER,
                    EvaluationCycleEnum.NOVEMBER,
                    EvaluationCycleEnum.DECEMBER
            );
        }
        // 季度
        if (periodEnum == PeriodEnum.QUARTER) {
            return Lists.newArrayList(
                    EvaluationCycleEnum.FIRST_QUARTER,
                    EvaluationCycleEnum.SECOND_QUARTER,
                    EvaluationCycleEnum.THIRD_QUARTER,
                    EvaluationCycleEnum.FOURTH_QUARTER
            );
        }
        // 半年
        if (periodEnum == PeriodEnum.HALF_YEAR) {
            return Lists.newArrayList(
                    EvaluationCycleEnum.FIRST_HALF_YEAR,
                    EvaluationCycleEnum.SECOND_HALF_YEAR
            );
        }
        // 年度
        if (periodEnum == PeriodEnum.YEAR) {
            return Lists.newArrayList(
                    EvaluationCycleEnum.ANNUAL
            );
        }

        return null;
    }

}
