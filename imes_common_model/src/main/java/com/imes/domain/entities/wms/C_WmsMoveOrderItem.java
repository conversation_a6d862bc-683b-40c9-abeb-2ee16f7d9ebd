package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsMoveOrderItem implements Serializable {	
    /**	
     * 	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 移库单主键	
     */	
	@ApiModelProperty("移库单主键")	
    private String moId;	
	
    /**	
     * 移库单号	
     */	
	@ApiModelProperty("移库单号")	
    private String moCode;	
	
    /**	
     * 移库明细单号	
     */	
	@ApiModelProperty("移库明细单号")	
    private String moItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 条码	
     */	
	@ApiModelProperty("条码")	
    private String barCode;	
	
    /**	
     * 移动数量	
     */	
	@ApiModelProperty("移动数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 可移动数量	
     */	
	@ApiModelProperty("可移动数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 主单位	
     */	
	@ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 下架库位	
     */	
	@ApiModelProperty("下架库位")	
    private String outBinCode;	
	
    /**	
     * 上架库位	
     */	
	@ApiModelProperty("上架库位")	
    private String inBinCode;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    /**	
     * 退回数量	
     */	
	@ApiModelProperty("退回数量")	
    private BigDecimal returnQty;	
	
    /**	
     * 亲亲食品定制字段 移动类型	
     */	
	@ApiModelProperty("亲亲食品定制字段 移动类型")	
    private String moveType;	
	
    @ApiModelProperty("仓库编码")	
    private String whCode;	
	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    @ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty("包装数量")	
    private BigDecimal packQty;	
	
    @ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
    @ApiModelProperty("箱码")	
    private String boxNo;	
	
    private static final long serialVersionUID = 1L;	
}	
