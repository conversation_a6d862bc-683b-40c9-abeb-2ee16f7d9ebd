package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.pm.typeHandler.WalletListTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.time.Year;
import java.util.List;

/**
 * (PerEvaluationPlan)实体类
 *
 * <AUTHOR>
 * @since 2021-11-11 14:17:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "per_evaluation_plan", resultMap = "BaseResultMap")
public class EvaluationPlan implements Serializable {
    private static final long serialVersionUID = -29489136383114836L;
    /**
     * id
     */
    private String id;
    /**
     * 被考评人工号
     */
    private String employeeCode;
    /**
     * 考评类型
     */
    private Integer type;
    /**
     * 部门编号
     */
    @TableField(exist = false)
    private String departCode;
    /**
     * 考评目标
     */
    @TableField(exist = false)
    private List<EvaluationTarget> targetList;
    /**
     * 被考评人姓名
     */
    @TableField(exist = false)
    private String employeeName;
    /**
     * 是否在职
     */
    @TableField(exist = false)
    private Boolean isWorker;
    /**
     * 上级领导工号
     */
    @TableField(exist = false)
    private String leaderEmployeeCode;
    /**
     * 上级领导姓名
     */
    @TableField(exist = false)
    private String leaderEmployeeName;
    /**
     * 考评周期(0：月度，1：季度，2：年度)
     */
    private Integer cycle;
    /**
     * 考评年份
     */
    private Year year;
    /**
     * 考评开始日期
     */
    private LocalDate startDate;
    /**
     * 考评结束日期
     */
    private LocalDate endDate;
    /**
     * 得分
     */
    @TableField(exist = false)
    private Integer score;
    /**
     * 员工举证
     */
    private String employeeProof;
    /**
     * 主管举证
     */
    private String supervisorProof;
    /**
     * 反馈内容
     */
    private String feedbackContent;
    /**
     * 公司评价
     */
    private String companyEvaluation;

    /**
     * 改善计划
     */
    @TableField(typeHandler = WalletListTypeHandler.class)
    private List<ImprovementProgram> improvementProgram;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;
    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private LocalDateTime updateTime;
    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill = FieldFill.UPDATE)
    private String updatedBy;
    /**
     * 流程步骤
     */
    private String processSteps;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 策略模板id
     */
    private String schemeId;
}