package com.imes.domain.entities.query.template.po;	
	
import io.swagger.annotations.ApiModel;	
	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.*;	
	
import lombok.Data;

import java.util.List;


@Data	
@ApiModel("采购申请")	
@QueryModel(	
        name = "0633",	
        remark = "采购申请",	
        searchApi = "/api/po/poPurchaseRequest/queryList",	
        alias = {"po_purchase_request", "po_purchase_request_detail"},	
        pushApi = PushApi.PoPurchaseRequestQueryVo,	
        link = {"1195","1212"},	
        customExp = true,	
        showMode = true,	
        backup = true,
        openapi = true,
        resume = "requestNo",
        auth = Auth.ALL,	
        authTenant = true	
)	
public class PoPurchaseRequestQueryVo extends BaseModel {	
	
	@ApiModelProperty("id")	
    @QueryField(name = "id", show = false)	
    private String id;	
	
	@ApiModelProperty("采购申请单号")	
    @QueryField(name = "采购申请单号", width = 150)	
    private String requestNo;	
	
	@ApiModelProperty("单据类型")	
    @QueryField(name = "单据类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_TYPE", width = 100)	
    private String billType;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_STATUS", required = true, width = 100)	
    private String status;	
	
	@ApiModelProperty("业务状态")	
    @QueryField(name = "业务状态", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_BUSINESS_STATUS", value = "10", required = true, width = 100)	
    private String businessStatus;	
	
	@ApiModelProperty("申请部门")	
    @QueryField(name = "申请部门", alias = "co_department.depart_name", level = Level.Main)	
    @EditField(required = true)	
    private String requestDepartmentName;	
	
	@ApiModelProperty("申请人")	
    @QueryField(name = "申请人", alias = "pe_user.user_name", level = Level.Main)	
    @EditField(required = true)	
    private String requestUserName;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "ppc_material", width = 100)	
    private String materialName;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "ppc_material", width = 100)	
    private String specification;	
	
	@ApiModelProperty("型号")	
    @QueryField(name = "型号", alias = "ppc_material.material_marker", width = 100)	
    private String modelNumber;	
	
	@ApiModelProperty("即时库存")	
    @QueryField(name = "即时库存",  query = false, sort = false, width = 100,level = Level.Detail)	
    private String availableQty;	
	
	@ApiModelProperty("申请数量")	
    @QueryField(name = "申请数量", type = Type.Number, alias = "po_purchase_request_detail", width = 100)	
    private String requestQty;	
	
	@ApiModelProperty("采购单位")	
    @QueryField(name = "采购单位", alias = "po_purchase_request_detail", width = 100,type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String unit;	
	
	@ApiModelProperty("申请部门编码")	
    @QueryField(name = "申请部门编码", width = 0)	
    @EditField(readonly = true)	
    private String requestDepartmentCode;	
	
	@ApiModelProperty("申请人工号")	
    @QueryField(name = "申请人工号", width = 0)	
    @EditField(readonly = true)	
    private String requestUserCode;	
	

	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.DateTime, order = OrderBy.DESC, width = 130)
    private String createOn;	
	
	@ApiModelProperty("申请日期")	
    @QueryField(name = "申请日期", type = Type.Date, format = "yyyy-MM-dd", width = 130)	
    @EditField(required = true)	
    private String requestDate;	
	
	@ApiModelProperty("申请到货日期")	
    @QueryField(name = "申请到货日期", type = Type.Date, alias = "po_purchase_request_detail", format = "yyyy-MM-dd", width = 130)	
    private String requestArriveDate;	
	
	@ApiModelProperty("行号")	
    @QueryField(name = "行号", alias = "po_purchase_request_detail", width = 80)	
    private String lineNo;	
	
	@ApiModelProperty("变更原因")	
    @QueryField(name = "变更原因")	
    private String changeRemarks;



    @ApiModelProperty("物料编码")
    @QueryField(name = "物料编码", alias = "po_purchase_request_detail")	
    private String materialCode;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性", alias = "po_purchase_request_detail")	
    private String skuCode;	
	
  //  @QueryField(name = "关联数量", alias = "po_purchase_request_detail", type = Type.Number, query = false, sort = false)	
    private String relationQty;	
	
	@ApiModelProperty("关联订单数量")	
    @QueryField(name = "关联订单数量", alias = "po_purchase_request_detail", type = Type.Number)	
    private String relationPoQty;	
	
	@ApiModelProperty("成品辅助属性")	
    @QueryField(name = "成品辅助属性", alias = "po_purchase_request_detail")	
    private String productSkuCode;	
	
	@ApiModelProperty("子单业务状态")	
    @QueryField(name = "子单业务状态", type = Type.MultiSelect, option = {"10", "正常", "20", "已关闭"}, alias = "po_purchase_request_detail.business_status")	
    private String detailBusinessStatus;	
	
	@ApiModelProperty("基础单位")	
    @QueryField(name = "基础单位", alias = "po_purchase_request_detail",type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String baseUnit;	
	
	@ApiModelProperty("基础单位数量")	
    @QueryField(name = "基础单位数量", alias = "po_purchase_request_detail", type = Type.Number)	
    private String baseUnitQty;	
	
	@ApiModelProperty("源单类型")	
    @QueryField(name = "源单类型", alias = "po_purchase_request_detail.business_source", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")	
    private String detailBusinessSource;	
	
	@ApiModelProperty("来源方式")	
    @QueryField(name = "来源方式", alias = "po_purchase_request_detail", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_WAY")	
    private String businessWay;	
	
	@ApiModelProperty("源单单号")	
    @QueryField(name = "源单单号", alias = "po_purchase_request_detail")	
    private String businessNo;	
	
	@ApiModelProperty("源单行号")	
    @QueryField(name = "源单行号", alias = "po_purchase_request_detail")	
    private String businessLineNo;	
	
	@ApiModelProperty("MRP计算号")	
    @QueryField(name = "MRP计算号", alias = "po_purchase_request_detail.calc_no")	
    private String calcNo;	
	
	@ApiModelProperty("子单备注")	
    @QueryField(name = "子单备注", alias = "po_purchase_request_detail.remarks")	
    private String detailRemarks;	
	
	@ApiModelProperty("源申请部门编码-名称")	
    @QueryField(name = "源申请部门编码-名称", alias = "po_purchase_request_detail")	
    private String businessDepartmentName;	
	
	@ApiModelProperty("源申请人编码-名称")	
    @QueryField(name = "源申请人编码-名称", alias = "po_purchase_request_detail")	
    private String businessUserName;	
	
	@ApiModelProperty("剩余数量")	
    @QueryField(name = "剩余数量", alias = ".(po_purchase_request_detail.request_qty - ifnull(po_purchase_request_detail.relation_po_qty,0))", type = Type.Number)	
    private String remainQty;	
	
	@ApiModelProperty("提交时间")	
    @QueryField(name = "提交时间", type = Type.DateTime)
    private String submitOn;	
	
	@ApiModelProperty("终审时间")	
    @QueryField(name = "终审时间", type = Type.DateTime)
    private String finalHearOn;	
	
	@ApiModelProperty("审批意见")	
    @QueryField(name = "审批意见")	
    private String approveRemark;

    @ApiModelProperty("备注")
    @QueryField(name = "备注", width = 130)
    private String remarks;

    //   @QueryField(name = "源单类型", type = Type.MultiSelect, dictOption = "PO_PURCHASE_REQUEST_SOURCE_TYPE")	
    private String businessSource;	
	@ApiModelProperty("创建人")	
    @QueryField(name = "创建人", show = false)	
    private String createBy;	
	
	@ApiModelProperty("更新时间")	
    @QueryField(name = "更新时间", show = false, type = Type.Date)	
    private String updateOn;	
	
	@ApiModelProperty("更新人")	
    @QueryField(name = "更新人", show = false)	
    private String updateBy;	
	
	@ApiModelProperty("detailId")	
    @QueryField(name = "detailId", show = false, alias = "po_purchase_request_detail.id")	
    private String detailId;

    private String processId;

    @ApiModelProperty("申请部门负责人")
    @QueryField(name = "申请部门负责人", show = false, flowableApprovalUser = true)
    private List<String> userCodes;
}	
