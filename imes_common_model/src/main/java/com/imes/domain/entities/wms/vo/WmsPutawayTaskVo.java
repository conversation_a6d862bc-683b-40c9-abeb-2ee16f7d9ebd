package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.wms.WmsPutawayTask;	
import lombok.Data;	
	
@Data	
public class WmsPutawayTaskVo extends WmsPutawayTask {	
	
    /**	
     *  进位方式	
     */	
	@ApiModelProperty(" 进位方式")
    private Byte carryMode;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")
    private Byte precisionDigit;	
	
    private String materialMarker;	
	
    private String specification;	
	
    /**	
     * 关联主单号	
     */	
	@ApiModelProperty("关联主单号")
    private String receiptCode;	
	
    /**	
     * 关联明细单号	
     */	
	@ApiModelProperty("关联明细单号")
    private String receiptItemCode;	
	
}	
