package com.imes.domain.entities.po.po;

import java.math.BigDecimal;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "采购申请单明细")
public class PoPurchaseRequestDetail implements Serializable {

    private static final long serialVersionUID = -79633273113643445L;

    @JSONField(name = "子单id")
    private String id;

    @JSONField(name = "申请单号")
    @ApiModelProperty(value = "申请单号")
    private String requestNo;

    @JSONField(name = "行号")
    @ApiModelProperty(value = "行号")
    private Integer lineNo;

    @JSONField(name = "物料编码")
    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @JSONField(name = "物料名称")
    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @JSONField(name = "规格")
    @ApiModelProperty(value = "规格")
    private String specification;

    @JSONField(name = "型号")
    @ApiModelProperty(name = "型号")
    private String modelNumber;

    @JSONField(name = "申请数量")
    @ApiModelProperty(value = "申请数量")
    private BigDecimal requestQty;

    @JSONField(name = "即时库存数量")
    @ApiModelProperty(value = "即时库存数量")
    private BigDecimal availableQty = BigDecimal.ZERO;

    @JSONField(name = "申请数量单位")
    @ApiModelProperty(value = "申请数量单位")
    private String unit;

    @JSONField(name = "关联数量")
    @ApiModelProperty(value = "关联数量")
    private BigDecimal relationQty;

    @JSONField(name = "业务状态 10-正常 20-已关闭")
    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @JSONField(name = "基础单位")
    @ApiModelProperty(value = "基础单位")
    private String baseUnit;

    @JSONField(name = "基础单位数量")
    @ApiModelProperty(value = "基础单位数量")
    private BigDecimal baseUnitQty;

    @JSONField(name = "申请到货日期")
    @ApiModelProperty(value = "申请到货日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    private Date requestArriveDate;

    @JSONField(name = "来源单据类型")
    @ApiModelProperty(value = "来源单据类型")
    private String businessSource;

    @JSONField(name = "来源单据号")
    @ApiModelProperty(value = "来源单据号")
    private String businessNo;

    @JSONField(name = "源单行号")
    @ApiModelProperty(value = "源单行号")
    private String businessLineNo;

    @JSONField(name = "来源方式")
    @ApiModelProperty(value = "来源方式")
    private String businessWay;

    @JSONField(name = "需求运算号")
    @ApiModelProperty(value = "需求运算号")
    private String calcNo;

    @JSONField(name = "辅助属性")
    @ApiModelProperty(value = "辅助属性")
    private String skuCode;

    @JSONField(name = "成品辅助属性")
    @ApiModelProperty(value = "成品辅助属性")
    private String productSkuCode;

    @JSONField(name = "源申请部门")
    @ApiModelProperty(value = "源申请部门")
    private String businessDepartmentName;

    @JSONField(name = "源申请人")
    @ApiModelProperty(value = "源申请人")
    private String businessUserName;

    @JSONField(name = "创建时间")
    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @JSONField(name = "创建人")
    @ApiModelProperty(value = "创建人")
    private String createBy;

    @JSONField(name = "更新时间")
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @JSONField(name = "更新人")
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    //更新保存标识
    @ApiModelProperty(value = "update-更新，add-新增，delete-删除")
    private String updateFlag;

    @JSONField(name = "子弹备注")
    @ApiModelProperty(value = "子弹备注")
    private String remarks;

    @JSONField(name = "自定义字段")
    @ApiModelProperty(value = "自定义字段")
    private String custom;

    //剩余数量
    private BigDecimal remainQty;

    //精度位数
    @TableField(exist = false)
    private String requestQtyPrecision;

    // 缺量
    private BigDecimal lackQty;

    // 总用量
    private BigDecimal needQty;

    // 计划单号
    private String ppNo;

    // 库存可用数
    private BigDecimal stockAvailableQty;

    // 基础单位
    private String primaryUnit;

    private String unitName;

    private String baseUnitName;

    private BigDecimal relationPoQty;

    @TableField(exist = false)
    private String detailBusinessSource;

    @TableField(exist = false)
    private String detailRemarks;
}