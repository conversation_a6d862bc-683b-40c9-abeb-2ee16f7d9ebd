package com.imes.domain.entities.query.plugin.imports.template.wms;

import com.imes.domain.entities.query.plugin.imports.BaseModel;
import com.imes.domain.entities.query.plugin.imports.ImportField;
import com.imes.domain.entities.query.plugin.imports.ImportModel;
import lombok.Data;

@Data
@ImportModel(
        name = "其他入库导入模板",
        modelName = "0263")
public class WmsOtherInboundImportVo extends BaseModel {

    @ImportField(name = "物料编码", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialCode;

    @ImportField(name = "物料名称", required = true, remark = "必填\n参考【物料基本信息】", maxLength = 255)
    private String materialName;

    @ImportField(name = "物料型号", remark = "参考【物料基本信息】", maxLength = 255)
    private String materialMarker;

    @ImportField(name = "规格", remark = "参考【物料基本信息】", maxLength = 255)
    private String specification;

    @ImportField(name = "物料辅助属性", maxLength = 500)
    private String skuCode;

    @ImportField(name = "批次", remark = "参考【物料基本信息】，若启用了【是否启用批次管理】，则必填", maxLength = 40)
    private String batch;

    @ImportField(name = "仓库", required = true, remark = "必填\n参考【仓库信息】", maxLength = 40)
    private String whCode;

    @ImportField(name = "库位", required = true, remark = "必填\n参考【库位信息】", maxLength = 40)
    private String binCode;

    @ImportField(name = "库存单位", required = true, remark = "必填\n参考【物料基本信息】，若启用了【是否序列号管理】，则库存单位必须等于物料基本单位", maxLength = 40)
    private String packCodeUnit;

    @ImportField(name = "入库数量", required = true, remark = "必填\n若启用了【是否序列号管理】入库数量必须填1", isNumber = true, maxNumber = "999999999.999999")
    private String packApplyQty;

    @ImportField(name = "SN码", remark = "参考【物料基本信息】，若启用了【是否序列号管理】，则必填", maxLength = 40)
    private String serialNo;

    @ImportField(name = "供应商", maxLength = 50)
    private String supplierCode;

    @ImportField(name = "供应商批次", maxLength = 40)
    private String batchSupplier;

    @ImportField(name = "物料单价", isNumber = true, maxNumber = "999999999.999999", remark = "不必填")
    private String unitPrice;
}
