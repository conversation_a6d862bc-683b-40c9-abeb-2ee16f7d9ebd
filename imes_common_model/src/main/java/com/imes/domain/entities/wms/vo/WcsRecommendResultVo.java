package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
@Data	
public class WcsRecommendResultVo {	
	
    /**	
     * 设备编码	
     */	
    @ApiModelProperty("设备编码")	
    private String deviceCode;	
	
    /**	
     * 库位排	
     */	
    @ApiModelProperty("库位排")	
    private String binRow;	
	
    /**	
     * 库位层	
     */	
    @ApiModelProperty("库位层")	
    private String binLayer;	
	
    /**	
     * 库位列	
     */	
    @ApiModelProperty("库位列")	
    private String binKind;	
	
    /**	
     * 最终点放货位排	
     */	
    @ApiModelProperty("最终点放货位排")	
    private String destRow;	
	
    /**	
     * 最终点放货位层	
     */	
    @ApiModelProperty("最终点放货位层")	
    private String destLayer;	
	
    /**	
     * 最终点放货位列	
     */	
    @ApiModelProperty("最终点放货位列")	
    private String destKind;	
	
    /**	
     * 1.条码已读取,2.正常可进入,3.托盘条码为空或格式有误,4.尺检数据为空或无法解析,5.重量不匹配,6.计划匹配失败,7.物料编码不存在	
     */	
    @ApiModelProperty("异常码")	
    private String errorCode;	
	
    /**	
     * 堆垛机编码	
     */	
    @ApiModelProperty("堆垛机编码")	
    private Integer stackerId;	
	
    /**	
     * 输送线起始线体编码	
     */	
    @ApiModelProperty("输送线起始线体编码")	
    private Integer sourceId;	
	
    /**	
     * 输送线终点线体编码	
     */	
    @ApiModelProperty("输送线终点线体编码")	
    private Integer targetId;	
	
}	
