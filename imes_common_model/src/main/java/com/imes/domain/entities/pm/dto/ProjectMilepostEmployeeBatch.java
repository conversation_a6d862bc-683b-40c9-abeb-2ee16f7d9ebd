package com.imes.domain.entities.pm.dto;

import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<里程碑参与人批量添加>>
 * @company 捷创智能技术有限公司
 * @create 2022-06-10 11:19
 */
@ApiModel("里程碑参与人批量添加")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectMilepostEmployeeBatch {
    /**
     * 里程碑id
     */
    @NotEmpty(message = "里程碑不能为空")
    private List<String> milepostIds;
    /**
     * 参与人工号
     */
    @NotBlank(message = "参与人不能为空")
    private String userCode;
    /**
     * 项目角色
     */
    private String job;
    /**
     * 计划工时
     */
    @NotNull(message = "计划工时不能为空")
    private BigDecimal workHour;
    /**
     * 负责人
     */
    private Boolean principal;
    /**
     * 任务内容
     */
    @NotBlank(message = "任务内容不能为空")
    private String task;
}
