package com.imes.domain.entities.query.template.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("极简出库")	
@QueryModel(	
        name = "4005",	
        remark = "极简出库",	
        searchApi = "/wms/outStorage/findOutboundMinimalist",	
        alias = {"wms_storage_out_bill", "wms_storage_out_bill_item"},	
        showMode = true,
        openapi = true,
        auth = Auth.ALL,	
        authTenant = true)	
public class OutboundMinimalistQueryVo extends BaseModel {	
	
	@ApiModelProperty("出库单号")	
    @QueryField(name = "出库单号")	
    private String storageOutCode;	
	
	@ApiModelProperty("明细单号")	
    @QueryField(name = "明细单号", alias = "wms_storage_out_bill_item")	
    @EditField(show = false)	
    private String storageOutItemCode;	
	
	@ApiModelProperty("出库类型")	
    @QueryField(name = "出库类型", type = Type.Select, dictOption = "OUTBOUND_TYPE")	
    @EditField(required = true)	
    private String receiptType;	
	
	@ApiModelProperty("关联单号")	
    @QueryField(name = "关联单号")	
    private String receiptCode;	
	
	@ApiModelProperty("出库时间")	
    @QueryField(name = "出库时间", type = Type.Date)	
    @EditField(required = true)	
    private String approveOn;	
	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间", type = Type.Date, order = OrderBy.DESC, show = false)	
    @EditField(show = false)	
    private String createOn;	
	
	@ApiModelProperty("仓库")	
    @QueryField(name = "仓库", type = Type.Select, sqlOption ="select warehouse_code as value ,warehouse_name as label from sys_wh_warehouse")	
    @EditField(required = true)	
    private String whCode;	
	
	@ApiModelProperty("备注")	
    @QueryField(name = "备注")	
    @EditField(span = 20)	
    private String remark;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "wms_storage_out_bill_item")	
    @EditField(readonly = true)	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "mat")	
    @EditField(readonly = true)	
    private String materialName;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "mat")	
    @EditField(readonly = true)	
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "mat")	
    @EditField(readonly = true)	
    private String specification;	
	
	@ApiModelProperty("可用数量")	
    @QueryField(name = "可用数量")	
    @EditField(readonly = true)	
    private String availableQty;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "wms_storage_out_bill_item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    @EditField(readonly = true)	
    private String unit;	
	
	@ApiModelProperty("出库数量")	
    @QueryField(name = "出库数量", alias = "wms_storage_out_bill_item")	
    @EditField(required = true)	
    private String orderQty;	
	
	@ApiModelProperty("出库单价")	
    @QueryField(name = "出库单价", alias = "wms_storage_out_bill_item")	
    @EditField(required = true)	
    private String unitPrice;	
	
	@ApiModelProperty("明细备注")	
    @QueryField(name = "明细备注", alias = "wms_storage_out_bill_item.remark")	
    @EditField(readonly = true)	
    private String detailRemark;	
	
	@ApiModelProperty("操作员工号")	
    @QueryField(name = "操作员工号")	
    @EditField(show = false)	
    private String applyBy;	
	
	@ApiModelProperty("操作员名称")	
    @QueryField(name = "操作员名称", alias = "u1.user_name")	
    @EditField(show = false)	
    private String applyName;	
	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")	
    @EditField(show = false)	
    private String status;	
	
	@ApiModelProperty("客户编码")	
    @QueryField(name = "客户编码")	
    @EditField(show = false)	
    private String customerCode;	
	
	@ApiModelProperty("客户名称")	
    @QueryField(name = "客户名称", alias = "cus", level = Level.Main)	
    private String customerName;	
	
}	
