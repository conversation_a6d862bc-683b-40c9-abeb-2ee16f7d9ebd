package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-23 9:54
 */
@Getter
@Setter
public class BaseDto {
    @ApiModelProperty("第几页")
    @JsonIgnore
    private Integer pageNum;
    @ApiModelProperty("每页显示数")
    @JsonIgnore
    private Integer pageSize;
    @ApiModelProperty("字段排序")
    @JsonIgnore
    private String orderBy;
    @ApiModelProperty("创建者")
    private String createdBy;
    @ApiModelProperty("更新者")
    private String updatedBy;
    @ApiModelProperty("创建时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @ApiModelProperty("更新时间")
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /* 分组校验 */
    public @interface Create { }
    /* 分组校验 */
    public @interface Update { }

}
