package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.po.WmsAssembleItemOrder;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
@Data	
public class WmsAssembleItemOrderVo extends WmsAssembleItemOrder {	
    @ApiModelProperty(value = "库存数量")	
    private BigDecimal availableQty;	
	
    @ApiModelProperty(value = "包装库存数量")	
    private BigDecimal packAvailableQty;	
	
    @ApiModelProperty(value = "是否批次管理")	
    private String isBatch;	
}	
