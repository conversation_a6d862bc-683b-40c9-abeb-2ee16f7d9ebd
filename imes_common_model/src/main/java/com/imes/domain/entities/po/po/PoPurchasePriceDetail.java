package com.imes.domain.entities.po.po;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "采购价目明细表")
public class PoPurchasePriceDetail implements Serializable {

    private static final long serialVersionUID = 261214623365760869L;

    private String id;

    @ApiModelProperty(value = "主表id")
    private String mainId;

    @ApiModelProperty(value = "定价单号")
    private String priceNo;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "型号")
    private String materialMarker;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "单价")
    private BigDecimal singlePrice;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String isIncludeTax;

    @ApiModelProperty(value = "税率编码")
    private String taxCode;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "未税单价")
    private BigDecimal unIncludePrice;

    @ApiModelProperty(value = "含税单价")
    private BigDecimal includePrice;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "价格生效日期")
    private Date effectTime;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd")
    @ApiModelProperty(value = "价格失效日期")
    private Date uneffectTime;

    @ApiModelProperty(value = "定价数量")
    private BigDecimal batchQty;

    @ApiModelProperty(value = "数量阶梯从")
    private BigDecimal startQty;

    @ApiModelProperty(value = "数量阶梯至")
    private BigDecimal endQty;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建人名称")
    private String createName;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "状态10录入20审核中30审核通过")
    private String status;

    private String custom;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "update-更新,add-新增,delete-删除")
    private String updateFlag;

    private String detailId;

    private String unitName;

    @ApiModelProperty(value = "明细表备注")
    private String detailRemarks;
}