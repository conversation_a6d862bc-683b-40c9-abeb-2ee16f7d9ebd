package com.imes.domain.entities.po.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@ApiModel(value = "江森采购变更单-主表")
public class PoPurchaseChange implements Serializable {

    private static final long serialVersionUID = 301737237270391660L;

    private String id;

    @ApiModelProperty(value = "采购单号")
    private String purchaseNo;

    @ApiModelProperty(value = "需求版本号")
    private String versionNo;

    @ApiModelProperty(value = "采购类型")
    private String purchaseType;

    @ApiModelProperty(value = "采购单名称")
    private String purchaseName;

    @ApiModelProperty(value = "付款条件")
    private String termOfPayment;

    @ApiModelProperty(value = "币种")
    private String currency;

    @ApiModelProperty(value = "产品阶段")
    private String productStage;

    @ApiModelProperty(value = "是否含税 0是 1否")
    private String includeTax;

    @ApiModelProperty(value = "税率")
    private BigDecimal taxRate;

    @ApiModelProperty(value = "结算公司")
    private String clearingCompany;

    @ApiModelProperty(value = "供应商推送状态")
    private String pushStatus;

    @ApiModelProperty(value = "采购员工号")
    private String demandUserCode;

    @ApiModelProperty(value = "采购员名称")
    private String demandUserName;

    @ApiModelProperty(value = "制单人")
    private String operatorName;

    @ApiModelProperty(value = "关闭人")
    private String closePerson;

    @ApiModelProperty(value = "关闭时间")
    private Date closeTime;

    @ApiModelProperty(value = "采购部门")
    private String demandDepCode;

    @ApiModelProperty(value = "采购部门名称")
    private String demandDepName;

    @ApiModelProperty(value = "采购员联系方式")
    private String demandUserTel;

    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;

    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    @ApiModelProperty(value = "供应商联系人")
    private String supplierLinkMan;

    @ApiModelProperty(value = "供应商联系方式")
    private String supplierLinkPhone;

    @ApiModelProperty(value = "采购金额")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "下单日期")
    private Date orderDate;

    @ApiModelProperty(value = "实际交货日期")
    private Object actualDeliveryDate;

    @ApiModelProperty(value = "单据状态 10-已录入,20-审核中,30-已审核")
    private String status;

    @ApiModelProperty(value = "业务状态 10-正常 20-已关闭")
    private String businessStatus;

    @ApiModelProperty(value = "订单状态 10未发布 20已发布 30已完成")
    private String orderStatus;

    @ApiModelProperty(value = "发货状态 10等待发货 20部分发货 30发货完成")
    private String shipStatus;

    @ApiModelProperty(value = "结算状态 10等待结算 20部分结算 30结算完成")
    private String settlementStatus;

    @ApiModelProperty(value = "创建时间")
    private Date createOn;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "更新时间")
    private Date updateOn;

    @ApiModelProperty(value = "更新人")
    private String updateBy;

    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "erp备注")
    private String erpRemarks;

    @ApiModelProperty(value = "自定义字段")
    private String custom;

    @ApiModelProperty(value = "是否已阅")
    private String readStatus;

    @ApiModelProperty(value = "供应商提交备注")
    private String supplierRemarks;

    @ApiModelProperty(value = "供应商拒绝原因")
    private String refuseRemarks;

    @ApiModelProperty(value = "业务类型")
    private String docType;
}