package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsBatchMsg;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
public class WmsBatchMsgVo extends WmsBatchMsg {	
	
    private String isBatch;	
	
    private String nearQualityPeriod;	
	
    private String autoGenerateBatchNum;	
	
    private BigDecimal qualityPeriod;	
	
    private String qualityPeriodUnit;	
	
    private String whCode;	
	
    private String materialName;	
	
    private String specification;	
	
    private BigDecimal availableQty;	
	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
}	
