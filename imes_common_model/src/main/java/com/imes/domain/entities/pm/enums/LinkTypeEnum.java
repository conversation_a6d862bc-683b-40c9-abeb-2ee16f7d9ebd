package com.imes.domain.entities.pm.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;

public enum LinkTypeEnum {
    // 结束->开始
    FS("0", "FS"),
    // 开始->结束
    SF("3", "SF"),
    // 结束->结束
    FF("2", "FF"),
    // 开始->开始
    SS("1", "SS");

    @JsonValue
    @EnumValue
    private final String type;
    private final String typeStr;

    LinkTypeEnum(String type, String typeStr) {
        this.type = type;
        this.typeStr = typeStr;
    }

    public static LinkTypeEnum match(String type) {
        return Arrays.stream(LinkTypeEnum.values())
                .filter(e -> e.getType().equals(type))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("链接方式错误"));
    }

    public String getType() {
        return type;
    }

    public String getTypeStr() {
        return typeStr;
    }
}
