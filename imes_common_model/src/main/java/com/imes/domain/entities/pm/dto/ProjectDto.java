package com.imes.domain.entities.pm.dto;

import com.fasterxml.jackson.annotation.*;
import com.google.common.collect.Maps;
import com.imes.domain.entities.pm.dto.ex.BaseDto;
import com.imes.domain.entities.pm.enums.ApprovalStatusEnum;
import com.imes.domain.entities.pm.enums.StateEnum;
import com.imes.domain.entities.pm.po.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<>>
 * @company 捷创智能技术有限公司
 * @create 2021-01-22 11:38
 */
@ApiModel("项目实体类")
@EqualsAndHashCode(callSuper = true)
@Data
public class ProjectDto extends BaseDto implements Serializable {
    private static final long serialVersionUID = 6499602554476293567L;
    /**
     * 项目编号
     */
    @ApiModelProperty("项目编号")
    private String code;

    /**
     * 超期天数
     */
    @ApiModelProperty("超期天数")
    private Long overdueDays;

    /**
     * 完成率
     */
    @ApiModelProperty("完成率")
    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Integer progress;
    /**
     * 立项日期
     */
    @ApiModelProperty("立项日期")
    private LocalDate projectDate;
    /**
     * 立项日期范围
     */
    @ApiModelProperty("立项日期范围")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> projectDates;
    /**
     * 是否精细化
     */
    @ApiModelProperty("是否精细化")
    private Boolean refinement;
    /**
     * 计划工时
     */
    @ApiModelProperty("计划工时")
    private Integer plannedHours;
    /**
     * 实际工时
     */
    @ApiModelProperty("实际工时")
    private BigDecimal actualHours;
    /**
     * 最大工时
     */
    @ApiModelProperty("最大工时")
    private Integer maxHours;
    /**
     * 最小工时
     */
    @ApiModelProperty("最小工时")
    private Integer minHours;
    /**
     * 合同总金额
     */
    @ApiModelProperty("合同总金额")
    private BigDecimal contractAmount;
    /**
     * 开票金额
     */
    @ApiModelProperty("开票金额")
    private BigDecimal invoicedAmount;
    /**
     * 到款金额
     */
    @ApiModelProperty("到款金额")
    private BigDecimal amountReceived;
    /**
     * 产品类别
     */
    @ApiModelProperty("产品类别")
    private String productType;
    /**
     * 主商机号
     */
    @ApiModelProperty("主商机号")
    private String businessOpportunityCode;
    /**
     * 合同号列表
     */
    @ApiModelProperty("合同号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ProjectContractDto> contracts;
    /**
     * 销售合同公司
     */
    @ApiModelProperty("销售合同公司")
    private String contractCompany;
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    @NotEmpty(message = "项目名称不能为空")
    private String name;
    /**
     * 项目阶段
     */
    @ApiModelProperty("项目阶段")
    private String stage;
    /**
     * 项目类型
     */
    @ApiModelProperty("项目类型")
    private String type;

    /**
     * 项目类别
     */
    @ApiModelProperty("项目类别")
    private List<String> category;
    /**
     * 销售员
     */
    @ApiModelProperty("销售员")
    private String salesperson;
    /**
     * 项目所属企业
     */
    @ApiModelProperty("所属企业")
    @NotEmpty(message = "项目所属企业不能为空")
    private String projectEnterprise;
    /**
     * 项目参与人
     */
    @ApiModelProperty("项目参与人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ProjectEmployeeDto> participants;
    /**
     * 项目经理id
     */
    @ApiModelProperty("项目经理id")
    @NotEmpty(message = "项目经理不能为空")
    private String projectManagerId;
    /**
     * 项目经理姓名
     */
    private String managerName;
    /**
     * 项目经理
     */
    @ApiModelProperty("项目经理")
    private EmployeePo projectManager = new EmployeePo();

    public void setProjectManagerId(String projectManagerId) {
        this.projectManagerId = projectManagerId;
        if (Objects.nonNull(projectManager)) {
            projectManager.setUserCode(projectManagerId);
            projectManager.setUserName(projectManagerId);
        }
    }

    /**
     * 项目总监id
     */
    @ApiModelProperty("项目总监id")
    @NotEmpty(message = "项目总监不能为空")
    private String projectDirectorId;
    /**
     * 项目总监姓名
     */
    private String directorName;
    /**
     * 测试主任id
     */
    @ApiModelProperty("测试主任id")
    private String testDirectorId;
    /**
     * 项目金额
     */
    @ApiModelProperty("金额")
    private BigDecimal money;
    /**
     * 指派（机构/公司）
     */
    @ApiModelProperty("指派（机构/公司）")
    private String organId;
    /**
     * 开始日期
     */
    @ApiModelProperty("开始日期")
    private LocalDate startDate;
    /**
     * 结束日期
     */
    @ApiModelProperty("结束日期")
    private LocalDate endDate;
    /**
     * 实际开始日期
     */
    @ApiModelProperty("实际开始日期")
    private LocalDate actualStartDate;
    /**
     * 实际完成日期
     */
    @ApiModelProperty("实际完成日期")
    private LocalDate actualFinishDate;
    /**
     * 客户名称
     */
    @ApiModelProperty("客户名称")
    private String ownerName;
    /**
     * 最终客户
     */
    @ApiModelProperty("最终客户")
    private String endCustomer;
    /**
     * 国家
     */
    @ApiModelProperty("国家")
    private String country;
    /**
     * 省
     */
    @ApiModelProperty("省")
    private String province;
    /**
     * 市
     */
    @ApiModelProperty("市")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty("区")
    private String district;
    /**
     * 项目地点
     */
    @ApiModelProperty("项目地点")
    // @NotEmpty(message = "项目地点不能为空")
    private String projectAddress;
    /**
     * 项目地点
     */
    private List<String> address;
    /**
     * 详细地址
     */
    @ApiModelProperty("详细地址")
    private String detailAddress;
    /**
     * 客户行业
     */
    @ApiModelProperty("客户行业")
    // @NotEmpty(message = "客户行业不能为空")
    private String customerIndustry;
    /**
     * 项目经纬度
     */
    @ApiModelProperty("项目经纬度")
    // @NotEmpty(message = "项目经纬度不能为空")
    private String projectLatlng;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remarks;
    /**
     * 审核状态（0：待审核 , 1：审核通过）
     */
    @ApiModelProperty("审核状态")
    @JsonIgnore
    private ApprovalStatusEnum approvalStatus;

    @JsonAnyGetter
    public Map<String, Object> getApprovalStatusAny() {
        if (Objects.nonNull(this.approvalStatus)) {
            LinkedHashMap<String, Object> map = Maps.newLinkedHashMap();
            map.put("approvalStatus", this.approvalStatus.getApprovalStatus());
            map.put("approvalName", this.approvalStatus.getApprovalName());
            return map;
        }
        return null;
    }

    @JsonAnySetter
    public void setApprovalStatusAny(String name, Object value) {
        if ("approvalStatus".equals(name)) {
            if (value instanceof Integer) {
                this.approvalStatus = ApprovalStatusEnum.match((Integer) value);
            }
        }
    }

    /**
     * 人天
     */
    @ApiModelProperty("人天")
    private BigDecimal ManDays;
    /**
     * 完成百分比
     */
    @ApiModelProperty("完成百分比")
    private Integer percentageComplete;
    /**
     * 状态
     */
    @ApiModelProperty("状态")
    private StateEnum state;

    @ApiModelProperty("状态别名")
    private String status;

    @ApiModelProperty("状态字符串")
    @JsonIgnore
    private String stateStr;
    /**
     * 多选状态
     */
    @ApiModelProperty("多选状态")
    private List<String> states;
    /**
     * 多选阶段
     */
    @ApiModelProperty("多选阶段")
    private List<String> stages;

    /**
     * 差旅费用
     */
    @ApiModelProperty("差旅费用")
    private BigDecimal travelCost;

    /**
     * 项目成本
     */
    @ApiModelProperty("项目成本")
    private List<ProjectCostDto> projectCosts;

    /**
     * 预计成本（含税）
     */
    @ApiModelProperty("预计成本（含税）")
    private BigDecimal estimatedMoneyTax;

    /**
     * 预计成本（不含税）
     */
    @ApiModelProperty("预计成本（不含税）")
    private BigDecimal estimatedMoney;

    /**
     * 实际成本（含税）
     */
    @ApiModelProperty("实际成本（含税）")
    private BigDecimal actualMoney;

    /**
     * 实际成本（不含税）
     */
    @ApiModelProperty("实际成本（不含税）")
    private BigDecimal actualMoneyTax;

    /**
     * 是否超期预警
     */
    @ApiModelProperty("是否超期预警")
    private Boolean warning;
    /**
     * 下次预警日期
     */
    @ApiModelProperty("下次预警日期")
    private LocalDate alarmDate;
    /**
     * 预警间隔
     */
    @ApiModelProperty("预警间隔")
    private Integer warningInterval;
    /**
     * 项目合同（只做查询）
     */
    @ApiModelProperty("项目合同（只做查询）")
    private String contractId;
}
