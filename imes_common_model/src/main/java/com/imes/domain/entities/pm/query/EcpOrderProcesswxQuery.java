package com.imes.domain.entities.pm.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<《AB维修更换采购合同》查询条件>>
 * @company 捷创智能技术有限公司
 * @create 2021-04-28 14:35
 */
@ApiModel("《AB维修更换采购合同》查询条件")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Builder
@Getter
@Setter
public class EcpOrderProcesswxQuery extends BaseQuery{
    @ApiModelProperty("单据编码")
    private String nid;
    @ApiModelProperty("单据编号")
    private String bid;
    @ApiModelProperty("父单据编号")
    private String pBid;
    @ApiModelProperty("制单人工号")
    private String bUser;
    @ApiModelProperty("制单部门路径")
    private String bDept;
    @ApiModelProperty("销售员工号")
    private String gUser;
    @ApiModelProperty("销售部门路径")
    private String gDept;
    @ApiModelProperty("客户编号")
    private String gClient;
    @ApiModelProperty("客户名称")
    private String gCName;
    @ApiModelProperty("备注")
    private String vBz;
    @ApiModelProperty("整单备注")
    private String vZdbz;
    /*@ApiModelProperty("创建日期")
    private LocalDateTime createTime;
    @ApiModelProperty("生效日期")
    private LocalDateTime updateTime;*/
    @ApiModelProperty("产品条码")
    private String vCpTxm;
    @ApiModelProperty("品牌")
    private String gBrand;
    @ApiModelProperty("客户型号")
    private String gIDC;
    @ApiModelProperty("型号")
    private String gID;
    @ApiModelProperty("规格")
    private String gModel;
    @ApiModelProperty("品名")
    private String gName;
    @ApiModelProperty("系列")
    private String gSerial;
    @ApiModelProperty("订货号")
    private String gIndex;
    @ApiModelProperty("单位")
    private String gUnit;
    @ApiModelProperty("物料编号")
    private String vWlBh;
    @ApiModelProperty("型号状态")
    private String vXhZt;
    @ApiModelProperty("型号类型(T/S)")
    private String vXhLx;
    @ApiModelProperty("统一物料编码")
    private String vEanUpc;
    @ApiModelProperty("PGC代码")
    private String vPgc;
    @ApiModelProperty("折扣代码")
    private String vZkDm;
    @ApiModelProperty("特价类型")
    private String vTjLx;
    @ApiModelProperty("EDI/NON-EDI")
    private String vEdi;
    @ApiModelProperty("预入仓库编码")
    private String gDepot;
    @ApiModelProperty("销售合同单号")
    private String htBid;
    @ApiModelProperty("申请单号")
    private String sqBid;
    @ApiModelProperty("工程任务单号")
    private String gcBid;
    @ApiModelProperty("采购员工号")
    private String pUser;
    @ApiModelProperty("采购部门路径")
    private String pDept;
    @ApiModelProperty("供应商编号")
    private String gProvider;
    @ApiModelProperty("供应商名称")
    private String gPName;
    @ApiModelProperty("备货类型")
    private String vBhLx;
    @ApiModelProperty("客户BPID")
    private String bPID;
    @ApiModelProperty("分销商BPID")
    private String pBPID;
    @ApiModelProperty("下单BPID")
    private String cBPID;
    @ApiModelProperty("供应商地址")
    private String vPAddress;
    @ApiModelProperty("联系人")
    private String vPMan;
    @ApiModelProperty("联系人传真")
    private String vPFax;
    @ApiModelProperty("联系人电话")
    private String vPTel;
    @ApiModelProperty("付款方式编码")
    private String vFkFs;
    @ApiModelProperty("订单号")
    private String vDdh;
    @ApiModelProperty("RA主导")
    private String vRaZd;
    @ApiModelProperty("RA系统号")
    private String vRaXth;
    @ApiModelProperty("RA销售")
    private String vRaXs;
    @ApiModelProperty("特价名称")
    private String vTjMc;
    @ApiModelProperty("特价号码")
    private String vTjHm;
    @ApiModelProperty("下单人")
    private String vXdRXm;
    @ApiModelProperty("下单人电话")
    private String vXdRDh;
    @ApiModelProperty("下单人邮箱")
    private String vXdRYx;
    @ApiModelProperty("提货要求编码")
    private String vThYq;
    @ApiModelProperty("采购交货期")
    private LocalDateTime dCghq;
    @ApiModelProperty("合同交货期")
    private LocalDateTime dHthq;
    @ApiModelProperty("申请交货期")
    private LocalDateTime dSqhq;
    @ApiModelProperty("下单日期")
    private LocalDateTime dXdRq;
    @ApiModelProperty("供应商发货日期")
    private LocalDateTime dFhRq;
    @ApiModelProperty("发票号码")
    private String vFpHm;
    @ApiModelProperty("订货调入")
    private Boolean bbDH;
    @ApiModelProperty("备货调入")
    private Boolean bbBH;
    @ApiModelProperty("维修订货调入")
    private Boolean bbWxDh;
    @ApiModelProperty("是否AB采购")
    private Boolean bbABCG;
    @ApiModelProperty("是否维修采购")
    private Boolean bbWXCG;
    @ApiModelProperty("项目编码")
    private String vXmBm;
    @ApiModelProperty("项目名称")
    private String vXmMc;
    @ApiModelProperty("合同货期")
    private String vHthq;
    @ApiModelProperty("申请货期")
    private String vSqhq;
    @ApiModelProperty("采购货期")
    private String vCghq;
    @ApiModelProperty("客户品名")
    private String gNameC;
    @ApiModelProperty("坏件退回运单号")
    private String vThDh;
    @ApiModelProperty("采购公司")
    private String cidName;
    @ApiModelProperty("制单人")
    private String bUserName;
    @ApiModelProperty("制单部门")
    private String bDeptName;
    @ApiModelProperty("销售员")
    private String gUserName;
    @ApiModelProperty("销售部门")
    private String gDeptName;
    @ApiModelProperty("申请订货公司")
    private String scidName;
    @ApiModelProperty("预入仓库")
    private String gDepotName;
    @ApiModelProperty("采购员")
    private String pUserName;
    @ApiModelProperty("采购部门")
    private String pDeptName;
    @ApiModelProperty("付款方式")
    private String vFkFsName;
    @ApiModelProperty("提货要求")
    private String vThYqName;
}
