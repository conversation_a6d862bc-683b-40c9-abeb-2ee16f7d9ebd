package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
@Data	
public class WmsStockBinOtherQueryVo {	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
	
    /**	
     * 库位码	
     */	
	@ApiModelProperty("库位码")	
    private String binCode;	
	
    /**	
     * 箱号	
     */	
	@ApiModelProperty("箱号")	
    private String boxNo;	
	
    private String whCode;	
	
    /**	
     * 字符串查询	
     */	
	@ApiModelProperty("字符串查询")	
    private String searchKey;	
	
    private Integer pageNum;	
	
    private Integer pageSize;	
}	
