package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * 岗位职级表(HrJobsRank)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-20 09:31:25	
 */	
@Data	
@ApiModel("岗位职等")	
@QueryModel(name = "0781",	
        remark = "岗位职等",	
        alias = "hr_jobs_rank",	
        searchApi = "/api/hr/position/rank/query")	
public class HrJobsRankQueryVo extends BaseModel {	
	
    /**	
     * 职等信息	
     */	
	@ApiModelProperty("职等信息")	
    @QueryField(name = "职等信息" )	
    private String jobsRank;	
    /**	
     * 等级	
     */	
	@ApiModelProperty("等级")	
    @QueryField(name = "等级" )	
    private String level;	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @QueryField(name = "创建时间",type = Type.Date, order = OrderBy.DESC)	
    private Date createOn;	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("级别")	
    @QueryField(name = "级别" )	
    private String createBy;	
	
}	
	
