package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.WmsPickingOrderItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
public class WmsPickOrderAndItemVo extends WmsPickingOrderItem {	
    /**	
     * 申请单号	
     */	
	@ApiModelProperty("申请单号")	
    private String poCode;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 仓库名称	
     */	
	@ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 关联单号	
     */	
	@ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 申请时间	
     */	
	@ApiModelProperty("申请时间")	
    private Date pickingDate;	
	
    /**	
     * 申请人名称	
     */	
	@ApiModelProperty("申请人名称")	
    private String applyUserName;	
	
    /**	
     * 领用人名称	
     */	
	@ApiModelProperty("领用人名称")	
    private String pickUserName;	
	
    /**	
     * 领用用途	
     */	
	@ApiModelProperty("领用用途")	
    private String purpose;	
	
    /**	
     * 单据状态	
     */	
	@ApiModelProperty("单据状态")	
    private int orderStatus;	
	
    /**	
     * 待出库数量	
     */	
	@ApiModelProperty("待出库数量")	
    private BigDecimal outQty;	
	
    /**	
     * 实际出库数量	
     */	
	@ApiModelProperty("实际出库数量")	
    private BigDecimal actualOutQty;	
	
    /**	
     * 是否入线边库	
     */	
	@ApiModelProperty("是否入线边库")	
    private int isInStorage;	
	
    /**	
     * 单据类型	
     */	
	@ApiModelProperty("单据类型")	
    private String orderType;	
	
    /**	
     * 流通库编码	
     */	
	@ApiModelProperty("流通库编码")	
    private String storageAreaCode;	
	
    /**	
     * 流通库名称	
     */	
	@ApiModelProperty("流通库名称")	
    private String storageAreaName;	
	
    private Boolean isConfirm;	
	
	
    /**	
     * 是否显示确认入库按钮	
     */	
	@ApiModelProperty("是否显示确认入库按钮")	
    private boolean isShowBtn;	
	
    /**	
     * 是否显示取消按钮	
     */	
	@ApiModelProperty("是否显示取消按钮")	
    private boolean isCancelBtn;	
	
    /**	
     * 标识符	
     */	
	@ApiModelProperty("标识符")	
    private String flag;	
	
    private String storageOutCode;	
	
    /**	
     * 第三方单号	
     */	
	@ApiModelProperty("第三方单号")	
    private String thirdOrderCode;	
	
    /**	
     * 车间编码	
     */	
	@ApiModelProperty("车间编码")	
    private String workshopCode;	
	
    /**	
     * 车间名称	
     */	
	@ApiModelProperty("车间名称")	
    private String workshopName;	
	
    /**	
     * 班组编码	
     */	
	@ApiModelProperty("班组编码")	
    private String teamCode;	
	
    /**	
     * 班组名称	
     */	
	@ApiModelProperty("班组名称")	
    private String teamName;	
	
    /**	
     * 出库列表	
     */	
	@ApiModelProperty("出库列表")	
    private List<WmsOutStorageItemVo> outBillItemVos;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 是否自动入线边库	
     */	
	@ApiModelProperty("是否自动入线边库")	
    private boolean autoInbound;	
	
    private String userName;	
	
    /**	
     * 销售订单号	
     */	
	@ApiModelProperty("销售订单号")	
    private String saleNo;

    /**
     * 明细单主键
     */
    @ApiModelProperty("明细单主键")
    private String detailId;
	
    private String theInventoryQty;	
	
    private String isLimitedBatch;	
	
    private String remark;	
	
    private String receiptType;	
}	
