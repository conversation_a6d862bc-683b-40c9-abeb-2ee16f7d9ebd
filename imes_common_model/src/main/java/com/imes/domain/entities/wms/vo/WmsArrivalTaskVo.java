package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.alibaba.fastjson.annotation.JSONField;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import com.imes.domain.entities.wms.WmsArrivalTask;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsArrivalTaskVo extends WmsArrivalTask {	
	
    /**	
     * 是否核检("0":false;"1":true)	
     */	
	@ApiModelProperty("是否核检(0:false;0:true)")
    private String qualityStatus;	
	
    /**	
     * 附件	
     */	
	@ApiModelProperty("附件")
    private String attachments;	
	
    /**	
     * 处置方式("1":预入库;"2":拒收)	
     */	
	@ApiModelProperty("处置方式(1:预入库;2:拒收)")
    private String storageType;	
	
    /**	
     * 供应商编码	
     */	
	@ApiModelProperty("供应商编码")
    private String supplierCode;
	
    /**	
     * 供应商名称	
     */	
	@ApiModelProperty("供应商名称")
    private String supplierName;

    @ApiModelProperty("规格")
    private String specification;	
	
}	
