package com.imes.domain.entities.hr.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.imes.domain.entities.hr.po.HrSurveyOption;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷调查题型表(ImSurveyTopic)实体类
 *
 * <AUTHOR> z
 * @since 2022-06-30 15:12:10
 */
@Data
@ApiModel("问卷调查题型")
@AllArgsConstructor
@NoArgsConstructor
public class HrSurveyTopicDTO implements Serializable {
    private static final long serialVersionUID = 924928592512904791L;

    private String id;
    /**
     * 问卷ID
     */
    private String surveyId;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型
     */
    private String type;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否必传
     */
    private Integer required;
    /**
     * 状态
     */
    private String status;
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 调查选项的集合
     */
    private List<HrSurveyOption> optionList;

    /**
     * 一行显示数量
     */
    private Integer lineNum;
    /**
     * 选项数量
     */
    private Integer optionNum;
    /**
     * 题目编号
     */
    private String topicNo;

}