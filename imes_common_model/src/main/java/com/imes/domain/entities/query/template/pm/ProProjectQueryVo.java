package com.imes.domain.entities.query.template.pm;

import com.imes.domain.entities.query.model.base.BaseModel;
import com.imes.domain.entities.query.model.base.QueryField;
import com.imes.domain.entities.query.model.base.QueryModel;
import com.imes.domain.entities.query.model.base.Type;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("项目基本信息")
@QueryModel(
        name = "0186",
        remark = "项目基本信息"
)
public class ProProjectQueryVo extends BaseModel {

    @ApiModelProperty("项目编号")
    @QueryField(name = "项目编号")
    private String code;

    @ApiModelProperty("项目名称")
    @QueryField(name = "项目名称")
    private String name;

    @ApiModelProperty("立项日期")
    @QueryField(name = "立项日期", type = Type.Date, format = "yyyy-MM-dd")
    private String projectDate;

    @ApiModelProperty("状态")
    @QueryField(name = "状态", type = Type.MultiSelect, option = {"0", "未开始", "1", "进行中", "4", "已完成"})
    private String state;

    @ApiModelProperty("完成率")
    @QueryField(name = "完成率", type = Type.Number, format = "0")
    private String progress;

    @ApiModelProperty("项目经理")
    @QueryField(name = "项目经理", flowableApprovalUser = true)
    private String projectManagerId;

    @ApiModelProperty("项目总监")
    @QueryField(name = "项目总监", flowableApprovalUser = true)
    private String projectDirectorId;

    @ApiModelProperty("项目阶段")
    @QueryField(name = "项目阶段", type = Type.Select, dictOption = "PROJECT_STAGE")
    private String stage;

    @ApiModelProperty("项目类型")
    @QueryField(name = "项目类型", type = Type.Select, dictOption = "PROJECT_TYPE")
    private String type;

    @ApiModelProperty("产品类别")
    @QueryField(name = "产品类别", type = Type.Select, dictOption = "PRODUCT_CATEGORY")
    private String productType;

    @ApiModelProperty("项目类别")
    @QueryField(name = "项目类别", type = Type.Select, dictOption = "PROJECT_CATEGORY")
    private String category;

    @ApiModelProperty("销售员")
    @QueryField(name = "销售员")
    private String salesperson;

    @ApiModelProperty("开始日期")
    @QueryField(name = "开始日期", type = Type.Date, format = "yyyy-MM-dd")
    private String startDate;

    @ApiModelProperty("结束日期")
    @QueryField(name = "结束日期", type = Type.Date, format = "yyyy-MM-dd")
    private String endDate;

    @ApiModelProperty("精细化")
    @QueryField(name = "精细化", type = Type.Select, option = {"1", "是", "0", "否"})
    private String refinement;

    @ApiModelProperty("计划工时")
    @QueryField(name = "计划工时", type = Type.Number, format = "0")
    private String plannedHours;

    @ApiModelProperty("最大工时")
    @QueryField(name = "最大工时", type = Type.Number, format = "0")
    private String maxHours;

    @ApiModelProperty("最小工时")
    @QueryField(name = "最小工时", type = Type.Number, format = "0")
    private String minHours;

    @ApiModelProperty("实际开始日期")
    @QueryField(name = "实际开始日期", type = Type.Date, format = "yyyy-MM-dd")
    private String actualStartDate;

    @ApiModelProperty("实际完成日期")
    @QueryField(name = "实际完成日期", type = Type.Date, format = "yyyy-MM-dd")
    private String actualFinishDate;

    @ApiModelProperty("完结状态")
    @QueryField(name = "完结状态", show = false, type = Type.Select, option = {"1", "完结", "0", "未完结"})
    private String willComplete;

}	
