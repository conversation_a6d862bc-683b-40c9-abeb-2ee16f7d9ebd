package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
/**	
 * 考勤记录表(Attendance)实体类	
 *	
 * <AUTHOR> zhang	
 * @since 2022-08-30 14:58:17	
 */	
	
@Data	
@ApiModel("考勤记录")	
@QueryModel(name = "0671",	
        remark = "考勤记录",	
        alias = "attendance",	
        searchApi = "/api/hr/attendance/query")	
public class AttendanceVo extends BaseModel {	
	
    private String id;	
    /**	
     * 员工编号	
     */	
	@ApiModelProperty("员工编号")	
    @QueryField(name = "员工编号")	
    private String userCode;	
    /**	
     * 员工名称	
     */	
	@ApiModelProperty("员工名称")	
    @QueryField(name = "员工名称")	
    private String userName;	
    /**	
     * 打卡地点	
     */	
	@ApiModelProperty("打卡地点")	
    @QueryField(name = "打卡地点")	
    private String place;	
    /**	
     * 公司	
     */	
	@ApiModelProperty("公司")	
    @QueryField(name = "公司")	
    private String company;	
    /**	
     * 工作时间长度	
     */	
	@ApiModelProperty("工作时间长度")	
    @QueryField(name = "工作时间长度", query = false, sort = false)	
    private String workingHours;	
    /**	
     * 考勤当天日期	
     */	
	@ApiModelProperty("考勤当天日期")	
    @QueryField(name = "考勤当天日期", type = Type.Date)	
    private String attendanceDays;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态")	
    private String status;	
    /**	
     * 上班打卡时间	
     */	
    private String workDate;	
    /**	
     * 最晚一次打卡时间 下班	
     */	
	@ApiModelProperty("最晚一次打卡时间")	
    @QueryField(name = "最晚一次打卡时间")	
    private String attendanceDate;	
	
	@ApiModelProperty("公司名称")	
    @QueryField(name = "公司名称")	
    private String departName;	
}	
	
