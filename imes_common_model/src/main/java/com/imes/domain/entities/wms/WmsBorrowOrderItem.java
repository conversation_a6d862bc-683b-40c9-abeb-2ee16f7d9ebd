package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import java.io.Serializable;	
import io.swagger.annotations.ApiModelProperty;	
import java.math.BigDecimal;	
import java.util.Date;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsBorrowOrderItem implements Serializable {	
    /**	
     * 主键	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 借货单主键	
     */	
	@ApiModelProperty("借货单主键")	
    private String boId;	
	
    /**	
     * 借货单号	
     */	
	@ApiModelProperty("借货单号")	
    private String boCode;	
	
    /**	
     * 借货明细单号	
     */	
	@ApiModelProperty("借货明细单号")	
    private String boItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 被借客户编码	
     */	
	@ApiModelProperty("被借客户编码")	
    private String fromCustomerCode;	
	
    /**	
     * 被借客户名称	
     */	
	@ApiModelProperty("被借客户名称")	
    private String fromCustomerName;	
	
    /**	
     * 借货数量	
     */	
	@ApiModelProperty("借货数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 订单数量	
     */	
	@ApiModelProperty("订单数量")	
    private BigDecimal purchaseQty;	
    /**	
     * 可借货数量	
     */	
	@ApiModelProperty("可借货数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 基本单位	
     */	
	@ApiModelProperty("基本单位")	
    private String unit;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    /**	
     * 被借货合同单号	
     */	
	@ApiModelProperty("被借货合同单号")	
    private String wasBoCode;	
	
    /**	
     * 被借在库库存	
     */	
	@ApiModelProperty("被借在库库存")	
    private BigDecimal onhandQty;	
	
    /**	
     * 借出数量	
     */	
	@ApiModelProperty("借出数量")	
    private BigDecimal lendQty;	
	
    private static final long serialVersionUID = 1L;	
}	
