package com.imes.domain.entities.qms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.qms.po.InBillInspectionDetail;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class MaterialInspectionSaveVo {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 产品名称	
     */	
	@ApiModelProperty("产品名称")	
    private String productName;	
	
    /**	
     * 生产计划编号	
     */	
	@ApiModelProperty("生产计划编号")	
    private String ppNo;	
	
    /**	
     * 检验编码	
     */	
	@ApiModelProperty("检验编码")	
    private String inspectionCode;	
	
    /**	
     * 检验方式	
     */	
	@ApiModelProperty("检验方式")	
    private String inspectionType;	
	
    /**	
     * 检验人	
     */	
	@ApiModelProperty("检验人")	
    private String inspector;	
	
    /**	
     * 检验项	
     */	
	@ApiModelProperty("检验项")	
    private String inspectionItems;	
	
    private List<MaterialInspectionDetail> details;	
}	
