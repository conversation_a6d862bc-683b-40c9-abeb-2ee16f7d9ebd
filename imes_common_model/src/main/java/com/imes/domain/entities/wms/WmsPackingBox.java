package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
import java.math.BigDecimal;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@ApiModel(value = "仓库拆装箱单箱码")	
public class WmsPackingBox implements Serializable {	
	
    private static final long serialVersionUID = -74498807836716592L;	
	
    @ApiModelProperty(value = "主键")	
    private String id;	
	
    @ApiModelProperty(value = "装箱单号")	
    private String packNo;	
	
    @ApiModelProperty(value = "箱号")	
    private String boxNo;	
	
    @ApiModelProperty(value = "是否混装1是0否")	
    private String isMixed;	
	
    @ApiModelProperty(value = "10待入库20入库申请中40已入库")	
    private String instorageStatus;	
	
    @ApiModelProperty(value = "标签打印模板名称")	
    private String printLabelName;	
	
    @ApiModelProperty(value = "装箱数量汇总")	
    private BigDecimal packTotalNum;	
	
    @ApiModelProperty(value = "仓库号编码")	
    private String whCode;	
	
    @ApiModelProperty(value = "仓库名称")	
    private String whName;	
	
    @ApiModelProperty(value = "存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty(value = "存储区名称")	
    private String areaName;	
	
    @ApiModelProperty(value = "仓位编码")	
    private String binCode;	
	
    @ApiModelProperty(value = "优先级")	
    private Integer priority;	
	
    @ApiModelProperty(value = "创建时间")	
    private Date createOn;	
	
    @ApiModelProperty(value = "创建人")	
    private String createBy;	
	
    @ApiModelProperty(value = "更新时间")	
    private Date updateOn;	
	
    @ApiModelProperty(value = "更新人")	
    private String updateBy;	
	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    @ApiModelProperty("打印次数")	
    private Integer printSum;	
	
    private List<WmsPackingBoxMaterial> wmsPackingBoxMaterials;	
}	
