package com.imes.domain.entities.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.wms.dto.WmsInventoryStockQueryDto;	
import com.imes.domain.entities.wms.po.WmsInventoryMaterialItem;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.io.Serializable;	
import java.util.Date;	
import java.util.List;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class WmsInventoryTask implements Serializable {	
    /**	
     * id	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 盘点单号	
     */	
	@ApiModelProperty("盘点单号")	
    private String inventoryCode;	
	
    /**	
     * 仓库编码	
     */	
	@ApiModelProperty("仓库编码")	
    private String whCode;	
	
    /**	
     * 盘点类型	
     */	
	@ApiModelProperty("盘点类型")	
    private String checkType;	
	
    /**	
     * 盘点员工号	
     */	
	@ApiModelProperty("盘点员工号")	
    private String checkBy;	
	
    /**	
     * 复核人工号	
     */	
	@ApiModelProperty("复核人工号")	
    private String reviewBy;	
	
    /**	
     * 盘点状态	
     */	
	@ApiModelProperty("盘点状态")	
    private String checkStatus;	
	
    /**	
     * 盘点结果	
     */	
	@ApiModelProperty("盘点结果")	
    private String checkResult;	
	
    /**	
     * 盘点开始时间	
     */	
	@ApiModelProperty("盘点开始时间")	
    private Date startTime;	
	
    /**	
     * 盘点完成日期	
     */	
	@ApiModelProperty("盘点完成日期")	
    private Date endTime;	
	
    /**	
     * 	
     */	
	@ApiModelProperty("备注")	
    private String remarks;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    private Date createOn;	
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    private Date updateOn;	
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    /**	
     * 库区	
     */	
	@ApiModelProperty("库区")	
    private String areaCode;	
	
    /**	
     * 盘点单明细	
     */	
	@ApiModelProperty("盘点单明细")	
    private List<WmsInventoryTaskItem> items;	
	
    /**	
     * 盘点物料明细表	
     */	
	@ApiModelProperty("盘点物料明细表")	
    private List<WmsInventoryMaterialItem> materialItems;	
	
    /**	
     * 关联单号	
     */	
    @ApiModelProperty("关联单号")	
    private String receiptCode;	
	
    /**	
     * 统计方式	
     */	
    @ApiModelProperty("统计方式")	
    private String statisticalMethod;	
	
	@ApiModelProperty("id")	
    private String ids;	
	
    @ApiModelProperty("复盘员工名称")	
    private String reviewByName;	
	
    @ApiModelProperty("操作员名称")	
    private String checkByName;	
	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 是否查询0库存(0:否,1:是)	
     */	
	@ApiModelProperty("是否查询0库存(0:否,1:是)")	
    private String isZero;	
	
    /**	
     * 库位编码	
     */	
	@ApiModelProperty("库位编码")	
    private String binCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 自定义字段	
     */	
	@ApiModelProperty("自定义字段")	
    private String custom;	
	
    /**	
     * 新增盘点单时查询库存条件	
     */	
	@ApiModelProperty("新增盘点单时查询库存条件")	
    private WmsInventoryStockQueryDto stockQueryDto;

    /**
     * 库区id
     */
    private String areaId;
	
    private static final long serialVersionUID = 1L;	
}	
