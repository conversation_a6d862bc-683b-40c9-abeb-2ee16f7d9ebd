package com.imes.domain.entities.dev.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.util.Date;

/**
 * (DevCheckingRecordDev)实体类
 *
 * <AUTHOR>
 * @since 2023-08-07 15:11:52
 */
@Data
@ApiModel(value = "点检记录设备表")
public class DevCheckingRecordDev implements Serializable {

    private static final long serialVersionUID = 674640333786776702L;

    @ApiModelProperty(value = "主键", dataType = "String")
    private String id;

    @ApiModelProperty(value = "主id", dataType = "String")
    private String recordId;

    @ApiModelProperty(value = "点检单号", dataType = "String")
    private String checkingRecordNo;

    @ApiModelProperty(value = "设备编码", dataType = "String")
    private String devCode;

    @ApiModelProperty(value = "设备名称", dataType = "String")
    private String devName;

    @ApiModelProperty(value = "处理状态10待认领20待完成30已完成", dataType = "String")
    private String status;

    @ApiModelProperty(value = "领取人工号", dataType = "String")
    private String receiveUserCode;

    @ApiModelProperty(value = "领取人名称", dataType = "String")
    private String receiveUserName;

    @ApiModelProperty(value = "辅助执行人工号", dataType = "String")
    private String minorUserCode;

    @ApiModelProperty(value = "辅助执行人编码", dataType = "String")
    private String minorUserName;

    @ApiModelProperty(value = "领取时间", dataType = "Date")
    private Date receiveTime;

    @ApiModelProperty(value = "完成时间", dataType = "Date")
    private Date finishTime;

    @ApiModelProperty(value = "用时")
    private Integer takeTime;

    /**
     * 审核时间
     * */
    private Date auditTime;

    /**
     * 流程id
     * */
    private String taskId;

    private String devNumber;



    @ApiModelProperty(value = "创建人", dataType = "String")
    private String createdBy;

    @ApiModelProperty(value = "创建时间", dataType = "Date")
    private Date createdOn;

    @ApiModelProperty(value = "更新时间", dataType = "Date")
    private Date updatedOn;

    @ApiModelProperty(value = "更新人", dataType = "String")
    private String updatedBy;

    @Transient
    private String deptCode;

    @Transient
    private String deptName;
}