package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import lombok.Data;	
	
/**	
 * hr考勤假期内容(HrVacationList)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-05-10 10:21:25	
 */	
@Data	
@ApiModel("员工信息")	
@QueryModel(name = "0655",	
        remark = "员工信息",	
        alias = "hr_user",	
        searchApi = "/api/hr/user/query")	
public class HrVacationListQueryVo extends BaseModel {	
	
	
    /**	
     * 假期类型	
     */	
	@ApiModelProperty("假期类型")	
    @QueryField(name = "假期类型")	
    private String vacationType;	
    /**	
     * 假期名称	
     */	
	@ApiModelProperty("假期名称")	
    @QueryField(name = "假期名称")	
    private String vacationName;	
	
	
}	
	
