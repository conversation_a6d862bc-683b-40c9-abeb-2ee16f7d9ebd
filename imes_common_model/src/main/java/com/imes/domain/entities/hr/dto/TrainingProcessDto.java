package com.imes.domain.entities.hr.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 培训过程记录表(TrainingProcess)实体类
 *
 * <AUTHOR> z
 * @since 2022-08-31 10:26:56
 */
@ApiModel("培训过程记录")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TrainingProcessDto implements Serializable {

    private String id;
    /**
     * 培训id
     */
//    @NotNull(message = "培训ID不为空")
    private String trainingId;
    /**
     * 培训内容
     */
    private String content;
    /**
         * 培训日期
     */
    @NotNull(message = "培训日期不为空")
    private Date trainingTime;
    /**
         * 培训文件
     */
    private String files;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createOn;
    /**
     * 培训名称
     */
    private String name;
    /**
     * 讲师名称
     */
    private String userName;


}

