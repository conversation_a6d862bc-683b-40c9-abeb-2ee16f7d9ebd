package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import lombok.Data;	
import io.swagger.annotations.ApiModelProperty;	
	
import java.math.BigDecimal;	
	
@Data	
public class WmsOutBillDetailForReturnVo {	
	
    /**	
     * 出库单号	
     */	
	@ApiModelProperty("出库单号")	
    private String outboundCode;	
	
    /**	
     * 出库单号	
     */	
	@ApiModelProperty("出库单号")	
    private String storageOutCode;	
	
    /**	
     * 出库子单号	
     */	
	@ApiModelProperty("出库子单号")	
    private String storageOutItemCode;	
	
    /**	
     * 出库类型	
     */	
	@ApiModelProperty("出库类型")	
    private String receiptType;	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
    /**	
     * 型号	
     */	
	@ApiModelProperty("型号")	
    private String materialMarker;	
    /**	
     * 规格	
     */	
	@ApiModelProperty("规格")	
    private String specification;	
    /**	
     * 批次	
     */	
	@ApiModelProperty("批次")	
    private String batch;	
    /**	
     * 已退货数量	
     */	
	@ApiModelProperty("已退货数量")	
    private BigDecimal returnPackQty;	
    /**	
     * 退货单位	
     */	
	@ApiModelProperty("退货单位")	
    private String returnPackUnit;	
    /**	
     * 出库数量	
     */	
	@ApiModelProperty("出库数量")	
    private BigDecimal packOutStorageQty;	
    /**	
     * 出库单位	
     */	
	@ApiModelProperty("出库单位")	
    private String packCodeUnit;	
    /**	
     * 出库基本数量	
     */	
	@ApiModelProperty("出库基本数量")	
    private BigDecimal pickedQty;	
    /**	
     * 出库基本单位	
     */	
	@ApiModelProperty("出库基本单位")	
    private String unit;	
    /**	
     * 出库日期	
     */	
	@ApiModelProperty("出库日期")	
    private String approveOn;	
    /**	
     * 客户	
     */	
	@ApiModelProperty("客户")	
    private String customerName;	
	
    /**	
     * 客户编码	
     */	
	@ApiModelProperty("客户编码")	
    private String customerCode;	
	
    /**	
     * 可退货数量	
     */	
	@ApiModelProperty("可退货数量")	
    private BigDecimal allowReturnNum;	
	
    /**	
     * 小数精度位数	
     */	
	@ApiModelProperty("小数精度位数")	
    private Integer precisionDigit;	
	
    /**	
     * 进位方式	
     */	
	@ApiModelProperty("进位方式")	
    private Integer carryMode;	
	
    /**	
     * 销售订单号	
     */	
	@ApiModelProperty("销售订单号")	
    private String soNo;	
	
    /**	
     * 销售订单名称	
     */	
	@ApiModelProperty("销售订单名称")	
    private String soName;	
	
    /**	
     * 出库数量	
     */	
	@ApiModelProperty("出库数量")	
    private BigDecimal packInventoryQty;	
	
    /**	
     * 辅助属性	
     */	
	@ApiModelProperty("辅助属性")	
    private String skuCode;	
}	
