package com.imes.domain.entities.hr.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.imes.domain.entities.hr.po.HrAnswer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷调查表(ImSurvey)实体类
 *
 * <AUTHOR> z
 * @since 2022-06-30 15:12:03
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
public class HrSurveyDTO implements Serializable {

    private static final long serialVersionUID = 7861863375473098512L;
    /**
     * id
     */
    private String id;
    /**
     * 问卷编码
     */
    private String surveyNo;
    /**
     * 标题
     */
    private String title;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 描述
     */
    private String description;
    /**
     * 状态
     */
    private String status;
    /**
     * 答题数量
     */
    private Integer answerNumber;
    /**
     * 发布时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    private LocalDateTime pushTime;
    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    private LocalDateTime createTime;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    private LocalDateTime updateTime;
    /**
     * 更新人
     */
    private String updateBy;

    private List<HrSurveyTopicDTO> surveyTopics;

    private HrAnswer answer;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")//页面写入数据库时格式化
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")//从数据库读出日期格式时，进行转换的规则
    @TableField(exist = false)
    private List<LocalDateTime> dates;


}