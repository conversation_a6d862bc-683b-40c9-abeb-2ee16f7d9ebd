package com.imes.domain.entities.wms.po;	
	
import io.swagger.annotations.ApiModel;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.Data;	
	
import java.io.Serializable;	
	
/**	
 * 移动端到货计划列表过滤条件	
 */	
@Data	
public class WmsArrivalPlanPo implements Serializable {	
    private static final long serialVersionUID = -61743583528138280L;	
	
    /**	
     * 页码	
     */	
	@ApiModelProperty("页码")	
    private Integer pageNum;	
	
    /**	
     * 页数	
     */	
	@ApiModelProperty("页数")	
    private Integer pageSize;	
	
    /**	
     * 搜索关键字	
     */	
	@ApiModelProperty("搜索关键字")	
    private String searchKey;	
	
    /**	
     * 是否显示主单(main:显示主单；detail：显示名次)	
     */	
	@ApiModelProperty("是否显示主单(main:显示主单；detail：显示名次)")	
    private String isShowMain;	
	
    /**	
     * 单据状态（10-已录入；20-已生效；50-已完成）	
     */	
	@ApiModelProperty("单据状态（10-已录入；20-已生效；50-已完成）")	
    private String planStatus;	
	
}	
	
