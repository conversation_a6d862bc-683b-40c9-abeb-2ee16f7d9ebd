package com.imes.domain.entities.wms.vo;	
	
import io.swagger.annotations.ApiModel;	
import com.fasterxml.jackson.annotation.JsonFormat;	
import io.swagger.annotations.ApiModelProperty;	
import lombok.AllArgsConstructor;	
import lombok.Data;	
import lombok.NoArgsConstructor;	
	
import java.math.BigDecimal;	
import java.util.Date;	
	
@Data	
@AllArgsConstructor	
@NoArgsConstructor	
public class C_WmsMoveOrderItemVo {	
    /**	
     *	
     */	
	@ApiModelProperty("id")	
    private String id;	
	
    /**	
     * 移库单主键	
     */	
	@ApiModelProperty("移库单主键")	
    private String moId;	
	
    /**	
     * 移库单号	
     */	
	@ApiModelProperty("移库单号")	
    private String moCode;	
	
    /**	
     * 移库明细单号	
     */	
	@ApiModelProperty("移库明细单号")	
    private String moItemCode;	
	
    /**	
     * 物料编码	
     */	
	@ApiModelProperty("物料编码")	
    private String materialCode;	
	
    /**	
     * 物料名称	
     */	
	@ApiModelProperty("物料名称")	
    private String materialName;	
	
    /**	
     * 物料型号	
     */	
	@ApiModelProperty("物料型号")	
    private String materialMarker;	
	
    /**	
     * 条码	
     */	
	@ApiModelProperty("条码")	
    private String barCode;	
	
    /**	
     * 移动数量	
     */	
	@ApiModelProperty("移动数量")	
    private BigDecimal orderQty;	
	
    /**	
     * 可移动数量	
     */	
	@ApiModelProperty("可移动数量")	
    private BigDecimal availableQty;	
	
    /**	
     * 主单位	
     */	
	@ApiModelProperty("主单位")	
    private String unit;	
	
    /**	
     * 批次号	
     */	
	@ApiModelProperty("批次号")	
    private String batch;	
	
    /**	
     * 下架库位	
     */	
	@ApiModelProperty("下架库位")	
    private String outBinCode;	
	
    /**	
     * 上架库位	
     */	
	@ApiModelProperty("上架库位")	
    private String inBinCode;	
	
    /**	
     * 创建时间	
     */	
	@ApiModelProperty("创建时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date createOn;
	
    /**	
     * 创建人	
     */	
	@ApiModelProperty("创建人")	
    private String createBy;	
	
    /**	
     * 更新时间	
     */	
	@ApiModelProperty("更新时间")	
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")	
    private Date updateOn;
	
    /**	
     * 更新人	
     */	
	@ApiModelProperty("更新人")	
    private String updateBy;	
	
    @ApiModelProperty("存储区编码")	
    private String areaCode;	
	
    @ApiModelProperty("仓库编码")	
    private String whCode;	
	
    @ApiModelProperty("仓库名称")	
    private String whName;	
	
    /**	
     * 逻辑删除标识	
     */	
	@ApiModelProperty("逻辑删除标识")	
    private Integer deleteStatus;	
	
    @ApiModelProperty("包装单位")	
    private String packCodeUnit;	
	
    @ApiModelProperty("退回数量")	
    private BigDecimal returnQty;	
	
    @ApiModelProperty("包装数量")	
    private BigDecimal packQty;	
	
    @ApiModelProperty("进位方式")	
    private Byte carryMode;	
	
    @ApiModelProperty("小数精度位数")	
    private Byte precisionDigit;	
	
    @ApiModelProperty("规格")	
    private String specification;	
	
    @ApiModelProperty("物料辅助属性")	
    private String skuCode;	
	
    @ApiModelProperty("箱码")	
    private String boxNo;	
	
    @ApiModelProperty("亲亲定制字段,移动类型")	
    private String moveType;	
}	
