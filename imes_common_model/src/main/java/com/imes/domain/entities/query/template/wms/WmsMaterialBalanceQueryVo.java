package com.imes.domain.entities.query.template.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.*;	
import io.swagger.annotations.ApiModelProperty;	
	
import lombok.Data;	
	
@Data	
@ApiModel("物料出入库明细表")
@QueryModel(name = "0400", remark = "物料出入库明细表", alias = "history", searchApi = "/wms/wmsStockHistory/findMaterialBalance")	
public class WmsMaterialBalanceQueryVo extends BaseModel {	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "mat")	
    private String materialName;	
	
	@ApiModelProperty("批次")	
    @QueryField(name = "批次")	
    private String batch;	
	
	@ApiModelProperty("辅助属性")	
    @QueryField(name = "辅助属性")	
    private String skuCode;	
	
	@ApiModelProperty("箱号")	
    @QueryField(name = "箱号")	
    private String boxNo;	
	
	@ApiModelProperty("物料类型")	
    @QueryField(name = "物料类型", alias = "mat", type = Type.Select, treeKey = {"typeCode", "parentCode", "0"}, sqlOption = "select type_code as value, type_name as label, type_code as typeCode, parent_type_code as parentCode from sys_material_type order by type_code")	
    private String materialTypeCode;	
	
	@ApiModelProperty("仓库")	
    @QueryField(name = "仓库", alias = "bin")	
    private String whName;	
	
	@ApiModelProperty("库区")	
    @QueryField(name = "库区", alias = "bin")	
    private String areaName;	
	
	@ApiModelProperty("库位")	
    @QueryField(name = "库位")	
    private String binCode;	
	
	@ApiModelProperty("结存数量")	
    @QueryField(name = "结存数量")	
    private String newOnhandQty;	
	
	@ApiModelProperty("出/入库数量")	
    @QueryField(name = "出/入库数量")	
    private String qty;	
	
	@ApiModelProperty("日期")	
    @QueryField(name = "日期", type = Type.Date, order = OrderBy.DESC)	
    private String createOn;	
	
	@ApiModelProperty("出/入库单号")	
    @QueryField(name = "出/入库单号")	
    private String receiptCode;	
	
	@ApiModelProperty("操作人")	
    @QueryField(name = "操作人", alias = "us")	
    private String userName;	
	
	@ApiModelProperty("出库类型")	
    @QueryField(name = "出库类型", alias = ".CASE history.option_type WHEN 2 THEN history.receipt_type WHEN 4 THEN history.receipt_type END")	
    private String outBillType;	
	
	@ApiModelProperty("入库类型")	
    @QueryField(name = "入库类型", alias = ".CASE history.option_type WHEN 1 THEN history.receipt_type WHEN 3 THEN history.receipt_type END")	
    private String inBillType;	
	
	@ApiModelProperty("旧库存")	
    @QueryField(name = "旧库存")	
    private String oldOnhandQty;	
	
	@ApiModelProperty("库位结存数量")	
    @QueryField(name = "库位结存数量")	
    private String newBinQty;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "mat")	
    private String specification;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号",logic = Logic.Like, alias = "mat")	
    private String materialMarker;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "mat", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String primaryUnit;	
}	
