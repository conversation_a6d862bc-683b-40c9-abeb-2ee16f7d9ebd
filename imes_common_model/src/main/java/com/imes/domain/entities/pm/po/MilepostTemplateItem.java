package com.imes.domain.entities.pm.po;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.imes.domain.entities.pm.enums.MilepostTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * (ProMilepostTemplateItem)实体类
 *
 * <AUTHOR>
 * @since 2020-11-25 09:23:33
 */
@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "pro_milepost_template_item",resultMap = "BaseResultMap")
public class MilepostTemplateItem implements Serializable {
    private static final long serialVersionUID = -67494686508749944L;
    /**
    * id
    */
    private String id;
    /**
    * 父id
    */
    private String pid;
    /**
     * 里程碑类型（0：里程碑；1：任务）
     */
    private MilepostTypeEnum type;
    /**
    * 里程碑名称
    */
    @TableField(condition = SqlCondition.LIKE)
    private String text;

    /**
    * 里程碑模板id
    */
    private String milepostTemplateId;
    /**
    * 顺序
    */
    private Integer sort;
    /**
    * 备注
    */
    private String remarks;
    /**
     * 创建人
     */
    @TableField(fill= FieldFill.INSERT)
    private String createdBy;
    /**
     * 修改人
     */
    @TableField(fill= FieldFill.UPDATE)
    private String updatedBy;
    /**
    * 创建时间
    */
    @TableField(fill= FieldFill.INSERT)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    /**
    * 修改时间
    */
    @TableField(fill= FieldFill.UPDATE)
    // @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    /**
    * 逻辑删除
    */
    private Boolean deleted;
    /**
    * 乐观锁
    */
    private Long version;
    /**
     * 子节点
     */
    @TableField(exist = false)
    List<MilepostTemplateItem> children;

    @TableField(exist = false)
    private boolean hasChildren;

}