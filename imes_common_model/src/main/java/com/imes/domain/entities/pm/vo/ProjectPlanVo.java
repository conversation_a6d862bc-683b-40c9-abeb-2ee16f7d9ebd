package com.imes.domain.entities.pm.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @Description: <<项目计划预览>>
 * @company 捷创智能技术有限公司
 * @create 2021-03-16 13:57
 */
@ApiModel("项目计划")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectPlanVo {
    @ApiModelProperty("日期-月")
    // @JsonFormat(pattern = "yyyy-MM")
    // @DateTimeFormat(pattern = "yyyy-MM")
    private YearMonth month;
    @ApiModelProperty("参与人")
    private List<ParticiEmployeesVo> particiEmployees;
    @ApiModelProperty("月计划")
    private BigDecimal monthlyPlan;
    @ApiModelProperty("月初")
    private BigDecimal beginningMonth;
    @ApiModelProperty("月工时")
    private BigDecimal monthWorkHours;
    @ApiModelProperty("月末")
    private BigDecimal endMonth;
    @ApiModelProperty("月超额")
    private BigDecimal monthlyExcess;
    @ApiModelProperty("总计划")
    private BigDecimal masterPlan;
    @ApiModelProperty("剩余")
    private BigDecimal surplus;
    @ApiModelProperty("占比")
    private BigDecimal ratio;
}
