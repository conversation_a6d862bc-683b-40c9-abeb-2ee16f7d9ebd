package com.imes.domain.entities.query.template.hr;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
import java.util.Date;	
	
/**	
 * 离职申请单(HrDimission)实体类	
 *	
 * <AUTHOR> z	
 * @since 2023-04-04 09:55:08	
 */	
@Data	
@ApiModel("离职申请单管理")	
@QueryModel(name = "0699",	
        remark = "离职申请单管理",	
        alias = "hr_dimission",	
        searchApi = "/api/hr/dimission/query")	
public class HrDimissionQueryVo {	
	
    private String id;	
    /**	
     * 姓名	
     */	
	@ApiModelProperty("姓名")	
    @QueryField(name = "姓名" )	
    private String name;	
    /**	
     * 工号	
     */	
	@ApiModelProperty("工号")	
    @QueryField(name = "工号" )	
    private String userCode;	
    /**	
     * 离职时间	
     */	
	@ApiModelProperty("离职时间")	
    @QueryField(name = "离职时间", type = Type.Date )	
    private String dimissionTime;	
    /**	
     * 离职类型	
     */	
	@ApiModelProperty("离职类型")	
    @QueryField(name = "离职类型", type = Type.Select, dictOption = "HR_DIMISSION_TYPE")	
    private String type;	
    /**	
     * 离职原因类型	
     */	
	@ApiModelProperty("离职原因类型")	
    @QueryField(name = "离职原因类型", type = Type.Select, dictOption = "HR_DIMISSION_REASON_TYPE")	
    private String reasonType;	
    /**	
     * 实际离职时间	
     */	
	@ApiModelProperty("实际离职时间")	
    @QueryField(name = "实际离职时间", type = Type.Date, show = false)	
    private Date actualDimissionTime;	
    /**	
     * 状态	
     */	
	@ApiModelProperty("状态")	
    @QueryField(name = "状态", type = Type.Select, dictOption = "HR_AUDIT_STATUS_STRING")	
    private String status;	
	
}	
	
