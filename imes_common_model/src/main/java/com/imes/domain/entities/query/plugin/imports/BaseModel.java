package com.imes.domain.entities.query.plugin.imports;

import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.util.ArrayList;
import java.util.List;

/**
 * 默认自定义导入模型
 */
@Data
@Alias("ImportBaseModel")
public class BaseModel {

    /**
     * 行号
     */
    private Integer rowIndex;

    /**
     * 自定义字段
     */
    private String custom;

    /**
     * 自定义字段列表
     */
    private List<String> customList;

    /**
     * 错误信息收集
     */
    private List<String> errorInfo;

    /**
     * 是否通过基础错误检查
     *
     * @return true 通过 / false 未通过
     */
    public boolean isSuccess(){
        return errorInfo == null || errorInfo.isEmpty();
    }

    /**
     * 添加错误信息
     *
     * @param title 信息标题
     * @param args  标题参数
     */
    public void addError(String title, Object... args) {
        if (this.getErrorInfo() == null) {
            this.errorInfo = new ArrayList<>();
        }
        this.getErrorInfo().add(String.format(title, args));
    }
}