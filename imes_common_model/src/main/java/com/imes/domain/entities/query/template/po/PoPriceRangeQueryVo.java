package com.imes.domain.entities.query.template.po;

import io.swagger.annotations.ApiModel;
import com.imes.domain.entities.query.model.base.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购取价方式高级查询模型
 *
 * <AUTHOR>
 * @since 2023-11-07 10:05:32
 */
@Data
@ApiModel("采购取价方式")
@QueryModel(
        name = "1281",
        remark = "采购取价方式",
        authTenant = true,
        auth = Auth.ALL,
        openapi = true,
        resume = "rangeNo",
        alias = "t1",
        showMode = true,
        searchApi = "/api/po/priceRange/queryList")
public class PoPriceRangeQueryVo extends BaseModel {

    @ApiModelProperty("取价方式编号")
    @QueryField(name = "取价方式编号")
    @EditField(readonly = true)
    private String rangeNo;

    @ApiModelProperty("允许人工输入")
    @QueryField(name = "允许人工输入", type = Type.Select, option = {"20", "否", "10", "是"})
    @EditField(required = true)
    private String limitInput;

    @ApiModelProperty("价格限制")
    @QueryField(name = "价格限制", type = Type.Select, dictOption = "PO_PRICE_RANGE_EDIT_TYPE")
    @EditField(required = true)
    private String editType;

    @ApiModelProperty("说明")
    @QueryField(name = "说明")
    @EditField(required = true)
    private String remarks;

    @ApiModelProperty("允许价格为0")
    @QueryField(name = "允许价格为0", type = Type.Select, option = {"20", "否", "10", "是"})
    @EditField(required = true)
    private String limitPriceZero;

    @ApiModelProperty("修改百分比(%)")
    @QueryField(name = "修改百分比(%)", type = Type.Number)
    private String editPercent;

    @ApiModelProperty("取价顺序")
    @QueryField(name = "取价顺序", alias = "t2")
    @EditField(required = true)
    private String priceNum;

    @ApiModelProperty("取价来源")
    @QueryField(name = "取价来源", alias = "t2", type = Type.Select, dictOption = "PO_PRICE_RANGE_DETAIL_PRICE_SOURCE")
    @EditField(required = true)
    private String priceSource;

    @ApiModelProperty("最近有效期(月)")
    @QueryField(name = "最近有效期(月)", alias = "t2")
    private String nearlyMonth;

    @ApiModelProperty("创建时间")
    @QueryField(name = "创建时间", type = Type.DateTime, order = OrderBy.DESC)
    @EditField(readonly = true)
    private String createOn;
}	
	
