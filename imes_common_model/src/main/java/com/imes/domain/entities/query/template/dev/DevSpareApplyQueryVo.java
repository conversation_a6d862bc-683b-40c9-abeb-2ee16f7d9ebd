package com.imes.domain.entities.query.template.dev;		
		
import com.imes.domain.entities.query.model.base.*;		
import io.swagger.annotations.ApiModel;		
import io.swagger.annotations.ApiModelProperty;		
import lombok.Data;		
@ApiModel(value = "备件领料申请高级查询模型")		
@Data		
@QueryModel(		
        name = "1222",		
        remark = "备件领料申请",		
        alias = "spare",		
        searchApi = "/dev/spareApply/findByCondition")
public class DevSpareApplyQueryVo extends BaseModel {		
		
	@ApiModelProperty("id")		
    @QueryField(name = "id", show = false)		
    private String id;		
		
    /**		
     * 申请单号		
     */		
    @ApiModelProperty(value = "申请单号")		
    @QueryField(name = "申请单号")		
    private String applyNo;		
		
    /**		
     * 申请部门		
     */		
    @ApiModelProperty(value = "申请部门")		
    @QueryField(name = "申请部门", alias = "co_department.depart_name")		
    private String deptName;		
		
    /**		
     * 申请人工号		
     */		
    @ApiModelProperty(value = "申请人工号")		
    @QueryField(name = "申请人工号")		
    private String userCode;		
		
    /**		
     * 申请人名称		
     */		
    @ApiModelProperty(value = "申请人名称")		
    @QueryField(name = "申请人名称")		
    private String userName;		
		
    /**		
     * 单据状态		
     */		
    @ApiModelProperty(value = "单据状态:20-生效待出库；30-已出库")		
    @QueryField(name = "单据状态", type = Type.Select, option = {"20", "生效待出库", "30", "已出库"})		
    private String status;		
		
		
    /**		
     * 领用类型		
     */		
    @ApiModelProperty(value = "领用类型:请参考数据字典：DEV_APPLY_TYPE")		
    @QueryField(name = "领用类型", type = Type.Select, dictOption = "DEV_APPLY_TYPE")		
    private String applyType;		
		
    /**		
     * 设备编码		
     */		
    @ApiModelProperty(value = "领用设备编码")		
    @QueryField(name = "领用设备编码")		
    private String devCode;		
		
    /**		
     * 设备名称		
     */		
    @ApiModelProperty(value = "领用设备名称")		
    @QueryField(name = "领用设备名称", alias = "dev")		
    private String devName;		
		
    /**		
     * 单据号		
     */		
    @ApiModelProperty(value = "业务单据号")		
    @QueryField(name = "单据号")		
    private String businessNo;		
		
    /**		
     * 记录号		
     */		
    @ApiModelProperty(value = "记录/设备号")		
    @QueryField(name = "记录/设备号")		
    private String reportNo;		
		
    /**		
     * 申请时间		
     */		
    @ApiModelProperty(value = "申请时间")		
    @QueryField(name = "申请时间", type = Type.Date, order = OrderBy.DESC)		
    private String applyDate;		
		
		
    /**		
     * 备注		
     */		
    @ApiModelProperty(value = "备注")		
    @QueryField(name = "备注")		
    private String remarks;		
		
		
}		
