package com.imes.domain.entities.query.template.wms;	
	
import io.swagger.annotations.ApiModel;	
import com.imes.domain.entities.query.model.base.Auth;	
import io.swagger.annotations.ApiModelProperty;	
import com.imes.domain.entities.query.model.base.BaseModel;	
import com.imes.domain.entities.query.model.base.QueryField;	
import com.imes.domain.entities.query.model.base.QueryModel;	
import com.imes.domain.entities.query.model.base.Type;	
import lombok.Data;	
	
@Data	
@ApiModel("出库明细查询")	
@QueryModel(	
        name = "0433",	
        remark = "出库明细查询",	
        searchApi = "/api/wms/outStorage/findOutBoundAndItemNew",	
        alias = {"bill", "item", "material", "bin"},	
        auth = Auth.PC)	
public class WmsOutStorageItemSearchVo extends BaseModel {	
	
	@ApiModelProperty("出库单号")	
    @QueryField(name = "出库单号")	
    private String storageOutCode;	
	
	@ApiModelProperty("出库明细单号")	
    @QueryField(name = "出库明细单号", alias = "item")	
    private String storageOutItemCode;	
	
	@ApiModelProperty("关联单号")	
    @QueryField(name = "关联单号")	
    private String receiptCode;	
	
	@ApiModelProperty("出库类型")	
    @QueryField(name = "出库类型", type = Type.Select, dictOption = "OUTBOUND_TYPE")	
    private String receiptType;	
	
	@ApiModelProperty("物料编码")	
    @QueryField(name = "物料编码", alias = "item")	
    private String materialCode;	
	
	@ApiModelProperty("物料名称")	
    @QueryField(name = "物料名称", alias = "material")	
    private String materialName;	
	
	@ApiModelProperty("物料型号")	
    @QueryField(name = "物料型号", alias = "material")	
    private String materialMarker;	
	
	@ApiModelProperty("规格")	
    @QueryField(name = "规格", alias = "material")	
    private String specification;	
	
	@ApiModelProperty("物料辅助属性")	
    @QueryField(name = "物料辅助属性", alias = "item")	
    private String skuCode;	
	
	@ApiModelProperty("单位")	
    @QueryField(name = "单位", alias = "item", type = Type.MultiSelect, sqlOption ="select code as value ,name as label from sys_unit")	
    private String unit;	
	
	@ApiModelProperty("出库数量")	
    @QueryField(name = "出库数量", alias = "item", type = Type.Number)	
    private String orderQty;	
	
	@ApiModelProperty("已拣货数量")	
    @QueryField(name = "已拣货数量", alias = "bin", type = Type.Number)	
    private String theInventoryQty;	
	
	@ApiModelProperty("仓库")	
    @QueryField(name = "仓库", alias = "wh.warehouse_name")	
    private String whName;	
	
	@ApiModelProperty("库区")	
    @QueryField(name = "库区", alias = "area")	
    private String areaName;	
	
	@ApiModelProperty("库位")	
    @QueryField(name = "库位", alias = "bin")	
    private String binCode;	
	
	@ApiModelProperty("物料类型")	
    @QueryField(name = "物料类型", type = Type.MultiSelect, dictOption = "MATERIAL_CATEGORY", alias = "material")	
    private String category;	
	
	@ApiModelProperty("批次")	
    @QueryField(name = "批次", alias = "bin")	
    private String batch;	
	
	@ApiModelProperty("出库日期")	
    @QueryField(name = "出库日期", type = Type.Date, format = "yyyy-MM-dd")	
    private String approveOn;	
	
	@ApiModelProperty("申请人")	
    @QueryField(name = "申请人", alias = "us.user_name")	
    private String applyName;	
	
	@ApiModelProperty("单据状态")	
    @QueryField(name = "单据状态", type = Type.Select, dictOption = "BUS_ORDERS_STATUS")	
    private String status;	
	
	@ApiModelProperty("申请部门")	
    @QueryField(name = "申请部门", alias = "item")	
    private String departName;	
	
	@ApiModelProperty("箱码")	
    @QueryField(name = "箱码", alias = "bin")	
    private String boxNo;	
	
}	
