package com.imes.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.imes.common.exception.CommonException;
import com.imes.domain.entities.system.base.po.SysExportTemplateField;
import com.imes.domain.entities.system.base.vo.SysExportTemplateWrap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.hssf.usermodel.HSSFPalette;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.beans.BeanInfo;
import java.beans.IntrospectionException;
import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class ExcelUtils {

    private static final short COLUMN_WIDTH = (short) (37.5 * 100);

    private static final String EXCEL_2003 = ".xls";

    private static final String EXCEL_2007 = ".xlsx";

    /**
     * 默认起始行
     */
    private static final Integer DEFAULT_START_ROW = 1;

    /**
     * 导出excel默认title行index
     */
    private static final Integer DEFAULT_TITLE_INDEX = 0;

    /**
     * 带注意事项的title行index
     */
    private static final Integer WITH_ATTENTIONS_TITLE_INDEX = 1;

    /**
     * 注意事项的行index
     */
    private static final Integer ATTENTIONS_INDEX = 0;

    private static final String SHEET_NAME = "Sheet0";

    private static final short ATTENTIONS_ROW_HEIGHT = 3500;

    /**
     * 最多可导出行数限制
     */
    private static final Integer MAX_EXPORT_NUM = 10000;

    /**
     * 导入限制
     */
    private static final Integer MAX_IMPORT_NUM = 10000;

    /**
     * 默认 excel 保护密码
     */
    private static final String DEFAULT_PWD = "ASDFGHJKL;'";

    /**
     * 获取导入的数据，使用默认起始行
     *
     * @param is      输入流
     * @param name    文件名称
     * @param maxCell 最大列
     * @return excel数据列表
     * @throws IOException 文件不存在
     */
    public static List<List<Object>> getImportData(InputStream is, String name, Integer maxCell) throws IOException {
        return ExcelUtils.getImportData(is, name, maxCell, DEFAULT_START_ROW);
    }

    /**
     * 获取导入的数据，使用自定义起始行
     *
     * @param is       输入流
     * @param name     文件名称 固定Sheet0
     * @param maxCell  最大列 从0开始
     * @param startRow 开始列 从0开始
     * @return excel数据列表
     * @throws IOException 文件不存在
     */
    public static List<List<Object>> getImportData(InputStream is, String name, Integer maxCell, Integer startRow) throws IOException {
        try (Workbook wb = getWorkbook(name, is)) {
            Sheet sheet;
            Row row;
            Cell cell;
            int flag;
            Object cellVal;
            List<Object> li;
            List<List<Object>> result = new ArrayList<>();
            sheet = wb.getSheetAt(0);
            AssertUtil.isFalse(sheet == null || !SHEET_NAME.equals(sheet.getSheetName()), "导入模板Sheet名称必须为【Sheet0】");
            for (int j = startRow; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
                if (row == null || row.getFirstCellNum() > maxCell) continue;
                li = new ArrayList<>();
                flag = 0;
                for (int k = 0; k <= maxCell; k++) {
                    cell = row.getCell(k);
                    cellVal = ExcelUtils.getCellValue(cell);
                    // 判断是否所有行都为空
                    if (StringUtils.isNullOrBlank(cellVal)) {
                        flag++;
                    }
                    // 检查Null数据
                    if (cellVal == null) {
                        cellVal = "";
                    }
                    li.add(cellVal);
                }
                if (!li.isEmpty()) {
                    if (flag <= maxCell) {
                        result.add(li);
                    }
                }
            }
            AssertUtil.notEmpty(result, "导入数据不能为空");
            AssertUtil.isFalse(result.size() > MAX_IMPORT_NUM, "已超出系统导入【10000】条数量限制，请分批次导入");
            return result;
        } finally {
            if (is != null) is.close();
        }
    }


    //并行处理优化（适用于大文件）
    public static List<List<Object>> getImportDataParallel(InputStream is, String name, Integer maxCell, Integer startRow) throws IOException {
        try (Workbook wb = getWorkbook(name, is)) {
            Sheet sheet = wb.getSheetAt(0);
            AssertUtil.isFalse(sheet == null || !SHEET_NAME.equals(sheet.getSheetName()), "导入模板Sheet名称必须为【Sheet0】");

            int lastRowNum = sheet.getLastRowNum();

            // 使用并行流处理
            List<List<Object>> result = IntStream.rangeClosed(startRow, lastRowNum)
                    .parallel()
                    .mapToObj(rowIndex -> {
                        Row row = sheet.getRow(rowIndex);
                        if (row == null || row.getFirstCellNum() > maxCell) return null;
                        return processRowOptimized(row, maxCell);
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            AssertUtil.notEmpty(result, "导入数据不能为空");
            AssertUtil.isFalse(result.size() > MAX_IMPORT_NUM, "已超出系统导入【10000】条数量限制，请分批次导入");
            return result;
        } finally {
            if (is != null) is.close();
        }
    }
    private static List<Object> processRowOptimized(Row row, Integer maxCell) {
        List<Object> rowData = new ArrayList<>(maxCell + 1);
        int emptyCount = 0;

        for (int k = 0; k <= maxCell; k++) {
            Cell cell = row.getCell(k);
            Object cellVal = ExcelUtils.getCellValue(cell);

            if (cellVal == null || StringUtils.isNullOrBlank(cellVal)) {
                cellVal = "";
                emptyCount++;
            }
            rowData.add(cellVal);
        }

        // 如果所有单元格都为空，返回null
        return emptyCount > maxCell ? null : rowData;
    }

    /**
     * 将内容写进EXCEL
     *
     * @param data  要写的数据
     * @param keys  字段的主键
     * @param heads 表头
     * @return excel
     */
    public static Workbook writeExcel(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions) {
        return ExcelUtils.writeExcel(data, keys, heads, attentions, null);
    }


    /**
     * 将内容写进EXCEL
     *
     * @param data  要写的数据
     * @param keys  字段的主键
     * @param heads 表头
     * @return excel
     */
    public static Workbook writeExcelForExport(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions) {
        return ExcelUtils.writeExcelForImport(data, keys, heads, attentions, null);
    }
    public static Workbook writeExcelSpecifyColor(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions,HashMap<String, String> colorItem) {
        Set<Integer> lockeCols = new HashSet<>();
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        // 判断是否需要锁定列
        if (CollectionUtils.isNotEmpty(lockeCols)) {
            sheet.protectSheet(DEFAULT_PWD);
        }
        for (int i = 0; i < heads.length; i++){
            sheet.setColumnWidth(i, COLUMN_WIDTH);
            if ("行号".equals( heads[i])){
                sheet.setColumnWidth(i, COLUMN_WIDTH/2);
            }
        }


        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyle1 = buildTitleStyleSpecifyColor(wb,IndexedColors.YELLOW1.getIndex());
        CellStyle cellStyleGreen = buildTitleStyleSpecifyColor(wb,IndexedColors.BRIGHT_GREEN1.getIndex());
        CellStyle cellStyle2 = buildDataCellStyle(wb, false);
        CellStyle lockedStyle = buildDataCellStyle(wb, true);

        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            if (colorItem.containsKey(heads[i])){
                cell.setCellStyle(cellStyleGreen);
            }else {
                cell.setCellStyle(cellStyle1);
            }
        }

        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < keys.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, data.get(i).get(keys[j]));
                if (lockeCols != null && lockeCols.contains(j)) {
                    cell.setCellStyle(lockedStyle);
                } else {
                    // 如果不在锁定之列中就解锁
                    cell.setCellStyle(cellStyle2);
                }
            }
        }
        return wb;
    }

    public static Workbook writeExcel(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions, Set<Integer> lockeCols) {
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        // 判断是否需要锁定列
        if (CollectionUtils.isNotEmpty(lockeCols)) {
            sheet.protectSheet(DEFAULT_PWD);
        }
        for (int i = 0; i < heads.length; i++)
            sheet.setColumnWidth(i, COLUMN_WIDTH);

        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyle1 = buildTitleStyle(wb);
        CellStyle cellStyle2 = buildDataCellStyle(wb, false);
        CellStyle lockedStyle = buildDataCellStyle(wb, true);

        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            cell.setCellStyle(cellStyle1);
        }


        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < keys.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, data.get(i).get(keys[j]));
                if (lockeCols != null && lockeCols.contains(j)) {
                    cell.setCellStyle(lockedStyle);
                } else {
                    // 如果不在锁定之列中就解锁
                    cell.setCellStyle(cellStyle2);
                }
            }
        }
        return wb;
    }

    public static Workbook writeExcelForImport(List<Map<String, Object>> data, String[] keys, String[] heads, String attentions, Set<Integer> lockeCols) {
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        // 判断是否需要锁定列
        if (CollectionUtils.isNotEmpty(lockeCols)) {
            sheet.protectSheet(DEFAULT_PWD);
        }
        for (int i = 0; i < heads.length; i++)
            sheet.setColumnWidth(i, COLUMN_WIDTH);

        // 新的标题索引，考虑到添加了新的第一行
        int titleIndex = attentions == null ? 1 : 2; // 原来是 DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX

        // 3.设置样式和字体
        CellStyle cellStyle1 = buildTitleStyle(wb);
        CellStyle cellStyle2 = buildDataCellStyle(wb, false);
        CellStyle lockedStyle = buildDataCellStyle(wb, true);

        // 添加说明行（最上面一行）
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
        Row infoRow = sheet.createRow(0);
        infoRow.setHeight((short)(9 * 200)); // 设置行高
        CellStyle infoStyle = wb.createCellStyle();
        Font infoFont = wb.createFont();
        infoFont.setBold(true);
        infoFont.setFontHeightInPoints((short) 10);
        infoFont.setColor(IndexedColors.RED.index); // 文字红色
        infoStyle.setWrapText(true); // 自动换行
        infoStyle.setFont(infoFont);
        Cell infoCell = infoRow.createCell(0);
        infoCell.setCellValue("1、导入文档必须使用本模板，有效导入数据从第3行开始，截止H列 \n2、模板中所有输入框都为文本，如果从其他excel拷贝数据时，只拷贝值，不要拷贝格式 \n3、带*必填");
        infoCell.setCellStyle(infoStyle);

        // 注意事项
        if (attentions != null) {
            // 合并第二行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(1, 1, 0, 2));
            Row attRow = sheet.createRow(1); // 原来是 ATTENTIONS_INDEX
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        // 创建红色字体
        CellStyle redFontStyle = wb.createCellStyle();
        // 复制原有标题样式的所有属性
        redFontStyle.cloneStyleFrom(cellStyle1);
        Font redFont = wb.createFont();
        redFont.setColor(IndexedColors.RED.index);
        redFont.setFontHeightInPoints((short) 12);
        redFont.setBold(true);
        redFontStyle.setFont(redFont);

        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            // 如果表头包含*号，则使用红色字体样式
            if (heads[i] != null && heads[i].contains("*")) {
                cell.setCellStyle(redFontStyle);
            } else {
                cell.setCellStyle(cellStyle1);
            }
        }

        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < keys.length; j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, data.get(i).get(keys[j]));
                if (lockeCols != null && lockeCols.contains(j)) {
                    cell.setCellStyle(lockedStyle);
                } else {
                    // 如果不在锁定之列中就解锁
                    cell.setCellStyle(cellStyle2);
                }
            }
        }
        return wb;
    }






    /**
     * 错误信息导出
     * */
    public static Workbook writeErrorExcel(List<List<String>> data, List<String> heads, String attentions) {
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        for (int i = 0; i < heads.size(); i++)
            sheet.setColumnWidth(i, COLUMN_WIDTH);

        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyle1 = buildTitleStyle(wb);
        CellStyle cellStyle = buildDataCellStyle(wb, false);
        CellStyle errorStyle = buildErrorDataCellStyle(wb, false);
        CellStyle sucessStyle = buildSucessDataCellStyle(wb, false);
        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.size(); i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads.get(i));
            cell.setCellStyle(cellStyle1);
        }
        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < heads.size(); j++) {
                Cell cell = row.createCell(j);
                setCellValue(cell, data.get(i).get(j));
                if (j == 0) {
                    if ("成功导入".equals(cell.getStringCellValue())) {
                        cell.setCellStyle(sucessStyle);
                    } else {
                        cell.setCellStyle(errorStyle);
                    }
                } else {
                    cell.setCellStyle(cellStyle);
                }
            }
        }
        return wb;
    }


    /**
     * 初始化数据单元格样式
     *
     * @param wb     工作空间
     * @param locked 是否锁定行
     * @return 单元格样式
     */
    public static CellStyle buildDataCellStyle(Workbook wb, boolean locked) {
        Font font2 = wb.createFont();
        font2.setColor(IndexedColors.BLACK.getIndex());
        font2.setFontHeightInPoints((short) 10);
        CellStyle cs = wb.createCellStyle();
        cs.setFont(font2);
        cs.setBorderLeft(BorderStyle.THIN);
        cs.setBorderRight(BorderStyle.THIN);
        cs.setBorderTop(BorderStyle.THIN);
        cs.setBorderBottom(BorderStyle.THIN);
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setLocked(locked);
        cs.setDataFormat(wb.createDataFormat().getFormat("@"));
        return cs;
    }


    public static CellStyle buildErrorDataCellStyle(Workbook wb, boolean locked) {
        Font font2 = wb.createFont();
        font2.setColor(IndexedColors.RED.getIndex());
        font2.setFontHeightInPoints((short) 10);
        CellStyle cs = wb.createCellStyle();
        cs.setFont(font2);
        cs.setBorderLeft(BorderStyle.THIN);
        cs.setBorderRight(BorderStyle.THIN);
        cs.setBorderTop(BorderStyle.THIN);
        cs.setBorderBottom(BorderStyle.THIN);
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setLocked(locked);
        cs.setDataFormat(wb.createDataFormat().getFormat("@"));
        return cs;
    }


    public static CellStyle buildSucessDataCellStyle(Workbook wb, boolean locked) {
        Font font2 = wb.createFont();
        font2.setColor(IndexedColors.GREEN.getIndex());
        font2.setFontHeightInPoints((short) 10);
        CellStyle cs = wb.createCellStyle();
        cs.setFont(font2);
        cs.setBorderLeft(BorderStyle.THIN);
        cs.setBorderRight(BorderStyle.THIN);
        cs.setBorderTop(BorderStyle.THIN);
        cs.setBorderBottom(BorderStyle.THIN);
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setLocked(locked);
        cs.setDataFormat(wb.createDataFormat().getFormat("@"));
        return cs;
    }


    public static CellStyle buildTitleStyle(Workbook wb) {
        CellStyle cs = wb.createCellStyle();
        Font font1 = wb.createFont();
        font1.setColor(IndexedColors.BLACK.getIndex());
        font1.setFontHeightInPoints((short) 12);
        font1.setBold(true);
        // 字体
        cs.setFont(font1);
        // 边框
        cs.setBorderLeft(BorderStyle.THICK);
        cs.setBorderRight(BorderStyle.THICK);
        cs.setBorderTop(BorderStyle.THICK);
        cs.setBorderBottom(BorderStyle.THICK);
        // 居中
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setVerticalAlignment(VerticalAlignment.CENTER);
        cs.setFillForegroundColor(IndexedColors.YELLOW1.getIndex());
        cs.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cs;
    }

    public static CellStyle buildTitleStyleSpecifyColor(Workbook wb,short color) {
        CellStyle cs = wb.createCellStyle();
        Font font1 = wb.createFont();
        font1.setColor(IndexedColors.BLACK.getIndex());
        font1.setFontHeightInPoints((short) 12);
        font1.setBold(true);
        // 字体
        cs.setFont(font1);
        // 边框
        cs.setBorderLeft(BorderStyle.THICK);
        cs.setBorderRight(BorderStyle.THICK);
        cs.setBorderTop(BorderStyle.THICK);
        cs.setBorderBottom(BorderStyle.THICK);
        // 居中
        cs.setAlignment(HorizontalAlignment.CENTER);
        cs.setVerticalAlignment(VerticalAlignment.CENTER);
        cs.setFillForegroundColor(color);
        cs.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cs;
    }

    /**
     * 将内容写进EXCEL
     *
     * @param data     要写的数据
     * @param template EXCEL配置模板
     * @return excel
     */
    public static Workbook writeExcel(List<Map<String, Object>> data, SysExportTemplateWrap template) {
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();
        // 2.创建一个sheet并命名
        Sheet sheet = wb.createSheet();
        for (int i = 0; i < template.getFields().size(); i++)
            sheet.setColumnWidth(i, COLUMN_WIDTH);

        int titleIndex = StringUtils.isNullOrBlank(template.getRemarks()) ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyle1 = wb.createCellStyle();
        CellStyle cellStyle1_detail = wb.createCellStyle();
        CellStyle cellStyle2 = wb.createCellStyle();

        Font font1 = wb.createFont();
        Font font2 = wb.createFont();

        font1.setColor(IndexedColors.BLACK.getIndex());
        font1.setFontHeightInPoints((short) 12);
        font1.setBold(true);

        font2.setColor(IndexedColors.BLACK.getIndex());
        font2.setFontHeightInPoints((short) 10);

        cellStyle1.setFont(font1);
        // 边框
        cellStyle1.setBorderLeft(BorderStyle.THICK);
        cellStyle1.setBorderRight(BorderStyle.THICK);
        cellStyle1.setBorderTop(BorderStyle.THICK);
        cellStyle1.setBorderBottom(BorderStyle.THICK);
        // 居中
        cellStyle1.setAlignment(HorizontalAlignment.CENTER);
        cellStyle1.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle1.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle1.setFillForegroundColor(IndexedColors.YELLOW1.getIndex());

        cellStyle1_detail.setFont(font1);
        // 边框
        cellStyle1_detail.setBorderLeft(BorderStyle.THICK);
        cellStyle1_detail.setBorderRight(BorderStyle.THICK);
        cellStyle1_detail.setBorderTop(BorderStyle.THICK);
        cellStyle1_detail.setBorderBottom(BorderStyle.THICK);
        // 居中
        cellStyle1_detail.setAlignment(HorizontalAlignment.CENTER);
        cellStyle1_detail.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle1_detail.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cellStyle1_detail.setFillForegroundColor(IndexedColors.AQUA.getIndex());

        cellStyle2.setFont(font2);
        cellStyle2.setBorderLeft(BorderStyle.THIN);
        cellStyle2.setBorderRight(BorderStyle.THIN);
        cellStyle2.setBorderTop(BorderStyle.THIN);
        cellStyle2.setBorderBottom(BorderStyle.THIN);
        cellStyle2.setAlignment(HorizontalAlignment.CENTER);

        // 注意事项
        if (!StringUtils.isNullOrBlank(template.getRemarks())) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight((short) ((StringUtils.countStr(template.getRemarks(), "\n") + 1) * 400));
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(template.getRemarks());
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < template.getFields().size(); i++) {
            SysExportTemplateField f = template.getFields().get(i);
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(f.getName() + (StringUtils.isNullOrBlank(f.getRemarks()) ? "" : "(" + f.getRemarks() + ")"));
            if (f.getIsDetail()) {
                cell.setCellStyle(cellStyle1_detail);
            } else {
                cell.setCellStyle(cellStyle1);
            }
        }

        // 5.写表体内容
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            for (int j = 0; j < template.getFields().size(); j++) {
                SysExportTemplateField f = template.getFields().get(j);
                Cell cell = row.createCell(j);
                Object val = data.get(i).get(f.getField());
                //进行字典转换
                if (!StringUtils.isNullOrBlank(f.getDict())) {
                    Map<String, String> dictMap = JSONObject.parseObject(f.getDict(), Map.class);
                    val = dictMap.containsKey(val) ? dictMap.get(val) : val;
                }
                setCellValue(cell, val);
                cell.setCellStyle(cellStyle2);
            }
        }
        return wb;
    }


    /**
     * 浏览器导出Excel
     *
     * @param response http响应
     * @param request  http请求
     * @param dataMap  导出数据的map格式
     * @param keys     字段名
     * @param heads    显示的title
     * @param fileName 导出文件名
     * @throws IOException 写excel错误
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new CommonException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new CommonException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcel(dataMap, keys, heads, null); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }


    public static void exportExcelAndSpecifyColor(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName,HashMap<String, String> colorItem) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new CommonException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new CommonException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcelSpecifyColor(dataMap, keys, heads, null,colorItem); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }

    /**
     * 浏览器导出Excel
     *
     * @param response http响应
     * @param request  http请求
     * @param dataMap  导出数据的map格式
     * @param template 导出模型对象
     * @throws IOException 写excel错误
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, SysExportTemplateWrap template) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new CommonException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new CommonException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        String fileName = template.getName();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcel(dataMap, template); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }

    /**
     * 带注意事项的浏览器导出Excel
     *
     * @param response   响应结果
     * @param request    响应结果
     * @param dataMap    导出数据
     * @param keys       字段名
     * @param heads      表头名
     * @param fileName   导出文件名
     * @param attentions 注意事项
     * @throws IOException 写excel错误
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName, String attentions) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new CommonException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
            throw new CommonException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcel(dataMap, keys, heads, attentions); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }


    /**
     * 错误报告输出
     * */
    public static void exportErrorExcel(HttpServletResponse response, HttpServletRequest request, List<List<String>> data, List<String> heads,
                                        String fileName, String attentions, int total, int errorNum, String errorMessage) throws IOException {
        //导出数量检查
        if (data.size() > MAX_EXPORT_NUM) {
            if (errorMessage == null) {
                errorMessage = "导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出";
            }
        } else if (data.size() <= 0) {
            if (errorMessage == null) {
                errorMessage = "暂无可导出的数据";
            }
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        Workbook wb = null;
        JSONObject jsonObject = new JSONObject();
        if (StringUtils.isNullOrBlank(errorMessage)) {
            wb = ExcelUtils.writeErrorExcel(data, heads, attentions);
            jsonObject.put("success", true);
        } else {
            jsonObject.put("success", false);
            jsonObject.put("error", URLEncoder.encode(errorMessage, "UTF8"));
        }
        jsonObject.put("total", String.valueOf(total));
        jsonObject.put("errorNum", String.valueOf(errorNum));
        jsonObject.put("successNum", String.valueOf(total - errorNum));
        response.setHeader("message", jsonObject.toJSONString());
        response.addHeader("Access-Control-Expose-Headers", "message");
        // 这时候把创建好的excel写入到输出流
        try (OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }


    /**
     * 自定义模版的导出
     *
     * @param response 响应结果
     * @param request  响应结果
     * @param data     导出数据
     * @param keys     字段名
     * @param fileName 导出文件名
     * @param is       模版文件输入流
     * @throws IOException 模版文件不存在
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> data, String[] keys,
                                   String fileName, InputStream is) throws IOException {
        exportExcel(response, request, data, keys, fileName, is, DEFAULT_START_ROW);
    }

    /**
     * 自定义模版的导出，若字符有为数字，导出类型则自动转换为数字
     *
     * @param response 响应结果
     * @param request  响应结果
     * @param data     导出数据
     * @param keys     字段名
     * @param fileName 导出文件名
     * @param is       模版文件输入流
     * @param hs       模板预留的行数
     * @throws IOException 模版文件不存在
     */
    public static void exportExcel(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> data, String[] keys,
                                   String fileName, InputStream is, Integer hs) throws IOException {
        Workbook wb = null;
        OutputStream out = null;
        try {
            wb = getWorkbook(fileName, is);
            Sheet sheet = wb.getSheet(SHEET_NAME);
            if (sheet == null)
                throw new IOException("请检查[Sheet0]是否存在");
            // 写表体内容  i=1,此为从数据的第1行开始取值，实际数据为从第0行开始，会丢数据；sheet.createRow：开始写数据的行数
            for (int i = 0; i < data.size(); i++) {
                Row row = sheet.createRow(i + hs);
                for (int j = 0; j < keys.length; j++) {

                    Cell cell = row.createCell(j);
                    //判断值是否为数字，若为数字则转换
                    if (data.get(i).get(keys[j]) != null && !("").equals(data.get(i).get(keys[j]))) {
                        boolean a = isNumeric(data.get(i).get(keys[j]).toString());
                        //System.out.println(a+"      存储值类型"+i+"  "+j);
                        if (a) {
                            cell.setCellValue(Double.parseDouble(data.get(i).get(keys[j]).toString()));
                        } else {
                            cell.setCellValue(data.get(i).get(keys[j]).toString());
                        }
                    } else {
                        cell.setCellValue("");
                    }

                }
            }
            // 导出
            out = response.getOutputStream();
            response.reset();
            // 定义浏览器响应表头，顺带定义下载名，比如students
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
            }
            // 定义下载的类型，标明是excel文件
            response.setContentType("application/vnd.ms-excel");
            // 这时候把创建好的excel写入到输出流
            wb.write(out);
            out.flush();
        } finally {
            if (out != null) out.close();
            if (wb != null) wb.close();
            if (is != null) is.close();

        }
    }

    /**
     * 正则 判断字符类型值是否为数字,包含小数、负数
     *
     * @param str 数字类型字符串
     * @return 是否为数字类型
     */
    public static boolean isNumeric(String str) {
        return str.matches("-?[0-9]+(\\.[0-9]+)?");
    }

    /**
     * 将bean List装换维Map List
     *
     * @param beans 要导出的pojo数据
     * @return 导出数据的<code>List<Map></code>格式
     */
    public static List<Map<String, Object>> getBeanMaps(List<?> beans) throws IllegalAccessException,
            IntrospectionException, InvocationTargetException {
        List<Map<String, Object>> dataMaps = new ArrayList<>();
        for (Object bean : beans) {
            dataMaps.add(convertBean(bean));
        }
        return dataMaps;
    }

    public static List<Map<String, Object>> getBeanMaps(List<?> beans, String pattern) throws IllegalAccessException,
            IntrospectionException, InvocationTargetException {
        List<Map<String, Object>> dataMaps = new ArrayList<>();
        for (Object bean : beans) {
            dataMaps.add(convertBean(bean, pattern));
        }
        return dataMaps;
    }

    /**
     * 将bean转换为Map
     *
     * @param bean pojo对象
     * @return pojo的map类型
     * @throws IntrospectionException    e
     * @throws InvocationTargetException e
     * @throws IllegalAccessException    e
     */
    public static <T> Map<String, Object> convertBean(T bean) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return convertBean(bean, sdf);
    }

    public static <T> Map<String, Object> convertBean(T bean, String pattern) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return convertBean(bean, sdf);
    }

    /**
     * @param bean pojo对象
     * @param sdf  日期格式
     * @return
     * @throws IntrospectionException
     * @throws InvocationTargetException
     * @throws IllegalAccessException
     */
    public static <T> Map<String, Object> convertBean(T bean, SimpleDateFormat sdf) throws IntrospectionException,
            InvocationTargetException, IllegalAccessException {
        Map<String, Object> dataMap = new HashMap<>();
        Class type = bean.getClass();
        BeanInfo beanInfo = Introspector.getBeanInfo(type);
        PropertyDescriptor[] propertyDescriptors = beanInfo.getPropertyDescriptors();
        PropertyDescriptor descriptor;
        for (PropertyDescriptor propertyDescriptor : propertyDescriptors) {
            descriptor = propertyDescriptor;
            String propertyName = descriptor.getName();
            if (!"class".equals(propertyName)) {
                Method readMethod = descriptor.getReadMethod();
                Object result = readMethod.invoke(bean);
                if (result != null) {
                    // 时间格式单独处理
                    if (result instanceof Date) {
                        result = sdf.format(result);
                    }
                    dataMap.put(propertyName, result);
                } else {
                    dataMap.put(propertyName, "");
                }
            }
        }
        return dataMap;
    }

    /**
     * 判断是否为低版本浏览器
     *
     * @param request 浏览器请求头
     * @return
     */
    public static boolean isLowVersionBrowser(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        return userAgent.contains("msie")
                || userAgent.contains("Trident")
                || userAgent.contains("Edge");
    }

    public static Object getCellValue(Cell cell) {
        Object value = null;
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                value = cell.getStringCellValue().trim();
                break;
            case BLANK:
                value = "";
                break;
            case BOOLEAN:
                value = cell.getBooleanCellValue();
                break;
            case FORMULA:
                try {
                    // 创建公式计算器
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                    // 直接计算公式并获取结果
                    CellValue cellValue = evaluator.evaluate(cell);

                    // 根据计算结果的类型来处理
                    switch (cellValue.getCellType()) {
                        case STRING:
                            value = cellValue.getStringValue().trim();
                            break;
                        case NUMERIC:
                            // 检查是否为日期格式
                            if (HSSFDateUtil.isCellDateFormatted(cell)) {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                value = sdf.format(HSSFDateUtil.getJavaDate(cellValue.getNumberValue()));
                            } else {
                                // 直接获取数值，避免格式化问题
                                double numValue = cellValue.getNumberValue();
                                // 如果是整数，返回整数格式
                                if (numValue == Math.floor(numValue)) {
                                    value = String.valueOf((long) numValue);
                                } else {
                                    value = String.valueOf(numValue);
                                }
                            }
                            break;
                        case BOOLEAN:
                            value = cellValue.getBooleanValue();
                            break;
                        case BLANK:
                            value = "";
                            break;
                        default:
                            value = "";
                    }
                } catch (Exception e) {
                    // 如果FormulaEvaluator失败，尝试直接获取数值
                    try {
                        // 尝试作为数值处理
                        double numValue = cell.getNumericCellValue();
                        if (HSSFDateUtil.isCellDateFormatted(cell)) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            value = sdf.format(HSSFDateUtil.getJavaDate(numValue));
                        } else {
                            if (numValue == Math.floor(numValue)) {
                                value = String.valueOf((long) numValue);
                            } else {
                                value = String.valueOf(numValue);
                            }
                        }
                    } catch (Exception ex) {
                        // 尝试作为字符串处理
                        try {
                            value = cell.getStringCellValue().trim();
                        } catch (Exception exc) {
                            value = "";
                        }
                    }
                }
                break;
            case NUMERIC:
                if (HSSFDateUtil.isCellDateFormatted(cell)) {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    value = sdf.format(HSSFDateUtil.getJavaDate(cell.getNumericCellValue()));
                } else {
                    double numValue = cell.getNumericCellValue();
                    if (numValue == Math.floor(numValue)) {
                        value = String.valueOf((long) numValue);
                    } else {
                        value = String.valueOf(numValue);
                    }
                }
                break;
            default:
                break;
        }
        return value;
    }

    /**
     * 通过文件名创建工作簿
     *
     * @param fileName 文件名
     * @param is       模版excel输入流
     * @return
     * @throws IOException
     */
    public static Workbook getWorkbook(String fileName, InputStream is) throws IOException {
        String suffixName = fileName.substring(fileName.lastIndexOf("."));
        if (!EXCEL_2003.equals(suffixName) && !EXCEL_2007.equals(suffixName)) {
            throw new CommonException("请上传excel文件。");
        }
        Workbook wb;
        if (EXCEL_2003.equals(suffixName)) {
            wb = new HSSFWorkbook(is);
        } else {
            wb = new XSSFWorkbook(is);
        }
        return wb;
    }

    /**
     * 文本单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getTextStyle(Workbook workbook) {
        CellStyle textStyle = workbook.createCellStyle();
        textStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        textStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        textStyle.setFont(getNormalFont(workbook)); //字体
        textStyle.setBorderBottom(BorderStyle.THIN); //下边框
        textStyle.setBorderLeft(BorderStyle.THIN); //左边框
        textStyle.setBorderTop(BorderStyle.THIN); //上边框
        textStyle.setBorderRight(BorderStyle.THIN); //右边框
        DataFormat format = workbook.createDataFormat();
        textStyle.setDataFormat(format.getFormat("@")); //文本格式
        return textStyle;
    }

    /**
     * 整数单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getIntegerStyle(Workbook workbook) {
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        integerStyle.setFont(getNormalFont(workbook)); //字体
        integerStyle.setBorderBottom(BorderStyle.THIN); //下边框
        integerStyle.setBorderLeft(BorderStyle.THIN); //左边框
        integerStyle.setBorderTop(BorderStyle.THIN); //上边框
        integerStyle.setBorderRight(BorderStyle.THIN); //右边框
        integerStyle.setDataFormat(HSSFDataFormat.getBuiltinFormat("#,#0")); //数据格式只显示整数
        return integerStyle;
    }

    /**
     * 整数单元格风格
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static CellStyle getFloatStyle(Workbook workbook) {
        CellStyle integerStyle = workbook.createCellStyle();
        integerStyle.setAlignment(HorizontalAlignment.CENTER); // 居中
        integerStyle.setVerticalAlignment(VerticalAlignment.CENTER); //垂直
        integerStyle.setFont(getNormalFont(workbook)); //字体
        integerStyle.setBorderBottom(BorderStyle.THIN); //下边框
        integerStyle.setBorderLeft(BorderStyle.THIN); //左边框
        integerStyle.setBorderTop(BorderStyle.THIN); //上边框
        integerStyle.setBorderRight(BorderStyle.THIN); //右边框
        DataFormat format = workbook.createDataFormat();
        integerStyle.setDataFormat(format.getFormat("0.00")); //两位小数
        return integerStyle;
    }


    public static CellStyle getNormalStyle(Workbook workbook){
        Font font2 = workbook.createFont();
        font2.setFontName("宋体");
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setFont(font2);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        return cellStyle;
    }
    public static CellStyle getNormalStyleYellow(Workbook workbook){
        Font font2 = workbook.createFont();
        font2.setFontName("宋体");
        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setFont(font2);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.YELLOW.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    /**
     * 普通字体
     *
     * @param workbook workbook
     * @return CellStyle
     */
    public static Font getNormalFont(Workbook workbook) {
        Font font = workbook.createFont();
        font.setBold(true);
        return font;
    }

    //验证excel导入的数据的最大长度
    public static void lengthValidation(List<Object> cells, int rowNo, String[] maxSize, String[] var) {
        if (cells == null || cells.size() == 0 || var == null || var.length == 0 || maxSize == null || maxSize.length == 0) {
            return;
        }
        if (var.length != maxSize.length) {
            return;
        }
        for (int i = 0; i < maxSize.length; i++) {
            if (!StringUtils.lengthValidation(cells.get(i), maxSize[i])) {
                int index = maxSize[i].toString().indexOf(",");
                if (index == -1) {
                    throw new CommonException(String.format("第[%d]行的[%s]的长度不能超过[%s]个字符", rowNo, var[i], maxSize[i]));
                } else {
                    String s = maxSize[i].replaceAll(",", "");
                    int j = Integer.parseInt(s);
                    throw new CommonException(String.format("第[%d]行的[%s]的长度不能超过[%d]且小数部分长度不能超过[%d]", rowNo, var[i]
                            , j / 10, j % 10));

                }
            }
        }
    }

    public static void downloadTemplate(HttpServletRequest request, HttpServletResponse response, String excelPath) {
        InputStream in = null;
        OutputStream out = null;
        try {
            ClassPathResource resource = new ClassPathResource(excelPath);
            String fileName = resource.getFilename();
            if (StringUtils.isNullOrBlank(fileName)) {
                throw new CommonException("excel模板文件不存在");
            }
            response.setContentType("application/octet-stream");
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            }
            in = resource.getInputStream();
            out = response.getOutputStream();
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
            throw new CommonException("模板下载时发生了异常！");
        } finally {
            try {
                if (out != null) {
                    out.close();
                }
                if (in != null) {
                    in.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 填写单元格值
     *
     * @param cell  单元格
     * @param value 值
     */
    private static void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Number) {
            cell.setCellValue(Double.parseDouble(value.toString()));
        } else if (value instanceof Date) {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            cell.setCellValue(simpleDateFormat.format(value));
        } else if (value instanceof LocalDate) {
            DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            cell.setCellValue(sdf.format((LocalDate) value));
        } else if (value instanceof LocalDateTime) {
            DateTimeFormatter sdf = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            cell.setCellValue(sdf.format((LocalDateTime) value));
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else {
            cell.setCellValue(value.toString());
        }
    }


    public static void writeExcelForZT(Set<String> ztColorItemSet,HttpServletRequest request, HttpServletResponse response, List<Map<String, Object>> exportForZT,List<Map<String, Object>> exportForMOM, String[] ztKeys, String[] ztHeads, String[] momKeys, String[] momHeads, String attentions) throws IOException {
        Set<Integer> lockeCols = new HashSet<>();
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();

        // 2.创建两个sheet并命名
        Sheet sheet1 = wb.createSheet("业务比对系统数据"); // 第一个sheet，命名为"业务比对系统数据"
        Sheet sheet2 = wb.createSheet("业务没有的数据"); // 第二个sheet，命名为"业务没有的数据"

        // 对第一个sheet进行操作
        processSheet(ztColorItemSet,sheet1, exportForZT, ztKeys, ztHeads, attentions, lockeCols, wb);

        // 对第二个sheet进行操作（如果需要相同数据）
        processSheet(null,sheet2, exportForMOM, momKeys, momHeads, attentions,lockeCols, wb);
        // 这时候把创建好的excel写入到输出流
        try ( OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }
    // 将原来的sheet处理逻辑提取为单独方法
    public static void processSheet(Set<String> ztColorItemSet, Sheet sheet, List<Map<String, Object>> data, String[] keys, String[] heads, String attentions, Set<Integer> lockeCols, Workbook wb) {

        // 判断是否需要锁定列
        if (CollectionUtils.isNotEmpty(lockeCols)) {
            sheet.protectSheet(DEFAULT_PWD);
        }

        for (int i = 0; i < heads.length; i++) {
            sheet.setColumnWidth(i, COLUMN_WIDTH);
            if ("行号".equals(heads[i])) {
                sheet.setColumnWidth(i, COLUMN_WIDTH / 2);
            }
        }

        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyleYellow = buildTitleStyleSpecifyColor(wb, IndexedColors.YELLOW1.getIndex());
        CellStyle cellStylePick = buildTitleStyleSpecifyColor(wb, IndexedColors.CORAL.getIndex());
        CellStyle cellStyleViolet = buildTitleStyleSpecifyColor(wb, IndexedColors.LAVENDER.getIndex());
        CellStyle cellStyle2 = buildDataCellStyle(wb, false);
        CellStyle lockedStyle = buildDataCellStyle(wb, true);

        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 2));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight(ATTENTIONS_ROW_HEIGHT);
            CellStyle cellStyle3 = wb.createCellStyle();
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 12);
            // 文字红色
            font3.setColor(IndexedColors.RED.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            if (ztColorItemSet != null && !ztColorItemSet.contains(heads[i])){
                cell.setCellStyle(cellStylePick);
            }else {
                cell.setCellStyle(cellStyleYellow);
            }
            if (heads[i].equals("匹配结果")) {
                cell.setCellStyle(cellStyleViolet);
            }
        }
        //因为多查了id，所以j要从下标为1的数据开始，是为了导入信息匹配到多条我们的数据时，将重复数据做个标记
        String id = "";
        // 5.写表体内容
        Set<Integer> jumpCellIndex = new HashSet<>(Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 8));
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            if (id.equals(data.get(i).get("id").toString()) && ztColorItemSet != null) {
                for (int j = 0; j < keys.length; j++) {
                    if (!jumpCellIndex.contains(j)) {
                        Cell cell = row.createCell(j);
                        setCellValue(cell, data.get(i).get(keys[j]));
                        if (lockeCols != null && lockeCols.contains(j)) {
                            cell.setCellStyle(lockedStyle);
                        } else {
                            // 如果不在锁定之列中就解锁
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            } else {
                id = data.get(i).get("id").toString();
                for (int j = 0; j < keys.length; j++) {
                    Cell cell = row.createCell(j);
                    setCellValue(cell, data.get(i).get(keys[j]));
                    if (lockeCols != null && lockeCols.contains(j)) {
                        cell.setCellStyle(lockedStyle);
                    } else {
                        // 如果不在锁定之列中就解锁
                        cell.setCellStyle(cellStyle2);
                    }
                }
            }
        }
    }


    // 将原来的sheet处理逻辑提取为单独方法
    public static void newOldStatisticsSheet(Set<String> ztColorItemSet, Sheet sheet, List<Map<String, Object>> data, String[] keys, String[] heads, String attentions, Set<Integer> lockeCols, Workbook wb,Map<String, Object> totalMap) {

        // 判断是否需要锁定列
        if (CollectionUtils.isNotEmpty(lockeCols)) {
            sheet.protectSheet(DEFAULT_PWD);
        }

        for (int i = 0; i < heads.length; i++) {
            sheet.setColumnWidth(i, COLUMN_WIDTH);
            if ("行号".equals(heads[i])) {
                sheet.setColumnWidth(i, COLUMN_WIDTH / 2);
            }
        }

        int titleIndex = attentions == null ? DEFAULT_TITLE_INDEX : WITH_ATTENTIONS_TITLE_INDEX;

        // 3.设置样式和字体
        CellStyle cellStyleYellow = buildTitleStyleSpecifyColor(wb, IndexedColors.YELLOW1.getIndex());
        CellStyle cellStyleGreen = buildTitleStyleSpecifyColor(wb, IndexedColors.GREEN.getIndex());
        CellStyle cellStylePick = buildTitleStyleSpecifyColor(wb, IndexedColors.CORAL.getIndex());
        CellStyle cellStyleViolet = buildTitleStyleSpecifyColor(wb, IndexedColors.LAVENDER.getIndex());
        CellStyle cellStyle2 = buildDataCellStyle(wb, false);
        CellStyle lockedStyle = buildDataCellStyle(wb, true);

        // 注意事项
        if (attentions != null) {
            // 合并第一行 1-3个cell
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 8));
            Row attRow = sheet.createRow(ATTENTIONS_INDEX);
            attRow.setHeight((short) 1150);
            CellStyle cellStyle3 = wb.createCellStyle();
            cellStyle3.setAlignment(HorizontalAlignment.CENTER);
            Font font3 = wb.createFont();
            font3.setBold(true);
            font3.setFontHeightInPoints((short) 36);
            // 文字红色
            font3.setColor(IndexedColors.BLACK.index);
            // 自动换行
            cellStyle3.setWrapText(true);
            cellStyle3.setFont(font3);
            Cell cell = attRow.createCell(0);
            cell.setCellValue(attentions);
            cell.setCellStyle(cellStyle3);
        }

        // 4.写表头
        // 单独设置标题行的样式
        Row rowOne = sheet.createRow(titleIndex);
        rowOne.setHeight((short) (2.5 * 200));
        for (int i = 0; i < heads.length; i++) {
            Cell cell = rowOne.createCell(i);
            cell.setCellValue(heads[i]);
            cell.setCellStyle(lockedStyle);
            if (heads[i].equals("匹配结果")) {
                cell.setCellStyle(cellStyleViolet);
            }
        }

        // 添加面积合计标题到最后一列再往后三列
        int lastColIndex = heads.length + 3; // 最后一列再往后三列
        // 如果totalMap中有面积合计的值，则添加到面积合计后面那一列
        if (totalMap != null && totalMap.containsKey("面积合计")) {
            Cell totalTitleCell = rowOne.createCell(lastColIndex);
            totalTitleCell.setCellValue("面积合计");
            totalTitleCell.setCellStyle(cellStyleGreen);

            // 设置列宽为原来的两倍
            sheet.setColumnWidth(lastColIndex, COLUMN_WIDTH * 2);

            Cell totalValueCell = rowOne.createCell(lastColIndex + 1);
            setCellValue(totalValueCell, totalMap.get("面积合计"));
            totalValueCell.setCellStyle(cellStyleGreen);
        }

        if (totalMap != null && totalMap.containsKey("楼栋总面积")) {
            Cell totalTitleCell = rowOne.createCell(lastColIndex + 3);
            totalTitleCell.setCellValue("楼栋总面积");

            // 设置列宽为原来的两倍
            sheet.setColumnWidth(lastColIndex + 3, COLUMN_WIDTH * 2);

            totalTitleCell.setCellStyle(cellStyleYellow);
            Cell totalValueCell = rowOne.createCell(lastColIndex + 4);
            setCellValue(totalValueCell, totalMap.get("楼栋总面积"));
            totalValueCell.setCellStyle(cellStyleYellow);
        }

        //因为多查了id，所以j要从下标为1的数据开始，是为了导入信息匹配到多条我们的数据时，将重复数据做个标记
        String id = "";
        // 5.写表体内容
        Set<Integer> jumpCellIndex = new HashSet<>(Arrays.asList(0, 1, 2, 3, 4, 5, 6, 7, 8));
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + titleIndex + 1);
            if (i == 0) {
                if (totalMap != null && totalMap.containsKey("新料占面积比率：")) {
                    Cell ratioTitleCell = row.createCell(lastColIndex);
                    ratioTitleCell.setCellValue("新料占面积比率：");
                    ratioTitleCell.setCellStyle(cellStyleGreen);
                    Cell ratioValueCell = row.createCell(lastColIndex + 1);
                    setCellValue(ratioValueCell, totalMap.get("新料占面积比率："));
                    ratioValueCell.setCellStyle(cellStyleGreen);
                }
                if (totalMap != null && totalMap.containsKey("旧料占面积比率：")) {
                    Cell totalTitleCell = row.createCell(lastColIndex + 3);
                    totalTitleCell.setCellValue("旧料占面积比率：");
                    totalTitleCell.setCellStyle(cellStyleYellow);
                    Cell totalValueCell = row.createCell(lastColIndex + 4);
                    setCellValue(totalValueCell, totalMap.get("旧料占面积比率："));
                    totalValueCell.setCellStyle(cellStyleYellow);
                }
            }
            if (id.equals(data.get(i).get("id").toString()) && ztColorItemSet != null) {
                for (int j = 0; j < keys.length; j++) {
                    if (!jumpCellIndex.contains(j)) {
                        Cell cell = row.createCell(j);
                        setCellValue(cell, data.get(i).get(keys[j]));
                        if (lockeCols != null && lockeCols.contains(j)) {
                            cell.setCellStyle(lockedStyle);
                        } else {
                            // 如果不在锁定之列中就解锁
                            cell.setCellStyle(cellStyle2);
                        }
                    }
                }
            } else {
                id = data.get(i).get("id").toString();
                for (int j = 0; j < keys.length; j++) {
                    Cell cell = row.createCell(j);
                    setCellValue(cell, data.get(i).get(keys[j]));
                    if (lockeCols != null && lockeCols.contains(j)) {
                        cell.setCellStyle(lockedStyle);
                    } else {
                        // 如果不在锁定之列中就解锁
                        cell.setCellStyle(cellStyle2);
                    }
                }
            }
        }
    }


    public static void writeTeamInfoExcelForZT(Set<String> ztColorItemSet,HttpServletRequest request, HttpServletResponse response, List<Map<String, Object>> exportForZT,List<Map<String, Object>> exportForMOM, String[] ztKeys, String[] ztHeads, String[] momKeys, String[] momHeads, String attentions) throws IOException {
        Set<Integer> lockeCols = new HashSet<>();
        // 1.创建工作簿
        Workbook wb = new HSSFWorkbook();

        // 2.创建两个sheet并命名
        Sheet sheet1 = wb.createSheet("计件项"); // 第一个sheet，命名为"业务比对系统数据"
        Sheet sheet2 = wb.createSheet("汇总-个人工资"); // 第二个sheet，命名为"业务没有的数据"

        // 对第一个sheet进行操作
        processSheet(ztColorItemSet,sheet1, exportForZT, ztKeys, ztHeads, attentions, lockeCols, wb);

        // 对第二个sheet进行操作（如果需要相同数据）
        processSheet(null,sheet2, exportForMOM, momKeys, momHeads, attentions,lockeCols, wb);
        // 这时候把创建好的excel写入到输出流
        try ( OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }


    /**
     * 浏览器导出Excel
     *
     * @param response http响应
     * @param request  http请求
     * @param dataMap  导出数据的map格式
     * @param keys     字段名
     * @param heads    显示的title
     * @param fileName 导出文件名
     * @throws IOException 写excel错误
     */
    public static void exportExcelForImport(HttpServletResponse response, HttpServletRequest request, List<Map<String, Object>> dataMap, String[] keys,
                                   String[] heads, String fileName) throws IOException {
        //导出数量检查
        if (dataMap.size() > MAX_EXPORT_NUM) {
            throw new CommonException("导出数量已超出" + MAX_EXPORT_NUM + "条，请修改查询条件再尝试导出");
        } else if (dataMap.size() <= 0) {
//            throw new CommonException("暂无可导出的数据，请修改查询条件再尝试导出");
        }
        response.reset();
        // 定义浏览器响应表头，顺带定义下载名，比如students
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8") + ".xls";
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName + ".xls").getBytes("gb2312"), "ISO8859-1"));
        }
        // 定义下载的类型，标明是excel文件
        response.setContentType("application/vnd.ms-excel");
        // 这时候把创建好的excel写入到输出流
        try (Workbook wb = ExcelUtils.writeExcelForExport(dataMap, keys, heads, null); OutputStream out = response.getOutputStream()) {
            wb.write(out);
            out.flush();
        }
    }

}
