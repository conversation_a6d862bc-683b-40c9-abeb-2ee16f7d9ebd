package com.imes.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;

@Slf4j
public class DateUtils {
    /**
     * yyyy-MM-dd HH:mm:ss
     *
     * @return
     */
    public static String getNowTime19() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(new Date());
    }

    public static Date transString2Date(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.parse(dateStr);
    }

    public static Date transString2Date10(String dateStr) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.parse(dateStr);
    }

    public static Date transString2Date(String dateStr, String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.parse(dateStr);
    }

    public static String transDate2String(Date date) {
        if (!StringUtils.isNullOrBlank(date)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } else {
            return "";
        }
    }

    public static String transDate2String(Date date, String pattern) {
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(date);
    }

    public static Date transDate2Date(Date date, String pattern) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat sdf1 = new SimpleDateFormat(pattern);
        String format = sdf.format(date);
        return sdf1.parse(format);
    }

    public static String transDate2Str10(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(date);
    }

    public static String transDate2MonthStr(Date date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        return sdf.format(date);
    }
//    public static long tranMinitue2second(BigDecimal b) throws ParseException {
//        b.multiply(new BigDecimal(60)).set
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
//        return sdf.format(date);
//    }

    public static Date getTomorrow() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 1);
        return cal.getTime();
    }

    public static Date getDateYMD() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            return sdf.parse(sdf.format(new Date()));
        } catch (ParseException e) {
            log.error("getDateYMD 日期解析异常：{}", e.getMessage());
        }
        return new Date();
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param hours     +1 后一小时，-2 前两小时
     * @return
     */
    public static Date getCalDayByHour(Date inputDate, int hours) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.HOUR_OF_DAY, hours);
        return cal.getTime();
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param mins      +1 后一分钟，-2 前两分钟
     * @return
     */
    public static Date getCalDayByMinitue(Date inputDate, int mins) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.MINUTE, mins);
        return cal.getTime();
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param second    +1 后一秒，-2 前两秒
     * @return
     */
    public static Date getCalDayBySecond(Date inputDate, int second) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.SECOND, second);
        return cal.getTime();
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param days      +1 后一天，-2 前两天
     * @return
     */
    public static Date getCalDay(Date inputDate, int days) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.DATE, days);
        return cal.getTime();
    }

    public static Date getCalDayStr(String inputDate, int days) throws ParseException {
        Date date = transString2Date(inputDate, "yyyy-MM-dd");
        return getCalDay(date, days);
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param weeks     +1 后一周，-2 前两周
     * @return
     */
    public static Date getCalWeek(Date inputDate, int weeks) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.WEEK_OF_MONTH, weeks);
        return cal.getTime();
    }


    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param quarters  +1 下一季度，-2 前两季度
     * @return
     */
    public static Date getCalQuarter(Date inputDate, int quarters) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        quarters = quarters * 3;
        cal.add(Calendar.MONTH, quarters);
        return cal.getTime();
    }


    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param years     +1 后一年，-2 前两年
     * @return
     */
    public static Date getCalYear(Date inputDate, int years) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.YEAR, years);
        return cal.getTime();
    }

    /**
     * 计算返回相对日期
     *
     * @param inputDate
     * @param months    +1 后一月，-2 前两月
     * @return
     */
    public static Date getCalMonth(Date inputDate, int months) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.add(Calendar.MONTH, months);
        return cal.getTime();
    }

    /**
     * 返回前N月、后N月的第一天
     *
     * @param inputDate
     * @param month     +1 后一月，-2 前两月
     * @return
     */
    public static Date getCalDayByMonth(Date inputDate, int month) {
        inputDate = getFirstDayOfMonth(inputDate);//每月日数不一样，所以一定要设定1号进行计算
        Calendar cal = Calendar.getInstance();
        cal.setTime(inputDate);
        cal.set(Calendar.MONTH, cal.get(Calendar.MONTH) + month);
        return cal.getTime();
    }

    /**
     * 返回前N月、后N月的相对的一天，不跨月
     * eg：1月31日的后一个月，返回2月28或29，不会是3月1日
     *
     * @param inputDate
     * @param month     +1 后一月，-2 前两月
     * @return
     */
    public static Date getCalDayByMonthRelative(Date inputDate, int month) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("dd");
        SimpleDateFormat sdfYM = new SimpleDateFormat("yyyy-MM");
        SimpleDateFormat sdfYMD = new SimpleDateFormat("yyyy-MM-dd");
        String dayAct = sdf.format(inputDate);


        Date targetMonth = getCalDayByMonth(inputDate, month);
        Date lastDay = getLastDayInMonth(targetMonth);
        if (sdf.format(lastDay).compareTo(dayAct) > 0) {
            return sdfYMD.parse(sdfYM.format(targetMonth) + "-" + dayAct);
        } else {
            return sdfYMD.parse(sdfYM.format(targetMonth) + "-" + sdf.format(lastDay));
        }
    }

    public static Date getLastDayInMonth(Date inputDate) {
        // 获取下月第一天
        inputDate = getCalDayByMonth(inputDate, 1);
        // 减一天，获取当月最后一天
        return getCalDay(inputDate, -1);
    }

    public static String dateToStringByModel(String model, Date date) {
        return new SimpleDateFormat(model).format(date);
    }

    public static String dateToStringByModel(String model) {
        return new SimpleDateFormat(model).format(new Date());
    }

    public static String dateToStringDefault(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(date);
    }

    public static String dateToStringDefault() {
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
    }

    public static Date getDateByCondition(Integer month, Integer day) {
        Date date = getDateYMD();
        Calendar cal = Calendar.getInstance();
        cal.clear();
        cal.setTime(date);
        cal.set(Calendar.MONTH, month - 1);
        cal.set(Calendar.DAY_OF_MONTH, day);
        return cal.getTime();
    }

    public static float exeMinitue1F(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        long oneMinitue = (long) 1000 * 60;
        return new BigDecimal(ms).divide(new BigDecimal(oneMinitue),
                1, RoundingMode.HALF_UP).floatValue();
    }

    /**
     * 计算整数分钟
     */
    public static Integer exeMinitue(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        long oneMinitue = (long) 1000 * 60;
        Integer time = new BigDecimal(ms).divide(new BigDecimal(oneMinitue),
                0, RoundingMode.HALF_UP).intValue();
        return time;
    }


    public static int exeHour(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        long oneHour = (long) 1000 * 60 * 60;
        return new BigDecimal(ms).divide(new BigDecimal(oneHour),
                0, RoundingMode.HALF_UP).intValue();
    }

    public static float exeHour1F(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        long oneHour = (long) 1000 * 60 * 60;
        return new BigDecimal(ms).divide(new BigDecimal(oneHour),
                1, RoundingMode.HALF_UP).floatValue();
    }

    public static BigDecimal exeDay(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        long oneDay = (long) 1000 * 60 * 60 * 24;
        return new BigDecimal(ms).divide(new BigDecimal(oneDay),
                1, RoundingMode.HALF_UP);
    }

    public static int exeMonth(Date startDate, Date endDate) {
        Calendar c1 = Calendar.getInstance();
        Calendar c2 = Calendar.getInstance();
        c1.setTime(startDate);
        c2.setTime(endDate);
        int year = c2.get(Calendar.YEAR) - c1.get(Calendar.YEAR);
        return c2.get(Calendar.MONTH) + year * 12 - c1.get(Calendar.MONTH);
    }

    public static Date middleDate(Date startDate, Date endDate) {
        long ms = endDate.getTime() - startDate.getTime();
        Calendar cal = Calendar.getInstance();
        cal.setTime(startDate);
        cal.add(Calendar.SECOND, (int) ms / 2000);
        return cal.getTime();
    }

    /**
     * 计算计划日期和实际日期差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static int getDays(Date startTime, Date endTime) {
        // 返回的日期差
        java.util.Calendar calst = java.util.Calendar.getInstance();
        java.util.Calendar caled = java.util.Calendar.getInstance();
        calst.setTime(startTime);
        caled.setTime(endTime);
        // 设置时间为0时
        calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
        calst.set(java.util.Calendar.MINUTE, 0);
        calst.set(java.util.Calendar.SECOND, 0);
        caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
        caled.set(java.util.Calendar.MINUTE, 0);
        caled.set(java.util.Calendar.SECOND, 0);
        // 得到两个日期相差的天数
        int days = ((int) (caled.getTime().getTime() / 1000) - (int) (calst.getTime().getTime() / 1000)) / 3600 / 24;

        // 提前开工/完工日期差显示为0
        if (days < 0) {
            days = 0;
        }
        return days;
    }

    /**
     * 获取下一个整点的时间
     *
     * @return
     * @throws ParseException
     */

    public static Date getNextIntegerHour() throws ParseException {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR_OF_DAY, 1);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH");
        return sdf.parse(sdf.format(calendar.getTime()) + ":00:00");
    }


    /**
     * 获取当天指定整点日期
     *
     * @param hour
     * @return
     */
    public static Date specifiedHour(Integer hour) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获取当天指定小时、分钟日期
     *
     * @param hour
     * @return
     */
    public static Date specifiedHourAndMinute(Integer hour, Integer minute) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.set(Calendar.HOUR_OF_DAY, hour);
        cal.set(Calendar.MINUTE, minute);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    /**
     * 获得该月第一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getFirstDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String firstDayOfMonth = sdf.format(cal.getTime());
        return firstDayOfMonth;
    }

    /**
     * 获得该月第一天
     *
     * @return
     */
    public static Date getFirstDayOfMonth(Date inputDate) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.setTime(inputDate);
        //获取某月最小天数
        int firstDay = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最小天数
        cal.set(Calendar.DAY_OF_MONTH, firstDay);
        return cal.getTime();
    }

    /**
     * 获得该月最后一天
     *
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfMonth(int year, int month) {
        Calendar cal = Calendar.getInstance();
        cal.clear();
        //设置年份
        cal.set(Calendar.YEAR, year);
        //设置月份
        cal.set(Calendar.MONTH, month - 1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String lastDayOfMonth = sdf.format(cal.getTime());
        return lastDayOfMonth;
    }

    public static int getDaysOfMonth(String strDate) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        Date date = sdf.parse(strDate);
        return DateUtils.getDaysOfMonth(date);
    }

    /**
     * 获取传入月份有多少天
     *
     * @param date
     * @return
     */
    public static int getDaysOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    /**
     * 判断传入日期是否为当月第一天
     *
     * @param date
     * @return
     */
    public static boolean isFirstDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH) == calendar
                .getActualMinimum(Calendar.DAY_OF_MONTH);
    }


    /**
     * 判断传入日期是否为当月最后一天
     *
     * @param date
     * @return
     */
    public static boolean isLastDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.DAY_OF_MONTH) == calendar
                .getActualMaximum(Calendar.DAY_OF_MONTH);
    }

    //获取传入日期的月份
    public static int getMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static int getYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 获取传入年份有多少天
     *
     * @param date
     * @return
     */
    public static int getDaysOfYear(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.getActualMaximum(Calendar.DAY_OF_YEAR);
    }

    public static Date getDateStart(Date d) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf2.parse(sdf.format(d) + " 00:00:00");
    }

    public static Date getDateEnd(Date d) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf2.parse(sdf.format(d) + " 23:59:59");
    }

    public static boolean isToday(Date d) {
        if (d == null) {
            return false;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        return sdf.format(c.getTime()).equals(sdf.format(d));
    }

    /**
     * 比较两个不同日期中的时间部分
     *
     * @param date1
     * @param date2
     * @return date1时间部分大于等于date2时间部分返回true, 反之false
     */
    public static boolean afterTime(Date date1, Date date2) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String str1 = sdf.format(date1);
        String str2 = sdf.format(date2);
        int i1 = Integer.parseInt(str1.replace(":", ""));
        int i2 = Integer.parseInt(str2.replace(":", ""));
        return i1 >= i2 ? true : false;
    }

    /**
     * 比较两个不同日期中的时间部分
     *
     * @param date1
     * @param date2
     * @return date1时间部分小于date2时间部分返回true, 反之false
     */
    public static boolean beforeTime(Date date1, Date date2) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        String str1 = sdf.format(date1);
        String str2 = sdf.format(date2);
        int i1 = Integer.parseInt(str1.replace(":", ""));
        int i2 = Integer.parseInt(str2.replace(":", ""));
        return i1 < i2 ? true : false;
    }


    /**
     * 计算计划日期和实际日期差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static long getLongDays(Date startTime, Date endTime) {
        // 返回的日期差
        java.util.Calendar calst = java.util.Calendar.getInstance();
        java.util.Calendar caled = java.util.Calendar.getInstance();
        calst.setTime(startTime);
        caled.setTime(endTime);
        // 设置时间为0时
        calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
        calst.set(java.util.Calendar.MINUTE, 0);
        calst.set(java.util.Calendar.SECOND, 0);
        caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
        caled.set(java.util.Calendar.MINUTE, 0);
        caled.set(java.util.Calendar.SECOND, 0);
        // 得到两个日期相差的天数
        long days = ((long) (caled.getTime().getTime() / 1000) - (long) (calst.getTime().getTime() / 1000)) / 3600 / 24;

        // 提前开工/完工日期差显示为0
        if (days < 0) {
            days = 0;
        }
        return days;
    }

    /**
     * 获取指定月份中的指定日期前的每一天
     *
     * @param year    年份
     * @param month   月份
     * @param maxDate 最大日期
     * @return
     */
    public static List<String> getMonthEveryDays(Integer year, Integer month, Date maxDate) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        List<String> rsList = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        Calendar curCal = Calendar.getInstance();
        Calendar maxCal = Calendar.getInstance();
        maxCal.set(Calendar.YEAR, year);
        maxCal.set(Calendar.MONTH, month - 1);

        cal.set(Calendar.YEAR, year);
        cal.set(Calendar.MONTH, month - 1);
        int actualMinimum = cal.getActualMinimum(Calendar.DAY_OF_MONTH);
        int actualMaximum = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        maxCal.set(Calendar.DAY_OF_MONTH, actualMaximum);
        cal.set(Calendar.DAY_OF_MONTH, actualMaximum);
        if (curCal.before(maxCal) || dateIsEquals(curCal.getTime(), maxCal.getTime())) {
            actualMaximum = curCal.get(Calendar.DAY_OF_MONTH) - 1;
        }
        for (int i = actualMinimum; i <= actualMaximum; i++) {
            cal.set(Calendar.DAY_OF_MONTH, i);
            rsList.add(df.format(cal.getTime()));
        }
        return rsList;
    }

    /**
     * 比较两个日期中
     *
     * @param date1
     * @param date2
     * @return date1时间部分大于date2时间部分返回true, 反之false
     */
    public static boolean timeStringCompare(String date1, String date2, String format) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(format);
        Date d1 = sdf.parse(date1);
        Date d2 = sdf.parse(date2);
        return d1.getTime() - d2.getTime() > 0 ? true : false;
    }


    /**
     * 计算计划日期和实际日期差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static long getDaysGap(Date startTime, Date endTime) {
        long time = startTime.getTime();
        long time1 = endTime.getTime();
        // 得到两个日期相差的天数
        long days = ((time1 / 1000) - (time / 1000)) / 3600 / 24;

        // 提前开工/完工日期差显示为0
        if (days < 0) {
            days = 0;
        }
        return days;
    }

    /**
     * 将秒数转换为日时分秒，
     *
     * @param second
     * @return
     */
    public static String secondToTime(long second) {
        long days = second / 86400;            //转换天数
        second = second % 86400;            //剩余秒数
        long hours = second / 3600;            //转换小时
        second = second % 3600;                //剩余秒数
        long minutes = second / 60;            //转换分钟
        second = second % 60;                //剩余秒数
        if (days > 0) {
            return days + "天" + hours + "小时" + minutes + "分" + second + "秒";
        } else {
            return hours + "小时" + minutes + "分" + second + "秒";
        }
    }


    /**
     * 获取当前时间的前n天的时间
     *
     * @param beforeNum
     * @return
     */
    public static Date getBeforeDate(int beforeNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) - beforeNum);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取当前时间的后n天的时间
     *
     * @param afterNum
     * @return
     */
    public static Date getAfterDate(int afterNum) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DATE, calendar.get(Calendar.DATE) + afterNum);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 时间切割 传入两个时间截取每日的00:00:00以及23:59:59
     *
     * @param sDate
     * @param eDate
     * @return
     */
    public static List<Date[]> cutDate(Date sDate, Date eDate) {
        Date sdateCopy = sDate;
        Date eDateCopy = eDate;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<Date[]> dateList = new ArrayList<>();
        while (sDate.compareTo(eDate) < 1) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(sDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date tempSDate = calendar.getTime();
            String string1 = sdf.format(tempSDate);
            tempSDate = Timestamp.valueOf(string1);

            calendar = Calendar.getInstance();
            calendar.setTime(sDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);

            Date tempEDate = calendar.getTime();
            String string = sdf.format(tempEDate);
            tempEDate = Timestamp.valueOf(string);
            dateList.add(new Date[]{tempSDate, tempEDate});

            //日期+1
            calendar = Calendar.getInstance();
            calendar.setTime(sDate);
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            sDate = calendar.getTime();
        }
        //第一天
        Date[] date0 = dateList.get(0);
        date0[0] = sdateCopy;

        //最后一天的日期改成截止的时间点，因为数据库可能不是23:59:59
        Date[] dates1 = dateList.get(dateList.size() - 1);
        dates1[1] = eDateCopy;
        return dateList;
    }

    /**
     * 判断两个日期是否为同一天
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Boolean dateIsEquals(Date date1, Date date2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        return fmt.format(date1).equals(fmt.format(date2));
    }

    /**
     * 判断两个日期是否为同一月
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Boolean monthIsEquals(Date date1, Date date2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMM");
        return fmt.format(date1).equals(fmt.format(date2));
    }

    /**
     * 判断两个日期是否为相同
     *
     * @param date1
     * @param date2
     * @return
     */
    public static Boolean dateIsEquals(Date date1, Date date2, String pattern) {
        SimpleDateFormat fmt = new SimpleDateFormat(pattern);
        return fmt.format(date1).equals(fmt.format(date2));
    }

    /**
     * 获取指定时间的前n秒的时间
     *
     * @param time
     * @param second
     * @return
     */
    public static Date getTimeBeforeSecond(String time, int second) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(df.parse(time));
        } catch (ParseException e) {
            log.error("getTimeBeforeSecond 日期设置异常：{}", e.getMessage());
        }
        calendar.add(Calendar.SECOND, -second);
        return calendar.getTime();
    }

    /**
     * 获取指定时间的后n秒的时间
     *
     * @param time
     * @param second
     * @return
     */
    public static Date getTimeAfterSecond(String time, int second) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Calendar calendar = Calendar.getInstance();
        try {
            calendar.setTime(df.parse(time));
        } catch (ParseException e) {
            log.error("getTimeAfterSecond 日期设置异常：{}", e.getMessage());
        }
        calendar.add(Calendar.SECOND, +second);
        return calendar.getTime();
    }


    /**
     * 计算计划日期和实际日期差
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static double getHoursGap(Date startTime, Date endTime) {
        double time = startTime.getTime();
        double time1 = endTime.getTime();
        // 得到两个日期相差的天数
        double days = ((time1 / 1000) - (time / 1000)) / 3600;

        // 提前开工/完工日期差显示为0
        if (days < 0) {
            days = 0;
        }
        return days;
    }

    /**
     * LocalDateTime转Date
     */
    public static Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * LocalDateTime转ZonedDateTime
     */
    public static ZonedDateTime toZonedDateTime(LocalDateTime localDateTime) {
        return localDateTime.atZone(ZoneId.systemDefault()).withZoneSameInstant(ZoneId.of("UTC"));
    }

    /**
     * Date转LocalDateTime
     */
    public static LocalDateTime toLocalDateTime(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * 获取传入日期所在周的第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDayOfWeek(Date date) throws ParseException {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        cal.setFirstDayOfWeek(Calendar.MONDAY);
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return transDate2Date(cal.getTime(), "yyyy-MM-dd");
    }

    /**
     * 获取传入日期所在周的第N天，1~7，不可超过7天
     *
     * @param date
     * @return
     */
    public static Date getOneDayOfWeek(Date date, int n) throws ParseException {
        if (n > 7) {
            AssertUtil.throwException("获取传入日期所在周的第N天不可超过7，传入的是{}", n);
        }
        if (n == 1) {
            return getFirstDayOfWeek(date);
        } else {
            Calendar cal = Calendar.getInstance();
            cal.setTime(getFirstDayOfWeek(date));
            cal.add(Calendar.DATE, n - 1);
            return transDate2Date(cal.getTime(), "yyyy-MM-dd");
        }
    }

    /**
     * 获取传入日期所在一周7天，返回结果类型List<Date>
     *
     * @param date
     * @return
     */
    public static List<Date> get7DayOfWeek(Date date) throws ParseException {
        List<Date> reList = new ArrayList<>();
        reList.add(getOneDayOfWeek(date, 1));
        reList.add(getOneDayOfWeek(date, 2));
        reList.add(getOneDayOfWeek(date, 3));
        reList.add(getOneDayOfWeek(date, 4));
        reList.add(getOneDayOfWeek(date, 5));
        reList.add(getOneDayOfWeek(date, 6));
        reList.add(getOneDayOfWeek(date, 7));
        return reList;
    }

    public static void main(String[] args) throws ParseException {
//        List<Date> dateList = get7DayOfWeek(new Date());
//        dateList.forEach(date -> {
//            System.out.println(date);
//        });
//        System.out.println(DateUtils.transDate2String(getBeforeDate(6), "yyyy-MM-dd"));

        for (String string : getAscDateList("2024-08-02", "2024-08-09")) {
            System.out.println(string);
        }

    }

    /**
     * 获取传入日期所在周的最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDayOfWeek(Date date) throws ParseException {
        return getCalDay(getFirstDayOfWeek(date), 6);
    }

    /**
     * 计算计划日期和实际日期差 负数也为负数 (日)
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static long getDiffLongDate(Date startTime, Date endTime) {
        // 返回的日期差
        java.util.Calendar calst = java.util.Calendar.getInstance();
        java.util.Calendar caled = java.util.Calendar.getInstance();
        calst.setTime(startTime);
        caled.setTime(endTime);
        // 设置时间为0时
        calst.set(java.util.Calendar.HOUR_OF_DAY, 0);
        calst.set(java.util.Calendar.MINUTE, 0);
        calst.set(java.util.Calendar.SECOND, 0);
        caled.set(java.util.Calendar.HOUR_OF_DAY, 0);
        caled.set(java.util.Calendar.MINUTE, 0);
        caled.set(java.util.Calendar.SECOND, 0);
        // 得到两个日期相差的天数
        long days = ((long) (caled.getTime().getTime() / 1000) - (long) (calst.getTime().getTime() / 1000)) / 3600 / 24;

        return days;
    }

    /**
     * 计算计划日期和实际日期差 负数也为负数 (精确到秒)
     *
     * @param startTime
     * @param endTime
     * @return
     */
    public static long getDiffLongDateTime(Date startTime, Date endTime) {
        // 返回的日期差
        java.util.Calendar calst = java.util.Calendar.getInstance();
        java.util.Calendar caled = java.util.Calendar.getInstance();
        calst.setTime(startTime);
        caled.setTime(endTime);

        long times = (long) (caled.getTime().getTime()) - (long) (calst.getTime().getTime());

        return times;
    }

    /**
     * 在List时间集合中获取最接近指定时间的时间
     *
     * @param dateTimeList 时间集合 yyyy-MM-dd HH:mm:ss
     * @param compareTime  要比对的时间
     * @return 集合中最接近比对时间的值
     */
    public static String getTheClosestDateTime(List<String> dateTimeList, String compareTime) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTimeList.stream()
                .min(Comparator.comparingLong(dateTime ->
                        Math.abs(ChronoUnit.SECONDS.between(LocalDateTime.parse(dateTime, df),
                                LocalDateTime.parse(compareTime, df))))).orElse(null);
    }

    /**
     * 获取两个月份之间的所有月份(含跨年)
     */
    public static List getMonthBetween(String minDate, String maxDate) throws ParseException {
        ArrayList result = new ArrayList();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化为年月
        Calendar min = Calendar.getInstance();
        Calendar max = Calendar.getInstance();
        min.setTime(sdf.parse(minDate));
        min.set(min.get(Calendar.YEAR), min.get(Calendar.MONTH), 1);
        max.setTime(sdf.parse(maxDate));
        max.set(max.get(Calendar.YEAR), max.get(Calendar.MONTH), 2);
        Calendar curr = min;
        while (curr.before(max)) {
            result.add(sdf.format(curr.getTime()));
            curr.add(Calendar.MONTH, 1);
        }
        Collections.sort(result, new Comparator() {
            @Override
            public int compare(Object o1, Object o2) {
                String str1 = (String) o1;
                String str2 = (String) o2;
                SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
                Date date1 = null;
                Date date2 = null;
                try {
                    date1 = format.parse(str1);
                    date2 = format.parse(str2);
                } catch (ParseException e) {
                    log.error("getMonthBetween 日期解析异常", e.getMessage());
                }
                if (date2.compareTo(date1) > 0) {
                    return -1;
                }
                return 1;
            }
        });
        return result;
    }

    /**
     * 根据时间 和时间格式 校验是否正确
     *
     * @param sDate  校验的日期
     * @param format 校验的格式
     * @return
     */
    public static boolean isLegalDate(String sDate, String format) {
        if (StringUtils.isNullOrBlank(sDate)) {
            return false;
        }
        DateFormat formatter = new SimpleDateFormat(format);
        try {
            Date date = formatter.parse(sDate);
            return sDate.equals(formatter.format(date));
        } catch (Exception e) {
            return false;
        }
    }

    /*
     * 判断今天是礼拜几 返回integer的数据便于使用计算
     * param: 当前时间
     * */
    public static Integer getWeekDayIndex(Date date) {
        //如果date没传，默认为当前日期
        if (StringUtils.isNullOrBlank(date)) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int index = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        Integer[] weeks = new Integer[]{7, 1, 2, 3, 4, 5, 6};
        return weeks[index];
    }

    /*
     * 获取2个日期相差的年数
     * */
    public static int yearDateDiff(String startDate, String endDate) throws ParseException {
        if (StringUtils.isNullOrBlank(startDate) || StringUtils.isNullOrBlank(startDate)) {
            return 0;
        }
        Calendar calBegin = Calendar.getInstance(); //获取日历实例
        Calendar calEnd = Calendar.getInstance();
        calBegin.setTime(transString2Date(startDate, "yyyy")); //字符串按照指定格式转化为日期
        calEnd.setTime(transString2Date(endDate, "yyyy"));
        return calEnd.get(Calendar.YEAR) - calBegin.get(Calendar.YEAR);
    }

    /***
     * 获取 日期月份，所有周的起始和结束时间
     * @param date
     * @return
     */
    public static List<Map<String, Object>> getWeekListOfMonth(Date date) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.setFirstDayOfWeek(Calendar.MONDAY);
        int weeks = c.getActualMaximum(Calendar.WEEK_OF_MONTH);

        LocalDate localDateate = LocalDate.parse(DateUtils.dateToStringByModel("yyyy-MM-dd", date), dateTimeFormatter);
        //月份第一周的起始时间和结束时间
        LocalDate firstDay = localDateate.with(TemporalAdjusters.firstDayOfMonth());
        String firstDayStr = firstDay.format(dateTimeFormatter);
        String sunStr = getSunOfWeek(firstDayStr);

        List<Map<String, Object>> weekInfos = new ArrayList<>();
        for (int i = 1; i <= weeks; i++) {
            Map<String, Object> weekInfo = new HashMap();
            //第一周的起始时间就是当月的1号，结束时间就是周日
            if (i == 1) {
                weekInfo.put("start", firstDayStr);
                weekInfo.put("end", sunStr);
                weekInfo.put("order", i);
                //计算接下来每周的周一和周日
            } else if (i < weeks) {
                //由于sunStr是上一周的周日，所以取周一要取sunStr的下一周的周一
                String monDay = getLastMonOfWeek(sunStr);
                sunStr = getSunOfWeek(monDay);
                weekInfo.put("start", monDay);
                weekInfo.put("end", sunStr);
                weekInfo.put("order", i);
                //由于最后一周可能结束时间不是周日，所以要单独处理
            } else {
                String monDay = getLastMonOfWeek(sunStr);
                //结束时间肯定就是当前月的最后一天
                LocalDate lastDay = localDateate.with(TemporalAdjusters.lastDayOfMonth());
                String endDay = lastDay.format(dateTimeFormatter);
                weekInfo.put("start", monDay);
                weekInfo.put("end", endDay);
                weekInfo.put("order", i);
            }

            weekInfos.add(weekInfo);

        }
        return weekInfos;
    }

    //算出所在周的周日
    public static String getSunOfWeek(String time) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDateate = LocalDate.parse(time, dateTimeFormatter);
        LocalDate endday = localDateate.with(TemporalAdjusters.next(java.time.DayOfWeek.MONDAY)).minusDays(1);
        String endDayStr = endday.format(dateTimeFormatter);
        return endDayStr;
    }

    //下一周的周一
    public static String getLastMonOfWeek(String time) {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate localDateate = LocalDate.parse(time, dateTimeFormatter);
        LocalDate endday = localDateate.with(TemporalAdjusters.next(java.time.DayOfWeek.MONDAY));
        String endDayStr = endday.format(dateTimeFormatter);
        return endDayStr;
    }

    /**
     * 计算两个日期之间的所有日期
     */
    public static List<String> getAscDateList(String startDate, String endDate) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        List<String> result = new ArrayList<>();
        if (end.compareTo(start) < 0) {
            return result;
        }
        while (true) {
            result.add(start.toString());
            if (start.compareTo(end) >= 0) {
                break;
            }
            start = start.plusDays(1);
        }
        return result;
    }

    public static List<Date> getAscDateList(Date start, Date end) {
        List<Date> result = new ArrayList<>();
        if (end.compareTo(start) < 0) {
            return result;
        }
        while (true) {
            result.add(start);
            if (start.compareTo(end) >= 0) {
                break;
            }
            start = getCalDay(start, 1);
        }
        return result;
    }

    /**
     * 将时间戳转化为日期字符串
     */
    public static String convertTimestamp(long timestamp) {
        // 将时间戳转换为Date对象
        Date date = new Date(timestamp);

        // 创建一个SimpleDateFormat对象并设置日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        // 格式化为日期字符串
        return sdf.format(date);
    }

    public static Double getConsumeTime(long beginTime) {
        long consumeTime = System.currentTimeMillis() - beginTime;
        //4.算出程序执行的时间
        BigDecimal consume = new BigDecimal(Long.toString(consumeTime)).divide(new BigDecimal("1000"), 1, BigDecimal.ROUND_HALF_UP);
        return consume.doubleValue();
    }

    /**
     * 将yyyy-MM-dd格式的日期字符串转换为Calendar对象
     *
     * @param dateStr 日期字符串（格式：yyyy-MM-dd）
     * @return Calendar对象
     * @throws IllegalArgumentException 如果日期格式错误
     */
    public static Calendar parseToCalendar(String dateStr) {
        if (dateStr == null || dateStr.isEmpty()) {
            return null;
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            Date date = sdf.parse(dateStr);
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            return cal;
        } catch (ParseException e) {
            throw new IllegalArgumentException("日期格式错误，应为 'yyyy-MM-dd' 格式", e);
        }
    }

    /**
     * 将数字（1-12）转换为中文大写数字
     *
     * @param number 数字
     * @return 中文大写数字字符串
     * @throws IllegalArgumentException 数字超出范围
     */
    public static String toChinese(int number) {
        if (number < 1 || number > 12) {
            throw new IllegalArgumentException("数字必须在1-12之间");
        }

        // 中文数字映射
        String[] chineseNumbers = {"一", "二", "三", "四", "五", "六",
                "七", "八", "九", "十", "十一", "十二"};

        return chineseNumbers[number - 1];
    }

    public static boolean isSameMonth(String dateStr1, String dateStr2) {
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        try {
            // 解析两个日期字符串
            LocalDate date1 = LocalDate.parse(dateStr1, formatter);
            LocalDate date2 = LocalDate.parse(dateStr2, formatter);

            // 判断两个日期是否在同一年的同一个月
            return date1.getYear() == date2.getYear() &&
                    date1.getMonthValue() == date2.getMonthValue();
        } catch (DateTimeParseException e) {
            // 处理日期解析异常
            System.err.println("日期格式错误，应使用 yyyy-MM-dd 格式");
            return false;
        }
    }

}
