server:
  port: 6100
    
spring:
  application:
    name: imes-qms
  cloud:
    config:
      name: bootstrap
      profile: ${SPRING_CLOUD_PROFILE_K8S:k8s}
      label: master
      uri: ${SPRING_CLOUD_URI_K8S:http://config-service.default:5001}
      password: '{cipher}0ffaf01797f2a1c80a98710afac1f86ea86937631eb0e17b1a80cb2d42ad6664951be2e24684ef0039a0b0d8e39c8d1a'
      username: fdlajfladjf

imes:
  base-url: /app
  lims:
    template:
      laboratory-report: classpath:template/化验报告.xlsx #化验报告导出模板
      test-excel: classpath:template/区域分析数据统计.xlsx #区域分析数据统计
  qms:
    template:
      qmsInspectionMaterial-model: classpath:template/多物料共同关联质检项导入模版.xlsx #多物料共同关联质检项导入模版
      qmsVerificationMaterial-model: template/物料检验基础数据导入模版.xlsx #物料检验基础数据导入模版
      qmsMaterialInspection-model: template/物料检验项导入模版.xlsx #物料检验项导入模版

# 日志配置
logback:
  path: ${imes.base-url}/logs/qms                 # 输出日目录
  file: ${spring.application.name}                # 输出日志名称
  level: ${K8S_LOGBACK_LEVEL:info}                # 输出级别

encrypt:
  key: fdafdas