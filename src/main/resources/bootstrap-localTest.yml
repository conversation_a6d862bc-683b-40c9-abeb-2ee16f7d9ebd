mybatis:
  config-location: classpath:mybatis/mybatis.cfg.xml        # mybatis配置文件所在路径
  type-aliases-package: com.imes.domain.entities    # 所有Entity别名类所在包
  mapper-locations:
    - classpath:mybatis/mapper/**/*.xml                       # mapper映射文件
# mybatis-plus映射
mybatis-plus:
  mapper-locations:
    - classpath:mybatis/mapper/**/*.xml
  configuration:
    # 配置sql打印
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource            # 当前数据源操作类型
    driver-class-name: com.mysql.cj.jdbc.Driver              # mysql驱动包
    url: ********************************************************************************************************************************************************************************************
    username: mycat
    password: MomStandalone#
    druid:
      initial-size: 20
      min-idle: 20
      max-active: 50
      max-wait: 60000
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  mvc:
    date-format: yyyy-MM-dd HH:mm:ss
  jpa:
    database: MYSQL
    open-in-view: true
    properties:
      hibernate:
        session_factory:
          statement_inspector: com.imes.common.interceptor.JpaInterceptor
    show-sql: true
  application:
    name: imes-qms
    cloud:
      config:
        name: bootstrap
        profile: localTest
        label: master
        uri: http://localhost:5001
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: MomStandalone
    publisher-confirms: true
    listener:
      simple:
        acknowledge-mode: manual
        concurrency: 1
        max-concurrency: 1
        prefetch: 1
  servlet:
    multipart:
      max-file-size: 400MB             # 单文件最大50MB
      max-request-size: 500MB
      enabled: true
  messages:
    basename: i18n/messages
    cache-duration: 3600

# 增加客户端手动刷新配置
management:
  endpoints:
    web:
      exposure:
        include: "*"
  health:
    show-details: always
    defaults:
      enabled: false
  metrics:
    web:
      server:
        auto-time-requests: false

eureka:
  client: #客户端注册进eureka服务列表内
    service-url:
      defaultZone: http://localhost:7100/eureka

## 分页插件配置
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

redis:
  host: ************
  port: 6379
  password: MomStandalone
  MaxWaitMillis: 10000
  maxIdle: 30
  minIdle: 20
  maxTotal: 100
  # 连接超时3秒
  connTimeout: 3000
  # 存储时间2小时
  expireTime: 7200

is_multi_tenant: true

# 实时数据库参数
processdb:
  # 连接名称
  pdconnectname: **************
  # 连接ip
  pdhostname: **************
  # 端口
  pdhostport: 38081
  # 用户名
  pdusername: root
  # 密码
  pdpassword: Rockwell123!@#
  # 组态前端端口
  prePort: 48082
zipkin:
  url: http://127.0.0.1:9411
  connectTimeout: 6000
  readTimeout: 6000
  flushInterval: 1
  compressionEnabled: true
  serviceName: ${spring.application.name}

server:
  port: 6105



##  自定义配置
imes:
  base-url: D:\test\log
  lims:
    template:
      laboratory-report: classpath:template/化验报告.xlsx #化验报告导出模板
      test-excel: classpath:template/区域分析数据统计.xlsx #区域分析数据统计
  qms:
    template:
      qmsInspectionMaterial-model: classpath:template/多物料共同关联质检项导入模版.xlsx #多物料共同关联质检项导入模版
      qmsVerificationMaterial-model: template/物料检验基础数据导入模版.xlsx #物料检验基础数据导入模版
      qmsMaterialInspection-model: template/物料检验项导入模版.xlsx #物料检验项导入模版                                                  # 黑名单类型

# 日志配置
logback:
  path: ${imes.base-url}/logs/sys                 # 输出日目录
  file: ${spring.application.name}                # 输出日志名称
  level: info

mobileServer:
  workUrl: http://127.0.0.1:9528/#/
  statisticUrl: http://127.0.0.1:9528/#/statistics
  warningUrl: http://127.0.0.1:9528/#/warn/detail


common:
  config:
    influxdb:
      url: http://************:8086/
      org: jetrun
      bucket: mom
      token: 5LHSicyY8s2CzHid4m7cxMmU6YzIQUQkOhbscusz4ewVeR2rri8EmWQEUBxuUp0pjnoazczqO9wdq8TR2Uswtw==
      enableGzip: true
      logLevel: NONE
      username: admin
      password: Rockwell123
      readTimeout: 5
      writeTimeout: 10
      connectTimeout: 5
    message:
      # 华为云短信状态报告接收地址
      statusCallBack: http://wx.nb-jetron.com:39999/api/sys/outsideInteface/receive/message/statusReport

arthas:
  http-port:  -1
  telnet-port: -1