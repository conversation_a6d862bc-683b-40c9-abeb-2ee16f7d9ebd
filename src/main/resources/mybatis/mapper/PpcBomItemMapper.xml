<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ProductionBomItemDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.BomItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_ver" jdbcType="INTEGER" property="bomVer" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="qty" jdbcType="REAL" property="qty" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    id, bom_code, bom_ver, material_code, qty, create_on, create_by, update_on, update_by, 
    remarks, unit_code
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from ppc_bom_item
    where id = #{id,jdbcType=VARCHAR}
  </select>


  <delete id="deleteByBomCodeAndBomVer" parameterType="com.imes.domain.entities.ppc.po.BomItem">
    delete from ppc_bom_item
    where bom_code = #{bomCode,jdbcType=VARCHAR}
    and Bom_Ver=#{bomVer,jdbcType=INTEGER}
  </delete>

  <select id="queryByParam" parameterType="com.imes.domain.entities.ppc.po.BomItem" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ppc_bom_item
    where 1=1
    <if test="id != null and id != ''">
      and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="bomCode != null and bomCode != ''">
      and bom_code =#{bomCode,jdbcType=VARCHAR}
    </if>
    <if test="materialCode != null and materialCode != ''">
      and material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="bomVer != null and bomVer != ''">
      and bom_ver = #{bomVer,jdbcType=INTEGER}
    </if>
  </select>
  <select id="queryByMap" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ppc_bom_item t1
    where 1=1
    <if test="id != null and id != ''">
      and t1.id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="bomCode != null and bomCode != ''">
      and t1.bom_code =#{bomCode,jdbcType=VARCHAR}
    </if>
    <if test="materialCode != null and materialCode != ''">
      and t1.material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="bomVer != null and bomVer != ''">
      and t1.bom_ver = #{bomVer,jdbcType=INTEGER}
    </if>
    <if test="isBom != null and isBom != ''">
      and exists (select 1 from ppc_material t2 where t2.material_code=t1.material_code and t2.category !='4')
    </if>

  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    delete from ppc_bom_item
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.BomItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    insert into ppc_bom_item (id, bom_code, bom_ver, 
      material_code, qty, create_on, 
      create_by, update_on, update_by, 
      remarks, unit_code)
    values (#{id,jdbcType=VARCHAR}, #{bomCode,jdbcType=VARCHAR}, #{bomVer,jdbcType=INTEGER}, 
      #{materialCode,jdbcType=VARCHAR}, #{qty,jdbcType=REAL}, #{createOn,jdbcType=TIMESTAMP}, 
      #{createBy,jdbcType=VARCHAR}, #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, 
      #{remarks,jdbcType=VARCHAR}, #{unitCode,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.BomItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    insert into ppc_bom_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bomCode != null">
        bom_code,
      </if>
      <if test="bomVer != null">
        bom_ver,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="qty != null">
        qty,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="unitCode != null">
        unit_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="bomCode != null">
        #{bomCode,jdbcType=VARCHAR},
      </if>
      <if test="bomVer != null">
        #{bomVer,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="qty != null">
        #{qty,jdbcType=REAL},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        #{unitCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.BomItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    update ppc_bom_item
    <set>
      <if test="bomCode != null">
        bom_code = #{bomCode,jdbcType=VARCHAR},
      </if>
      <if test="bomVer != null">
        bom_ver = #{bomVer,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="qty != null">
        qty = #{qty,jdbcType=REAL},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="unitCode != null">
        unit_code = #{unitCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.BomItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:20 CST 2019.
    -->
    update ppc_bom_item
    set bom_code = #{bomCode,jdbcType=VARCHAR},
      bom_ver = #{bomVer,jdbcType=INTEGER},
      material_code = #{materialCode,jdbcType=VARCHAR},
      qty = #{qty,jdbcType=REAL},
      create_on = #{createOn,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_on = #{updateOn,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      unit_code = #{unitCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>