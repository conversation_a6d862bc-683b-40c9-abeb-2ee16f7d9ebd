<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.ProcessDataProtectDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.StandardInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="upload_file" jdbcType="VARCHAR" property="uploadFile" />
    <result column="inspection_type" jdbcType="VARCHAR" property="inspectionType" />
    <result column="inspection_items" jdbcType="VARCHAR" property="inspectionItems" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <resultMap id="BaseResultMapDetail" type="com.imes.domain.entities.qms.po.StandardInspectionDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="child_item" jdbcType="VARCHAR" property="childItem" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="inspection_tool" jdbcType="VARCHAR" property="inspectionTool" />
    <result column="up_limit" jdbcType="VARCHAR" property="upLimit" />
    <result column="down_limit" jdbcType="VARCHAR" property="downLimit" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <resultMap id="BaseResultMapVo" type="com.imes.domain.entities.qms.vo.StandardInspectionVo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="version_code" jdbcType="VARCHAR" property="versionCode" />
    <result column="upload_file" jdbcType="VARCHAR" property="uploadFile" />
    <result column="inspection_type" jdbcType="VARCHAR" property="inspectionType" />
    <result column="inspection_items" jdbcType="VARCHAR" property="inspectionItems" />
    <result column="child_item" jdbcType="VARCHAR" property="childItem" />
    <result column="inspection_tool" jdbcType="VARCHAR" property="inspectionTool" />
    <result column="up_limit" jdbcType="VARCHAR" property="upLimit" />
    <result column="down_limit" jdbcType="VARCHAR" property="downLimit" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, product_name, version_code, upload_file, inspection_type,
      inspection_items,create_on,create_by,update_on,update_by,remarks
  </sql>
  <sql id="Base_Column_ListDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, parent_id,version_code,child_item, inspection_tool, up_limit, down_limit,create_on,
    create_by,update_on,update_by,remarks
  </sql>
    <select id="queryList"  parameterType="java.lang.String" resultType="com.imes.domain.entities.qms.vo.StandardInspectionVo">
      SELECT
        q.id id,
        q.product_name productName,
        q.version_code versionCode,
        q.upload_file uploadFile,
        q.inspection_type inspectionType,
        q.inspection_items inspectionItems,
        d.child_item childItem,
        d.inspection_tool inspectionTool,
        d.up_limit upLimit,
        d.down_limit downLimit
      FROM
        qms_std_inspection q
          LEFT JOIN qms_std_inspection_detail d ON q.id = d.parent_id
      <if test="productName != null and productName != ''">
        and q.product_name like concat('%',#{productName,jdbcType=VARCHAR},'%')
      </if>
    </select>
  <insert id="createNewItems" parameterType="java.util.List">
    insert into
    qms_inspection_items
    (id,inspection_type,inspection_item,create_on,create_by,remarks)
    values
    <foreach collection="list" item="item" separator =",">
      (
      #{item.id},
      #{item.inspectionType},
      #{item.inspectionItem},
      #{item.createOn},
      #{item.createBy},
      #{item.remarks}
      )
    </foreach>
  </insert>
  <select id="queryItemList" parameterType="java.lang.String" resultType="com.imes.domain.entities.qms.po.InspectionItems">
    select
           inspection_type inspectionType,
           inspection_item inspectionItem
    from qms_inspection_items
    where inspection_type=#{type}
  </select>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.qms.po.StandardInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 22 15:16:27 CST 2020.
    -->
    insert into qms_std_inspection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="versionCode != null">
        version_code,
      </if>
      <if test="inspectionItems != null">
        inspection_items,
      </if>
      <if test="uploadFile != null">
        upload_file,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="inspectionType != null">
        inspection_type,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="versionCode != null">
        #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="inspectionItems != null">
        #{inspectionItems,jdbcType=VARCHAR},
      </if>
      <if test="uploadFile != null">
        #{uploadFile,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="inspectionType != null">
        #{inspectionType,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="insertSelectiveDetails" parameterType="java.util.List">
    insert into
    qms_std_inspection_detail
    (<include refid="Base_Column_ListDetail"/>)
    values
    <foreach collection="list" item="item" separator =",">
      (
      #{item.id},
      #{item.parentId},
      #{item.versionCode},
      #{item.childItem},
      <if test="item.inspectionTool!=null">
        #{item.inspectionTool},
      </if>
      <if test="item.inspectionTool==null">
        null,
      </if>
      <if test="item.upLimit!=null">
        #{item.upLimit},
      </if>
      <if test="item.upLimit==null">
        null,
      </if>
      <if test="item.downLimit != null">
        #{item.downLimit},
      </if>
      <if test="item.downLimit == null">
        null,
      </if>
      <if test="item.createOn !=null">
        #{item.createOn},
      </if>
      <if test="item.createOn==null">
        null,
      </if>
      <if test="item.createBy != null">
        #{item.createBy},
      </if>
      <if test="item.createBy == null">
        null,
      </if>
      <if test="item.updateOn != null">
        #{item.updateOn},
      </if>
      <if test="item.updateOn == null">
        null,
      </if>
      <if test="item.updateBy != null">
        #{item.updateBy},
      </if>
      <if test="item.updateBy == null">
        null,
      </if>
      <if test="item.remarks != null">
        #{item.remarks},
      </if>
      <if test="item.remarks == null">
        null
      </if>
      )
    </foreach>
  </insert>
    <delete id="deleteItem" parameterType="java.lang.String">
      delete from qms_inspection_items where id =#{id}
    </delete>
  <delete id="deleteStandardListDetail" parameterType="java.lang.String">
    delete from qms_std_inspection_detail where parent_id =#{id}
  </delete>
  <delete id="deleteStandardList" parameterType="java.lang.String">
    delete from qms_std_inspection where id =#{id}
  </delete>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.qms.po.StandardInspection">
  <!--
    WARNING - @mbg.generated
    This element is automatically generated by MyBatis Generator, do not modify.
    This element was generated on Fri Mar 13 17:56:22 CST 2020.
  -->
  update qms_std_inspection
  <set>
    <if test="versionCode != null">
      version_code = #{versionCode,jdbcType=VARCHAR},
    </if>
    <if test="inspectionItems != null">
      inspection_items = #{inspectionItems,jdbcType=VARCHAR},
    </if>
    <if test="uploadFile != null">
      upload_file = #{uploadFile,jdbcType=VARCHAR},
    </if>
    <if test="productName != null">
      product_name = #{productName,jdbcType=VARCHAR},
    </if>
    <if test="inspectionType != null">
      inspection_type = #{inspectionType,jdbcType=VARCHAR},
    </if>
    <if test="createOn != null">
      create_on = #{createOn,jdbcType=TIMESTAMP},
    </if>
    <if test="createBy != null">
      create_by = #{createBy,jdbcType=VARCHAR},
    </if>
    <if test="updateOn != null">
      update_on = #{updateOn,jdbcType=TIMESTAMP},
    </if>
    <if test="updateBy != null">
      update_by = #{updateBy,jdbcType=VARCHAR},
    </if>
    <if test="remarks != null">
      remarks = #{remarks,jdbcType=VARCHAR},
    </if>
  </set>
  where id = #{id,jdbcType=VARCHAR}
</update>
<update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.qms.po.StandardInspection">
<!--
  WARNING - @mbg.generated
  This element is automatically generated by MyBatis Generator, do not modify.
  This element was generated on Fri Mar 13 17:56:22 CST 2020.
-->
update qms_std_inspection
set version_code = #{versionCode,jdbcType=VARCHAR},
inspection_items = #{inspectionItems,jdbcType=VARCHAR},
upload_file = #{uploadFile,jdbcType=VARCHAR},
product_name = #{productName,jdbcType=VARCHAR},
inspection_type = #{inspectionType,jdbcType=VARCHAR},
create_on = #{createOn,jdbcType=TIMESTAMP},
create_by = #{createBy,jdbcType=VARCHAR},
update_on = #{updateOn,jdbcType=TIMESTAMP},
update_by = #{updateBy,jdbcType=VARCHAR},
remarks = #{remarks,jdbcType=VARCHAR}
where id = #{id,jdbcType=VARCHAR}
</update>
  <update id="updateByPrimaryKeySelectivedetail" parameterType="com.imes.domain.entities.qms.po.StandardInspectionDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_std_inspection_detail
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="versionCode != null">
        version_code = #{versionCode,jdbcType=VARCHAR},
      </if>
      <if test="childItem != null">
        child_item = #{childItem,jdbcType=VARCHAR},
      </if>
      <if test="inspectionTool != null">
        inspection_tool = #{inspectionTool,jdbcType=VARCHAR},
      </if>
      <if test="upLimit != null">
        up_limit = #{upLimit,jdbcType=VARCHAR},
      </if>
      <if test="downLimit != null">
        down_limit = #{downLimit,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyDetail" parameterType="com.imes.domain.entities.qms.po.StandardInspectionDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_std_inspection_detail
    set version_code = #{versionCode,jdbcType=VARCHAR},
    parent_id = #{parentId,jdbcType=VARCHAR},
    child_item = #{childItem,jdbcType=VARCHAR},
    inspection_tool = #{inspectionTool,jdbcType=VARCHAR},
    up_limit = #{upLimit,jdbcType=VARCHAR},
    down_limit = #{downLimit,jdbcType=VARCHAR},
    create_on = #{createOn,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_on = #{updateOn,jdbcType=TIMESTAMP},
    update_by = #{updateBy,jdbcType=VARCHAR},
    remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateItems" parameterType="com.imes.domain.entities.qms.po.InspectionItems">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_inspection_items
    <set>
      <if test="inspectionType != null">
        inspection_type = #{inspectionType,jdbcType=VARCHAR},
      </if>
      <if test="inspectionItem != null">
        inspection_item = #{inspectionItem,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>