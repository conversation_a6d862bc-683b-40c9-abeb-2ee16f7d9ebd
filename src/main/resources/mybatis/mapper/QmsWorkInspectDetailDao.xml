<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.QmsWorkInspectDetailDao">

       <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.QmsWorkInspectDetail">
              <id column="id" jdbcType="VARCHAR" property="id"/>
              <result column="inspect_no" jdbcType="VARCHAR" property="inspectNo"/>
              <result column="line_no" jdbcType="INTEGER" property="lineNo"/>
              <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
              <result column="produce_qty" jdbcType="DECIMAL" property="produceQty"/>
              <result column="status" jdbcType="VARCHAR" property="status"/>
              <result column="area" jdbcType="DECIMAL" property="area"/>
              <result column="wide" jdbcType="VARCHAR" property="wide"/>
              <result column="name" jdbcType="VARCHAR" property="name"/>
              <result column="length" jdbcType="VARCHAR" property="length"/>
              <result column="material_marker" jdbcType="VARCHAR" property="materialMarker"/>
              <result column="milling_groove" jdbcType="VARCHAR" property="millingGroove"/>
              <result column="screw_width" jdbcType="VARCHAR" property="screwWidth"/>
              <result column="screw_heigh" jdbcType="VARCHAR" property="screwHeigh"/>
              <result column="ec_r" jdbcType="VARCHAR" property="ecR"/>
              <result column="patch_info" jdbcType="VARCHAR" property="patchInfo"/>
              <result column="prepare_finish_qty" jdbcType="DECIMAL" property="prepareFinishQty"/>
              <result column="material_change_qty" jdbcType="DECIMAL" property="materialChangeQty"/>
              <result column="material_finish_qty" jdbcType="DECIMAL" property="materialFinishQty"/>
              <result column="new_material_qty" jdbcType="DECIMAL" property="newMaterialQty"/>
              <result column="is_cut_board" jdbcType="VARCHAR" property="isCutBoard"/>
              <result column="is_cut_corner" jdbcType="VARCHAR" property="isCutCorner"/>
              <result column="is_contain_rabbet" jdbcType="VARCHAR" property="isContainRabbet"/>
              <result column="sample_pic" jdbcType="VARCHAR" property="samplePic"/>
              <result column="part_pic" jdbcType="VARCHAR" property="partPic"/>
              <result column="main_material" jdbcType="VARCHAR" property="mainMaterial"/>
              <result column="line_remarks" jdbcType="VARCHAR" property="lineRemarks"/>
              <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
              <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
              <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
              <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
       </resultMap>

       <sql id="Base_Column_List">
              id,
        inspect_no,
        line_no,
        material_code,
        produce_qty,
        status,
        area,
        wide,
        name,
        length,
        material_marker,
        milling_groove,
        screw_width,
        screw_heigh,
        ec_r,
        patch_info,
        prepare_finish_qty,
        material_change_qty,
        material_finish_qty,
        new_material_qty,
        is_cut_board,
        is_cut_corner,
        is_contain_rabbet,
        sample_pic,
        part_pic,
        main_material,
        line_remarks,
        create_on,
        create_by,
        update_on,
        update_by
       </sql>

    <select id="queryList" resultType="com.imes.domain.entities.query.template.qms.QmsWorkInspectDetailSearchVo">
        select qms_work_inspect_detail.id,
               qms_work_inspect_detail.inspect_no,
               qms_work_inspect_detail.line_no,
               qms_work_inspect_detail.material_code,
               qms_work_inspect_detail.produce_qty,
               qms_work_inspect_detail.status,
               qms_work_inspect_detail.area,
               qms_work_inspect_detail.wide,
               qms_work_inspect_detail.name,
               qms_work_inspect_detail.length,
               qms_work_inspect_detail.material_marker,
               qms_work_inspect_detail.milling_groove,
               qms_work_inspect_detail.screw_width,
               qms_work_inspect_detail.screw_heigh,
               qms_work_inspect_detail.ec_r,
               qms_work_inspect_detail.patch_info,
               qms_work_inspect_detail.prepare_finish_qty,
               qms_work_inspect_detail.material_change_qty,
               qms_work_inspect_detail.material_finish_qty,
               qms_work_inspect_detail.new_material_qty,
               qms_work_inspect_detail.is_cut_board,
               qms_work_inspect_detail.is_cut_corner,
               qms_work_inspect_detail.is_contain_rabbet,
               qms_work_inspect_detail.sample_pic,
               qms_work_inspect_detail.part_pic,
               qms_work_inspect_detail.main_material,
               qms_work_inspect_detail.line_remarks,
               qms_work_inspect_detail.create_on,
               qms_work_inspect_detail.create_by,
               qms_work_inspect_detail.update_on,
               qms_work_inspect_detail.update_by
        from qms_work_inspect_detail ${ew.customSqlSegment}
    </select>

       <select id="queryByCond" resultMap="BaseResultMap">
              SELECT
              <include refid="Base_Column_List"/>
              FROM
              qms_work_inspect_detail
              <where>
                     <if test="id != null and id != ''">
                            AND id = #{id, jdbcType=VARCHAR}
                     </if>
                     <if test="inspectNo != null and inspectNo != ''">
                            AND inspect_no = #{inspectNo, jdbcType=VARCHAR}
                     </if>
                     <if test="lineNo != null">
                            AND line_no = #{lineNo, jdbcType=INTEGER}
                     </if>
                     <if test="materialCode != null and materialCode != ''">
                            AND material_code = #{materialCode, jdbcType=VARCHAR}
                     </if>
                     <if test="produceQty != null">
                            AND produce_qty = #{produceQty, jdbcType=DECIMAL}
                     </if>
                     <if test="status != null and status != ''">
                            AND status = #{status, jdbcType=VARCHAR}
                     </if>
                     <if test="area != null">
                            AND area = #{area, jdbcType=DECIMAL}
                     </if>
                     <if test="wide != null and wide != ''">
                            AND wide = #{wide, jdbcType=VARCHAR}
                     </if>
                     <if test="name != null and name != ''">
                            AND name = #{name, jdbcType=VARCHAR}
                     </if>
                     <if test="length != null and length != ''">
                            AND length = #{length, jdbcType=VARCHAR}
                     </if>
                     <if test="materialMarker != null and materialMarker != ''">
                            AND material_marker = #{materialMarker, jdbcType=VARCHAR}
                     </if>
                     <if test="millingGroove != null and millingGroove != ''">
                            AND milling_groove = #{millingGroove, jdbcType=VARCHAR}
                     </if>
                     <if test="screwWidth != null and screwWidth != ''">
                            AND screw_width = #{screwWidth, jdbcType=VARCHAR}
                     </if>
                     <if test="screwHeigh != null and screwHeigh != ''">
                            AND screw_heigh = #{screwHeigh, jdbcType=VARCHAR}
                     </if>
                     <if test="ecR != null and ecR != ''">
                            AND ec_r = #{ecR, jdbcType=VARCHAR}
                     </if>
                     <if test="patchInfo != null and patchInfo != ''">
                            AND patch_info = #{patchInfo, jdbcType=VARCHAR}
                     </if>
                     <if test="prepareFinishQty != null">
                            AND prepare_finish_qty = #{prepareFinishQty, jdbcType=DECIMAL}
                     </if>
                     <if test="materialChangeQty != null">
                            AND material_change_qty = #{materialChangeQty, jdbcType=DECIMAL}
                     </if>
                     <if test="materialFinishQty != null">
                            AND material_finish_qty = #{materialFinishQty, jdbcType=DECIMAL}
                     </if>
                     <if test="newMaterialQty != null">
                            AND new_material_qty = #{newMaterialQty, jdbcType=DECIMAL}
                     </if>
                     <if test="isCutBoard != null and isCutBoard != ''">
                            AND is_cut_board = #{isCutBoard, jdbcType=VARCHAR}
                     </if>
                     <if test="isCutCorner != null and isCutCorner != ''">
                            AND is_cut_corner = #{isCutCorner, jdbcType=VARCHAR}
                     </if>
                     <if test="isContainRabbet != null and isContainRabbet != ''">
                            AND is_contain_rabbet = #{isContainRabbet, jdbcType=VARCHAR}
                     </if>
                     <if test="samplePic != null and samplePic != ''">
                            AND sample_pic = #{samplePic, jdbcType=VARCHAR}
                     </if>
                     <if test="partPic != null and partPic != ''">
                            AND part_pic = #{partPic, jdbcType=VARCHAR}
                     </if>
                     <if test="mainMaterial != null and mainMaterial != ''">
                            AND main_material = #{mainMaterial, jdbcType=VARCHAR}
                     </if>
                     <if test="lineRemarks != null and lineRemarks != ''">
                            AND line_remarks = #{lineRemarks, jdbcType=VARCHAR}
                     </if>
                     <if test="createOn != null">
                            AND create_on = #{createOn, jdbcType=TIMESTAMP}
                     </if>
                     <if test="createBy != null and createBy != ''">
                            AND create_by = #{createBy, jdbcType=VARCHAR}
                     </if>
                     <if test="updateOn != null">
                            AND update_on = #{updateOn, jdbcType=TIMESTAMP}
                     </if>
                     <if test="updateBy != null and updateBy != ''">
                            AND update_by = #{updateBy, jdbcType=VARCHAR}
                     </if>
              </where>
            order by qms_work_inspect_detail.line_no + 0 asc
       </select>

       <insert id="batchRepalceInto" parameterType="java.util.List">
              REPLACE INTO qms_work_inspect_detail (<include refid="Base_Column_List"></include>) values
              <foreach collection="list" separator="," item="item">
                     (
                     #{item.id, jdbcType=VARCHAR},
                     #{item.inspectNo, jdbcType=VARCHAR},
                     #{item.lineNo, jdbcType=INTEGER},
                     #{item.materialCode, jdbcType=VARCHAR},
                     #{item.produceQty, jdbcType=DECIMAL},
                     #{item.status, jdbcType=VARCHAR},
                     #{item.area, jdbcType=DECIMAL},
                     #{item.wide, jdbcType=VARCHAR},
                     #{item.name, jdbcType=VARCHAR},
                     #{item.length, jdbcType=VARCHAR},
                     #{item.materialMarker, jdbcType=VARCHAR},
                     #{item.millingGroove, jdbcType=VARCHAR},
                     #{item.screwWidth, jdbcType=VARCHAR},
                     #{item.screwHeigh, jdbcType=VARCHAR},
                     #{item.ecR, jdbcType=VARCHAR},
                     #{item.patchInfo, jdbcType=VARCHAR},
                     #{item.prepareFinishQty, jdbcType=DECIMAL},
                     #{item.materialChangeQty, jdbcType=DECIMAL},
                     #{item.materialFinishQty, jdbcType=DECIMAL},
                     #{item.newMaterialQty, jdbcType=DECIMAL},
                     #{item.isCutBoard, jdbcType=VARCHAR},
                     #{item.isCutCorner, jdbcType=VARCHAR},
                     #{item.isContainRabbet, jdbcType=VARCHAR},
                     #{item.samplePic, jdbcType=VARCHAR},
                     #{item.partPic, jdbcType=VARCHAR},
                     #{item.mainMaterial, jdbcType=VARCHAR},
                     #{item.lineRemarks, jdbcType=VARCHAR},
                     #{item.createOn, jdbcType=TIMESTAMP},
                     #{item.createBy, jdbcType=VARCHAR},
                     #{item.updateOn, jdbcType=TIMESTAMP},
                     #{item.updateBy, jdbcType=VARCHAR}
                     )
              </foreach>
       </insert>


       <select id="queryByInspectList" resultMap="BaseResultMap">
              SELECT
              <include refid="Base_Column_List"/>
              FROM
              qms_work_inspect_detail
              where 1=1
              <if test="list != null and list.size > 0 ">
                     and  inspect_no in
                     <foreach collection="list" open="(" item="item" separator="," close=")">
                            #{item,jdbcType=VARCHAR}
                     </foreach>
              </if>
              order by qms_work_inspect_detail.line_no + 0 asc
       </select>

    <insert id="insertAllBatch" parameterType="java.util.List">
        insert into qms_work_inspect_detail
        ( id,
        inspect_no,
        line_no,
        material_code,
        produce_qty,
        status,
        area,
        wide,
        name,
        length,
        material_marker,
        milling_groove,
        screw_width,
        screw_heigh,
        ec_r,
        patch_info,
        prepare_finish_qty,
        material_change_qty,
        material_finish_qty,
        new_material_qty,
        is_cut_board,
        is_cut_corner,
        is_contain_rabbet,
        sample_pic,
        part_pic,
        main_material,
        line_remarks,
        create_on,
        create_by,
        update_on,
        update_by
        )
        values
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            (
            #{item.id},
            #{item.inspectNo},
            #{item.lineNo},
            #{item.materialCode},
            #{item.produceQty},
            #{item.status},
            #{item.area},
            #{item.wide},
            #{item.name},
            #{item.length},
            #{item.materialMarker},
            #{item.millingGroove},
            #{item.screwWidth},
            #{item.screwHeigh},
            #{item.ecR},
            #{item.patchInfo},
            #{item.prepareFinishQty},
            #{item.materialChangeQty},
            #{item.materialFinishQty},
            #{item.newMaterialQty},
            #{item.isCutBoard},
            #{item.isCutCorner},
            #{item.isContainRabbet},
            #{item.samplePic},
            #{item.partPic},
            #{item.mainMaterial},
            #{item.lineRemarks},
            #{item.createOn},
            #{item.createBy},
            #{item.updateOn},
            #{item.updateBy}
            )
        </foreach>
    </insert>
</mapper>

