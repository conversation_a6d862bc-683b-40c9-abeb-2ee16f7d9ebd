<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ShiftRecordsDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.ShiftRecords">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Nov 07 10:20:23 CST 2019.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="sr_no" jdbcType="VARCHAR" property="srNo"/>
        <result column="dept_code" jdbcType="VARCHAR" property="deptCode"/>
        <result column="dept_name" jdbcType="VARCHAR" property="deptName"/>
        <result column="module_type" jdbcType="VARCHAR" property="moduleType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="shift_record_create_time" jdbcType="TIMESTAMP" property="shiftRecordCreateTime"/>
        <result column="shift_record_receive_time" jdbcType="TIMESTAMP" property="shiftRecordReceiveTime"/>
        <result column="shift_record_creator_code" jdbcType="VARCHAR" property="shiftRecordCreatorCode"/>
        <result column="shift_record_creator_name" jdbcType="VARCHAR" property="shiftRecordCreatorName"/>
        <result column="shift_record_receiver_code" jdbcType="VARCHAR" property="shiftRecordReceiverCode"/>
        <result column="shift_record_receiver_name" jdbcType="VARCHAR" property="shiftRecordReceiverName"/>
        <result column="shift_record_receiver_dept_name" jdbcType="VARCHAR" property="shiftRecordReceiverDeptName"/>
        <result column="shift_record_receiver_dept_code" jdbcType="VARCHAR" property="shiftRecordReceiverDeptCode"/>
        <result column="shift_records_content" jdbcType="VARCHAR" property="shiftRecordsContent"/>
        <result column="shift_records_deal_msg" jdbcType="VARCHAR" property="shiftRecordsDealMsg"/>
        <result column="work_order_id" jdbcType="VARCHAR" property="productionNo"/>
        <result column="line_code" jdbcType="VARCHAR" property="lineCode"/>
        <result column="line_name" jdbcType="VARCHAR" property="lineName"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Nov 07 10:20:23 CST 2019.
        -->
        id, sr_no, dept_code,dept_name,module_type, status, shift_record_create_time, shift_record_receive_time,
        shift_record_creator_code,shift_record_creator_name, shift_record_receiver_code,shift_record_receiver_name,shift_record_receiver_dept_code,shift_record_receiver_dept_name, shift_records_content, shift_records_deal_msg,work_order_id,line_code,line_name,
        create_on, create_by, update_on, update_by, remarks
    </sql>


    <select id="queryByParam" parameterType="java.util.Map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ppc_shift_records
        where 1=1
        <if test="srNo != null and srNo != ''">
            and sr_no like concat('%', #{shiftRecordCreatorCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="shiftRecordCreatorName != null and shiftRecordCreatorName != ''">
            and shift_record_creator_name like concat('%', #{shiftRecordCreatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="shiftRecordCreatorCode != null and shiftRecordCreatorCode != ''">
            and shift_record_creator_code = #{shiftRecordCreatorCode,jdbcType=VARCHAR}
        </if>
        <if test="productionNo != null and productionNo != ''">
            and work_order_id = #{productionNo,jdbcType=VARCHAR}
        </if>
        <if test="lineCode != null and lineCode != ''">
            and line_code = #{lineCode,jdbcType=VARCHAR}
        </if>
        <if test="statusIn != null">
            and status in
            <foreach collection="statusIn" open="(" item="status" separator="," close=")">
                #{status,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="shiftRecordCreateTime != null and shiftRecordCreateTime != ''">
            and shift_record_create_time = #{shiftRecordCreateTime,jdbcType=VARCHAR}
        </if>
        <if test="shiftRecordsContent != null and shiftRecordsContent != ''">
            and shift_records_content like concat('%', #{shiftRecordsContent,jdbcType=VARCHAR},'%')
        </if>
        <if test="shiftRecordsDealMsg != null and shiftRecordsDealMsg != ''">
            and shift_records_deal_msg like concat('%', #{shiftRecordsDealMsg,jdbcType=VARCHAR},'%')
        </if>
        <if test="startData != null and startData != ''">
            and shift_record_create_time <![CDATA[ >= ]]> #{startData}
        </if>
        <if test="endData != null and endData != ''">
            and shift_record_create_time  <![CDATA[ <= ]]> #{endData}
        </if>

        order by create_on desc
    </select>

    <select id="getByCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from ppc_shift_records
        where sr_no  = #{srNo,jdbcType=VARCHAR}
    </select>

    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.ShiftRecords">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Thu Aug 26 14:48:51 CST 2021.
        -->
        insert into ppc_shift_records (id,sr_no,dept_code, dept_name,module_type, status,
        shift_record_create_time, shift_record_creator_code,shift_record_creator_name,shift_record_receiver_code,shift_record_receiver_name, shift_record_receiver_dept_code,shift_record_receiver_dept_name,shift_records_content,work_order_id,line_code,
        line_name,create_by, create_on, update_by, update_on,remarks)
        values (#{id,jdbcType=VARCHAR}, #{srNo,jdbcType=VARCHAR},  #{deptCode,jdbcType=VARCHAR},#{deptName,jdbcType=VARCHAR}, #{moduleType,jdbcType=VARCHAR},#{status,jdbcType=VARCHAR},
        #{shiftRecordCreateTime,jdbcType=TIMESTAMP},#{shiftRecordCreatorCode,jdbcType=VARCHAR},#{shiftRecordCreatorName,jdbcType=VARCHAR},#{shiftRecordReceiverCode,jdbcType=VARCHAR},#{shiftRecordReceiverName,jdbcType=VARCHAR},#{shiftRecordReceiverDeptCode,jdbcType=VARCHAR},#{shiftRecordReceiverDeptName,jdbcType=VARCHAR},#{shiftRecordsContent,jdbcType=VARCHAR},#{productionNo,jdbcType=VARCHAR},#{lineCode,jdbcType=VARCHAR},
        #{lineName,jdbcType=VARCHAR}, #{createBy,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{updateOn,jdbcType=TIMESTAMP},#{remarks,jdbcType=VARCHAR})
    </insert>

    <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.ShiftRecords">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Tue Nov 26 19:29:52 CST 2019.
        -->
        update ppc_shift_records
        set sr_no = #{srNo,jdbcType=VARCHAR},
        status = #{status,jdbcType=VARCHAR},
        shift_record_create_time = #{shiftRecordCreateTime,jdbcType=VARCHAR},
        shift_record_receiver_code = #{shiftRecordReceiverCode,jdbcType=VARCHAR},
        shift_record_receiver_name = #{shiftRecordReceiverName,jdbcType=VARCHAR},
        shift_record_receiver_dept_code = #{shiftRecordReceiverDeptCode,jdbcType=VARCHAR},
        shift_record_receiver_dept_name = #{shiftRecordReceiverDeptName,jdbcType=VARCHAR},
        shift_records_content = #{shiftRecordsContent,jdbcType=VARCHAR},
        shift_record_receive_time = #{shiftRecordReceiveTime,jdbcType=VARCHAR},
        shift_records_deal_msg = #{shiftRecordsDealMsg,jdbcType=VARCHAR},
        update_by = #{updateBy,jdbcType=VARCHAR},
        update_on = #{updateOn,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>