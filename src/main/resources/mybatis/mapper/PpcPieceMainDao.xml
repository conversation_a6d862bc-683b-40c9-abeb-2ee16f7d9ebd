<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceMainDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceMainQueryVo">
        select
        <if test="ew.isMainMode">
            ppc_piece_main.id,
               ppc_piece_main.effect_date_start,
               ppc_piece_main.effect_date_end,
               ppc_piece_main.factory_name,
               ppc_piece_main.factory_region_name,
               ppc_piece_main.big_process_code,
               ppc_piece_main.big_process_name,
               ppc_piece_main.priority,
               ppc_piece_main.name,
               ppc_piece_main.small_process_code,
               ppc_piece_main.small_process_name,
               ppc_piece_main.group_type,
               ppc_piece_main.create_on,
               ppc_piece_main.create_by,
               ppc_piece_main.update_on,
               ppc_piece_main.update_by,
               ppc_piece_main.status,
               ppc_piece_main.remarks,
               ppc_piece_main.work_finish_no
            from ppc_piece_main
        </if>
        <if test="!ew.isMainMode">
            ppc_piece_main.id,
            ppc_piece_main.effect_date_start,
            ppc_piece_main.effect_date_end,
            ppc_piece_main.factory_name,
            ppc_piece_main.factory_region_name,
            ppc_piece_main.big_process_code,
            ppc_piece_main.big_process_name,
            ppc_piece_main.priority,
            ppc_piece_main.name,
            ppc_piece_main.small_process_code,
            ppc_piece_main.small_process_name,
            ppc_piece_main.group_type,
            ppc_piece_main.create_on,
            ppc_piece_main.create_by,
            ppc_piece_main.update_on,
            ppc_piece_main.update_by,
            ppc_piece_main.status,
            ppc_piece_main.remarks,
            ppc_piece_main.work_finish_no,
            ppc_piece_main_item.main_id,
            ppc_piece_main_item.item_name,
            ppc_piece_main_item.single_price,
            ppc_piece_main_item.unit,
            ppc_piece_main_item.report_method,
            ppc_piece_main_item.is_statistic
            from ppc_piece_main  left join ppc_piece_main_item on ppc_piece_main.id=ppc_piece_main_item.main_id
        </if>
            ${ew.customSqlSegment}
    </select>
    <select id="getReportColumnByBigProcess" resultType="com.imes.domain.entities.ppc.po.PpcPieceMainItem">
        SELECT
                t2.*
        FROM
        ppc_piece_main t
        LEFT JOIN ppc_piece_main_item t2 ON t.id = t2.main_id
        WHERE
            t.effect_date_start <![CDATA[ <= ]]> #{finishedDate}
          AND t.effect_date_end <![CDATA[ >= ]]> #{finishedDate}
          and t.big_process_code = #{bigProcessCode}
          and (t.small_process_code = #{smallProcessCode} or t.small_process_code ='')
        <!-- 使用 FIND_IN_SET 替代 IN -->
        <if test="reportMethodList != null and reportMethodList.size() > 0">
            AND (
            <foreach collection="reportMethodList" item="method" separator=" OR ">
                FIND_IN_SET(#{method}, t2.report_method)
            </foreach>
            )
        </if>
        order by t2.item_name
    </select>

    <select id="getBigReportColumnByDate" resultType="com.imes.domain.entities.ppc.po.PpcPieceMainItem">
        SELECT
            t2.*
        FROM
            ppc_piece_main t
                LEFT JOIN ppc_piece_main_item t2 ON t.id = t2.main_id
        WHERE
            t.effect_date_start <![CDATA[ <= ]]> #{finishedDate}
          AND t.effect_date_end <![CDATA[ >= ]]> #{finishedDate}
        <!-- 使用 FIND_IN_SET 替代 IN -->
        <if test="reportMethodList != null and reportMethodList.size() > 0">
            AND (
            <foreach collection="reportMethodList" item="method" separator=" OR ">
                FIND_IN_SET(#{method}, t2.report_method)
            </foreach>
            )
        </if>
    </select>

    <select id="queryItemByBigProcessCode" resultType="com.imes.domain.entities.ppc.po.PpcPieceMainItem">
        SELECT
              distinct  t2.item_name
        FROM
        ppc_piece_main t
        LEFT JOIN ppc_piece_main_item t2 ON t.id = t2.main_id
        WHERE
           t.big_process_code = #{bigProcessCode}
    </select>

</mapper>

