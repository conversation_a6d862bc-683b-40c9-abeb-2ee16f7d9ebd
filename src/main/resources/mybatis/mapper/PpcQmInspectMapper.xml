<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcQmInspectMapper">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcQmInspect">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="inspect_no" jdbcType="VARCHAR" property="inspectNo"/>
        <result column="inspect_date" jdbcType="TIMESTAMP" property="inspectDate"/>
        <result column="inspect_code" jdbcType="VARCHAR" property="inspectCode"/>
        <result column="inspect_name" jdbcType="VARCHAR" property="inspectName"/>
        <result column="produce_plan_id" jdbcType="VARCHAR" property="producePlanId"/>
        <result column="produce_plan_no" jdbcType="VARCHAR" property="producePlanNo"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="workshop_code" jdbcType="VARCHAR" property="workshopCode"/>
        <result column="workshop_name" jdbcType="VARCHAR" property="workshopName"/>
        <result column="process_code" jdbcType="VARCHAR" property="processCode"/>
        <result column="process_name" jdbcType="VARCHAR" property="processName"/>
        <result column="inspect_method" jdbcType="VARCHAR" property="inspectMethod"/>
        <result column="sample_type" jdbcType="VARCHAR" property="sampleType"/>
        <result column="sample_method" jdbcType="VARCHAR" property="sampleMethod"/>
        <result column="sample_stage" jdbcType="VARCHAR" property="sampleStage"/>
        <result column="inspect_batch_no" jdbcType="VARCHAR" property="inspectBatchNo"/>
        <result column="prod_shift_date" jdbcType="DATE" property="prodShiftDate"/>
        <result column="prod_shift_code" jdbcType="VARCHAR" property="prodShiftCode"/>
        <result column="prod_shift_name" jdbcType="VARCHAR" property="prodShiftName"/>
        <result column="inspect_user_code" jdbcType="VARCHAR" property="inspectUserCode"/>
        <result column="inspect_user_name" jdbcType="VARCHAR" property="inspectUserName"/>
        <result column="inspect_status" jdbcType="VARCHAR" property="inspectStatus"/>
        <result column="is_broken" jdbcType="VARCHAR" property="isBroken"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        id, inspect_no, inspect_date, inspect_code, inspect_name, produce_plan_id, produce_plan_no,
        material_code, material_name, workshop_code, workshop_name, process_code, process_name,
        inspect_method, sample_type, sample_method, sample_stage, inspect_batch_no, prod_shift_date,
        prod_shift_code, prod_shift_name, inspect_user_code, inspect_user_name, inspect_status,
        is_broken, create_on, create_by, update_on, update_by, remarks
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from ppc_qm_inspect
        where 1=1

        <if test="_parameter != null">
            and id = #{id,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="findAll" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from ppc_qm_inspect
        where 1=1

        <if test="id != null and ''!=id ">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="inspectNo != null and ''!=inspectNo">
            and inspect_no = #{inspectNo,jdbcType=VARCHAR}
        </if>
        <if test="materialCode != null and ''!=materialCode">
            and material_code = #{materialCode,jdbcType=VARCHAR}
        </if>
        <if test="inspectCode != null and ''!=inspectCode">
            and inspect_code = #{inspectCode,jdbcType=VARCHAR}
        </if>
        <if test="inspectDate != null and ''!=inspectDate">
            and inspect_date = #{inspectDate,jdbcType=VARCHAR}
        </if>
        <if test="inspectStatus != null and ''!=inspectStatus">
            and inspect_status = #{inspectStatus,jdbcType=VARCHAR}
        </if>
        <if test="startDate != null and ''!=startDate and endDate != null and ''!=endDate">
            and inspect_date between #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="inspectCode != null and ''!=inspectCode ">
            and inspect_code = #{inspectCode,jdbcType=VARCHAR}
        </if>
        <if test="inspectStatus != null and ''!=inspectStatus ">
            and inspect_status = #{inspectStatus,jdbcType=VARCHAR}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        delete from ppc_qm_inspect
        where id = #{id,jdbcType=VARCHAR}
    </delete>

    <delete id="deleteByInspectCode" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        delete from ppc_qm_inspect
        where inspect_code = #{inspectCode,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcQmInspect">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        insert into ppc_qm_inspect (id, inspect_no, inspect_date,
        inspect_code, inspect_name, produce_plan_id,
        produce_plan_no, material_code, material_name,
        workshop_code, workshop_name, process_code,
        process_name, inspect_method, sample_type,
        sample_method, sample_stage, inspect_batch_no,
        prod_shift_date, prod_shift_code, prod_shift_name,
        inspect_user_code, inspect_user_name, inspect_status,
        is_broken, create_on, create_by,
        update_on, update_by, remarks
        )
        values (#{id,jdbcType=VARCHAR}, #{inspectNo,jdbcType=VARCHAR}, #{inspectDate,jdbcType=TIMESTAMP},
        #{inspectCode,jdbcType=VARCHAR}, #{inspectName,jdbcType=VARCHAR}, #{producePlanId,jdbcType=VARCHAR},
        #{producePlanNo,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR},
        #{workshopCode,jdbcType=VARCHAR}, #{workshopName,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR},
        #{processName,jdbcType=VARCHAR}, #{inspectMethod,jdbcType=VARCHAR}, #{sampleType,jdbcType=VARCHAR},
        #{sampleMethod,jdbcType=VARCHAR}, #{sampleStage,jdbcType=VARCHAR}, #{inspectBatchNo,jdbcType=VARCHAR},
        #{prodShiftDate,jdbcType=DATE}, #{prodShiftCode,jdbcType=VARCHAR}, #{prodShiftName,jdbcType=VARCHAR},
        #{inspectUserCode,jdbcType=VARCHAR}, #{inspectUserName,jdbcType=VARCHAR}, #{inspectStatus,jdbcType=VARCHAR},
        #{isBroken,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
        #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}
        )
    </insert>

    <insert id="insertInspect" parameterType="java.util.Map">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        insert into ppc_qm_inspect (id, inspect_no, inspect_date,
        inspect_code, inspect_name, produce_plan_id,
        produce_plan_no, material_code, material_name,
        workshop_code, workshop_name, process_code,
        process_name, inspect_method, sample_type,
        sample_method, sample_stage, inspect_batch_no,
        prod_shift_date, prod_shift_code, prod_shift_name,
        inspect_user_code, inspect_user_name, inspect_status,
        is_broken, create_on, create_by,
        update_on, update_by, remarks
        )
        values (#{id,jdbcType=VARCHAR}, #{inspectNo,jdbcType=VARCHAR}, #{inspectDate,jdbcType=TIMESTAMP},
        #{inspectCode,jdbcType=VARCHAR}, #{inspectName,jdbcType=VARCHAR}, #{producePlanId,jdbcType=VARCHAR},
        #{producePlanNo,jdbcType=VARCHAR}, #{materialCode,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR},
        #{workshopCode,jdbcType=VARCHAR}, #{workshopName,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR},
        #{processName,jdbcType=VARCHAR}, #{inspectMethod,jdbcType=VARCHAR}, #{sampleType,jdbcType=VARCHAR},
        #{sampleMethod,jdbcType=VARCHAR}, #{sampleStage,jdbcType=VARCHAR}, #{inspectBatchNo,jdbcType=VARCHAR},
        #{prodShiftDate,jdbcType=DATE}, #{prodShiftCode,jdbcType=VARCHAR}, #{prodShiftName,jdbcType=VARCHAR},
        #{inspectUserCode,jdbcType=VARCHAR}, #{inspectUserName,jdbcType=VARCHAR}, #{inspectStatus,jdbcType=VARCHAR},
        #{isBroken,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
        #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcQmInspect">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        insert into ppc_qm_inspect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="inspectNo != null">
                inspect_no,
            </if>
            <if test="inspectDate != null">
                inspect_date,
            </if>
            <if test="inspectCode != null">
                inspect_code,
            </if>
            <if test="inspectName != null">
                inspect_name,
            </if>
            <if test="producePlanId != null">
                produce_plan_id,
            </if>
            <if test="producePlanNo != null">
                produce_plan_no,
            </if>
            <if test="materialCode != null">
                material_code,
            </if>
            <if test="materialName != null">
                material_name,
            </if>
            <if test="workshopCode != null">
                workshop_code,
            </if>
            <if test="workshopName != null">
                workshop_name,
            </if>
            <if test="processCode != null">
                process_code,
            </if>
            <if test="processName != null">
                process_name,
            </if>
            <if test="inspectMethod != null">
                inspect_method,
            </if>
            <if test="sampleType != null">
                sample_type,
            </if>
            <if test="sampleMethod != null">
                sample_method,
            </if>
            <if test="sampleStage != null">
                sample_stage,
            </if>
            <if test="inspectBatchNo != null">
                inspect_batch_no,
            </if>
            <if test="prodShiftDate != null">
                prod_shift_date,
            </if>
            <if test="prodShiftCode != null">
                prod_shift_code,
            </if>
            <if test="prodShiftName != null">
                prod_shift_name,
            </if>
            <if test="inspectUserCode != null">
                inspect_user_code,
            </if>
            <if test="inspectUserName != null">
                inspect_user_name,
            </if>
            <if test="inspectStatus != null">
                inspect_status,
            </if>
            <if test="isBroken != null">
                is_broken,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="inspectNo != null">
                #{inspectNo,jdbcType=VARCHAR},
            </if>
            <if test="inspectDate != null">
                #{inspectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inspectCode != null">
                #{inspectCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectName != null">
                #{inspectName,jdbcType=VARCHAR},
            </if>
            <if test="producePlanId != null">
                #{producePlanId,jdbcType=VARCHAR},
            </if>
            <if test="producePlanNo != null">
                #{producePlanNo,jdbcType=VARCHAR},
            </if>
            <if test="materialCode != null">
                #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="materialName != null">
                #{materialName,jdbcType=VARCHAR},
            </if>
            <if test="workshopCode != null">
                #{workshopCode,jdbcType=VARCHAR},
            </if>
            <if test="workshopName != null">
                #{workshopName,jdbcType=VARCHAR},
            </if>
            <if test="processCode != null">
                #{processCode,jdbcType=VARCHAR},
            </if>
            <if test="processName != null">
                #{processName,jdbcType=VARCHAR},
            </if>
            <if test="inspectMethod != null">
                #{inspectMethod,jdbcType=VARCHAR},
            </if>
            <if test="sampleType != null">
                #{sampleType,jdbcType=VARCHAR},
            </if>
            <if test="sampleMethod != null">
                #{sampleMethod,jdbcType=VARCHAR},
            </if>
            <if test="sampleStage != null">
                #{sampleStage,jdbcType=VARCHAR},
            </if>
            <if test="inspectBatchNo != null">
                #{inspectBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="prodShiftDate != null">
                #{prodShiftDate,jdbcType=DATE},
            </if>
            <if test="prodShiftCode != null">
                #{prodShiftCode,jdbcType=VARCHAR},
            </if>
            <if test="prodShiftName != null">
                #{prodShiftName,jdbcType=VARCHAR},
            </if>
            <if test="inspectUserCode != null">
                #{inspectUserCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectUserName != null">
                #{inspectUserName,jdbcType=VARCHAR},
            </if>
            <if test="inspectStatus != null">
                #{inspectStatus,jdbcType=VARCHAR},
            </if>
            <if test="isBroken != null">
                #{isBroken,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcQmInspect">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        update ppc_qm_inspect
        <set>
            <if test="inspectNo != null">
                inspect_no = #{inspectNo,jdbcType=VARCHAR},
            </if>
            <if test="inspectDate != null">
                inspect_date = #{inspectDate,jdbcType=TIMESTAMP},
            </if>
            <if test="inspectCode != null">
                inspect_code = #{inspectCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectName != null">
                inspect_name = #{inspectName,jdbcType=VARCHAR},
            </if>
            <if test="producePlanId != null">
                produce_plan_id = #{producePlanId,jdbcType=VARCHAR},
            </if>
            <if test="producePlanNo != null">
                produce_plan_no = #{producePlanNo,jdbcType=VARCHAR},
            </if>
            <if test="materialCode != null">
                material_code = #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="materialName != null">
                material_name = #{materialName,jdbcType=VARCHAR},
            </if>
            <if test="workshopCode != null">
                workshop_code = #{workshopCode,jdbcType=VARCHAR},
            </if>
            <if test="workshopName != null">
                workshop_name = #{workshopName,jdbcType=VARCHAR},
            </if>
            <if test="processCode != null">
                process_code = #{processCode,jdbcType=VARCHAR},
            </if>
            <if test="processName != null">
                process_name = #{processName,jdbcType=VARCHAR},
            </if>
            <if test="inspectMethod != null">
                inspect_method = #{inspectMethod,jdbcType=VARCHAR},
            </if>
            <if test="sampleType != null">
                sample_type = #{sampleType,jdbcType=VARCHAR},
            </if>
            <if test="sampleMethod != null">
                sample_method = #{sampleMethod,jdbcType=VARCHAR},
            </if>
            <if test="sampleStage != null">
                sample_stage = #{sampleStage,jdbcType=VARCHAR},
            </if>
            <if test="inspectBatchNo != null">
                inspect_batch_no = #{inspectBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="prodShiftDate != null">
                prod_shift_date = #{prodShiftDate,jdbcType=DATE},
            </if>
            <if test="prodShiftCode != null">
                prod_shift_code = #{prodShiftCode,jdbcType=VARCHAR},
            </if>
            <if test="prodShiftName != null">
                prod_shift_name = #{prodShiftName,jdbcType=VARCHAR},
            </if>
            <if test="inspectUserCode != null">
                inspect_user_code = #{inspectUserCode,jdbcType=VARCHAR},
            </if>
            <if test="inspectUserName != null">
                inspect_user_name = #{inspectUserName,jdbcType=VARCHAR},
            </if>
            <if test="inspectStatus != null">
                inspect_status = #{inspectStatus,jdbcType=VARCHAR},
            </if>
            <if test="isBroken != null">
                is_broken = #{isBroken,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.PpcQmInspect">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Aug 28 15:50:28 CST 2020.
        -->
        update ppc_qm_inspect
        set
        <if test="inspectNo != null">
            inspect_no = #{inspectNo,jdbcType=VARCHAR},
        </if>
        <if test="inspectDate != null">
            inspect_date = #{inspectDate,jdbcType=TIMESTAMP}
        </if>
        <if test="inspectCode != null">
            inspect_code = #{inspectCode,jdbcType=VARCHAR},
        </if>
        <if test="inspectName != null">
            inspect_name = #{inspectName,jdbcType=VARCHAR},
        </if>
        <if test="producePlanId != null">
            produce_plan_id = #{producePlanId,jdbcType=VARCHAR},
        </if>
        <if test="producePlanNo != null">
            produce_plan_no = #{producePlanNo,jdbcType=VARCHAR},
        </if>
        <if test="materialCode != null">
            material_code = #{materialCode,jdbcType=VARCHAR},
        </if>
        <if test="materialName != null">
            material_name = #{materialName,jdbcType=VARCHAR},
        </if>
        <if test="workshopCode != null">
            workshop_code = #{workshopCode,jdbcType=VARCHAR},
        </if>
        <if test="workshopName != null">
            workshop_name = #{workshopName,jdbcType=VARCHAR},
        </if>
        <if test="processCode != null">
            process_code = #{processCode,jdbcType=VARCHAR},
        </if>
        <if test="processName != null">
            process_name = #{processName,jdbcType=VARCHAR},
        </if>
        <if test="inspectMethod != null">
            inspect_method = #{inspectMethod,jdbcType=VARCHAR},
        </if>
        <if test="sampleType != null">
            sample_type = #{sampleType,jdbcType=VARCHAR},
        </if>
        <if test="sampleMethod != null">
            sample_method = #{sampleMethod,jdbcType=VARCHAR},
        </if>
        <if test="sampleStage != null">
            sample_stage = #{sampleStage,jdbcType=VARCHAR},
        </if>
        <if test="inspectBatchNo != null">
            inspect_batch_no = #{inspectBatchNo,jdbcType=VARCHAR},
        </if>
        <if test="prodShiftDate != null">
            prod_shift_date = #{prodShiftDate,jdbcType=DATE},
        </if>
        <if test="prodShiftCode != null">
            prod_shift_code = #{prodShiftCode,jdbcType=VARCHAR},
        </if>
        <if test="prodShiftName != null">
            prod_shift_name = #{prodShiftName,jdbcType=VARCHAR},
        </if>
        <if test="inspectUserCode != null">
            inspect_user_code = #{inspectUserCode,jdbcType=VARCHAR},
        </if>
        <if test="inspectUserName != null">
            inspect_user_name = #{inspectUserName,jdbcType=VARCHAR},
        </if>
        <if test="inspectStatus != null">
            inspect_status = #{inspectStatus,jdbcType=VARCHAR},
        </if>
        <if test="isBroken != null">
            is_broken = #{isBroken,jdbcType=VARCHAR},
        </if>
        <if test="createOn != null">
            create_on = #{createOn,jdbcType=TIMESTAMP},
        </if>
        <if test="createBy != null">
            create_by = #{createBy,jdbcType=VARCHAR},
        </if>
        <if test="updateOn != null">
            update_on = #{updateOn,jdbcType=TIMESTAMP},
        </if>
        <if test="updateBy != null">
            update_by = #{updateBy,jdbcType=VARCHAR},
        </if>
        <if test="remarks != null">
            remarks = #{remarks,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <update id="updateInspectStatusByInspectNo">
        update ppc_qm_inspect set inspect_status = #{inspectStatus,jdbcType=VARCHAR}
        where  inspect_no = #{inspectNo,jdbcType=VARCHAR}
    </update>
</mapper>