<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.C_HuaDunDao">

    <select id="findBomList" resultType="com.imes.domain.entities.ppc.po.Bom">
        select bom_code, bom_name, bom_ver, material_code from ppc_bom
        <trim prefix="where" prefixOverrides="and">
            <if test="materialCodeList != null">
                and material_code in
                <foreach collection="materialCodeList" open="(" item="materialCode" separator="," close=")">
                    #{materialCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="findMaterialExtendList" resultType="com.imes.domain.entities.ppc.po.PpcMaterialExtend">
        select id, material_code, property_key, property_unit, value, priority, create_on, create_by,
        update_on, update_by, delete_status from ppc_material_extend
        <trim prefix="where" prefixOverrides="and">
            <if test="materialCodeList != null">
                and material_code in
                <foreach collection="materialCodeList" open="(" item="materialCode" separator="," close=")">
                    #{materialCode,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="propertyKeyList != null">
                and property_key in
                <foreach collection="propertyKeyList" open="(" item="item" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <select id="findMaterialList" resultType="com.imes.domain.entities.ppc.po.Promaterial">
        select material_code,custom,primary_unit,category from ppc_material
        <trim prefix="where" prefixOverrides="and">
            <if test="materialCodeList != null">
                and material_code in
                <foreach collection="materialCodeList" open="(" item="materialCode" separator="," close=")">
                    #{materialCode,jdbcType=VARCHAR}
                </foreach>
            </if>
        </trim>
    </select>

    <insert id="insertOrderPlanList" >
        insert into huadun_order_plan (
        id,sale_main,`status`,so_no,sd_no,detail_id
        ,material_code,material_name,customer_code,customer_name,sales_person_code
        ,sales_person_name,document_type,receive_date,ship_date,qty
        ,area,plan_start_date,plan_end_date,remarks,create_on
        ,create_by,update_on,update_by,length,width
        ,actual_qty,actual_area
        )
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.id}, #{item.saleMain}, #{item.status}, #{item.soNo}, #{item.sdNo}, #{item.detailId}
            , #{item.materialCode}, #{item.materialName}, #{item.customerCode}, #{item.customerName}, #{item.salesPersonCode}
            , #{item.salesPersonName}, #{item.documentType}, #{item.receiveDate}, #{item.shipDate}, #{item.qty}
            , #{item.area}, #{item.planStartDate}, #{item.planEndDate}, #{item.remarks}, #{item.createOn}
            , #{item.createBy}, #{item.updateOn}, #{item.updateBy}, #{item.length}, #{item.width}
            , #{item.actualQty}, #{item.actualArea}
            )

        </foreach>
    </insert>

    <select id="findNotHaveSaleMainList" resultType="com.imes.domain.entities.ppc.po.C_HuaDunOrderPlan">
         select max(id) id,sale_main from huadun_order_plan
         GROUP BY so_no, sale_main
         HAVING sale_main is null
    </select>

    <select id="findOrderPlanMainList" resultType="com.imes.domain.entities.ppc.po.C_HuaDunOrderPlan">
        select  a.id,a.`status`,a.so_no
        ,a.material_code,a.material_name,a.customer_code,a.customer_name,a.sales_person_code
        ,a.sales_person_name,a.document_type,a.receive_date,a.ship_date
        ,a.plan_start_date,a.plan_end_date,a.plan_remarks
		,t2.actual_qty,t2.actual_area
        ,case  when t1.change_status = '30' then '已确认'
        when t1.change_status != '30' then '未确认'
        when t1.change_status is null then '无变更' end  as saleDetailChangeStatus
        from huadun_order_plan a
        left join (
            select t1.so_no,t1.change_status from ppc_sale_main_change t1
            inner join (
                select so_no,max(id) as id from ppc_sale_main_change GROUP BY so_no
            ) t2 on t1.so_no = t2.so_no and t1.id = t2.id
        ) t1 on a.so_no = t1.so_no
        left join (
            select so_no,sum(actual_qty) actual_qty,sum(actual_area) actual_area from huadun_order_plan
            GROUP BY so_no
        ) t2 on a.so_no = t2.so_no
        <trim prefix="where" prefixOverrides="and">
            <if test="saleMain != null and saleMain != ''">
                and a.sale_main = #{saleMain}
            </if>
            <if test="shipDateGe != null">
                and a.ship_date >= #{shipDateGe}
            </if>
            <if test="shipDateLe != null">
                and a.ship_date &lt;= #{shipDateLe}
            </if>
        </trim>
    </select>

    <select id="findOrderPlanDetailList" resultType="com.imes.domain.entities.ppc.po.C_HuaDunOrderPlan">
        select  a.id,a.`status`,a.so_no,a.sd_no,a.detail_id,a.material_code
        ,a.material_name,a.sku_code,a.receive_date,a.ship_date,a.qty
        ,a.area,a.plan_start_date,a.plan_end_date,a.remarks,a.create_on
        ,a.create_by,a.update_on,a.update_by,a.actual_qty,a.actual_area
        ,a.length,a.width
        ,case   when t1.change_status = '30' then '已确认'
        when t1.change_status != '30' then '未确认'
        when t1.change_status is null then '无变更' end  as saleDetailChangeStatus
        ,t2.material_type_code,t2.specification,t2.quality,t2.bar_code,t2.primary_unit
        ,t2.custom
        ,t3.onhand_qty
        from huadun_order_plan a
        left join (
            select t1.so_no,t1.change_status from ppc_sale_main_change t1
            inner join (
                select so_no,max(id) as id from ppc_sale_main_change GROUP BY so_no
            ) t2 on t1.so_no = t2.so_no and t1.id = t2.id
        ) t1 on a.so_no = t1.so_no
        left join ppc_material t2 on a.material_code = t2.material_code
        left join wms_stock t3 on  a.material_code = t3.material_code
        <trim prefix="where" prefixOverrides="and">
            <if test="soNo != null and soNo != ''">
                and a.so_no = #{soNo}
            </if>
            <if test="shipDateGe != null">
                and a.ship_date >= #{shipDateGe}
            </if>
            <if test="shipDateLe != null">
                and a.ship_date &lt;= #{shipDateLe}
            </if>
        </trim>
    </select>

    <insert id="insertProcessPlanList" >
        insert into huadun_process_plan (
        id,sale_main,`status`,so_no,sd_no,detail_id
        ,material_code,material_name,customer_code,customer_name
        ,ship_date,qty,area,sku_code
        ,remarks,create_on,create_by,update_on,update_by
        ,sale_change_status,add_qty
        )
        values
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.id}, #{item.saleMain}, #{item.status}, #{item.soNo}, #{item.sdNo}, #{item.detailId}
            , #{item.materialCode}, #{item.materialName}, #{item.customerCode}, #{item.customerName}
            , #{item.shipDate}, #{item.qty}, #{item.area}, #{item.skuCode}
            , #{item.remarks}, #{item.createOn}, #{item.createBy}, #{item.updateOn}, #{item.updateBy}
            , #{item.saleChangeStatus}, #{item.addQty}
            )
        </foreach>
    </insert>

    <select id="findProcessPlanMainList" resultType="com.imes.domain.entities.ppc.po.C_HuaDunProcessPlan">
        select  a.id,a.sale_main,a.`status`,a.so_no,a.sd_no,a.detail_id,a.customer_name
        ,a.material_code,a.material_name,a.add_qty
        ,a.qty,a.area,a.ship_date
        ,a.spin_start,a.spin_end,a.weave_start,a.weave_end
        ,a.dye_start,a.dye_end,a.composite_start,a.composite_end
        ,a.cut_start,a.cut_end,a.sew_start,a.sew_end
        ,a.pack_start,a.pack_end
        ,case   when t2.num = 0 then '已提交'
        when t2.num &lt; t3.num then '部分提交'
        when t2.num  = t3.num then '未提交' end  as submitStatus
        from huadun_process_plan a
        left join (
            select so_no,ifnull(count(id),0) num from huadun_process_plan
            where status = '10' group by so_no
        ) t2 on a.so_no = t2.so_no
        left join (
            select so_no,ifnull(count(id),0) num from huadun_process_plan
             group by so_no
        ) t3 on a.so_no = t3.so_no
        <trim prefix="where" prefixOverrides="and">
            <if test="soNo != null and soNo != ''">
                and a.so_no = #{soNo}
            </if>
            <if test="saleMain != null and saleMain != ''">
                and a.sale_main = #{saleMain}
            </if>
            <if test="process == '纺纱'.toString() and start != null and end != null ">
                and a.spin_start >= #{start} and a.spin_start &lt;= #{end}
            </if>
            <if test="process == '织布'.toString() and start != null and end != null ">
                and a.weave_start >= #{start} and a.weave_start &lt;= #{end}
            </if>
            <if test="process == '染色'.toString() and start != null and end != null ">
                and a.dye_start >= #{start} and a.dye_start &lt;= #{end}
            </if>
            <if test="process == '复合'.toString() and start != null and end != null ">
                and a.composite_start >= #{start} and a.composite_start &lt;= #{end}
            </if>
            <if test="process == '裁剪'.toString() and start != null and end != null ">
                and a.cut_start >= #{start} and a.cut_start &lt;= #{end}
            </if>
            <if test="process == '缝制'.toString() and start != null and end != null ">
                and a.sew_start >= #{start} and a.sew_start &lt;= #{end}
            </if>
            <if test="process == '包装'.toString() and start != null and end != null ">
                and a.pack_start >= #{start} and a.pack_start &lt;= #{end}
            </if>
        </trim>
    </select>

    <select id="findProcessPlanDetailList" resultType="com.imes.domain.entities.ppc.po.C_HuaDunProcessPlan">
        select  a.id,a.`status`,a.so_no,a.sd_no,a.detail_id,a.customer_name
        ,a.material_code,a.material_name,a.bottom,a.add_qty
        ,a.sku_code
        ,t2.specification,t2.quality
        from huadun_process_plan a
        left join ppc_material t2 on a.material_code = t2.material_code
        <trim prefix="where" prefixOverrides="and">
            <if test="soNo != null and soNo != ''">
                and a.so_no = #{soNo}
            </if>

        </trim>
    </select>

    <select id="findWorkOrderList" resultType="com.imes.domain.entities.ppc.vo.C_WorkOderVo">
        select
        t1.id,t1.wo_no,t1.process_code,t1.material_code
        ,t3.main_id as SaleId,t3.id as detailId,t3.sku_code
        from ppc_work_order t1
        left join ppc_produce_plan t2 on t1.plan_id = t2.id
        left join ppc_sale_detail t3 on t2.sale_detail_id = t3.id
        ${ew.customSqlSegment}
    </select>

</mapper>