<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcContractMainDao">
    <delete id="deleteBySoNos">
        delete from ppc_contract_main where so_no in
        <foreach collection="soNos" open="(" item="soNo" separator="," close=")">
            #{soNo,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteByMainIds">
        delete from ppc_contract_detail where main_id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </delete>


    <select id="queryListHt" resultType="com.imes.domain.entities.query.template.ppc.PpcContractSearchVo">
        select distinct ppc_contract_main.id,
                        ppc_contract_main.so_no                                 as souceNo,
                        ppc_contract_main.so_name,
                        ppc_contract_main.depart_name,
                        ppc_contract_main.contract_no,
                        ppc_contract_main.contract_description,
                        ppc_contract_main.depart_code,
                        ppc_contract_main.customer_name,
                        ppc_contract_main.customer_code,
                        ppc_contract_main.sales_person_name,
                        ppc_contract_main.sales_person_code,
                        ppc_contract_main.create_on,
                        ppc_contract_main.receive_date,
                        ppc_contract_main.date_sign,
                        ppc_contract_detail.id                                  as detailId,
                        ppc_contract_detail.sd_no                               as souceSdNo,
                        ppc_contract_detail.material_code,
                        ppc_contract_detail.material_name,
                        ppc_contract_detail.specification,
                        if(ppc_contract_detail.qty_limit = '1',
                           ifnull(ppc_contract_detail.max_qty, 0) - ifnull((select sum(ifnull(a.qty, 0))
                                                                            from ppc_sale_detail a
                                                                                     left join ppc_sale_main b on a.main_id = b.id
                                                                            where a.souce_sd_no = ppc_contract_detail.sd_no
                                                                              and a.souce_no = ppc_contract_main.so_no), 0)
                            , ppc_contract_detail.qty) as qty,
                        ifnull((select sum(ifnull(a.qty, 0))
                                from ppc_sale_detail a
                                         left join ppc_sale_main b on a.main_id = b.id
                                where a.souce_sd_no = ppc_contract_detail.sd_no
                                  and a.souce_no = ppc_contract_main.so_no), 0) as useQty,
                        ppc_contract_detail.qty                                 as busQty,
                        ppc_contract_detail.qty  -  (ifnull((select sum(ifnull(a.qty, 0))
                                from ppc_sale_detail a
                                         left join ppc_sale_main b on a.main_id = b.id
                                where a.souce_sd_no = ppc_contract_detail.sd_no
                                  and a.souce_no = ppc_contract_main.so_no), 0)) as residueQty,
                        ppc_contract_detail.qty_limit,
                        ppc_contract_detail.max_qty,
                        ppc_contract_detail.unit,
                        ppc_contract_detail.single_price,
                        ppc_contract_detail.include_tax,
                        ppc_contract_detail.tax_code,
                        ppc_contract_detail.tax_rate,
                        ppc_contract_detail.discount_type,
                        ppc_contract_detail.discount_rate,
                        ppc_contract_detail.effective_date,
                        ppc_contract_detail.customer_material_code,
                        b.customer_material_name,
                        ppc_contract_detail.customer_material_marker,
                        ppc_contract_detail.customer_sale_no,
                        ppc_contract_detail.item_type                           as productType,
                        ppc_contract_detail.remarks,
                        ppc_material.custom,
                        ppc_contract_detail.expiry_date,
                        b.pack_code,
                        b.customer_material_description,
                        if(ifnull(d.id, '') = '', '0', '1')                     as bomDetail

        from ppc_contract_main
                 left join ppc_contract_detail on ppc_contract_detail.main_id = ppc_contract_main.id
                 left join ppc_material on ppc_material.material_code = ppc_contract_detail.material_code
                 left join sys_material_customer b on ppc_contract_detail.material_code = b.material_code and
                                                      ppc_contract_detail.customer_material_code =
                                                      b.customer_material_code and
                                                      b.customer_code = ppc_contract_main.customer_code
                 left join ppc_bom d on d.material_code = ppc_material.material_code and d.version_status = '1'
            ${ew.customSqlSegment}
    </select>
    <select id="queryFxListNotInId"
            resultType="com.imes.domain.entities.query.template.ppc.PpcContractSearchVo">
        select ppc_contract_main.id,
               ppc_contract_main.so_no        as souceNo,
               ppc_contract_main.so_name,
               ppc_contract_main.depart_name,
               ppc_contract_main.depart_code,
               ppc_contract_main.customer_name,
               ppc_contract_main.customer_code,
               ppc_contract_main.sales_person_name,
               ppc_contract_main.sales_person_code,
               ppc_contract_main.create_on,
               ppc_contract_main.receive_date,
               ppc_contract_main.date_sign,
               ppc_contract_detail.id         as detailId,
               ppc_contract_detail.sd_no      as souceSdNo,
               ppc_contract_detail.material_code,
               ppc_contract_detail.material_name,
               ppc_contract_detail.specification,
               if(ppc_contract_detail.qty_limit = '1',
                  ifnull(ppc_contract_detail.max_qty, 0) - ifnull((select sum(ifnull(a.qty, 0))
                                                                   from ppc_sale_detail a
                                                                            left join ppc_sale_main b on a.main_id = b.id
                                                                   where a.souce_sd_no = ppc_contract_detail.sd_no
                                                                     and a.souce_no = ppc_contract_main.so_no
                                                                     and b.id != #{id} ), 0)
                   , ppc_contract_detail.qty) as qty,
               ppc_contract_detail.qty_limit,
               ppc_contract_detail.max_qty,
               ppc_contract_detail.unit,
               ppc_contract_detail.single_price,
               ppc_contract_detail.include_tax,
               ppc_contract_detail.tax_code,
               ppc_contract_detail.tax_rate,
               ppc_contract_detail.discount_type,
               ppc_contract_detail.discount_rate,
               ppc_contract_detail.customer_material_code,
               ppc_contract_detail.customer_material_name,
               ppc_contract_detail.customer_material_marker,
               ppc_contract_detail.customer_sale_no,
               ppc_contract_detail.effective_date,
               ppc_contract_detail.expiry_date
        from ppc_contract_main
                 left join ppc_contract_detail on ppc_contract_detail.main_id = ppc_contract_main.id
        where (if(ppc_contract_detail.qty_limit = '1',
                  ifnull(ppc_contract_detail.max_qty, 0) - ifnull((select sum(ifnull(a.qty, 0))
                                                                   from ppc_sale_detail a
                                                                            left join ppc_sale_main b on a.main_id = b.id
                                                                   where a.souce_sd_no = ppc_contract_detail.sd_no
                                                                     and a.souce_no = ppc_contract_main.so_no
                                                                     and b.id != #{id}),0)
            , ppc_contract_detail.qty)) > 0

    </select>
    <select id="getIdBySoNos" resultType="java.lang.String">
        select id from ppc_contract_main where so_no in
        <foreach collection="soNos" open="(" item="soNo" separator="," close=")">
            #{soNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>