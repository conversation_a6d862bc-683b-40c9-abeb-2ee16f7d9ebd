<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ProcessDevDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcProcessDev">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 25 16:14:25 CST 2019.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="dev_code" jdbcType="VARCHAR" property="devCode" />
    <result column="dev_name" jdbcType="VARCHAR" property="devName" />
    <result column="workshop_code" jdbcType="VARCHAR" property="workshopCode" />
    <result column="workshop_name" jdbcType="VARCHAR" property="workshopName" />
    <result column="is_prod" jdbcType="VARCHAR" property="isProd" />
    <result column="is_insp" jdbcType="VARCHAR" property="isInsp" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 25 16:14:25 CST 2019.
    -->
    id, process_code, dev_code, dev_name, workshop_code, workshop_name, is_prod, is_insp,
    create_on, create_by, update_on, update_by, remarks
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 25 16:14:25 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from ppc_process_dev
    where id = #{id,jdbcType=VARCHAR}
  </select>

  <select id="queryByParam" resultMap="BaseResultMap" parameterType="java.lang.String">
    select
    <include refid="Base_Column_List"/>
    from ppc_process_dev
    where 1=1
    <if test="id != null and id != ''">
      and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="processCode != null and processCode != ''">
      and process_code = #{processCode,jdbcType=VARCHAR}
    </if>
    <if test="devCode != null and devCode != ''">
      and dev_code like concat('%', #{devCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="devcode != null and devcode != ''">
      and dev_code = #{devcode,jdbcType=VARCHAR}
    </if>
    <if test="workshopCode != null and workshopCode != ''">
      and workshop_code = #{workshopCode,jdbcType=VARCHAR}
    </if>
  </select>

  <delete id="deleteByParam" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 25 16:14:25 CST 2019.
    -->
    delete from  ppc_process_dev
    where 1=1
    <if test="id != null and id != ''">
      and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="processCode != null and processCode != ''">
      and process_code = #{processCode,jdbcType=VARCHAR}
    </if>
    <if test="devCode != null and devCode != ''">
      and dev_code = #{devCode,jdbcType=VARCHAR}
    </if>
  </delete>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Oct 25 16:14:25 CST 2019.
    -->
    delete from  ppc_process_dev
    where id = #{id,jdbcType=VARCHAR}
  </delete>

  <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcProcessDev">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 12 19:26:32 CST 2019.
    -->
    insert into ppc_process_dev (id, process_code, dev_code,
    dev_name, workshop_code, workshop_name,
    is_prod, is_insp, create_on,
    create_by, update_on, update_by,
    remarks)
    values (#{id,jdbcType=VARCHAR}, #{processCode,jdbcType=VARCHAR}, #{devCode,jdbcType=VARCHAR},
    #{devName,jdbcType=VARCHAR}, #{workshopCode,jdbcType=VARCHAR}, #{workshopName,jdbcType=VARCHAR},
    #{isProd,jdbcType=VARCHAR}, #{isInsp,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP},
    #{createBy,jdbcType=VARCHAR}, #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
    #{remarks,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcProcessDev">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 12 19:26:32 CST 2019.
    -->
    insert into ppc_process_dev
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
      <if test="devCode != null">
        dev_code,
      </if>
      <if test="devName != null">
        dev_name,
      </if>
      <if test="workshopCode != null">
        workshop_code,
      </if>
      <if test="workshopName != null">
        workshop_name,
      </if>
      <if test="isProd != null">
        is_prod,
      </if>
      <if test="isInsp != null">
        is_insp,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="devCode != null">
        #{devCode,jdbcType=VARCHAR},
      </if>
      <if test="devName != null">
        #{devName,jdbcType=VARCHAR},
      </if>
      <if test="workshopCode != null">
        #{workshopCode,jdbcType=VARCHAR},
      </if>
      <if test="workshopName != null">
        #{workshopName,jdbcType=VARCHAR},
      </if>
      <if test="isProd != null">
        #{isProd,jdbcType=VARCHAR},
      </if>
      <if test="isInsp != null">
        #{isInsp,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcProcessDev">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 12 19:26:32 CST 2019.
    -->
    update ppc_process_dev
    <set>
      <if test="processCode != null">
        process_code = #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="devCode != null">
        dev_code = #{devCode,jdbcType=VARCHAR},
      </if>
      <if test="devName != null">
        dev_name = #{devName,jdbcType=VARCHAR},
      </if>
      <if test="workshopCode != null">
        workshop_code = #{workshopCode,jdbcType=VARCHAR},
      </if>
      <if test="workshopName != null">
        workshop_name = #{workshopName,jdbcType=VARCHAR},
      </if>
      <if test="isProd != null">
        is_prod = #{isProd,jdbcType=VARCHAR},
      </if>
      <if test="isInsp != null">
        is_insp = #{isInsp,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.PpcProcessDev">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Dec 12 19:26:32 CST 2019.
    -->
    update ppc_process_dev
    set process_code = #{processCode,jdbcType=VARCHAR},
    dev_code = #{devCode,jdbcType=VARCHAR},
    dev_name = #{devName,jdbcType=VARCHAR},
    workshop_code = #{workshopCode,jdbcType=VARCHAR},
    workshop_name = #{workshopName,jdbcType=VARCHAR},
    is_prod = #{isProd,jdbcType=VARCHAR},
    is_insp = #{isInsp,jdbcType=VARCHAR},
    create_on = #{createOn,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_on = #{updateOn,jdbcType=TIMESTAMP},
    update_by = #{updateBy,jdbcType=VARCHAR},
    remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>