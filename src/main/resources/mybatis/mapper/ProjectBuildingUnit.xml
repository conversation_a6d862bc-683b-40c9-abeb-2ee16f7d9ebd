<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ProjectBuildingUnitDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="local_building_unit_id" jdbcType="VARCHAR" property="localBuildingUnitId" />
        <result column="project_serial_number" jdbcType="VARCHAR" property="projectSerialNumber" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_short_name" jdbcType="CHAR" property="projectShortName" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="region_type" jdbcType="VARCHAR" property="regionType" />
        <result column="building_unit" jdbcType="VARCHAR" property="buildingUnit" />
        <result column="size_name" jdbcType="VARCHAR" property="sizeName" />
        <result column="system_name" jdbcType="VARCHAR" property="systemName" />
        <result column="material_type_name" jdbcType="VARCHAR" property="materialTypeName" />
        <result column="floor_height" jdbcType="DECIMAL" property="floorHeight" />
        <result column="design_order_date_start" jdbcType="DATE" property="designOrderDateStart" />
        <result column="design_order_date_end" jdbcType="DATE" property="designOrderDateEnd" />
        <result column="design_actual_release_time_start" jdbcType="TIMESTAMP" property="designActualReleaseTimeStart" />
        <result column="design_actual_release_time_end" jdbcType="TIMESTAMP" property="designActualReleaseTimeEnd" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
    </resultMap>
    <sql id="Base_Column_List">
        id, local_building_unit_id, project_serial_number, project_name, project_short_name, 
    business_type, region_type, building_unit, size_name, system_name, material_type_name, 
    floor_height, design_order_date_start, design_order_date_end, design_actual_release_time_start, 
    design_actual_release_time_end, `status`, is_delete, create_user, create_time, update_time, 
    update_user, delete_user, delete_time, error_msg
    </sql>
    <select id="findUnHandleList" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit"
            resultMap="BaseResultMap">
        select *
        from project_building_unit
        where 1 = 1
        and project_building_unit.status = '1'
        order by project_building_unit.id asc
    </select>

    <update id="updatePo" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit">
        UPDATE project_building_unit
        SET
        <if test="status != null and status != ''">
            status = #{status, jdbcType=VARCHAR},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="updateUser != null and updateUser != ''">
            update_user = #{updateUser, jdbcType=VARCHAR},
        </if>
        <if test="errorMsg != null and errorMsg != ''">
            error_Msg = #{errorMsg, jdbcType=VARCHAR},
        </if>
        id =#{id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updatePoByIdAndStatus" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit">
        UPDATE project_building_unit
        SET
        <if test="po.status != null and po.status != ''">
            status = #{po.status, jdbcType=VARCHAR},
        </if>
        <if test="po.updateTime != null">
            update_time = #{po.updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="po.updateUser != null and po.updateUser != ''">
            update_user = #{po.updateUser, jdbcType=VARCHAR},
        </if>
        <if test="po.errorMsg != null and po.errorMsg != ''">
            error_Msg = #{po.errorMsg, jdbcType=VARCHAR},
        </if>
        id =#{po.id,jdbcType=VARCHAR}
        where id = #{po.id,jdbcType=VARCHAR}
        and status = #{status}
    </update>
    <update id="updateErpProductOrderErrorMsg" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit">
        UPDATE project_building_unit
        SET
        <if test="updateTime != null">
            update_time = #{updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="updateUser != null and updateUser != ''">
            update_user = #{updateUser, jdbcType=VARCHAR},
        </if>
        <if test="errorMsg != null and errorMsg != ''">
            error_Msg = #{errorMsg, jdbcType=VARCHAR},
        </if>
        id =#{id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>


    <select id="queryAll" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProjectBuildingUnit"
            resultMap="BaseResultMap">
        select
            *
        from
            project_building_unit
                ${ew.customSqlSegment}

    </select>
</mapper>

