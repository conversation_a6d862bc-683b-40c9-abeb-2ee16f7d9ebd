<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ErpProductOrderDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="local_order_id" jdbcType="INTEGER" property="localOrderId" />
        <result column="local_application_id" jdbcType="INTEGER" property="localApplicationId" />
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName" />
        <result column="factory_region_name" jdbcType="VARCHAR" property="factoryRegionName" />
        <result column="factory_line_name" jdbcType="VARCHAR" property="factoryLineName" />
        <result column="is_new_material" jdbcType="CHAR" property="isNewMaterial" />
        <result column="is_machine_product" jdbcType="VARCHAR" property="isMachineProduct" />
        <result column="order_type" jdbcType="VARCHAR" property="orderType" />
        <result column="project_serial_number" jdbcType="VARCHAR" property="projectSerialNumber" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="project_short_name" jdbcType="CHAR" property="projectShortName" />
        <result column="business_type" jdbcType="VARCHAR" property="businessType" />
        <result column="region_type" jdbcType="VARCHAR" property="regionType" />
        <result column="building_unit" jdbcType="VARCHAR" property="buildingUnit" />
        <result column="size_system_material" jdbcType="VARCHAR" property="sizeSystemMaterial" />
        <result column="assemble_type" jdbcType="VARCHAR" property="assembleType" />
        <result column="part_type" jdbcType="VARCHAR" property="partType" />
        <result column="doc_number" jdbcType="VARCHAR" property="docNumber" />
        <result column="leader_name" jdbcType="VARCHAR" property="leaderName" />
        <result column="ds_spec" jdbcType="VARCHAR" property="dsSpec" />
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="first_step_name" jdbcType="VARCHAR" property="firstStepName" />
        <result column="documentation_date" jdbcType="DATE" property="documentationDate" />
        <result column="spray_type_name" jdbcType="VARCHAR" property="sprayTypeName" />
        <result column="project_material_code" jdbcType="VARCHAR" property="projectMaterialCode" />
        <result column="production_material_code" jdbcType="VARCHAR" property="productionMaterialCode" />
        <result column="spec_release_time" jdbcType="TIMESTAMP" property="specReleaseTime" />
        <result column="documentation_user" jdbcType="VARCHAR" property="documentationUser" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="mes_status" jdbcType="TINYINT" property="mesStatus" />
        <result column="is_delete" jdbcType="TINYINT" property="isDelete" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="delete_user" jdbcType="VARCHAR" property="deleteUser" />
        <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
        <result column="error_msg" jdbcType="VARCHAR" property="errorMsg" />
        <result column="spec_doc_id" jdbcType="VARCHAR" property="specDocId" />
        <result column="feeding_submit_user" jdbcType="VARCHAR" property="feedingSubmitUser" />
        <result column="feeding_arrival_date" jdbcType="DATE" property="feedingArrivalDate" />
        <result column="feeding_urgency_level" jdbcType="VARCHAR" property="feedingUrgencyLevel" />
        <result column="feeding_order_sn" jdbcType="VARCHAR" property="feedingOrderSn" />
        <result column="feeding_reason" jdbcType="VARCHAR" property="feedingReason" />
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo" />
        <result column="floor_height" jdbcType="VARCHAR" property="floorHeight" />
    </resultMap>
    <sql id="Base_Column_List">
        id, local_order_id, local_application_id, factory_name, factory_region_name, factory_line_name,
    is_new_material, is_machine_product, order_type, project_serial_number, project_name,
    project_short_name, business_type, region_type, building_unit, size_system_material,
    assemble_type, part_type, doc_number, leader_name, r_corner, ds_spec, order_no, first_step_name,
    documentation_date, spray_type_name, project_material_code, production_material_code,
    spec_release_time, documentation_user, remark, `status`, mes_status, is_delete, create_user,
    create_time, update_time, update_user, delete_user, delete_time, error_msg, spec_doc_id,
    feeding_submit_user, feeding_arrival_date, feeding_urgency_level, feeding_order_sn,
    feeding_reason,
    batch_no,
    floor_height
    </sql>
    <select id="findByPo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_order
        where 1=1
        <if test="status != null and status != ''">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        order by product_order.id asc
    </select>
    <select id="findByIdIn" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_order
        where 1=1
        and id in ${idIn}
        order by product_order.id asc
    </select>
    <select id="findById" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from product_order
        where 1=1
        and id =#{id}
    </select>
    <select id="findUnhandleOld" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from product_order
        where 1 = 1
          and product_order.status in ('19')
          and order_type in ${orderType}
        order by product_order.id asc
    </select>
    <select id="findUnhandleList" parameterType="java.lang.String" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from product_order
        where 1 = 1
          and product_order.status in ('1', '3', '7')
          and order_type in ${orderType}
        order by product_order.id asc
    </select>

    <update id="updatePo" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        UPDATE product_order
        SET
        <if test="status != null and status != ''">
            status = #{status, jdbcType=VARCHAR},
        </if>
        <if test="updateTime != null">
            update_time = #{updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="updateUser != null and updateUser != ''">
            update_user = #{updateUser, jdbcType=VARCHAR},
        </if>
        <if test="errorMsg != null and errorMsg != ''">
            error_Msg = #{errorMsg, jdbcType=VARCHAR},
        </if>
        id =#{id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updatePoByIdAndStatus" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        UPDATE product_order
        SET
        <if test="po.status != null and po.status != ''">
            status = #{po.status, jdbcType=VARCHAR},
        </if>
        <if test="po.updateTime != null">
            update_time = #{po.updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="po.updateUser != null and po.updateUser != ''">
            update_user = #{po.updateUser, jdbcType=VARCHAR},
        </if>
        <if test="po.errorMsg != null and po.errorMsg != ''">
            error_Msg = #{po.errorMsg, jdbcType=VARCHAR},
        </if>
        <if test="po.mesStatus != null and po.mesStatus != ''">
            mes_status = #{po.mesStatus, jdbcType=VARCHAR},
        </if>
        id =#{po.id,jdbcType=VARCHAR}
        where id = #{po.id,jdbcType=VARCHAR}
        and status = #{status}
    </update>
    <update id="updateErpProductOrderErrorMsg" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        UPDATE product_order
        SET
        <if test="updateTime != null">
            update_time = #{updateTime, jdbcType=TIMESTAMP},
        </if>
        <if test="updateUser != null and updateUser != ''">
            update_user = #{updateUser, jdbcType=VARCHAR},
        </if>
        <if test="errorMsg != null and errorMsg != ''">
            error_Msg = #{errorMsg, jdbcType=VARCHAR},
        </if>
        id =#{id,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateMesStatus" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        UPDATE product_order
        SET
            update_time = #{updateTime, jdbcType=TIMESTAMP},
            update_user = #{updateUser, jdbcType=VARCHAR},
            mes_status = #{mesStatus, jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateMesStatusBatch" parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        UPDATE product_order
        SET
            update_time = #{po.updateTime, jdbcType=TIMESTAMP},
            update_user = #{po.updateUser, jdbcType=VARCHAR},
            mes_status = #{po.mesStatus, jdbcType=VARCHAR}
        where id in ${idIn}
    </update>


    <select id="queryAll" resultType="com.imes.domain.entities.query.template.ppc.ErpProductOrderQueryVo">
        select
        <if test="ew.isMainMode">
            product_order.id AS id,
            product_order.local_order_id AS localOrderId,
            product_order.local_application_id AS localApplicationId,
            product_order.factory_name AS factoryName,
            product_order.factory_region_name AS factoryRegionName,
            product_order.factory_line_name AS factoryLineName,
            product_order.is_new_material AS isNewMaterial,
            product_order.is_machine_product AS isMachineProduct,
            product_order.order_type AS orderType,
            product_order.project_serial_number AS projectSerialNumber,
            product_order.project_name AS projectName,
            product_order.project_short_name AS projectShortName,
            product_order.business_type AS businessType,
            product_order.region_type AS regionType,
            product_order.building_unit AS buildingUnit,
            product_order.size_system_material AS sizeSystemMaterial,
            product_order.assemble_type AS assembleType,
            product_order.part_type AS partType,
            product_order.doc_number AS docNumber,
            product_order.leader_name AS leaderName,
            product_order.r_corner AS raCorner,
            product_order.ds_spec AS dsSpec,
            product_order.order_no AS orderNo,
            product_order.first_step_name AS firstStepName,
            product_order.documentation_date AS documentationDate,
            product_order.spray_type_name AS sprayTypeName,
            product_order.project_material_code AS projectMaterialCode,
            product_order.production_material_code AS productionMaterialCode,
            product_order.spec_release_time AS specReleaseTime,
            product_order.documentation_user AS documentationUser,
            product_order.remark AS remark,
            product_order.`status` AS status,
            product_order.mes_status AS mesStatus,
            product_order.is_delete AS isDelete,
            product_order.create_user AS createUser,
            product_order.create_time AS createTime,
            product_order.update_time AS updateTime,
            product_order.update_user AS updateUser,
            product_order.delete_user AS deleteUser,
            product_order.delete_time AS deleteTime,
            product_order.error_msg AS errorMsg,
            product_order.spec_doc_id AS specDocId,
            product_order.feeding_submit_user AS feedingSubmitUser,
            product_order.feeding_arrival_date AS feedingArrivalDate,
            product_order.feeding_urgency_level AS feedingUrgencyLevel,
            product_order.feeding_order_sn AS feedingOrderSn,
            product_order.feeding_reason AS feedingReason,
            product_order.batch_no AS batchNo,
            product_order.floor_height AS floorHeight
            from product_order
        </if>
        <if test="!ew.isMainMode">
            product_order.id AS id,
            product_order.local_order_id AS localOrderId,
            product_order.local_application_id AS localApplicationId,
            product_order.factory_name AS factoryName,
            product_order.factory_region_name AS factoryRegionName,
            product_order.factory_line_name AS factoryLineName,
            product_order.is_new_material AS isNewMaterial,
            product_order.is_machine_product AS isMachineProduct,
            product_order.order_type AS orderType,
            product_order.project_serial_number AS projectSerialNumber,
            product_order.project_name AS projectName,
            product_order.project_short_name AS projectShortName,
            product_order.business_type AS businessType,
            product_order.region_type AS regionType,
            product_order.building_unit AS buildingUnit,
            product_order.size_system_material AS sizeSystemMaterial,
            product_order.assemble_type AS assembleType,
            product_order.part_type AS partType,
            product_order.doc_number AS docNumber,
            product_order.leader_name AS leaderName,
            product_order.r_corner AS raCorner,
            product_order.ds_spec AS dsSpec,
            product_order.order_no AS orderNo,
            product_order.first_step_name AS firstStepName,
            product_order.documentation_date AS documentationDate,
            product_order.spray_type_name AS sprayTypeName,
            product_order.project_material_code AS projectMaterialCode,
            product_order.production_material_code AS productionMaterialCode,
            product_order.spec_release_time AS specReleaseTime,
            product_order.documentation_user AS documentationUser,
            product_order.remark AS remark,
            product_order.`status` AS status,
            product_order.mes_status AS mesStatus,
            product_order.is_delete AS isDelete,
            product_order.create_user AS createUser,
            product_order.create_time AS createTime,
            product_order.update_time AS updateTime,
            product_order.update_user AS updateUser,
            product_order.delete_user AS deleteUser,
            product_order.delete_time AS deleteTime,
            product_order.error_msg AS errorMsg,
            product_order.spec_doc_id AS specDocId,
            product_order.feeding_submit_user AS feedingSubmitUser,
            product_order.feeding_arrival_date AS feedingArrivalDate,
            product_order.feeding_urgency_level AS feedingUrgencyLevel,
            product_order.feeding_order_sn AS feedingOrderSn,
            product_order.feeding_reason AS feedingReason,
            product_order.batch_no AS batchNo,
            product_order.floor_height AS floorHeight,
            product_order_detail.id AS detailId,
            product_order_detail.product_order_id AS productOrderId,
            product_order_detail.material_code AS materialCode,
            product_order_detail.sort_num AS sortNum,
            product_order_detail.material_width AS materialWidth,
            product_order_detail.material_name AS materialName,
            product_order_detail.material_length AS materialLength,
            product_order_detail.heterotype AS heterotype,
            product_order_detail.order_amount AS orderAmount,
            product_order_detail.un_standard_milling_groove AS unStandardMillingGroove,
            product_order_detail.screw_width AS screwWidth,
            product_order_detail.screw_height AS screwHeight,
            product_order_detail.attach_ec_r_corner AS attachEcRCorner,
            product_order_detail.patch_info AS patchInfo,
            product_order_detail.chart_filename AS chartFilename,
            product_order_detail.accessory_filename AS accessoryFilename,
            product_order_detail.unit_area AS unitArea,
            product_order_detail.area AS area,
            product_order_detail.remark AS remark,
            product_order_detail.change_type AS changeType,
            product_order_detail.raw_local_order_id AS rawLocalOrderId,
            product_order_detail.assemble_num AS assembleNum,
            product_order_detail.chart_file_url AS chartFileUrl,
            product_order_detail.change_to_order_detail_id AS changeToOrderDetailId,
            product_order_detail_material_ext.piece_attr as pieceAttr

            from product_order  left join product_order_detail on product_order.id=product_order_detail.product_order_id
            left join product_order_detail_material_ext on
            product_order_detail.id=product_order_detail_material_ext.product_order_detail_id
        </if>

        ${ew.customSqlSegment}

    </select>

    <update id="updateStatusForce"   parameterType="com.imes.domain.entities.ppc.po.erp.ErpProductOrder">
        update product_order
        set
            `status` = #{status}

        where id in ${id}
    </update>
</mapper>

