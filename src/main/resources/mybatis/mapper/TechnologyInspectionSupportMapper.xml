<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.TechnologyInspectionSupportDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.TechnologyInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
    <result column="pp_no" jdbcType="VARCHAR" property="ppNo" />
    <result column="pc_no" jdbcType="VARCHAR" property="pcNo" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_ver" jdbcType="VARCHAR" property="bomVer" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="process_name" jdbcType="VARCHAR" property="processName" />
    <result column="station_ip" jdbcType="VARCHAR" property="stationIp" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="inspect_code" jdbcType="VARCHAR" property="inspectDepartCode" />
    <result column="inspect_name" jdbcType="VARCHAR" property="inspectDepartName" />
    <result column="inspect_type" jdbcType="VARCHAR" property="inspectType" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="start_plan" jdbcType="TIMESTAMP" property="startPlan" />
    <result column="end_plan" jdbcType="TIMESTAMP" property="endPlan" />
    <result column="publish_status" jdbcType="VARCHAR" property="publishStatus" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="material_marker" jdbcType="VARCHAR" property="materialMarker" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="patrol_id" jdbcType="VARCHAR" property="patrolId" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="claim_name" jdbcType="VARCHAR" property="claimName" />
    <result column="claim_code" jdbcType="VARCHAR" property="claimCode" />
    <result column="wo_no" jdbcType="VARCHAR" property="woNo" />
    <result column="work_unit_code" jdbcType="VARCHAR" property="workUnitCode" />
    <result column="task_inspection_code" jdbcType="VARCHAR" property="taskInspectionCode" />

  </resultMap>

  <resultMap id="BaseResultMapForSku" type="com.imes.domain.entities.qms.vo.TechnologyInspectionForSku">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
    <result column="pp_no" jdbcType="VARCHAR" property="ppNo" />
    <result column="pc_no" jdbcType="VARCHAR" property="pcNo" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_ver" jdbcType="VARCHAR" property="bomVer" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="process_name" jdbcType="VARCHAR" property="processName" />
    <result column="station_ip" jdbcType="VARCHAR" property="stationIp" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="inspect_code" jdbcType="VARCHAR" property="inspectDepartCode" />
    <result column="inspect_name" jdbcType="VARCHAR" property="inspectDepartName" />
    <result column="inspect_type" jdbcType="VARCHAR" property="inspectType" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="start_plan" jdbcType="TIMESTAMP" property="startPlan" />
    <result column="end_plan" jdbcType="TIMESTAMP" property="endPlan" />
    <result column="publish_status" jdbcType="VARCHAR" property="publishStatus" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="material_marker" jdbcType="VARCHAR" property="materialMarker" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="patrol_id" jdbcType="VARCHAR" property="patrolId" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="claim_name" jdbcType="VARCHAR" property="claimName" />
    <result column="claim_code" jdbcType="VARCHAR" property="claimCode" />
    <result column="wo_no" jdbcType="VARCHAR" property="woNo" />
    <result column="work_unit_code" jdbcType="VARCHAR" property="workUnitCode" />
    <result column="task_inspection_code" jdbcType="VARCHAR" property="taskInspectionCode" />
    <result column="sku_code" jdbcType="VARCHAR" property="skuCode" />

  </resultMap>

  <resultMap id="BaseResultMapForInforVo" type="com.imes.domain.entities.qms.vo.TechnologyInspectionVo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="plan_code" jdbcType="VARCHAR" property="planCode" />
    <result column="pp_no" jdbcType="VARCHAR" property="ppNo" />
    <result column="pc_no" jdbcType="VARCHAR" property="pcNo" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_ver" jdbcType="VARCHAR" property="bomVer" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="process_name" jdbcType="VARCHAR" property="processName" />
    <result column="station_ip" jdbcType="VARCHAR" property="stationIp" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="station_code" jdbcType="VARCHAR" property="stationCode" />
    <result column="station_name" jdbcType="VARCHAR" property="stationName" />
    <result column="inspect_code" jdbcType="VARCHAR" property="inspectDepartCode" />
    <result column="inspect_name" jdbcType="VARCHAR" property="inspectDepartName" />
    <result column="inspect_type" jdbcType="VARCHAR" property="inspectType" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="user_code" jdbcType="VARCHAR" property="userCode" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="start_plan" jdbcType="TIMESTAMP" property="startPlan" />
    <result column="end_plan" jdbcType="TIMESTAMP" property="endPlan" />
    <result column="publish_status" jdbcType="VARCHAR" property="publishStatus" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="material_marker" jdbcType="VARCHAR" property="materialMarker" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="patrol_id" jdbcType="VARCHAR" property="patrolId" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="claim_name" jdbcType="VARCHAR" property="claimName" />
    <result column="claim_code" jdbcType="VARCHAR" property="claimCode" />
    <result column="wo_no" jdbcType="VARCHAR" property="woNo" />
    <result column="work_unit_code" jdbcType="VARCHAR" property="workUnitCode" />
    <result column="task_inspection_code" jdbcType="VARCHAR" property="taskInspectionCode" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="inspectionId" jdbcType="VARCHAR" property="inspectionId" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, plan_code, pp_no,pc_no,material_code, material_name, bom_code,is_qualified,patrol_id,
    bom_name,process_code,process_name,station_ip,station_code,station_name,
    inspect_type,inspector,inspection_num,start_plan,end_plan,status,inspection_status,
    publish_status,create_on,create_by,update_by,update_on,remarks,specification,unit,material_marker,
    user_code,user_name,inspect_code,inspect_name,bom_ver,route_code,wo_no,work_unit_code,task_inspection_code
  </sql>
  <select id="queryList" parameterType="java.lang.String" resultMap="BaseResultMapForSku">
    select
      qms_technology_inspection.id,
      qms_technology_inspection.plan_code,
      qms_technology_inspection.pp_no,
      qms_technology_inspection.pc_no,
      qms_technology_inspection.material_code,
      ppc_material.material_name,
      qms_technology_inspection.bom_code,
      qms_technology_inspection.is_qualified,
      qms_technology_inspection.patrol_id,
      qms_technology_inspection.bom_name,
      qms_technology_inspection.process_code,
      qms_technology_inspection.process_name,
      qms_technology_inspection.station_ip,
      qms_technology_inspection.station_code,
      qms_technology_inspection.station_name,
      qms_technology_inspection.inspect_type,
      qms_technology_inspection.inspector,
      qms_technology_inspection.inspection_num,
      qms_technology_inspection.start_plan,
      qms_technology_inspection.end_plan,
      qms_technology_inspection.STATUS,
      qms_technology_inspection.inspection_status,
      qms_technology_inspection.publish_status,
      qms_technology_inspection.create_on,
      qms_technology_inspection.create_by,
      qms_technology_inspection.update_by,
      qms_technology_inspection.update_on,
      qms_technology_inspection.remarks,
      ppc_material.specification,
      qms_technology_inspection.unit,
      ppc_material.material_marker,
      qms_technology_inspection.user_code,
      qms_technology_inspection.user_name,
      qms_technology_inspection.inspect_code,
      qms_technology_inspection.inspect_name,
      qms_technology_inspection.bom_ver,
      qms_technology_inspection.route_code,
      qms_technology_inspection.wo_no,
      qms_technology_inspection.work_unit_code,
      qms_technology_inspection.task_inspection_code,
      ppc_produce_plan.sku_code
    FROM
    qms_technology_inspection
    left join ppc_produce_plan on qms_technology_inspection.pp_no = ppc_produce_plan.pp_no
    left join ppc_material on ppc_material.material_code = qms_technology_inspection.material_code
    where 1=1
    <if test="publishStatus != null and publishStatus != ''">
      and qms_technology_inspection.publish_status =#{publishStatus,jdbcType=VARCHAR}
    </if>
    <if test="unSupport != null and unSupport != ''">
      and qms_technology_inspection.inspection_status <![CDATA[ <> ]]> #{unSupport,jdbcType=VARCHAR}
    </if>
    <if test="inspectionType != null and inspectionType != ''">
      and qms_technology_inspection.inspect_type like concat('%',#{inspectionType,jdbcType=VARCHAR},'%')
    </if>
    <if test="id != null and id != ''">
      and qms_technology_inspection.id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="isQualified != null and isQualified != ''">
      and qms_technology_inspection.is_qualified = #{isQualified,jdbcType=VARCHAR}
    </if>
    <if test="materialName != null and materialName != ''">
      and qms_technology_inspection.material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
    </if>
    <if test="startTime != null and startTime != ''">
      and qms_technology_inspection.create_on  <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null and endTime != ''">
      and qms_technology_inspection.create_on  <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
    </if>
    order by qms_technology_inspection.update_on desc
  </select>
  <select id="queryTaskList" parameterType="java.lang.String" resultType="com.imes.domain.entities.query.template.qms.GetPlanTasksVo">
    select
      task.id,
      task.plan_code,
      task.pp_no,
      task.pc_no,
      task.material_code,
      material.material_name,
      task.bom_code,
      task.is_qualified,
      task.patrol_id,
      task.bom_name,
      task.bom_ver,
      task.process_code,
      process.process_name,
      task.item_id,
      task.item_code,
      task.item_name,
      task.station,
      task.station_ip,
      task.station_code,
      task.station_name,
      task.inspect_type,
      task.inspector,
      task.unqualified_num,
      task.inspection_num,
      task.start_plan,
      task.end_plan,
      task.STATUS,
      task.user_code,
      task.user_name,
      task.inspection_status,
      task.inspection_code,
      task.inspection_no,
      task.publish_status,
      task.create_on,
      task.create_by,
      task.update_by,
      task.update_on,
      task.remarks,
      material.specification,
      unit.name as unit,
      material.material_marker,
      task.inspect_code,
      department.depart_name as inspect_name,
      task.route_code,
      task.wo_no,
      task.claim_name,
      task.claim_code,
      unit2.name as work_unit_code,
      task.task_inspection_code,
      CONCAT(material.material_name,task.material_code,task.pc_no,task.pp_no,process.process_name,task.task_inspection_code) as conditions
    FROM
    qms_technology_inspection task
    LEFT JOIN ppc_material material on material.material_code = task.material_code
    LEFT JOIN co_department department on department.depart_code = task.inspect_code
    LEFT JOIN sys_unit unit on unit.code = task.unit
    LEFT JOIN sys_unit unit2 on unit2.code = task.work_unit_code
    LEFT JOIN ppc_process process on process.process_code = task.process_code
    ${ew.customSqlSegment}
  </select>
  <select id="queryById" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    FROM
    qms_technology_inspection
    where id=#{id}
  </select>
  <select id="findQmsDepartCode" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT
      p1.id,
      p1.qms_depart_name AS qmsDepartName,
      p1.qms_depart_code AS qmsDepartCode
    FROM
      ppc_bom_qms_department p1
    WHERE
      1 = 1
      AND p1.bom_code = #{bomCode}
      AND p1.bom_ver = #{bomVer}
  </select>
  <select id="getMaterialByPpNo" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT
      bom_code bomCode,
      bom_name bomName,
      bom_ver bomVer,
      material_code materialCode,
      material_name materialName
    FROM
      ppc_produce_plan
    WHERE
      pp_no = #{ppNo}
    order by create_on desc
  </select>
  <select id="getMaterialInfo" resultType="com.imes.domain.entities.qms.vo.supportMaterialInfoVo">
    SELECT
      ps.id id,
      pp.pp_no ppNo,
      ps.production_no pcNo,
      ps.good_qty goodQty,
      pp.material_code materialCode,
      pp.material_name materialName,
      pp.bom_code bomCode,
      pp.bom_name bomName,
      pp.sku_code skuCode,
      pm.specification specification,
      pm.primary_unit unit,
      pm.material_marker materialMarker
    FROM
      ppc_produce_plan pp
        LEFT JOIN ppc_produce_plan_schedul ps ON pp.id = ps.plan_id
        LEFT JOIN ppc_material pm ON pp.material_code = pm.material_code
    WHERE
      pp.STATUS = 30
      and ps.status= 30
      <if test="materialName != null and materialName != ''">
          and pp.material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
      </if>
      AND pp.pp_no IN ( SELECT DISTINCT pp_no FROM ppc_work_order WHERE STATUS = 30 )
    order by pp.create_on desc
  </select>
  <select id="findByBomCodeAndBomVer" parameterType="java.lang.String" resultType="com.imes.domain.entities.ppc.po.PpcRouteLine">
        select
               route_name routeName,
               route_code routeCode
        from ppc_route_line where bom_code=#{bomCode} and bom_ver=#{bomVer}
  </select>
  <select id="getRouteByPcNo" parameterType="java.lang.String" resultType="java.util.Map">
    select
      route_name routeName,
      route_code routeCode,
      line_code lineCode
    from ppc_route_line where route_code=(select route_code from ppc_produce_plan_schedul where production_no=#{pcNo})
  </select>
  <select id="getProcessByRouteCode" parameterType="java.lang.String" resultType="com.imes.domain.entities.ppc.po.RouteAndProcess">
    SELECT
      r.process_no processNo,
      r.process_code processCode,
      p.process_name remarks
    FROM
      `ppc_route_process` r
        LEFT JOIN ppc_process p ON r.process_code = p.process_code
    WHERE
      r.route_code = #{routeCode}
      AND r.process_no IN ( SELECT process_no FROM ppc_work_order WHERE production_no = #{pcNo} AND status in ('30','40') )
  </select>
  <select id="getQmsRoute" parameterType="java.lang.String" resultType="com.imes.domain.entities.qms.po.QmsRouteInspection">
    SELECT
           id id,
      inspect_code inspectCode,
      inspect_name inspectName,
      inspect_type inspectType
    FROM
      qms_route_inspection
    WHERE
      route_code = #{routeCode}
      AND process_code = #{processCode}
    <if test="inspectType != null and inspectType != ''">
      and inspect_type like concat('%',#{inspectType,jdbcType=VARCHAR},'%')
    </if>
  </select>
  <select id="getStationByProcessCode" parameterType="java.lang.String" resultType="java.util.Map">
    SELECT
      station_ip stationIp,
      station_code stationCode,
      station_name stationName
    FROM
      sys_ppc_line_process_station
    WHERE
      process_code = #{processCode}
      AND station_ip IS NOT NULL
  </select>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.qms.po.TechnologyInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 22 15:16:27 CST 2020.
    -->
    insert into qms_technology_inspection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="planCode != null">
        plan_code,
      </if>
      <if test="ppNo != null">
        pp_no,
      </if>
      <if test="pcNo != null">
        pc_no,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="routeCode != null">
        route_code,
      </if>
      <if test="inspectDepartCode != null">
        inspect_code,
      </if>
      <if test="inspectDepartName != null">
        inspect_name,
      </if>
      <if test="userCode != null">
        user_code,
      </if>
      <if test="userName != null">
        user_name,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="primaryUnit != null">
        unit,
      </if>
      <if test="materialMarker != null">
        material_marker,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="bomCode != null">
        bom_code,
      </if>
      <if test="bomName != null">
        bom_name,
      </if>
      <if test="bomVer != null">
        bom_ver,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
      <if test="processName != null">
        process_name,
      </if>
      <if test="stationIp != null">
        station_ip,
      </if>
      <if test="stationCode != null">
        station_code,
      </if>
      <if test="stationName != null">
        station_name,
      </if>
      <if test="inspectType != null">
        inspect_type,
      </if>
      <if test="inspector != null">
        inspector,
      </if>
      <if test="isQualified != null">
        is_qualified,
      </if>
      <if test="patrolId != null">
        patrol_id,
      </if>
      <if test="inspectionNum != null">
        inspection_num,
      </if>
      <if test="startPlan != null">
        start_plan,
      </if>
      <if test="endPlan != null">
        end_plan,
      </if>
      <if test="publishStatus != null">
        publish_status,
      </if>
      <if test="inspectionStatus != null">
        inspection_status,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="woNo != null">
        wo_no,
      </if>
      <if test="workUnitCode != null">
        work_unit_code,
      </if>
      <if test="taskInspectionCode != null">
        task_inspection_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="planCode != null">
        #{planCode,jdbcType=VARCHAR},
      </if>
      <if test="ppNo != null">
        #{ppNo,jdbcType=VARCHAR},
      </if>
      <if test="pcNo != null">
        #{pcNo,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="routeCode != null">
        #{routeCode,jdbcType=VARCHAR},
      </if>
      <if test="inspectDepartCode != null">
        #{inspectDepartCode,jdbcType=VARCHAR},
      </if>
      <if test="inspectDepartName != null">
        #{inspectDepartName,jdbcType=VARCHAR},
      </if>
      <if test="userCode != null">
        #{userCode,jdbcType=VARCHAR},
      </if>
      <if test="userName != null">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="primaryUnit != null">
        #{primaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="materialMarker != null">
        #{materialMarker,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="bomCode != null">
        #{bomCode,jdbcType=VARCHAR},
      </if>
      <if test="bomName != null">
        #{bomName,jdbcType=VARCHAR},
      </if>
      <if test="bomVer != null">
        #{bomVer,jdbcType=VARCHAR},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="processName != null">
        #{processName,jdbcType=INTEGER},
      </if>
      <if test="stationIp != null">
        #{stationIp,jdbcType=VARCHAR},
      </if>
      <if test="stationCode != null">
        #{stationCode,jdbcType=VARCHAR},
      </if>
      <if test="stationName != null">
        #{stationName,jdbcType=VARCHAR},
      </if>
      <if test="inspectType != null">
        #{inspectType,jdbcType=VARCHAR},
      </if>
      <if test="inspector != null">
        #{inspector,jdbcType=VARCHAR},
      </if>
      <if test="isQualified != null">
        #{isQualified,jdbcType=VARCHAR},
      </if>
      <if test="patrolId != null">
        #{patrolId,jdbcType=VARCHAR},
      </if>
      <if test="inspectionNum != null">
        #{inspectionNum,jdbcType=INTEGER},
      </if>
      <if test="startPlan != null">
        #{startPlan,jdbcType=TIMESTAMP},
      </if>
      <if test="endPlan != null">
        #{endPlan,jdbcType=TIMESTAMP},
      </if>
      <if test="publishStatus != null">
        #{publishStatus,jdbcType=VARCHAR},
      </if>
      <if test="inspectionStatus != null">
        #{inspectionStatus,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="woNo != null">
        #{woNo,jdbcType=VARCHAR},
      </if>
      <if test="workUnitCode != null">
        #{workUnitCode,jdbcType=VARCHAR},
      </if>
      <if test="taskInspectionCode != null">
        #{taskInspectionCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateSelective" parameterType="com.imes.domain.entities.qms.po.TechnologyInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_technology_inspection
    <set>
      <if test="planCode != null">
        plan_code=#{planCode},
      </if>
      <if test="ppNo != null">
        pp_no=#{ppNo},
      </if>
      <if test="pcNo != null">
        pc_no=#{pcNo},
      </if>
      <if test="materialCode != null">
        material_code=#{materialCode},
      </if>
      <if test="materialName != null">
        material_name=#{materialName},
      </if>
      <if test="routeCode != null">
        route_code=#{routeCode},
      </if>
      <if test="inspectDepartCode != null">
        inspect_code=#{inspectDepartCode},
      </if>
      <if test="inspectDepartName != null">
        inspect_name=#{inspectDepartName},
      </if>
      <if test="userCode != null">
        user_code=#{userCode},
      </if>
      <if test="userName != null">
        user_name=#{userName},
      </if>
      <if test="specification != null">
        specification=#{specification},
      </if>
      <if test="primaryUnit != null">
        unit=#{primaryUnit},
      </if>
      <if test="materialMarker != null">
        material_marker=#{materialMarker},
      </if>
      <if test="status != null">
        status=#{status},
      </if>
      <if test="bomCode != null">
        bom_code=#{bomCode},
      </if>
      <if test="bomName != null">
        bom_name=#{bomName},
      </if>
      <if test="bomVer != null">
        bom_ver=#{bomVer},
      </if>
      <if test="processCode != null">
        process_code=#{processCode},
      </if>
      <if test="processName != null">
        process_name=#{processName},
      </if>
      <if test="stationIp != null">
        station_ip=#{stationIp},
      </if>
      <if test="stationCode != null">
        station_code=#{stationCode},
      </if>
      <if test="stationName != null">
        station_name=#{stationName},
      </if>
      <if test="inspectType != null">
        inspect_type=#{inspectType},
      </if>
      <if test="inspector != null">
        inspector=#{inspector},
      </if>
      <if test="isQualified != null">
        is_qualified=#{isQualified},
      </if>
      <if test="patrolId != null">
        patrol_id=#{patrolId},
      </if>
      <if test="(inspectionNum != null and inspectionNum != '') or inspectionNum==0">
        inspection_num=#{inspectionNum},
      </if>
      <if test="startPlan != null">
        start_plan=#{startPlan},
      </if>
      <if test="endPlan != null">
        end_plan=#{endPlan},
      </if>
      <if test="publishStatus != null">
        publish_status=#{publishStatus},
      </if>
      <if test="inspectionStatus != null">
        inspection_status=#{inspectionStatus},
      </if>
      <if test="createOn != null">
        create_on=#{createOn},
      </if>
      <if test="createBy != null">
        create_by=#{createBy},
      </if>
      <if test="updateOn != null">
        update_on=#{updateOn},
      </if>
      <if test="updateBy != null">
        update_by=#{updateBy},
      </if>
      <if test="remarks != null">
        remarks=#{remarks},
      </if>
      <if test="woNo != null">
        wo_no=#{woNo},
      </if>
      <if test="workUnitCode != null">
        work_unit_code=#{workUnitCode},
      </if>
      <if test="taskInspectionCode != null">
        task_inspection_code=#{taskInspectionCode},
      </if>
    </set>
    where 1=1
    <if test="id != null and id != ''">
        and id =#{id,jdbcType=VARCHAR}
    </if>
    <if test="patrolId != null and patrolId != ''">
        and patrol_id =#{patrolId,jdbcType=VARCHAR}
    </if>
  </update>
  <update id="updateStatusByTaskId" parameterType="com.imes.domain.entities.qms.po.TechnologyInspection">
    update
        qms_technology_inspection
    set
        inspection_status=#{inspectionStatus},
        publish_status = #{publishStatus},
        status = #{status},
        is_qualified=#{isQualified}
    where
        id=#{id}
  </update>
  <select id="getInspectionPlanList"  parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_technology_inspection
    where 1=1 and publish_status IS not NULL
    <if test="id != null and id != ''">
      and id =#{id,jdbcType=VARCHAR}
    </if>
    <if test="publishStatus != null and publishStatus != ''">
      and publish_status =#{publishStatus,jdbcType=VARCHAR}
    </if>
    <if test="planCode != null and planCode != ''">
      and plan_code = #{planCode,jdbcType=VARCHAR}
    </if>
    <if test="ppNo != null and ppNo != ''">
      and pp_no =#{ppNo,jdbcType=VARCHAR}
    </if>
    <if test="pcNo != null and pcNo != ''">
      and pc_no =#{pcNo,jdbcType=VARCHAR}
    </if>
    <if test="materialCode != null and materialCode != ''">
      and material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="materialName != null and materialName != ''">
      and material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
    </if>
    <if test="inspectType != null and inspectType != ''">
      and inspect_type in ${inspectType}
    </if>
    <if test="startPlanStartTime != null and startPlanStartTime != ''">
      and start_plan  <![CDATA[ >= ]]> #{startPlanStartTime,jdbcType=TIMESTAMP}
    </if>
    <if test="startPlanEndTime != null and startPlanEndTime != ''">
      and start_plan  <![CDATA[ <= ]]>  #{startPlanEndTime,jdbcType=TIMESTAMP}
    </if>
    order by publish_status,
    create_on DESC
  </select>
  <select id="getPpNos" resultType="java.lang.String">
    SELECT
      pp_no
    FROM
      ppc_produce_plan
    WHERE
      STATUS = '30'
    ORDER BY
      pp_no
  </select>
  <select id="getFileLists" parameterType="java.lang.String" resultType="java.lang.String">
    select
    file_id fileId
    from ppc_route_process_file
    <where>
      <if test="routeCode != null and routeCode != ''">
        and route_code = #{routeCode,jdbcType=VARCHAR}
      </if>
      <if test="processCode != null and processCode != ''">
        and process_code = #{processCode,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
  <delete id="deleteSelective" parameterType="java.lang.String">
    delete from qms_technology_inspection where id=#{id}
  </delete>
  <update id="updateStatusAndNumById" parameterType="java.util.Map">
    update qms_technology_inspection set inspection_status=#{status},inspection_num=#{inspectionNum} where id=#{id}
  </update>
  <update id="updateSelectiveReject" parameterType="java.lang.String">
    update
        qms_technology_inspection
    set
        inspection_status=#{inspectionStatus},
        publish_status=#{publishStatus},
        status = #{status}
    where
          id=#{id}
  </update>
  <update id="updateClaimCodeAndClaimNameById" parameterType="java.lang.String">
    UPDATE qms_technology_inspection SET claim_name = #{claimName}, claim_code = #{claimCode} WHERE id = #{id}
  </update>

  <update id="updateClaimAndTeamCode" parameterType="com.imes.domain.entities.qms.po.TechnologyInspection">
    update qms_technology_inspection set claim_code=#{claimCode},claim_name=#{claimName},inspect_code=#{inspectDepartCode},inspect_name=#{inspectDepartName} where id =#{id}
  </update>

  <update id="updateClaimNameIsNull" parameterType="com.imes.domain.entities.qms.po.TechnologyInspection">
    update qms_technology_inspection set claim_code=#{claimCode},claim_name=#{claimName}  where id = #{id}
  </update>


  <select id="queryFirstAndEndInfoByVo" parameterType="com.imes.domain.entities.qms.vo.FirstAndEndInforVo"  resultMap="BaseResultMapForInforVo">
    select
        a.id,
        a.plan_code,
        a.pp_no,
        a.pc_no,
        a.material_code,
        a.material_name,
        a.bom_code,
        a.is_qualified,
        a.patrol_id,
        a.bom_name,
        a.process_code,
        a.process_name,
        a.station_ip,
        a.station_code,
        a.station_name,
        a.inspect_type,
        a.inspector,
        a.inspection_num,
        a.start_plan,
        a.end_plan,
        a.STATUS,
        a.inspection_status,
        a.publish_status,
        a.create_on,
        a.create_by,
        a.update_by,
        a.update_on,
        a.remarks,
        a.specification,
        a.unit,
        a.material_marker,
        a.user_code,
        a.user_name,
        a.inspect_code,
        a.inspect_name,
        a.bom_ver,
        a.route_code,
        a.task_inspection_code,
        a.wo_no,
        a.work_unit_code,
        b.is_qualified,
        b.id as inspectionId
    FROM
    `qms_technology_inspection` a
    LEFT JOIN  qms_first_final_inspection b on a.id = b.order_id and b.inspection_status != '9'  AND  b.status != '1' AND b.STATUS IN (11,4,2,8,12)
    where
    1=1
    <if test="taskInspectionCode != null and taskInspectionCode != ''">
      and a.task_inspection_code =#{taskInspectionCode,jdbcType=VARCHAR}
    </if>
    <if test="materialCode != null and materialCode != ''">
      and a.material_code =#{materialCode,jdbcType=VARCHAR}
    </if>
    <if test="woNo != null and woNo != ''">
      and a.wo_no =#{woNo,jdbcType=VARCHAR}
    </if>
    <if test="ppNo != null and ppNo != ''">
      and a.pp_no =#{ppNo,jdbcType=VARCHAR}
    </if>
    <if test="productionNo != null and productionNo != ''">
      and a.pc_no =#{productionNo,jdbcType=VARCHAR}
    </if>
    <if test="processCode != null and processCode != ''">
      and a.process_code =#{processCode,jdbcType=VARCHAR}
    </if>
    <if test="status != null and status != ''">
      and a.inspection_status = #{status,jdbcType=VARCHAR}
    </if>
    <if test="statusIn != null and statusIn.size > 0">
      AND (a.inspection_status IN
      <foreach collection="statusIn" index="index" item="item" open="(" separator="," close=")">
        #{item}
      </foreach>
      )
    </if>
    <if test="createOnStart != null and createOnStart != ''">
      and a.create_on  <![CDATA[ >= ]]>  #{createOnStart, jdbcType=TIMESTAMP}
    </if>
    <if test="createOnEnd != null and createOnEnd != ''">
      and a.create_on  <![CDATA[ <= ]]>   #{createOnEnd, jdbcType=TIMESTAMP}
    </if>
    <if test="updateOnStart != null and updateOnStart != ''">
      and a.update_on  <![CDATA[ >= ]]>  #{updateOnStart, jdbcType=TIMESTAMP}
    </if>
    <if test="updateOnEnd != null and updateOnEnd != ''">
      and a.update_on <![CDATA[ <= ]]>  #{updateOnEnd, jdbcType=TIMESTAMP}
    </if>
  </select>

</mapper>