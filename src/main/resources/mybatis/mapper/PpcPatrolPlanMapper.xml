<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPatrolPlanDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcPatrolPlan">
        <!--@Table ppc_patrol_plan-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
        <result column="execute_department" jdbcType="VARCHAR" property="executeDepartment"/>
        <result column="template_table_code" jdbcType="VARCHAR" property="templateTableCode"/>
        <result column="template_table_name" jdbcType="VARCHAR" property="templateTableName"/>
        <result column="task_moment_code" jdbcType="VARCHAR" property="taskMomentCode"/>
        <result column="interval_type" jdbcType="VARCHAR" property="intervalType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="interval_qty" jdbcType="INTEGER" property="intervalQty"/>
        <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime"/>
        <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime"/>
        <result column="is_calendar" jdbcType="VARCHAR" property="isCalendar"/>
        <result column="calendar_code" jdbcType="VARCHAR" property="calendarCode"/>
        <result column="task_effect_hour" jdbcType="INTEGER" property="taskEffectHour"/>
        <result column="is_need_recheck" jdbcType="VARCHAR" property="isNeedRecheck"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.imes.domain.entities.ppc.vo.PpcPatrolPlanVo">
        <!--@Table ppc_patrol_plan-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_name" jdbcType="VARCHAR" property="planName"/>
        <result column="execute_department" jdbcType="VARCHAR" property="executeDepartment"/>
        <result column="template_table_code" jdbcType="VARCHAR" property="templateTableCode"/>
        <result column="template_table_name" jdbcType="VARCHAR" property="templateTableName"/>
        <result column="task_moment_code" jdbcType="VARCHAR" property="taskMomentCode"/>
        <result column="interval_type" jdbcType="VARCHAR" property="intervalType"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="interval_qty" jdbcType="INTEGER" property="intervalQty"/>
        <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime"/>
        <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime"/>
        <result column="is_calendar" jdbcType="VARCHAR" property="isCalendar"/>
        <result column="calendar_code" jdbcType="VARCHAR" property="calendarCode"/>
        <result column="task_effect_hour" jdbcType="INTEGER" property="taskEffectHour"/>
        <result column="is_need_recheck" jdbcType="VARCHAR" property="isNeedRecheck"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>

    <sql id="Base_Column_List">
    id    ,plan_name    ,execute_department    ,template_table_code    ,template_table_name     ,task_moment_code    ,interval_type    ,status      ,interval_qty    ,plan_start_time    ,plan_end_time    ,is_calendar    ,calendar_code    ,task_effect_hour    ,is_need_recheck    ,create_on    ,create_by    ,update_on    ,update_by    ,remarks
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_plan
        where id = #{id,jdbcType=VARCHAR}
    </select>


    <select id="findAllByStartTimeLessThanEqualAndEndTimeGreaterThanEqualAndStatusEquals" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_plan
        where status = #{stauts,jdbcType=VARCHAR}
        and plan_start_time  &lt;= #{date, jdbcType=TIMESTAMP}
        and lan_end_time &gt;= #{date, jdbcType=TIMESTAMP}
    </select>

    <select id="queryByCond" resultMap="BaseResultMap1" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolPlan">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_plan
        <where>
            <if test="id != null and id != ''">
                and id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="planName != null and planName != ''">
                and plan_name like CONCAT('%', #{planName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="executeDepartment != null and executeDepartment != ''">
                and execute_department = #{executeDepartment, jdbcType=VARCHAR}
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                and template_table_code = #{templateTableCode, jdbcType=VARCHAR}
            </if>
            <if test="templateTableName != null and templateTableName != ''">
                and template_table_name like CONCAT('%', #{templateTableName,jdbcType=VARCHAR}, '%')
            </if>
            <if test="taskMomentCode != null and taskMomentCode != ''">
                and task_moment_code = #{taskMomentCode, jdbcType=VARCHAR}
            </if>
            <if test="intervalType != null and intervalType != ''">
                and interval_type = #{intervalType, jdbcType=VARCHAR}
            </if>
            <if test="intervalQty != null">
                and interval_qty = #{intervalQty, jdbcType=INTEGER}
            </if>
            <if test="planStartTime != null">
                and plan_start_time = #{planStartTime, jdbcType=TIMESTAMP}
            </if>
            <if test="planEndTime != null">
                and plan_end_time = #{planEndTime, jdbcType=TIMESTAMP}
            </if>
            <if test="isCalendar != null and isCalendar != ''">
                and is_calendar = #{isCalendar, jdbcType=VARCHAR}
            </if>
            <if test="calendarCode != null and calendarCode != ''">
                and calendar_code = #{calendarCode, jdbcType=VARCHAR}
            </if>
            <if test="status != null and status != ''">
                and status = #{status, jdbcType=VARCHAR}
            </if>
            <if test="taskEffectHour != null">
                and task_effect_hour = #{taskEffectHour, jdbcType=INTEGER}
            </if>
            <if test="isNeedRecheck != null and isNeedRecheck != ''">
                and is_need_recheck = #{isNeedRecheck, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks, jdbcType=VARCHAR}
            </if>
        </where>
        order by create_on desc
    </select>
    <select id="getPpcPatrolPlanTemplate"
            resultType="com.imes.domain.entities.query.template.ppc.PpcPatrolPlanTemplate">
        select plan.id,
               plan.plan_name,
               plan.execute_department,
               plan.template_table_code,
               plan.template_table_name,
               plan.task_moment_code,
               plan.interval_type,
               plan.status,
               plan.interval_qty,
               plan.plan_start_time,
               plan.plan_end_time,
               plan.is_calendar,
               plan.calendar_code,
               plan.task_effect_hour,
               plan.is_need_recheck,
               plan.create_on,
               plan.create_by,
               plan.update_on,
               plan.update_by,
               plan.remarks,
               depart.depart_name
        from ppc_patrol_plan plan
                 left join co_department depart on plan.execute_department = depart.depart_code
            ${ew.customSqlSegment}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ppc_patrol_plan where id = #{id, jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolPlan">
        insert into ppc_patrol_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="planName != null and planName != ''">
                plan_name,
            </if>
            <if test="executeDepartment != null and executeDepartment != ''">
                execute_department,
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                template_table_code,
            </if>
            <if test="templateTableName != null and templateTableName != ''">
                template_table_name,
            </if>
            <if test="taskMomentCode != null and taskMomentCode != ''">
                task_moment_code,
            </if>
            <if test="intervalType != null and intervalType != ''">
                interval_type,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="intervalQty != null">
                interval_qty,
            </if>
            <if test="planStartTime != null">
                plan_start_time,
            </if>
            <if test="planEndTime != null">
                plan_end_time,
            </if>
            <if test="isCalendar != null and isCalendar != ''">
                is_calendar,
            </if>
            <if test="calendarCode != null and calendarCode != ''">
                calendar_code,
            </if>
            <if test="taskEffectHour != null">
                task_effect_hour,
            </if>
            <if test="isNeedRecheck != null and isNeedRecheck != ''">
                is_need_recheck,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id, jdbcType=VARCHAR},
            </if>
            <if test="planName != null and planName != ''">
                #{planName, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartment != null and executeDepartment != ''">
                #{executeDepartment, jdbcType=VARCHAR},
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                #{templateTableCode, jdbcType=VARCHAR},
            </if>
            <if test="templateTableName != null and templateTableName != ''">
                #{templateTableName, jdbcType=VARCHAR},
            </if>
            <if test="taskMomentCode != null and taskMomentCode != ''">
                #{taskMomentCode, jdbcType=VARCHAR},
            </if>
            <if test="intervalType != null and intervalType != ''">
                #{intervalType, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="intervalQty != null">
                #{intervalQty, jdbcType=INTEGER},
            </if>
            <if test="planStartTime != null">
                #{planStartTime, jdbcType=TIMESTAMP},
            </if>
            <if test="planEndTime != null">
                #{planEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="isCalendar != null and isCalendar != ''">
                #{isCalendar, jdbcType=VARCHAR},
            </if>
            <if test="calendarCode != null and calendarCode != ''">
                #{calendarCode, jdbcType=VARCHAR},
            </if>
            <if test="taskEffectHour != null">
                #{taskEffectHour, jdbcType=INTEGER},
            </if>
            <if test="isNeedRecheck != null and isNeedRecheck != ''">
                #{isNeedRecheck, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolPlan">
        update ppc_patrol_plan
        <set>
            <if test="planName != null and planName != ''">
                plan_name = #{planName, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartment != null and executeDepartment != ''">
                execute_department = #{executeDepartment, jdbcType=VARCHAR},
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                template_table_code = #{templateTableCode, jdbcType=VARCHAR},
            </if>
            <if test="templateTableName != null and templateTableName != ''">
                template_table_name = #{templateTableName, jdbcType=VARCHAR},
            </if>
            <if test="taskMomentCode != null and taskMomentCode != ''">
                task_moment_code = #{taskMomentCode, jdbcType=VARCHAR},
            </if>
            <if test="intervalType != null and intervalType != ''">
                interval_type = #{intervalType, jdbcType=VARCHAR},
            </if>
            <if test="intervalQty != null">
                interval_qty = #{intervalQty, jdbcType=INTEGER},
            </if>
            <if test="planStartTime != null">
                plan_start_time = #{planStartTime, jdbcType=TIMESTAMP},
            </if>
            <if test="planEndTime != null">
                plan_end_time = #{planEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="isCalendar != null and isCalendar != ''">
                is_calendar = #{isCalendar, jdbcType=VARCHAR},
            </if>
                calendar_code = #{calendarCode, jdbcType=VARCHAR},
            <if test="taskEffectHour != null">
                task_effect_hour = #{taskEffectHour, jdbcType=INTEGER},
            </if>
            <if test="isNeedRecheck != null and isNeedRecheck != ''">
                is_need_recheck = #{isNeedRecheck, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

    <select id="queryOnlyByCond" resultMap="BaseResultMap1" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolPlan">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_plan
        where plan_name = #{planName,jdbcType=VARCHAR}
        order by create_on desc
    </select>
</mapper>