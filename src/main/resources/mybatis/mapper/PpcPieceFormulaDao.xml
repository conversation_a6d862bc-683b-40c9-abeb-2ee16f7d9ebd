<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceFormulaDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceFormulaQueryVo">
        select ppc_piece_formula.id,
               ppc_piece_formula.formula_code,
               ppc_piece_formula.formula_name,
               ppc_piece_formula.type,
               ppc_piece_formula.unit,
               ppc_piece_formula.formula_expression,
               ppc_piece_formula.param_str,
               ppc_piece_formula.remarks,
               ppc_piece_formula.create_on,
               ppc_piece_formula.create_by,
               ppc_piece_formula.update_on,
               ppc_piece_formula.update_by
        from ppc_piece_formula ${ew.customSqlSegment}
    </select>

</mapper>

