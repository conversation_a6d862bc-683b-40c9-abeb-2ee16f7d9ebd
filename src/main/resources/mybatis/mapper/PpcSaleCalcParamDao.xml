<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcSaleCalcParamDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcSaleCalcParam">
        <!--@Table ppc_sale_calc_param-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_source" jdbcType="VARCHAR" property="stockSource"/>
        <result column="is_safe_stock" jdbcType="VARCHAR" property="isSafeStock"/>
        <result column="is_produce_input" jdbcType="VARCHAR" property="isProduceInput"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>

    <sql id="Base_Column_List">
    id    ,stock_source    ,is_safe_stock    ,is_produce_input    ,create_on    ,create_by    ,update_on    ,update_by    ,remarks
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_sale_calc_param
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryByCond" resultMap="BaseResultMap" parameterType="com.imes.domain.entities.ppc.po.PpcSaleCalcParam">
        select
                   a.id as id,
                    a.stock_source as stockSource,
                    a.is_safe_stock as isSafeStock,
                    a.is_produce_input as isProduceInput,
                    a.create_on as createOn,
                    a.create_by as createBy,
                    a.update_on as updateOn,
                    a.update_by as updateBy,
                    a.remarks as remarks
                from ppc_sale_calc_param a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="stockSource != null and stockSource != ''">
                and a.stock_source = #{stockSource, jdbcType=VARCHAR}
            </if>
            <if test="isSafeStock != null and isSafeStock != ''">
                and a.is_safe_stock = #{isSafeStock, jdbcType=VARCHAR}
            </if>
            <if test="isProduceInput != null and isProduceInput != ''">
                and a.is_produce_input = #{isProduceInput, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and a.create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and a.update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and a.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ppc_sale_calc_param where id = #{id, jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcSaleCalcParam">
        insert into ppc_sale_calc_param
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="stockSource != null and stockSource != ''">
                stock_source,
            </if>
            <if test="isSafeStock != null and isSafeStock != ''">
                is_safe_stock,
            </if>
            <if test="isProduceInput != null and isProduceInput != ''">
                is_produce_input,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                #{id, jdbcType=VARCHAR},
            </if>
            <if test="stockSource != null and stockSource != ''">
                #{stockSource, jdbcType=VARCHAR},
            </if>
            <if test="isSafeStock != null and isSafeStock != ''">
                #{isSafeStock, jdbcType=VARCHAR},
            </if>
            <if test="isProduceInput != null and isProduceInput != ''">
                #{isProduceInput, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    
    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcSaleCalcParam">
        insert into ppc_sale_calc_param
         (       id,
                stock_source,
                is_safe_stock,
                is_produce_input,
                create_on,
                create_by,
                update_on,
                update_by,
                remarks
             ) values 
          (
          #{id, jdbcType=VARCHAR},
                          #{stockSource, jdbcType=VARCHAR},
                           #{isSafeStock, jdbcType=VARCHAR},
                           #{isProduceInput, jdbcType=VARCHAR},
                           #{createOn, jdbcType=TIMESTAMP},
                           #{createBy, jdbcType=VARCHAR},
                           #{updateOn, jdbcType=TIMESTAMP},
                           #{updateBy, jdbcType=VARCHAR},
                           #{remarks, jdbcType=VARCHAR}
           )
     </insert>
     
    <insert id="insertBatchList" parameterType="java.util.List">
        insert into ppc_sale_calc_param
         (       id,
                stock_source,
                is_safe_stock,
                is_produce_input,
                create_on,
                create_by,
                update_on,
                update_by,
                remarks
             ) values 
       <foreach collection="list" item="item" separator=",">
          (
          #{item.id, jdbcType=VARCHAR},
                          #{item.stockSource, jdbcType=VARCHAR},
                           #{item.isSafeStock, jdbcType=VARCHAR},
                           #{item.isProduceInput, jdbcType=VARCHAR},
                           #{item.createOn, jdbcType=TIMESTAMP},
                           #{item.createBy, jdbcType=VARCHAR},
                           #{item.updateOn, jdbcType=TIMESTAMP},
                           #{item.updateBy, jdbcType=VARCHAR},
                           #{item.remarks, jdbcType=VARCHAR}
           )
       </foreach>
     </insert>
    
    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcSaleCalcParam">
        update ppc_sale_calc_param
        <set>
            <if test="stockSource != null">
                stock_source = #{stockSource, jdbcType=VARCHAR},
            </if>
            <if test="isSafeStock != null">
                is_safe_stock = #{isSafeStock, jdbcType=VARCHAR},
            </if>
            <if test="isProduceInput != null">
                is_produce_input = #{isProduceInput, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

 <delete id="deleteByIds">
        delete
        from ppc_sale_calc_param
        where
        id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>

    </delete>
</mapper>