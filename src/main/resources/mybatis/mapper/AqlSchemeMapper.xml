<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.AqlSchemeDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.AqlScheme">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="product_num" jdbcType="VARCHAR" property="productNum" />
    <result column="inspect_level_code" jdbcType="VARCHAR" property="inspectLevelCode" />
    <result column="inspect_level_name" jdbcType="VARCHAR" property="inspectLevelName" />
    <result column="sample_code" jdbcType="VARCHAR" property="sampleCode" />
    <result column="min_num" jdbcType="VARCHAR" property="minNum" />
    <result column="max_num" jdbcType="VARCHAR" property="maxNum" />
    <result column="sample_num" jdbcType="VARCHAR" property="sampleNum" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, product_num,inspect_level_code,inspect_level_name,sample_code,
    min_num,max_num,sample_num,create_on,create_by,update_by,update_on,remarks
  </sql>
  <select id="queryList" parameterType="java.lang.String"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    FROM
    `qms_aql_scheme`
    <where>
      1=1
      <if test="inspectLevel != null and inspectLevel != ''">
        and inspect_level_code = #{inspectLevel}
      </if>
    </where>
    order by id+0 desc
  </select>
  <select id="getNum"  resultType="java.lang.Integer">
    select
           sample_num
    from
         qms_aql_scheme
    where
          inspect_level_code=#{inspectLevel}
      and
          #{batchNum}<![CDATA[ >= ]]> min_num
      and
          #{batchNum}<![CDATA[ <= ]]> max_num
  </select>
  <select id="getNumNew"  resultType="java.lang.Integer">
    select
           sample_num
    from
         qms_aql_scheme
    where
          inspect_level_code=#{inspectLevel}
      and
          #{batchNum}<![CDATA[ >= ]]> min_num
      and
          max_num is null
  </select>
</mapper>