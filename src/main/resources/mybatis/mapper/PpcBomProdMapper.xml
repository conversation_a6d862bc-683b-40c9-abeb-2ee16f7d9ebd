<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcBomProdDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcBomProd">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="produce_plan_root_id" jdbcType="VARCHAR" property="producePlanRootId"/>
        <result column="produce_plan_root_no" jdbcType="VARCHAR" property="producePlanRootNo"/>
        <result column="bom_code" jdbcType="VARCHAR" property="bomCode"/>
        <result column="bom_name" jdbcType="VARCHAR" property="bomName"/>
        <result column="bom_ver" jdbcType="VARCHAR" property="bomVer"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
    </resultMap>
    <resultMap id="BaseResultMapVo" type="com.imes.domain.entities.ppc.vo.BomProdVo">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="produce_plan_root_id" jdbcType="VARCHAR" property="producePlanRootId"/>
        <result column="produce_plan_root_no" jdbcType="VARCHAR" property="producePlanRootNo"/>
        <result column="bom_code" jdbcType="VARCHAR" property="bomCode"/>
        <result column="bom_name" jdbcType="VARCHAR" property="bomName"/>
        <result column="bom_ver" jdbcType="VARCHAR" property="bomVer"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="category" jdbcType="VARCHAR" property="category"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="delete_status" jdbcType="INTEGER" property="deleteStatus"/>
        <result column="child_material_code" jdbcType="VARCHAR" property="childMaterialCode" />
        <result column="category" jdbcType="VARCHAR" property="category" />
        <result column="parent_output_qty" jdbcType="DECIMAL" property="parentOutputQty" />
        <result column="item_input_qty" jdbcType="DECIMAL" property="itemInputQty" />
        <result column="unit_code" jdbcType="VARCHAR" property="unitCode" />
        <result column="child_category" jdbcType="VARCHAR" property="childCategory"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        id, produce_plan_root_id, produce_plan_root_no, bom_code, bom_name, bom_ver, material_code,
        category, create_on, create_by, remarks, delete_status
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        select
        <include refid="Base_Column_List"/>
        from ppc_bom_prod
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryByParam" resultMap="BaseResultMap" parameterType="java.util.List">
        select
        prod.id, prod.produce_plan_root_id, prod.produce_plan_root_no, prod.bom_code, ppc_bom.bom_name, prod.bom_ver, prod.material_code,
        prod.category, prod.create_on, prod.create_by, prod.remarks, prod.delete_status
        from ppc_bom_prod prod
        left join ppc_bom on ppc_bom.bom_code = prod.bom_code and ppc_bom.bom_ver = prod.bom_ver
        where 1=1
        <if test="planId != null and planId != ''">
            and prod.produce_plan_root_id = #{planId,jdbcType=VARCHAR}
        </if>
        <if test="planIds != null and planIds != ''">
            and prod.produce_plan_root_id in
            <foreach collection="planIds" open="(" item="planId" separator="," close=")">
                #{planId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="bomCode != null and bomCode != ''" >
            and prod.bom_code = #{bomCode,jdbcType=VARCHAR}
        </if>
        <if test="bomVer != null and bomVer != ''" >
            and prod.bom_ver = #{bomVer,jdbcType=VARCHAR}
        </if>
        <if test="materialCode != null and materialCode != ''">
            and prod.material_code = #{materialCode,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="queryByBomIds" resultMap="BaseResultMapVo" parameterType="java.util.List">
        SELECT
            t1.*,
            t2.material_code AS child_material_code,
            t2.category AS child_category,
            t2.parent_output_qty,
            t2.item_input_qty,
            t2.unit_code,
            unit.name unit_name
        FROM
            ppc_bom_prod t1	LEFT JOIN ppc_bom_item_prod t2  on t1.id = t2.bom_prod_id
        left join sys_unit unit on unit.code = t2.unit_code
        where 1=1
        <if test="materialCodeIn != null and materialCodeIn != ''">
            and t1.material_code in
            <foreach collection="materialCodeIn" open="(" item="materialCode" separator="," close=")">
                #{materialCode,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="producePlanRootId != null and producePlanRootId != ''">
            and t1.produce_plan_root_id = #{producePlanRootId}
        </if>
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        delete from ppc_bom_prod
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteByRootPlanId" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        delete from ppc_bom_prod
        where produce_plan_root_id = #{rootPlanId,jdbcType=VARCHAR}
    </delete>
    <delete id="batchDeleteById">
        delete from ppc_bom_prod
        where id in
        <foreach collection="ids" open="(" item="item" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcBomProd">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        insert into ppc_bom_prod (id, produce_plan_root_id, produce_plan_root_no,
        bom_code, bom_name, bom_ver,
        material_code, category, create_on,
        create_by, remarks, delete_status
        )
        values (#{id,jdbcType=VARCHAR}, #{producePlanRootId,jdbcType=VARCHAR}, #{producePlanRootNo,jdbcType=VARCHAR},
        #{bomCode,jdbcType=VARCHAR}, #{bomName,jdbcType=VARCHAR}, #{bomVer,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{deleteStatus,jdbcType=INTEGER}
        )
    </insert>
    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcBomProd">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        insert into ppc_bom_prod
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="producePlanRootId != null">
                produce_plan_root_id,
            </if>
            <if test="producePlanRootNo != null">
                produce_plan_root_no,
            </if>
            <if test="bomCode != null">
                bom_code,
            </if>
            <if test="bomName != null">
                bom_name,
            </if>
            <if test="bomVer != null">
                bom_ver,
            </if>
            <if test="materialCode != null">
                material_code,
            </if>
            <if test="category != null">
                category,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
            <if test="deleteStatus != null">
                delete_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="producePlanRootId != null">
                #{producePlanRootId,jdbcType=VARCHAR},
            </if>
            <if test="producePlanRootNo != null">
                #{producePlanRootNo,jdbcType=VARCHAR},
            </if>
            <if test="bomCode != null">
                #{bomCode,jdbcType=VARCHAR},
            </if>
            <if test="bomName != null">
                #{bomName,jdbcType=VARCHAR},
            </if>
            <if test="bomVer != null">
                #{bomVer,jdbcType=VARCHAR},
            </if>
            <if test="materialCode != null">
                #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                #{category,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteStatus != null">
                #{deleteStatus,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcBomProd">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        update ppc_bom_prod
        <set>
            <if test="producePlanRootId != null">
                produce_plan_root_id = #{producePlanRootId,jdbcType=VARCHAR},
            </if>
            <if test="producePlanRootNo != null">
                produce_plan_root_no = #{producePlanRootNo,jdbcType=VARCHAR},
            </if>
            <if test="bomCode != null">
                bom_code = #{bomCode,jdbcType=VARCHAR},
            </if>
            <if test="bomName != null">
                bom_name = #{bomName,jdbcType=VARCHAR},
            </if>
            <if test="bomVer != null">
                bom_ver = #{bomVer,jdbcType=VARCHAR},
            </if>
            <if test="materialCode != null">
                material_code = #{materialCode,jdbcType=VARCHAR},
            </if>
            <if test="category != null">
                category = #{category,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.PpcBomProd">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Wed May 13 13:36:22 CST 2020.
        -->
        update ppc_bom_prod
        set produce_plan_root_id = #{producePlanRootId,jdbcType=VARCHAR},
        produce_plan_root_no = #{producePlanRootNo,jdbcType=VARCHAR},
        bom_code = #{bomCode,jdbcType=VARCHAR},
        bom_name = #{bomName,jdbcType=VARCHAR},
        bom_ver = #{bomVer,jdbcType=VARCHAR},
        material_code = #{materialCode,jdbcType=VARCHAR},
        category = #{category,jdbcType=VARCHAR},
        create_on = #{createOn,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        remarks = #{remarks,jdbcType=VARCHAR},
        delete_status = #{deleteStatus,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>