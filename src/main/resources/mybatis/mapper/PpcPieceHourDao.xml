<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceHourDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceHourSearchVo">
        select ppc_piece_hour.id,
               ppc_piece_hour.working_date,
               ppc_piece_hour.user_code,
               ppc_piece_hour.user_name,
               ppc_piece_hour.team_code,
               ppc_piece_hour.team_name,
               ppc_piece_hour.sub_team_name,
               ppc_piece_hour.big_process_code,
               ppc_piece_hour.big_process_name,
               ppc_piece_hour.start_time,
               ppc_piece_hour.end_time,
               ppc_piece_hour.calc_time,
               ppc_piece_hour.remarks,
               ppc_piece_hour.create_on,
               ppc_piece_hour.create_by,
               ppc_piece_hour.update_on,
               ppc_piece_hour.update_by
        from ppc_piece_hour ${ew.customSqlSegment}
    </select>

    <insert id="insertAllBatch" parameterType="java.util.List">
        insert into ppc_piece_hour
        ( id,
        working_date,
        user_code,
        user_name,
        team_code,
        team_name,
        sub_team_name,
        big_process_code,
        big_process_name,
        start_time,
        end_time,
        calc_time,
        remarks,
        create_on,
        create_by,
        update_on,
        update_by
        )
        values
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            (
            #{item.id},
            #{item.workingDate},
            #{item.userCode},
            #{item.userName},
            #{item.teamCode},
            #{item.teamName},
            #{item.subTeamName},
            #{item.bigProcessCode},
            #{item.bigProcessName},
            #{item.startTime},
            #{item.endTime},
            #{item.calcTime},
            #{item.remarks},
            #{item.createOn},
            #{item.createBy},
            #{item.updateOn},
            #{item.updateBy}
            )
        </foreach>
    </insert>

    <select id="checkUser" resultType="com.imes.domain.entities.system.vo.UserVo">
        select user_code as 'userCode' ,user_name  as 'userName' from pe_user where user_code in
        <foreach collection="list" open="(" item="code" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="checkTeamCode" resultType="com.imes.domain.entities.system.CoDepartment">
        select depart_code as 'departCode', depart_name as 'departName' from co_department where depart_code in
        <foreach collection="list" open="(" item="code" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="checkProcessCode" resultType="com.imes.domain.entities.ppc.po.PpcProcess">
        select process_code as 'processCode', process_name as 'processName' from ppc_process where process_code in
        <foreach collection="list" open="(" item="code" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
        and (parent_code is null  or parent_code = '')
    </select>


    <select id="checkTeamCodeProcessCodeSubTeam" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceHourSearchVo">
        SELECT
        CONCAT(team_code,'--',sub_team_name) as 'tbs'
        FROM
            ppc_piece_sub_team
        where CONCAT(team_code,'--',sub_team_name) in
        <foreach collection="list" open="(" item="code" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </select>


    <select id="checkWorkingDateUserCodeProcessCodeSubTeam" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceHourSearchVo">
        select ppc_piece_hour.id,
                CONCAT(working_date,'--',user_code,'--',big_process_code,'--',sub_team_name) as 'wubs',
               ppc_piece_hour.working_date,
               ppc_piece_hour.user_code,
               ppc_piece_hour.user_name,
               ppc_piece_hour.team_code,
               ppc_piece_hour.team_name,
               ppc_piece_hour.sub_team_name,
               ppc_piece_hour.big_process_code,
               ppc_piece_hour.big_process_name,
               ppc_piece_hour.start_time,
               ppc_piece_hour.end_time,
               ppc_piece_hour.calc_time,
               ppc_piece_hour.remarks,
               ppc_piece_hour.create_on,
               ppc_piece_hour.create_by,
               ppc_piece_hour.update_on,
               ppc_piece_hour.update_by
        from ppc_piece_hour
        where CONCAT(working_date,'--',user_code,'--',big_process_code,'--',sub_team_name) in
        <foreach collection="list" open="(" item="code" separator="," close=")">
            #{code,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>

