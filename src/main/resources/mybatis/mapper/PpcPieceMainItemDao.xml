<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceMainItemDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceMainItemQueryVo">
        select ppc_piece_main_item.id,
               ppc_piece_main_item.main_id,
               ppc_piece_main_item.item_name,
               ppc_piece_main_item.single_price,
               ppc_piece_main_item.unit,
               ppc_piece_main_item.report_method,
               ppc_piece_main_item.is_statistic,
               ppc_piece_main_item.create_on,
               ppc_piece_main_item.create_by,
               ppc_piece_main_item.update_on,
               ppc_piece_main_item.update_by,
               ppc_piece_main_item.remarks
        from ppc_piece_main_item ${ew.customSqlSegment}
    </select>
    <select id="queryItem" resultType="com.imes.domain.entities.ppc.po.PpcPieceMainItem">
        select t.*
        from ppc_piece_main_item t left join  ppc_piece_main t2 on t.main_id = t2.id
        where t2.big_process_code = #{bigProcessCode}
        and t2.effect_date_start <![CDATA[ <= ]]> #{finishedDate}
        and t2.effect_date_end  <![CDATA[ >= ]]> #{finishedDate}

        <if test="smallProcessCode != null and smallProcessCode != ''">
            and t2.small_process_code = #{smallProcessCode, jdbcType=VARCHAR}
        </if>
    </select>
    <select id="getByItemName" resultType="com.imes.domain.entities.ppc.po.PpcPieceMain">
        select main.* from ppc_piece_main_item item left join ppc_piece_main main on item.main_id=main.id
            where item.item_name =#{itemName}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ppc_piece_main_item (
        id,
        main_id,
        item_name,
        single_price,
        unit,
        report_method,
        is_statistic,
        create_on,
        create_by,
        update_on,
        update_by,
        remarks
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id,jdbcType=VARCHAR},
            #{item.mainId,jdbcType=VARCHAR},
            #{item.itemName,jdbcType=VARCHAR},
            #{item.singlePrice,jdbcType=DECIMAL},
            #{item.unit,jdbcType=VARCHAR},
            #{item.reportMethod,jdbcType=VARCHAR},
            #{item.isStatistic,jdbcType=VARCHAR},
            #{item.createOn,jdbcType=TIMESTAMP},
            #{item.createBy,jdbcType=VARCHAR},
            #{item.updateOn,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR},
            #{item.remarks,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>

