<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.InBillInspectionRelationDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.InBillInspectionRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <select id="countByCanceledIds" parameterType="java.lang.String" resultType="java.lang.String">
    SELECT
      parent_id
    FROM
      `qms_in_bill_relation`
    WHERE
      inspection_status=#{inspectionStatus}
      and order_id IN ${orderIds}
    GROUP BY
      parent_id
  </select>
  <select id="countEndByCanceledIds" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT
      count( id )
    FROM
      `qms_in_bill_relation`
    WHERE
      inspection_status=#{inspectionStatus}
      and order_id IN ${orderIds}
  </select>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.qms.po.InBillInspectionRelation">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 22 15:16:27 CST 2020.
    -->
    insert into qms_in_bill_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="inspectionStatus != null">
        inspection_status,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="parentId != null">
        #{parentId,jdbcType=VARCHAR},
      </if>
      <if test="inspectionStatus != null">
        #{inspectionStatus,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByParentId" parameterType="com.imes.domain.entities.qms.po.InMaterialInspectionRelation">
    update qms_in_bill_relation set inspection_status=#{inspectionStatus} where parent_id=#{parentId}
  </update>
  <delete id="deleteByParentId" parameterType="java.lang.String">
    delete from qms_in_bill_relation where parent_id=#{parentId}
  </delete>
</mapper>