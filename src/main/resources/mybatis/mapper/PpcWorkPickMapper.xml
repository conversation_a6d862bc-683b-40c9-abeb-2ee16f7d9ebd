<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.WorkPickDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcWorkPick">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 04 15:30:40 CST 2019.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="work_order_id" jdbcType="VARCHAR" property="workOrderId" />
    <result column="pk_no" jdbcType="VARCHAR" property="pkNo" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="quality" jdbcType="VARCHAR" property="quality" />
    <result column="pick_qty" jdbcType="DECIMAL" property="pickQty" />
    <result column="process_code" jdbcType="VARCHAR" property="processCode" />
    <result column="produce_process_id" jdbcType="VARCHAR" property="produceProcessId" />
    <result column="produce_plan_id" jdbcType="VARCHAR" property="producePlanId" />
    <result column="produce_input_id" jdbcType="VARCHAR" property="produceInputId" />
    <result column="in_no" jdbcType="VARCHAR" property="inNo" />
    <result column="primary_unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="bill_type" jdbcType="VARCHAR" property="billType" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 04 15:30:40 CST 2019.
    -->
    id, work_order_id, pk_no, material_code, material_name, specification, quality, pick_qty,
    process_code, produce_process_id, produce_plan_id, produce_input_id, in_no, primary_unit,
    status, bill_type, create_on, create_by, update_on, update_by, remarks
  </sql>
  
  <select id="selectPickNumber" parameterType="java.lang.String" resultType="java.math.BigDecimal">
  		SELECT
			ifnull( sum( pick.pick_qty ), 0 ) 
		FROM
			ppc_produce_plan plan,
			ppc_work_order WORK,
			ppc_work_pick pick 
		WHERE
			plan.id = WORK.plan_id 
			AND WORK.id = pick.work_order_id 
			AND pick.bill_type IN ( '1', '2' )	
			AND plan.id = #{id,jdbcType=VARCHAR}
			AND pick.material_code = #{materialCode,jdbcType=VARCHAR}
			AND WORK.wo_no = #{woNo,jdbcType=VARCHAR}
  </select>
  
    <select id="queryByParam" parameterType="java.lang.String" resultMap="BaseResultMap">
	    <!--
	      WARNING - @mbg.generated
	      This element is automatically generated by MyBatis Generator, do not modify.
	      This element was generated on Mon Oct 28 14:08:15 CST 2019.
	    -->
	    select
	    <include refid="Base_Column_List" />
	    from ppc_work_pick
	    where 1=1
	    <if test="workOrderId != null and workOrderId != ''">
	      and work_order_id = #{workOrderId,jdbcType=VARCHAR}
	    </if>
        <if test="pkNo != null and pkNo != ''">
          and pk_no like concat('%',#{pkNo,jdbcType=VARCHAR},'%')
        </if>
        <if test="billType != null and billType != ''">
          and bill_type = #{billType,jdbcType=VARCHAR}
        </if>
        <if test="materialCodes != null and materialCodes != ''">
          and material_code not in
          <foreach collection="materialCodes" open="(" item="materialCode" separator="," close=")">
            #{materialCode,jdbcType=VARCHAR}
          </foreach>
        </if>
        <if test="status != null and status != ''">
          and status = #{status,jdbcType=VARCHAR}
        </if>
 	 </select>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 04 15:30:40 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from ppc_work_pick
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Nov 04 15:30:40 CST 2019.
    -->
    delete from ppc_work_pick
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcWorkPick">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 19:29:52 CST 2019.
    -->
    insert into ppc_work_pick (id, work_order_id, pk_no,
    material_code, material_name, specification,
    quality, pick_qty, process_code,
    produce_process_id, produce_plan_id, produce_input_id,
    in_no, primary_unit, status,
    bill_type, create_on, create_by,
    update_on, update_by, remarks
    )
    values (#{id,jdbcType=VARCHAR}, #{workOrderId,jdbcType=VARCHAR}, #{pkNo,jdbcType=VARCHAR},
    #{materialCode,jdbcType=VARCHAR}, #{materialName,jdbcType=VARCHAR}, #{specification,jdbcType=VARCHAR},
    #{quality,jdbcType=VARCHAR}, #{pickQty,jdbcType=DECIMAL}, #{processCode,jdbcType=VARCHAR},
    #{produceProcessId,jdbcType=VARCHAR}, #{producePlanId,jdbcType=VARCHAR}, #{produceInputId,jdbcType=VARCHAR},
    #{inNo,jdbcType=VARCHAR}, #{primaryUnit,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR},
    #{billType,jdbcType=VARCHAR}, #{createOn,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR},
    #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}
    )
  </insert>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcWorkPick">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 19:29:52 CST 2019.
    -->
    insert into ppc_work_pick
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="workOrderId != null">
        work_order_id,
      </if>
      <if test="pkNo != null">
        pk_no,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="quality != null">
        quality,
      </if>
      <if test="pickQty != null">
        pick_qty,
      </if>
      <if test="processCode != null">
        process_code,
      </if>
      <if test="produceProcessId != null">
        produce_process_id,
      </if>
      <if test="producePlanId != null">
        produce_plan_id,
      </if>
      <if test="produceInputId != null">
        produce_input_id,
      </if>
      <if test="inNo != null">
        in_no,
      </if>
      <if test="primaryUnit != null">
        primary_unit,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="workOrderId != null">
        #{workOrderId,jdbcType=VARCHAR},
      </if>
      <if test="pkNo != null">
        #{pkNo,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="quality != null">
        #{quality,jdbcType=VARCHAR},
      </if>
      <if test="pickQty != null">
        #{pickQty,jdbcType=DECIMAL},
      </if>
      <if test="processCode != null">
        #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="produceProcessId != null">
        #{produceProcessId,jdbcType=VARCHAR},
      </if>
      <if test="producePlanId != null">
        #{producePlanId,jdbcType=VARCHAR},
      </if>
      <if test="produceInputId != null">
        #{produceInputId,jdbcType=VARCHAR},
      </if>
      <if test="inNo != null">
        #{inNo,jdbcType=VARCHAR},
      </if>
      <if test="primaryUnit != null">
        #{primaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcWorkPick">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 19:29:52 CST 2019.
    -->
    update ppc_work_pick
    <set>
      <if test="workOrderId != null">
        work_order_id = #{workOrderId,jdbcType=VARCHAR},
      </if>
      <if test="pkNo != null">
        pk_no = #{pkNo,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        material_name = #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        specification = #{specification,jdbcType=VARCHAR},
      </if>
      <if test="quality != null">
        quality = #{quality,jdbcType=VARCHAR},
      </if>
      <if test="pickQty != null">
        pick_qty = #{pickQty,jdbcType=DECIMAL},
      </if>
      <if test="processCode != null">
        process_code = #{processCode,jdbcType=VARCHAR},
      </if>
      <if test="produceProcessId != null">
        produce_process_id = #{produceProcessId,jdbcType=VARCHAR},
      </if>
      <if test="producePlanId != null">
        produce_plan_id = #{producePlanId,jdbcType=VARCHAR},
      </if>
      <if test="produceInputId != null">
        produce_input_id = #{produceInputId,jdbcType=VARCHAR},
      </if>
      <if test="inNo != null">
        in_no = #{inNo,jdbcType=VARCHAR},
      </if>
      <if test="primaryUnit != null">
        primary_unit = #{primaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.PpcWorkPick">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue Nov 26 19:29:52 CST 2019.
    -->
    update ppc_work_pick
    set work_order_id = #{workOrderId,jdbcType=VARCHAR},
    pk_no = #{pkNo,jdbcType=VARCHAR},
    material_code = #{materialCode,jdbcType=VARCHAR},
    material_name = #{materialName,jdbcType=VARCHAR},
    specification = #{specification,jdbcType=VARCHAR},
    quality = #{quality,jdbcType=VARCHAR},
    pick_qty = #{pickQty,jdbcType=DECIMAL},
    process_code = #{processCode,jdbcType=VARCHAR},
    produce_process_id = #{produceProcessId,jdbcType=VARCHAR},
    produce_plan_id = #{producePlanId,jdbcType=VARCHAR},
    produce_input_id = #{produceInputId,jdbcType=VARCHAR},
    in_no = #{inNo,jdbcType=VARCHAR},
    primary_unit = #{primaryUnit,jdbcType=VARCHAR},
    status = #{status,jdbcType=VARCHAR},
    bill_type = #{billType,jdbcType=VARCHAR},
    create_on = #{createOn,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_on = #{updateOn,jdbcType=TIMESTAMP},
    update_by = #{updateBy,jdbcType=VARCHAR},
    remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>


  <select id="getExistQty" parameterType="java.lang.String" resultType="java.math.BigDecimal">
  		SELECT
			ifnull( sum( detail.pick_qty ), 0 )
		FROM
		    ppc_work_pick_main main
		    left join ppc_work_pick_detail detail on detail.main_id = main.id
		WHERE
			 main.bill_type  = '1'
			AND status != '10'
			AND detail.material_code = #{materialCode,jdbcType=VARCHAR}
			AND main.work_schedul_no = #{productionNo,jdbcType=VARCHAR}
  </select>
</mapper>