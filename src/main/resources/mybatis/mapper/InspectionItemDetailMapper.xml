<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.InspectionItemDetailDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.InspectionItemDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="item_code" jdbcType="VARCHAR" property="itemCode" />
    <result column="child_item" jdbcType="VARCHAR" property="childItem" />
    <result column="inspection_tool" jdbcType="VARCHAR" property="inspectionTool" />
    <result column="determine_type" jdbcType="VARCHAR" property="determineType" />
    <result column="up_limit" jdbcType="DECIMAL" property="upLimit" />
    <result column="down_limit" jdbcType="DECIMAL" property="downLimit" />
    <result column="un_standard" jdbcType="VARCHAR" property="unStandard" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, item_code, child_item, inspection_tool, determine_type,up_limit,
    down_limit,un_standard,create_on,create_by,update_on,update_by,remarks
  </sql>
  <select id="queryItemDetailList"  parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_inspection_items_detail
    where 1=1
        and item_code=#{itemCode}
  </select>
  <select id="queryAll" resultMap="BaseResultMap">
    select
    item_code, child_item, inspection_tool, determine_type, un_standard
    from qms_inspection_items_detail
  </select>
  <select id="selectById" resultType="java.lang.String">
    select
    to_ppc_material
    from qms_inspection_items_detail where id = #{id,jdbcType=VARCHAR}
  </select>
  <insert id="batchInsert">
    insert into qms_inspection_items_detail (id, item_code, child_item, inspection_tool, determine_type,up_limit,
    down_limit,un_standard,create_on,create_by,update_on,update_by,remarks,to_ppc_material
    )
    values
    <foreach collection="list" item="item" index= "index" separator =",">
      (#{item.id,jdbcType=VARCHAR}, #{item.itemCode,jdbcType=VARCHAR}, #{item.childItem,jdbcType=VARCHAR},
      #{item.inspectionTool,jdbcType=VARCHAR}, #{item.determineType,jdbcType=VARCHAR}, #{item.upLimit,jdbcType=DECIMAL},
      #{item.downLimit,jdbcType=DECIMAL}, #{item.unStandard,jdbcType=VARCHAR},
      #{item.createOn,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=VARCHAR}, #{item.updateOn,jdbcType=TIMESTAMP},
      #{item.updateBy,jdbcType=VARCHAR}, #{item.remarks,jdbcType=VARCHAR}, #{item.toPpcMaterial,jdbcType=VARCHAR})
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.qms.po.InspectionItemDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 22 15:16:27 CST 2020.
    -->
    insert into qms_inspection_items_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="itemCode != null">
        item_code,
      </if>
      <if test="childItem != null">
        child_item,
      </if>
      <if test="inspectionTool != null">
        inspection_tool,
      </if>
      <if test="determineType != null">
        determine_type,
      </if>
      <if test="(upLimit != null and upLimit != '') or upLimit==0">
        up_limit,
      </if>
      <if test="(downLimit != null and downLimit != '') or downLimit==0">
        down_limit,
      </if>
      <if test="unStandard != null">
        un_standard,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="toPpcMaterial != null">
        to_ppc_material,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="itemCode != null">
        #{itemCode,jdbcType=VARCHAR},
      </if>
      <if test="childItem != null">
        #{childItem,jdbcType=VARCHAR},
      </if>
      <if test="inspectionTool != null">
        #{inspectionTool,jdbcType=VARCHAR},
      </if>
      <if test="determineType != null">
        #{determineType,jdbcType=VARCHAR},
      </if>
      <if test="(upLimit != null and upLimit != '') or upLimit==0">
        #{upLimit,jdbcType=DECIMAL},
      </if>
      <if test="(downLimit != null and downLimit != '') or downLimit==0">
        #{downLimit,jdbcType=DECIMAL},
      </if>
      <if test="unStandard != null">
        #{unStandard,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="toPpcMaterial != null">
        #{toPpcMaterial,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateSelectives" parameterType="com.imes.domain.entities.qms.po.InspectionItemDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_inspection_items_detail
    <set>
      <if test="childItem != null">
        child_item = #{childItem,jdbcType=VARCHAR},
      </if>
      <if test="inspectionTool != null">
        inspection_tool = #{inspectionTool,jdbcType=VARCHAR},
      </if>
      <if test="determineType != null">
        determine_type = #{determineType,jdbcType=VARCHAR},
      </if>
      <if test="(upLimit != null and upLimit != '') or upLimit==0">
        up_limit = #{upLimit,jdbcType=DECIMAL},
      </if>
      <if test="(downLimit != null and downLimit != '') or downLimit==0">
        down_limit = #{downLimit,jdbcType=DECIMAL},
      </if>
      <if test="unStandard != null">
        un_standard = #{unStandard,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateSelective" parameterType="com.imes.domain.entities.qms.po.InspectionItemDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_inspection_items_detail
        set child_item = #{childItem,jdbcType=VARCHAR},
        inspection_tool = #{inspectionTool,jdbcType=VARCHAR},
        determine_type = #{determineType,jdbcType=VARCHAR},
        up_limit = #{upLimit},
        down_limit = #{downLimit},
        un_standard = #{unStandard,jdbcType=VARCHAR},
        update_on = #{updateOn,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR},
        remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <delete id="deteleByProtectDataId" parameterType="java.lang.String">
    delete from qms_inspection_items_detail
    where
        item_code in (
        select item_code from qms_inspection_items
        where material_code=#{materialCode} and inspection_type=#{inspectionType}
        )
  </delete>
  <delete id="deleteItemDetail" parameterType="java.lang.String">
    delete from qms_inspection_items_detail where id =#{id}
  </delete>
  <delete id="deleteToPpc" parameterType="java.lang.String">
    delete from ppc_material_inspection_detail where child_id =#{toPpcMaterial}
  </delete>
  <delete id="deleteByItemCode" parameterType="java.lang.String">
    delete from ppc_material_inspection_detail where item_code =#{itemCode}
  </delete>
  <delete id="deleteItemDetailByItemCode" parameterType="java.lang.String">
    delete from qms_inspection_items_detail where item_code =#{itemCode}
  </delete>
  <delete id="deleteItemDetailByItemCodeIn" >
    delete from qms_inspection_items_detail
    where item_code in
    <foreach collection="list" item="itemCode" index="index" open="(" separator="," close=")">
      #{itemCode, jdbcType=VARCHAR}
    </foreach>
  </delete>

  <select id="queryInBillInspectionItemsDetailList" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_inspection_items_detail
    WHERE item_code = #{itemCode,jdbcType=VARCHAR}
  </select>

  <delete id="deleteInBill" parameterType="java.lang.String">
    delete from ppc_in_bill_inspection_detail where qms_inspection_items_detail_id =#{vo}
  </delete>

  <delete id="deleteInBillDetail" >
    delete from ppc_in_bill_inspection_detail where item_code = #{itemCode,jdbcType=VARCHAR}
  </delete>
</mapper>