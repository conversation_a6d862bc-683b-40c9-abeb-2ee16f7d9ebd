<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcUserSalaryDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcUserSalary">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:02:33 UTC 2024.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="salary_date" jdbcType="VARCHAR" property="salaryDate" />
    <result column="work_days" jdbcType="DOUBLE" property="workDays" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="salary" jdbcType="DECIMAL" property="salary" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>

  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu Jan 04 10:02:33 UTC 2024.
    -->
    id, salary_date, work_days, user_name, salary, create_on, create_by, 
    update_on, update_by
  </sql>

  <insert id="batchInsert">
    <foreach collection="list" item="item" separator=";" index="index">
      insert into ppc_user_salary (id, salary_date, work_days,
      user_name, salary, create_on,
      create_by, update_on, update_by
      )
      values (#{item.id,jdbcType=VARCHAR}, #{item.salaryDate,jdbcType=VARCHAR}, #{item.workDays,jdbcType=DOUBLE},
      #{item.userName,jdbcType=VARCHAR}, #{item.salary,jdbcType=DECIMAL}, #{item.createOn,jdbcType=TIMESTAMP},
      #{item.createBy,jdbcType=VARCHAR}, #{item.updateOn,jdbcType=TIMESTAMP}, #{item.updateBy,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>


</mapper>