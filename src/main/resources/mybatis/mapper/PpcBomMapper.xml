<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.ProductionBomDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.Bom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="bom_code" jdbcType="VARCHAR" property="bomCode" />
    <result column="bom_name" jdbcType="VARCHAR" property="bomName" />
    <result column="bom_ver" jdbcType="INTEGER" property="bomVer" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="version_status" jdbcType="VARCHAR" property="versionStatus" />
    <result column="is_have_child" jdbcType="VARCHAR" property="isHaveChild" />
    <result column="category" jdbcType="VARCHAR" property="category" />
    <result column="yntree" jdbcType="VARCHAR" property="yntree" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    id, bom_code, bom_name, bom_ver, material_code, version_status, is_have_child, category,
    yntree, create_on, create_by, update_on, update_by, remarks, delete_status
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    select 
    <include refid="Base_Column_List" />
    from ppc_bom
    where id = #{id,jdbcType=VARCHAR}
  </select>



  <select id="selectByCodeAndVer" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ppc_bom
    where 1=1
    <if test="bomCode != null and bomCode != ''">
      and bom_code = #{bomCode,jdbcType=VARCHAR}
    </if>
    <if test="bomCode != null and bomCode != ''">
      and bom_code = #{bomCode,jdbcType=VARCHAR}
    </if>
    <if test="bomVer != null and bomVer != ''">
      and bom_ver = #{bomVer,jdbcType=VARCHAR}
    </if>
  </select>
  <select id="queryByParam" parameterType="com.imes.domain.entities.ppc.po.Bom" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ppc_bom
    where 1=1
    <if test="id != null and id != ''">
      and id = #{id,jdbcType=VARCHAR}
    </if>
    <if test="bomVer != null and bomVer != ''">
      and bom_ver = #{bomVer,jdbcType=VARCHAR}
    </if>
    <if test="versionStatus != null and versionStatus != ''">
      and version_Status = #{versionStatus,jdbcType=VARCHAR}
    </if>
    <if test="bomCode != null and bomCode != ''">
      and bom_code like concat('%',#{bomCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="materialCode != null and materialCode != ''">
      and material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="bomName != null and bomName != ''">
      and bom_name like concat('%',#{bomName,jdbcType=VARCHAR},'%')
    </if>
  </select>

  <select id="queryGroupBomByParam" parameterType="com.imes.domain.entities.ppc.po.Bom" resultMap="BaseResultMap">
    select
    t.bom_code
    from ppc_bom t
    where 1=1
    <if test="bomCode != null and bomCode != ''">
      and t.bom_code like concat('%',#{bomCode,jdbcType=VARCHAR},'%')
    </if>
    group by t.bom_code

  </select>

  <select id="findByBomCode" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from ppc_bom
    where bom_Code = #{bomCode,jdbcType=VARCHAR}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    delete from ppc_bom
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.Bom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    insert into ppc_bom (id, bom_code, bom_name, 
      bom_ver, material_code, version_status, 
      is_have_child, category, yntree,
      create_on, create_by, update_on, 
      update_by, remarks, delete_status
      )
    values (#{id,jdbcType=VARCHAR}, #{bomCode,jdbcType=VARCHAR}, #{bomName,jdbcType=VARCHAR}, 
      #{bomVer,jdbcType=INTEGER}, #{materialCode,jdbcType=VARCHAR}, #{versionStatus,jdbcType=VARCHAR}, 
      #{isHaveChild,jdbcType=VARCHAR}, #{category,jdbcType=VARCHAR}, #{yntree,jdbcType=VARCHAR}, 
      #{createOn,jdbcType=TIMESTAMP}, #{createBy,jdbcType=VARCHAR}, #{updateOn,jdbcType=TIMESTAMP}, 
      #{updateBy,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR}, #{deleteStatus,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.Bom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    insert into ppc_bom
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="bomCode != null">
        bom_code,
      </if>
      <if test="bomName != null">
        bom_name,
      </if>
      <if test="bomVer != null">
        bom_ver,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="versionStatus != null">
        version_status,
      </if>
      <if test="isHaveChild != null">
        is_have_child,
      </if>
      <if test="category != null">
        category,
      </if>
      <if test="yntree != null">
        yntree,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="deleteStatus != null">
        delete_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="bomCode != null">
        #{bomCode,jdbcType=VARCHAR},
      </if>
      <if test="bomName != null">
        #{bomName,jdbcType=VARCHAR},
      </if>
      <if test="bomVer != null">
        #{bomVer,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="versionStatus != null">
        #{versionStatus,jdbcType=VARCHAR},
      </if>
      <if test="isHaveChild != null">
        #{isHaveChild,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        #{category,jdbcType=VARCHAR},
      </if>
      <if test="yntree != null">
        #{yntree,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        #{deleteStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.Bom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    update ppc_bom
    <set>
      <if test="bomCode != null">
        bom_code = #{bomCode,jdbcType=VARCHAR},
      </if>
      <if test="bomName != null">
        bom_name = #{bomName,jdbcType=VARCHAR},
      </if>
      <if test="bomVer != null">
        bom_ver = #{bomVer,jdbcType=INTEGER},
      </if>
      <if test="materialCode != null">
        material_code = #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="versionStatus != null">
        version_status = #{versionStatus,jdbcType=VARCHAR},
      </if>
      <if test="isHaveChild != null">
        is_have_child = #{isHaveChild,jdbcType=VARCHAR},
      </if>
      <if test="category != null">
        category = #{category,jdbcType=VARCHAR},
      </if>
      <if test="yntree != null">
        yntree = #{yntree,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="deleteStatus != null">
        delete_status = #{deleteStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.Bom">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Dec 11 13:31:28 CST 2019.
    -->
    update ppc_bom
    set bom_code = #{bomCode,jdbcType=VARCHAR},
      bom_name = #{bomName,jdbcType=VARCHAR},
      bom_ver = #{bomVer,jdbcType=INTEGER},
      material_code = #{materialCode,jdbcType=VARCHAR},
      version_status = #{versionStatus,jdbcType=VARCHAR},
      is_have_child = #{isHaveChild,jdbcType=VARCHAR},
      category = #{category,jdbcType=VARCHAR},
      yntree = #{yntree,jdbcType=VARCHAR},
      create_on = #{createOn,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=VARCHAR},
      update_on = #{updateOn,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      delete_status = #{deleteStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>