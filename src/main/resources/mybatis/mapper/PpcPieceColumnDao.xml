<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceColumnDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceColumnQueryVo">
        select ppc_piece_column.id,
               ppc_piece_column.source,
               ppc_piece_column.column_name,
               ppc_piece_column.column_key,
               ppc_piece_column.control_type,
               ppc_piece_column.usually_condition,
               ppc_piece_column.special_condition,
               ppc_piece_column.drop_list_source,
               ppc_piece_column.drop_list_source_type,
               ppc_piece_column.create_on,
               ppc_piece_column.create_by,
               ppc_piece_column.update_on,
               ppc_piece_column.update_by,
               ppc_piece_column.remarks
        from ppc_piece_column ${ew.customSqlSegment}
    </select>

</mapper>

