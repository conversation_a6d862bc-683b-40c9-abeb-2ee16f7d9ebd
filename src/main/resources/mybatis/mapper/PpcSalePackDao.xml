<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcSalePackDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcSalePack">
        <!--@Table ppc_sale_pack-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="pack_no" jdbcType="VARCHAR" property="packNo"/>
        <result column="sale_no" jdbcType="VARCHAR" property="saleNo"/>
        <result column="print_label_name" jdbcType="VARCHAR" property="printLabelName"/>
        <result column="produce_date" jdbcType="TIMESTAMP" property="produceDate"/>
        <result column="custom_name" jdbcType="VARCHAR" property="customName"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="rules" jdbcType="VARCHAR" property="rules"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="custom" jdbcType="VARCHAR" property="custom"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,pack_no    ,sale_no    ,print_label_name    ,produce_date    ,custom_name    ,create_on    ,create_by    ,update_on    ,update_by    ,remarks    ,rules,custom,status
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_sale_pack
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryByCond" resultMap="BaseResultMap" parameterType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select
        a.id as id,
        a.pack_no as packNo,
        a.sale_no as saleNo,
        a.print_label_name as printLabelName,
        a.produce_date as produceDate,
        a.custom_name as customName,
        a.create_on as createOn,
        a.create_by as createBy,
        a.update_on as updateOn,
        a.update_by as updateBy,
        a.remarks as remarks,
        a.status as status,
        a.custom as custom,
        a.rules as rules
        from ppc_sale_pack a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="packNo != null and packNo != ''">
                and a.pack_no = #{packNo, jdbcType=VARCHAR}
            </if>
            <if test="saleNo != null and saleNo != ''">
                and a.sale_no = #{saleNo, jdbcType=VARCHAR}
            </if>
            <if test="printLabelName != null and printLabelName != ''">
                and a.print_label_name = #{printLabelName, jdbcType=VARCHAR}
            </if>
            <if test="produceDate != null">
                and a.produce_date = #{produceDate, jdbcType=TIMESTAMP}
            </if>
            <if test="customName != null and customName != ''">
                and a.custom_name = #{customName, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and a.create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and a.update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and a.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
            <if test="rules != null and rules != ''">
                and a.rules = #{rules, jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcSalePackSearchVo">
        select
        ppc_sale_pack.id as id,
        ppc_sale_pack.pack_no as packNo,
        ppc_sale_pack.sale_no as saleNo,
        ppc_sale_pack.print_label_name as printLabelName,
        ppc_sale_pack.produce_date as produceDate,
        ppc_sale_pack.custom_name as customName,
        ppc_sale_pack.create_on as createOn,
        ppc_sale_pack.remarks as remarks,
        ppc_sale_pack.status as status,
        <if test="ew.isMainMode">
            ppc_sale_pack.custom as custom
            from ppc_sale_pack
        </if>
        <if test="!ew.isMainMode">
            ppc_sale_pack_detail.id as detailId,
            ppc_sale_pack_detail.sale_detail_no as saleDetailNo,
            ppc_sale_pack_detail.material_code as materialCode,
            material.material_name as materialName,
            material.specification as specification,
            material.material_marker as modelNumber,
            ppc_sale_pack_detail.pack_status as packStatus,
            ppc_sale_pack_detail.min_pack_num as minPackNum,
            ppc_sale_pack_detail.box_num as boxNum,
            ppc_sale_pack_detail.handle_num as handleNum,
            ppc_sale_pack_detail.product_batch_no as productBatchNo,
            ppc_sale_pack_detail.primary_unit_code  as primaryUnitCode,
            unit.name as primaryUnitName,
            ppc_sale_pack_detail.sku_code as skuCode,
            ppc_sale_pack_detail.remarks as detailRemarks,
            JSON_MERGE_PATCH(ifnull(ppc_sale_pack.custom,'{}'), IFNULL(ppc_sale_pack_detail.custom, '{}')) as custom,
            cg.id as cId,
            cg.box_no as boxNo,
            cg.is_mixed as isMixed,
            cg.instorage_status as instorageStatus,
            ifnull(pm.print_times, 0) printSum,
            cg.label_num as labelNum,
            ifnull(c.label_num,0) sumPackQty,
            ifnull(d.label_num,0) sumApplyWmsQty,
            ifnull(e.label_num,0) sumWmsQty
            from ppc_sale_pack
            left join ppc_sale_pack_detail on ppc_sale_pack.pack_no = ppc_sale_pack_detail.pack_no
            left join sys_unit unit on unit.code =  ppc_sale_pack_detail.primary_unit_code
            left join ppc_material material on material.material_code = ppc_sale_pack_detail.material_code
            left join (select a.pack_detail_id, b.id, b.box_no, b.is_mixed, b.instorage_status, a.label_num
            from ppc_sale_pack_detail_box_material a
            left join ppc_sale_pack_detail_box b
            on a.box_no = b.box_no) cg on cg.pack_detail_id = ppc_sale_pack_detail.id
            LEFT JOIN sys_print_times pm ON cg.box_no = pm.print_no AND pm.print_type = 'SN_PRINT'
            left join (select sum(md.label_num) label_num,ab.sale_no,aq.sale_detail_no
            from ppc_sale_pack_detail_box_material md
            left join ppc_sale_pack_detail aq on md.pack_detail_id = aq.id
            left join ppc_sale_pack ab on aq.pack_no = ab.pack_no
            group by ab.sale_no,aq.sale_detail_no
            ) c on c.sale_detail_no = ppc_sale_pack_detail.sale_detail_no and ppc_sale_pack.sale_no = c.sale_no
            left join (select sum(md.label_num) label_num,ab.sale_no,aq.sale_detail_no
            from ppc_sale_pack_detail_box_material md
            left join ppc_sale_pack_detail aq on md.pack_detail_id = aq.id
            left join ppc_sale_pack ab on aq.pack_no = ab.pack_no
            left join ppc_sale_pack_detail_box bo on bo.box_no = md.box_no and bo.instorage_status in ('20', '40')
            group by ab.sale_no,aq.sale_detail_no
            ) d on d.sale_detail_no = ppc_sale_pack_detail.sale_detail_no and ppc_sale_pack.sale_no = d.sale_no
            left join (select sum(md.label_num) label_num,ab.sale_no,aq.sale_detail_no
            from ppc_sale_pack_detail_box_material md
            left join ppc_sale_pack_detail aq on md.pack_detail_id = aq.id
            left join ppc_sale_pack ab on aq.pack_no = ab.pack_no
            left join ppc_sale_pack_detail_box bo on bo.box_no = md.box_no and bo.instorage_status in ('40')
            group by ab.sale_no,aq.sale_detail_no
            ) e on e.sale_detail_no = ppc_sale_pack_detail.sale_detail_no and ppc_sale_pack.sale_no = e.sale_no
        </if>
        ${ew.customSqlSegment}
    </select>
    <select id="selectByPackNo" resultType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select
        <include refid="Base_Column_List"/>
        from ppc_sale_pack
        where pack_no = #{packNo,jdbcType=VARCHAR}
    </select>
    <select id="selectByIds" resultType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select
        <include refid="Base_Column_List"/>
        from ppc_sale_pack
        where id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByPackNos" resultType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select
        <include refid="Base_Column_List"/>
        from ppc_sale_pack
        where pack_no in
        <foreach collection="packNos" open="(" item="packNo" separator="," close=")">
            #{packNo,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="queryListSelect"
            resultType="com.imes.domain.entities.query.template.ppc.PpcSalePackComSelectVo">
        select ppc_sale_pack.id as id,
        ppc_sale_pack.pack_no as packNo,
        ppc_sale_pack.sale_no as saleNo,

        <if test="ew.isMainMode">
            ppc_sale_pack.custom_name as customName
            from ppc_sale_pack
        </if>
        <if test="!ew.isMainMode">
            ppc_sale_pack.custom_name as customName,
            ppc_sale_pack_detail.id as detailId,
            ppc_sale_pack_detail.sale_detail_no as saleDetailNo,
            ppc_sale_pack_detail.material_code as materialCode,
            ppc_sale_pack_detail.material_name as materialName,
            ppc_sale_pack_detail.specification as specification,
            ppc_sale_pack_detail.model_number as modelNumber,
            ppc_sale_pack_detail.box_num as boxNum,
            ppc_sale_pack_detail.handle_num as handleNum,
            ppc_sale_pack_detail.product_batch_no as productBatchNo,
            ppc_sale_pack_detail.primary_unit_code as primaryUnitCode,
            ppc_sale_pack_detail.sku_code as skuCode,
            cg.id as cId,
            cg.box_no as boxNo,
            cg.is_mixed as isMixed,
            cg.instorage_status as instorageStatus,
            cg.label_num as labelNum
            from ppc_sale_pack
            left join ppc_sale_pack_detail on ppc_sale_pack.pack_no = ppc_sale_pack_detail.pack_no
            left join (select a.pack_detail_id, b.id, b.box_no, b.is_mixed, b.instorage_status, a.label_num
            from ppc_sale_pack_detail_box_material a
            left join ppc_sale_pack_detail_box b
            on a.box_no = b.box_no) cg on cg.pack_detail_id = ppc_sale_pack_detail.id
        </if>
        ${ew.customSqlSegment}

    </select>
    <select id="selectDetailsListByPackNos"
            resultType="com.imes.domain.entities.query.template.ppc.PpcSalePackComSelectVo">
        select ppc_sale_pack.id as id,
        ppc_sale_pack.pack_no as packNo,
        ppc_sale_pack.sale_no as saleNo,
        ppc_sale_pack.custom_name as customName,
        ppc_sale_pack_detail.id as detailId,
        ppc_sale_pack_detail.sale_detail_no as saleDetailNo,
        ppc_sale_pack_detail.material_code as materialCode,
        ppc_sale_pack_detail.material_name as materialName,
        ppc_sale_pack_detail.specification as specification,
        ppc_sale_pack_detail.model_number as modelNumber,
        ppc_sale_pack_detail.box_num as boxNum,
        ppc_sale_pack_detail.handle_num as handleNum,
        ppc_sale_pack_detail.product_batch_no as productBatchNo,
        ppc_sale_pack_detail.primary_unit_code as primaryUnitCode,
        ppc_sale_pack_detail.sku_code as skuCode,
        cg.id as cId,
        cg.box_no as boxNo,
        cg.is_mixed as isMixed,
        cg.instorage_status as instorageStatus,
        cg.label_num as labelNum
        from ppc_sale_pack
        left join ppc_sale_pack_detail on ppc_sale_pack.pack_no = ppc_sale_pack_detail.pack_no
        left join (select a.pack_detail_id, b.id, b.box_no, b.is_mixed, b.instorage_status, a.label_num
        from ppc_sale_pack_detail_box_material a
        left join ppc_sale_pack_detail_box b
        on a.box_no = b.box_no) cg on cg.pack_detail_id = ppc_sale_pack_detail.id
        where ppc_sale_pack.pack_no in
        <foreach collection="packNos" open="(" item="packNo" separator="," close=")">
            #{packNo,jdbcType=VARCHAR}
        </foreach>

    </select>
    <select id="selectBySaleNo" resultType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select * from ppc_sale_pack where sale_no = #{soNo}
    </select>
    <select id="queryPpcSalePackList" resultType="com.imes.domain.entities.ppc.po.PpcSalePack">
        select * from ppc_sale_pack
        where 1=1
        <if test="packNo != null and packNo != '' ">
           and pack_no in
            <foreach collection="packNo" open="(" item="item" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="status != null and status != '' ">
            and status = #{status,jdbcType=VARCHAR}
        </if>
        <if test="statusIn != null and statusIn != '' ">
            and status in
            <foreach collection="statusIn" open="(" item="item" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>


    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from ppc_sale_pack
        where id = #{id, jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcSalePack">
        insert into ppc_sale_pack
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="packNo != null and packNo != ''">
                pack_no,
            </if>
            <if test="saleNo != null and saleNo != ''">
                sale_no,
            </if>
            <if test="printLabelName != null and printLabelName != ''">
                print_label_name,
            </if>
            <if test="produceDate != null">
                produce_date,
            </if>
            <if test="customName != null and customName != ''">
                custom_name,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
            <if test="status != null and status != ''">
                status,
            </if>
            <if test="custom != null and custom != ''">
                custom,
            </if>
            <if test="rules != null and rules != ''">
                rules
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="packNo != null and packNo != ''">
                #{packNo, jdbcType=VARCHAR},
            </if>
            <if test="saleNo != null and saleNo != ''">
                #{saleNo, jdbcType=VARCHAR},
            </if>
            <if test="printLabelName != null and printLabelName != ''">
                #{printLabelName, jdbcType=VARCHAR},
            </if>
            <if test="produceDate != null">
                #{produceDate, jdbcType=TIMESTAMP},
            </if>
            <if test="customName != null and customName != ''">
                #{customName, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks, jdbcType=VARCHAR},
            </if>
            <if test="status != null and status != ''">
                #{status, jdbcType=VARCHAR},
            </if>
            <if test="custom != null and custom != ''">
                #{custom, jdbcType=VARCHAR},
            </if>
            <if test="rules != null and rules != ''">
                #{rules, jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcSalePack">
        insert into ppc_sale_pack
        (id,
         pack_no,
         sale_no,
         print_label_name,
         produce_date,
         custom_name,
         create_on,
         create_by,
         update_on,
         update_by,
         remarks,
         status,
         custom,
         rules)
        values (#{id, jdbcType=VARCHAR},
                #{packNo, jdbcType=VARCHAR},
                #{saleNo, jdbcType=VARCHAR},
                #{printLabelName, jdbcType=VARCHAR},
                #{produceDate, jdbcType=TIMESTAMP},
                #{customName, jdbcType=VARCHAR},
                #{createOn, jdbcType=TIMESTAMP},
                #{createBy, jdbcType=VARCHAR},
                #{updateOn, jdbcType=TIMESTAMP},
                #{updateBy, jdbcType=VARCHAR},
                #{remarks, jdbcType=VARCHAR},
                #{status, jdbcType=VARCHAR},
                #{custom, jdbcType=VARCHAR},
                #{rules, jdbcType=VARCHAR})
    </insert>

    <insert id="insertBatchList" parameterType="java.util.List">
        insert into ppc_sale_pack
        ( id,
        pack_no,
        sale_no,
        print_label_name,
        produce_date,
        custom_name,
        create_on,
        create_by,
        update_on,
        update_by,
        remarks,
        status,
        custom,
        rules
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id, jdbcType=VARCHAR},
            #{item.packNo, jdbcType=VARCHAR},
            #{item.saleNo, jdbcType=VARCHAR},
            #{item.printLabelName, jdbcType=VARCHAR},
            #{item.produceDate, jdbcType=TIMESTAMP},
            #{item.customName, jdbcType=VARCHAR},
            #{item.createOn, jdbcType=TIMESTAMP},
            #{item.createBy, jdbcType=VARCHAR},
            #{item.updateOn, jdbcType=TIMESTAMP},
            #{item.updateBy, jdbcType=VARCHAR},
            #{item.remarks, jdbcType=VARCHAR},
            #{item.status, jdbcType=VARCHAR},
            #{item.custom, jdbcType=VARCHAR},
            #{item.rules, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcSalePack">
        update ppc_sale_pack
        <set>
            <if test="packNo != null">
                pack_no = #{packNo, jdbcType=VARCHAR},
            </if>
            <if test="saleNo != null">
                sale_no = #{saleNo, jdbcType=VARCHAR},
            </if>
            <if test="printLabelName != null">
                print_label_name = #{printLabelName, jdbcType=VARCHAR},
            </if>
            <if test="produceDate != null">
                produce_date = #{produceDate, jdbcType=TIMESTAMP},
            </if>
            <if test="customName != null">
                custom_name = #{customName, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks, jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status, jdbcType=VARCHAR},
            </if>
            <if test="custom != null">
                custom = #{custom, jdbcType=VARCHAR},
            </if>
            <if test="rules != null">
                rules = #{rules, jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateStatusByIdList">
        update ppc_sale_pack set status = #{status} where id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateRules">
        update ppc_sale_pack
        set rules = #{rule}
        where id = #{id}
    </update>

    <delete id="deleteByIds">
        delete
        from ppc_sale_pack
        where
        id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>

    </delete>
</mapper>