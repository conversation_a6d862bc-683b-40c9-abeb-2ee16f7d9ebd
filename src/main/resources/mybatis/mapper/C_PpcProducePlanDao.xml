<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.C_PpcProducePlanDao">


    <select id="yuZhouBoardPlan" resultType="com.imes.domain.entities.ppc.po.PpcProducePlan">
         select pp.id,pp.pp_no,pp.material_name,pp.specification
         ,pp.produce_qty,pp.plan_start_date
         ,pp.plan_end_date,t1.good_qty,t1.remarks
         from  ppc_produce_plan pp
         left join (
            select plan_id as pp_id,good_qty,CONCAT(round(good_qty *100 /(good_qty + bad_qty)) ,'%') as remarks
            from ppc_produce_plan_schedul
         ) t1 on pp.id = t1.pp_id
        ${ew.customSqlSegment}
    </select>


    <select id="huaDunQueryList" resultType="com.imes.domain.entities.ppc.po.PpcProducePlan">
        SELECT
            ppc_produce_plan.id,
            ppc_produce_plan.sale_detail_id,
            ppc_produce_plan.sale_detail_no,
            ppc_produce_plan.pp_no,
            ppc_produce_plan.pp_level,
            ppc_produce_plan.parent_pp_id,
            ppc_produce_plan.parent_pp_no,
            ppc_produce_plan.root_pp_id,
            ppc_produce_plan.root_pp_no,
            ppc_produce_plan.workshop_code,
            ppc_produce_plan.workshop_leader_code,
            ppc_produce_plan.workshop_leader_name,
            ppc_produce_plan.material_code,
            ppc_produce_plan.material_name,
            ppc_produce_plan.specification,
            ppc_produce_plan.material_marker,
            ppc_produce_plan.bom_code,
            ppc_produce_plan.bom_name,
            ppc_produce_plan.bom_ver,
            ppc_produce_plan.produce_qty,
            ppc_produce_plan.target_good_qty,
            ppc_produce_plan.unit_code,
            ppc_produce_plan.`status`,
            ppc_produce_plan.plan_start_date,
            ppc_produce_plan.plan_end_date,
            ppc_produce_plan.actual_start_date,
            ppc_produce_plan.actual_end_date,
            ppc_produce_plan.issue_date,
            ppc_produce_plan.prod_standard,
            ppc_produce_plan.order_type,
            ppc_produce_plan.is_merge_plan,
            ppc_produce_plan.batch_no,
            ppc_produce_plan.workshop_note,
            ppc_produce_plan.planer_code,
            ppc_produce_plan.planer_name,
            ppc_produce_plan.create_on,
            ppc_produce_plan.create_by,
            ppc_produce_plan.update_on,
            ppc_produce_plan.update_by,
            ppc_produce_plan.remarks,
            ppc_produce_plan.procuct_priority,
            ppc_produce_plan.sale_detail_remarks,
            ppc_produce_plan.custom,
            ppc_produce_plan.is_urgent
        FROM
            ppc_produce_plan
        ${ew.customSqlSegment}
    </select>

</mapper>