<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPieceSubTeamDao">

    <select id="queryList" resultType="com.imes.domain.entities.query.template.ppc.PpcPieceSubTeamSearchVo">
        select ppc_piece_sub_team.id,
               ppc_piece_sub_team.sub_team_name,
               ppc_piece_sub_team.big_process_code,
               ppc_piece_sub_team.big_process_name,
               ppc_piece_sub_team.team_code,
               ppc_piece_sub_team.team_name,
               ppc_piece_sub_team.item_name,
               ppc_piece_sub_team.remarks,
               ppc_piece_sub_team.create_on,
               ppc_piece_sub_team.create_by,
               ppc_piece_sub_team.update_on,
               ppc_piece_sub_team.update_by,
               ppc_piece_sub_team.user_info
        from ppc_piece_sub_team ${ew.customSqlSegment}
    </select>

    <insert id="insertAllBatch" parameterType="java.util.List">
        insert into ppc_piece_sub_team
        ( id,
        sub_team_name,
        big_process_code,
        team_code,
        item_name,
        remarks,
        create_on,
        create_by,
        update_on,
        update_by,
        user_info
        )
        values
        <foreach collection="list" item="item" index="index" open="" close="" separator=",">
            (
            #{item.id},
            #{item.subTeamName},
            #{item.bigProcessCode},
            #{item.teamCode},
            #{item.itemName},
            #{item.remarks},
            #{item.createOn},
            #{item.createBy},
            #{item.updateOn},
            #{item.updateBy},
            #{item.userInfo}
            )
        </foreach>
    </insert>

</mapper>

