<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.qms.dao.MaterialInspectionDao">
  <resultMap id="BaseResultMap" type="com.imes.domain.entities.qms.po.MaterialInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="arrival_order" jdbcType="VARCHAR" property="arrivalOrder" />
    <result column="buy_order" jdbcType="VARCHAR" property="buyOrder" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="temporary_no" jdbcType="VARCHAR" property="temporaryNo" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="unqualified_num" jdbcType="INTEGER" property="unqualifiedNum" />
    <result column="inspection_code" jdbcType="VARCHAR" property="inspectionCode" />
    <result column="inbound_qty" jdbcType="VARCHAR" property="inboundQty" />
    <result column="arrived_qty" jdbcType="VARCHAR" property="arrivedQty" />
    <result column="purchase_qty" jdbcType="VARCHAR" property="purchaseQty" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="activiti_id" jdbcType="VARCHAR" property="activitiId" />
    <result column="reject_id" jdbcType="VARCHAR" property="rejectId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="material_marker" jdbcType="INTEGER" property="materialMarker" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="unit_price" jdbcType="VARCHAR" property="unitPrice" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="inspection_ratio" jdbcType="DECIMAL" property="inspectionRatio" />
    <result column="qualified_rate" jdbcType="DECIMAL" property="qualifiedRate" />
    <result column="inspect_type" jdbcType="VARCHAR" property="inspectType" />
    <result column="aql_code" jdbcType="VARCHAR" property="aqlCode" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="team_code" jdbcType="VARCHAR" property="teamCode" />
    <result column="team_name" jdbcType="VARCHAR" property="teamName" />
  </resultMap>
  <resultMap id="BaseResultMapForTask" type="com.imes.domain.entities.qms.vo.MaterialInspectionVo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="arrival_order" jdbcType="VARCHAR" property="arrivalOrder" />
    <result column="buy_order" jdbcType="VARCHAR" property="buyOrder" />
    <result column="order_id" jdbcType="VARCHAR" property="orderId" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="temporary_no" jdbcType="VARCHAR" property="temporaryNo" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="product_name" jdbcType="VARCHAR" property="productName" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="unqualified_num" jdbcType="INTEGER" property="unqualifiedNum" />
    <result column="inspection_code" jdbcType="VARCHAR" property="inspectionCode" />
    <result column="inbound_qty" jdbcType="VARCHAR" property="inboundQty" />
    <result column="arrived_qty" jdbcType="VARCHAR" property="arrivedQty" />
    <result column="purchase_qty" jdbcType="VARCHAR" property="purchaseQty" />
    <result column="inspection_status" jdbcType="VARCHAR" property="inspectionStatus" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="activiti_id" jdbcType="VARCHAR" property="activitiId" />
    <result column="reject_id" jdbcType="VARCHAR" property="rejectId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="material_marker" jdbcType="INTEGER" property="materialMarker" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="unit_price" jdbcType="VARCHAR" property="unitPrice" />
    <result column="reviewer" jdbcType="VARCHAR" property="reviewer" />
    <result column="audit_result" jdbcType="VARCHAR" property="auditResult" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="inspection_ratio" jdbcType="DECIMAL" property="inspectionRatio" />
    <result column="qualified_rate" jdbcType="DECIMAL" property="qualifiedRate" />
    <result column="inspect_type" jdbcType="VARCHAR" property="inspectType" />
    <result column="aql_code" jdbcType="VARCHAR" property="aqlCode" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="team_code" jdbcType="VARCHAR" property="teamCode" />
    <result column="team_name" jdbcType="VARCHAR" property="teamName" />
    <result column="task_inspection_code" jdbcType="VARCHAR" property="taskInspectionCode" />
  </resultMap>
  <resultMap id="BaseResultMapTraceVo" type="com.imes.domain.entities.qms.vo.QualityTraceVo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="inspection_num" jdbcType="INTEGER" property="inspectionNum" />
    <result column="unqualified_num" jdbcType="INTEGER" property="unQualified" />
    <result column="inspection_code" jdbcType="VARCHAR" property="inspectionCode" />
    <result column="specification" jdbcType="VARCHAR" property="specification" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="material_marker" jdbcType="INTEGER" property="materialMarker" />
    <result column="is_qualified" jdbcType="VARCHAR" property="isQualified" />
    <result column="create_on" jdbcType="TIMESTAMP" property="inspectionTime" />
    <result column="task_inspection_code" jdbcType="VARCHAR" property="taskInspectionCode" />
  </resultMap>
  <resultMap id="BaseResultMapOrderDetail" type="com.imes.domain.entities.wms.WmsArrivalOrderItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 01 14:31:14 CST 2021.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ao_id" jdbcType="VARCHAR" property="aoId" />
    <result column="ao_code" jdbcType="VARCHAR" property="aoCode" />
    <result column="ao_item_code" jdbcType="VARCHAR" property="aoItemCode" />
    <result column="material_code" jdbcType="VARCHAR" property="materialCode" />
    <result column="material_name" jdbcType="VARCHAR" property="materialName" />
    <result column="material_marker" jdbcType="VARCHAR" property="materialMarker" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="unit" jdbcType="VARCHAR" property="primaryUnit" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="purchase_qty" jdbcType="DECIMAL" property="purchaseQty" />
    <result column="arrival_qty" jdbcType="DECIMAL" property="arrivalQty" />
    <result column="received_qty" jdbcType="DECIMAL" property="receivedQty" />
    <result column="rejection_qty" jdbcType="DECIMAL" property="rejectionQty" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="third_item_code" jdbcType="VARCHAR" property="thirdItemCode" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="customer_name" jdbcType="VARCHAR" property="customerName" />
    <result column="sales_contract_code" jdbcType="VARCHAR" property="salesContractCode" />
  </resultMap>
  <resultMap id="BaseResultMapOrder" type="com.imes.domain.entities.wms.WmsArrivalOrder">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 01 14:31:14 CST 2021.
    -->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="ao_code" jdbcType="VARCHAR" property="aoCode" />
    <result column="order_type" jdbcType="INTEGER" property="orderType" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="third_order_code" jdbcType="VARCHAR" property="thirdOrderCode" />
    <result column="receipt_type" jdbcType="INTEGER" property="receiptType" />
    <result column="receipt_code" jdbcType="VARCHAR" property="receiptCode" />
    <result column="wh_code" jdbcType="VARCHAR" property="whCode" />
    <result column="wh_name" jdbcType="VARCHAR" property="whName" />
    <result column="arrival_time" jdbcType="TIMESTAMP" property="arrivalTime" />
    <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode" />
    <result column="supplier_name" jdbcType="VARCHAR" property="supplierName" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="shipper_code" jdbcType="VARCHAR" property="shipperCode" />
    <result column="shipper_name" jdbcType="VARCHAR" property="shipperName" />
    <result column="logistic_code" jdbcType="VARCHAR" property="logisticCode" />
    <result column="delivery_driver" jdbcType="VARCHAR" property="deliveryDriver" />
    <result column="driver_phone" jdbcType="VARCHAR" property="driverPhone" />
    <result column="receiver" jdbcType="VARCHAR" property="receiver" />
    <result column="inspector" jdbcType="VARCHAR" property="inspector" />
    <result column="approver" jdbcType="VARCHAR" property="approver" />
    <result column="order_status" jdbcType="INTEGER" property="orderStatus" />
    <result column="quality_status" jdbcType="INTEGER" property="qualityStatus" />
    <result column="storage_status" jdbcType="INTEGER" property="storageStatus" />
    <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
    <result column="delete_status" jdbcType="INTEGER" property="deleteStatus" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id, arrival_order, buy_order,material_code, material_name, inspector,inbound_qty,
    product_name,inspection_num,unqualified_num,inspection_code,order_id,reject_id,
    inspection_status,material_marker,supplier_code,unit_price,status,temporary_no,
    is_qualified,create_on,create_by,update_on,update_by,remarks,specification,unit,
    supplier_name,purchase_qty,arrived_qty,activiti_id,reviewer,audit_time,audit_result,
    inspection_ratio,qualified_rate,inspect_type,aql_code
  </sql>
  <sql id="Base_Column_ListTraceVo">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Nov 06 16:57:40 CST 2020.
    -->
    id,material_code, material_name,inspector,inspection_num,
    unqualified_num,inspection_code,create_on,specification,unit,
    temporary_no,material_marker,supplier_name,is_qualified
  </sql>
  <sql id="Base_Column_List_order">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 01 14:31:14 CST 2021.
    -->
    id, ao_code, order_type, source, third_order_code, receipt_type, receipt_code, wh_code, wh_name,
    arrival_time, supplier_code, supplier_name, license_plate,
    shipper_code, shipper_name, logistic_code, delivery_driver, driver_phone, receiver,
    inspector, approver, order_status, quality_status, storage_status, create_on, create_by,
    update_on, update_by, delete_status, remarks
  </sql>
  <sql id="Base_Column_List_order_detail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Mar 01 14:31:14 CST 2021.
    -->
    id, ao_id, ao_code, ao_item_code, material_code, material_name, material_marker, bar_code,
    unit, unit_price, purchase_qty, arrival_qty, received_qty, rejection_qty, batch, remark,
    create_on, create_by, update_on, update_by, delete_status, third_item_code, company_code,
    company_name, customer_code, customer_name, sales_contract_code
  </sql>
    <select id="queryList"  resultMap="BaseResultMapForTask">
      select
      qms_in_material_inspection.id,
      qms_in_material_inspection.arrival_order,
      qms_in_material_inspection.buy_order,
      qms_in_material_inspection.material_code,
      ppc_material.material_name,
      qms_in_material_inspection.inspector,
      qms_in_material_inspection.inbound_qty,
      qms_in_material_inspection.product_name,
      qms_in_material_inspection.inspection_num,
      qms_in_material_inspection.unqualified_num,
      qms_in_material_inspection.inspection_code,
      qms_in_material_inspection.order_id,
      qms_in_material_inspection.reject_id,
      qms_in_material_inspection.inspection_status,
      ppc_material.material_marker,
      qms_in_material_inspection.supplier_code,
      qms_in_material_inspection.unit_price,
      qms_in_material_inspection.STATUS,
      qms_in_material_inspection.temporary_no,
      qms_in_material_inspection.is_qualified,
      qms_in_material_inspection.create_on,
      qms_in_material_inspection.create_by,
      qms_in_material_inspection.update_on,
      qms_in_material_inspection.update_by,
      qms_in_material_inspection.remarks,
      ppc_material.specification,
      qms_in_material_inspection.unit,
      sys_supplier.supplier_name,
      qms_in_material_inspection.purchase_qty,
      qms_in_material_inspection.arrived_qty,
      qms_in_material_inspection.activiti_id,
      qms_in_material_inspection.reviewer,
      qms_in_material_inspection.audit_time,
      qms_in_material_inspection.audit_result,
      qms_in_material_inspection.inspection_ratio,
      qms_in_material_inspection.qualified_rate,
      qms_in_material_inspection.inspect_type,
      qms_in_material_inspection.aql_code,
      qms_material_task.task_inspection_code
      from qms_in_material_inspection qms_in_material_inspection
      left join qms_material_task qms_material_task on qms_in_material_inspection.order_id = qms_material_task.order_id
      left join ppc_material ppc_material on ppc_material.material_code = qms_in_material_inspection.material_code
      left join sys_supplier sys_supplier on sys_supplier.supplier_code = qms_in_material_inspection.supplier_code
      where 1=1
      and qms_in_material_inspection.inspection_status <![CDATA[ <> ]]> #{ending}
      and qms_in_material_inspection.status <![CDATA[ <> ]]> #{unSupport}
      <if test="taskInspectionCode != null and taskInspectionCode != ''">
        and qms_material_task.task_inspection_code like concat('%',#{taskInspectionCode,jdbcType=VARCHAR},'%')
      </if>
      <if test="isQualified != null and isQualified != ''">
        and qms_in_material_inspection.is_qualified = #{isQualified,jdbcType=VARCHAR}
      </if>
      <if test="inspectionStatus != null and inspectionStatus != ''">
        and qms_in_material_inspection.inspection_status = #{inspectionStatus,jdbcType=VARCHAR}
      </if>
      <if test="status != null and status != ''">
        and qms_in_material_inspection.status = #{status,jdbcType=VARCHAR}
      </if>
      <if test="materialCode != null and materialCode != ''">
        and qms_in_material_inspection.material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
      </if>
      <if test="materialName != null and materialName != ''">
        and ppc_material.material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
      </if>
      <if test="arrivalOrder != null and arrivalOrder != ''">
        and qms_in_material_inspection.arrival_order like concat('%',#{arrivalOrder,jdbcType=VARCHAR},'%')
      </if>
      <if test="buyOrder != null and buyOrder != ''">
        and qms_in_material_inspection.buy_order like concat('%',#{buyOrder,jdbcType=VARCHAR},'%')
      </if>
      <if test="inspectionCode != null and inspectionCode != ''">
        and qms_in_material_inspection.inspection_code like concat('%',#{inspectionCode,jdbcType=VARCHAR},'%')
      </if>
      <if test="startTime != null and startTime != ''">
        and qms_in_material_inspection.create_on  <![CDATA[ >= ]]>  #{startTime, jdbcType=TIMESTAMP}
      </if>
      <if test="endTime != null and endTime != ''">
        and qms_in_material_inspection.create_on <![CDATA[ <= ]]>  #{endTime, jdbcType=TIMESTAMP}
      </if>
      order by qms_in_material_inspection.create_on desc
    </select>
<!--  <select id="queryListNew"  resultMap="BaseResultMap">-->
<!--    select-->
<!--    <include refid="Base_Column_List" />-->
<!--    from qms_in_material_inspection-->
<!--    ${ew.customSqlSegment}-->
<!--    order by create_on desc-->
<!--  </select>-->
  <select id="getById" parameterType="java.lang.String" resultMap="BaseResultMap">
  select
    qms_in_material_inspection.id,
    qms_in_material_inspection.arrival_order,
    qms_in_material_inspection.buy_order,
    qms_in_material_inspection.material_code,
    ppc_material.material_name,
    qms_in_material_inspection.inspector,
    qms_in_material_inspection.inbound_qty,
    qms_in_material_inspection.product_name,
    qms_in_material_inspection.inspection_num,
    qms_in_material_inspection.unqualified_num,
    qms_in_material_inspection.inspection_code,
    qms_in_material_inspection.order_id,
    qms_in_material_inspection.reject_id,
    qms_in_material_inspection.inspection_status,
    ppc_material.material_marker,
    qms_in_material_inspection.supplier_code,
    qms_in_material_inspection.unit_price,
    qms_in_material_inspection.STATUS,
    qms_in_material_inspection.temporary_no,
    qms_in_material_inspection.is_qualified,
    qms_in_material_inspection.create_on,
    qms_in_material_inspection.create_by,
    qms_in_material_inspection.update_on,
    qms_in_material_inspection.update_by,
    qms_in_material_inspection.remarks,
    ppc_material.specification,
    sys_unit.name as unit,
    sys_supplier.supplier_name,
    qms_in_material_inspection.purchase_qty,
    qms_in_material_inspection.arrived_qty,
    qms_in_material_inspection.activiti_id,
    qms_in_material_inspection.reviewer,
    qms_in_material_inspection.audit_time,
    qms_in_material_inspection.audit_result,
    qms_in_material_inspection.inspection_ratio,
    qms_in_material_inspection.qualified_rate,
    qms_in_material_inspection.inspect_type,
    qms_in_material_inspection.aql_code
  from qms_in_material_inspection
  left join ppc_material on ppc_material.material_code = qms_in_material_inspection.material_code
  LEFT JOIN sys_unit on sys_unit.code = qms_in_material_inspection.unit
  LEFT JOIN sys_supplier on sys_supplier.supplier_code = qms_in_material_inspection.supplier_code
  where 1=1 and qms_in_material_inspection.id=#{id}
  </select>
  <select id="getByActivitiId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_in_material_inspection
    where 1=1 and activiti_id=#{activitiId}
  </select>
  <select id="getQualityTrace" parameterType="java.lang.String" resultMap="BaseResultMapTraceVo">
    select
    a.id,
    a.material_code,
    ppc_material.material_name,
    a.inspector,
    a.inspection_num,
    a.unqualified_num,
    a.inspection_code,
    a.create_on,
    ppc_material.specification,
    a.unit,
    a.temporary_no,
    ppc_material.material_marker,
    sys_supplier.supplier_name,
    a.is_qualified,
    b.task_inspection_code,
    CONCAT(IFNULL(ppc_material.material_name,''),IFNULL(b.task_inspection_code,'')) as conditions
    FROM
    qms_in_material_inspection a
    left join qms_material_task b on a.order_id = b.order_id
    left join ppc_material ppc_material on ppc_material.material_code = a.material_code
    left join sys_supplier sys_supplier on sys_supplier.supplier_code = a.supplier_code
    where a.id IN ( SELECT parent_id FROM qms_in_material_relation
    where
    inspection_status in(#{inspection},#{reject},#{process}))
    <if test="conditions != null and conditions != ''">
      and CONCAT(IFNULL(ppc_material.material_name,''),IFNULL(b.task_inspection_code,'')) like concat('%',#{conditions,jdbcType=VARCHAR},'%')
    </if>
    <if test="userCodesIn != null and userCodesIn != ''">
      and a.inspector in ${userCodesIn}
    </if>
    <if test="materialCode != null and materialCode != ''">
      and a.material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="materialName != null and materialName != ''">
      and ppc_material.material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
    </if>
    <if test="supplierName != null and supplierName != ''">
      and sys_supplier.supplier_name like concat('%',#{supplierName,jdbcType=VARCHAR},'%')
    </if>
    <if test="supplierCode != null and supplierCode != ''">
      and a.supplier_code =#{supplierCode,jdbcType=VARCHAR}
    </if>
    <if test="rejectId != null and rejectId != ''">
      and a.id =#{rejectId,jdbcType=VARCHAR}
    </if>
    <if test="inspectionCode != null and inspectionCode != ''">
      and a.inspection_code like concat('%',#{inspectionCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="inspectionStartTime != null and inspectionStartTime != ''">
      and a.create_on  <![CDATA[ >= ]]>  #{inspectionStartTime, jdbcType=TIMESTAMP}
    </if>
    <if test="inspectionEndTime != null and inspectionEndTime != ''">
      and a.create_on <![CDATA[ <= ]]>  #{inspectionEndTime, jdbcType=TIMESTAMP}
    </if>
    order by a.create_on desc
  </select>
  <select id="getMaterialByOrderNo" parameterType="java.lang.String" resultType="com.imes.domain.entities.qms.vo.MaterialBuyOrderVo">
    SELECT
    d.ao_code arrivalOrder,
    d.receipt_code buyOrder,
    d.material_code materialCode,
    d.material_name materialName,
    d.material_marker materialMarker,
    d.unit_price unitPrice,
    d.purchase_qty purchaseQty,
    d.arrival_qty arrivedQty,
    d.received_qty inboundQty,
    d.id orderId,
    d.id id,
    d.unit unit,
    o.supplier_code supplierCode,
    o.supplier_name supplierName,
    o.arrival_time arrivedTime,
    m.primary_unit primaryUnit,
    m.specification specification,
    r.inspection_status inspectionStatus,
    p.inspection_ratio inspectionRatio
    FROM
    wms_arrival_order_item d
    LEFT JOIN wms_arrival_order o ON d.ao_id = o.id
    LEFT join ppc_material m on d.material_code=m.material_code
    LEFT join qms_data_protect_inspection p on d.material_code=p.material_code and p.inspection_type=#{inspectionType}
    LEFT join qms_in_material_relation r on d.id=r.order_id and r.inspection_status <![CDATA[ <> ]]>#{ending}
    WHERE
          1=1
      and o.order_status not in ('10','99')
    <if test="orderType != null and orderType != ''">
      and o.order_type =#{orderType,jdbcType=VARCHAR}
    </if>
    <if test="arrivalOrder != null and arrivalOrder != ''">
      and d.ao_code like concat('%',#{arrivalOrder,jdbcType=VARCHAR},'%')
    </if>
    <if test="orderNos != null and orderNos != ''">
      and d.id in ${orderNos}
    </if>
    <if test="buyOrder != null and buyOrder != ''">
      and d.receipt_code like concat('%',#{buyOrder,jdbcType=VARCHAR},'%')
    </if>
    <if test="category != null and category != ''">
      and m.category =#{category}
    </if>
    <if test="materialName != null and materialName != ''">
      and d.material_name like concat('%',#{materialName,jdbcType=VARCHAR},'%')
    </if>
    <if test="materialCode != null and materialCode != ''">
      and d.material_code like concat('%',#{materialCode,jdbcType=VARCHAR},'%')
    </if>
    <if test="supplierName != null and supplierName != ''">
      and o.supplier_name like concat('%',#{supplierName,jdbcType=VARCHAR},'%')
    </if>
    <if test="startTime != null and startTime != ''">
      and o.arrival_time  <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
    </if>
    <if test="endTime != null and endTime != ''">
      and o.arrival_time  <![CDATA[ <= ]]>  #{endTime,jdbcType=TIMESTAMP}
    </if>
    <if test="inspectionStatus == null or inspectionStatus ==''">
      and d.id not in (SELECT DISTINCT order_id FROM qms_in_material_relation WHERE order_id IS NOT NULL and inspection_status not in (#{temporarySave},#{reject},#{ending}))
    </if>
    <if test="inspectionStatus == '5'.toString()">
      and d.id not in (SELECT DISTINCT order_id FROM qms_in_material_relation WHERE order_id IS NOT NULL)
    </if>
    <if test="inspectionStatus != null and inspectionStatus != '5'.toString() and inspectionStatus !=''">
      and d.id in (SELECT DISTINCT order_id FROM qms_in_material_relation WHERE order_id IS NOT NULL and inspection_status =#{inspectionStatus})
    </if>
    and d.material_code in (select distinct material_code from qms_data_protect_inspection where inspection_type=#{inspectionType} and material_code is not null)
    order by d.create_on desc
  </select>
  <select id="getMaterialByOrderNos" parameterType="java.lang.String" resultType="com.imes.domain.entities.qms.vo.MaterialBuyOrderVo">
    SELECT
    d.ao_code arrivalOrder,
    o.receipt_code buyOrder,
    d.material_code materialCode,
    d.material_name materialName,
    d.material_marker materialMarker,
    d.unit_price unitPrice,
    d.purchase_qty purchaseQty,
    d.arrival_qty arrivedQty,
    d.received_qty inboundQty,
    d.id orderId,
    d.id id,
    d.unit unit,
    o.supplier_code supplierCode,
    o.supplier_name supplierName,
    o.arrival_time arrivedTime,
    m.primary_unit primaryUnit,
    m.specification specification,
    p.inspection_ratio inspectionRatio,
    r.inspection_status inspectionStatus
    FROM
    wms_arrival_order_item d
    LEFT JOIN wms_arrival_order o ON d.ao_id = o.id
    LEFT JOIN ppc_material m on d.material_code=m.material_code
    LEFT JOIN qms_data_protect_inspection p on d.material_code=p.material_code and p.inspection_type=#{inspectionType}
    LEFT JOIN qms_in_material_relation r on d.id=r.order_id
    WHERE
    1=1
    and r.parent_id=#{parentId}
    order by d.create_on desc
  </select>
  <select id="findByAoIds" parameterType="java.lang.String" resultMap="BaseResultMapOrderDetail">
    select
    <include refid="Base_Column_List_order_detail" />
    from wms_arrival_order_item
    where ao_id in ${aoIds}
  </select>
  <select id="getIdByOrderId" parameterType="java.lang.String" resultType="java.lang.String">
    select parent_id from qms_in_material_relation where order_id=#{orderId} and inspection_status <![CDATA[ <> ]]>#{ending}
  </select>
  <select id="getIdByActivtiId" parameterType="java.lang.String" resultType="java.util.Map">
    select id mainId,reject_id rejectId from qms_in_material_inspection where activiti_id=#{activitiId}
  </select>
  <select id="getRejectIdById" parameterType="java.lang.String" resultType="java.util.Map">
    select reject_id rejectId from qms_in_material_inspection where id=#{id}
  </select>
  <select id="getMaterialReportTotal" resultType="long">
    select count(*) from qms_in_material_inspection where inspection_status=#{process} and create_by != 'admin' and create_by != '0000'
  </select>

  <select id="getMaterialTotal" resultType="long">
    select count(*) from qms_in_material_inspection where create_by != 'admin' and create_by != '0000'
  </select>
  <select id="getRejectByInspectionCode" parameterType="java.lang.String" resultType="java.util.Map">
    select id id,inspection_code inspectionCode from qms_in_material_inspection where reject_id=(
    select id  from qms_in_material_inspection where inspection_code=#{inspectionCode})
  </select>
  <select id="findAoByFilter" parameterType="java.util.Map" resultMap="BaseResultMapOrder">
    SELECT
    <include refid="Base_Column_List_order"/>
    FROM wms_arrival_order
    <where>
      <if test="arrivalStartDate!=null and arrivalStartDate!= ''">
        and arrival_time>=#{arrivalStartDate}
      </if>
      <if test="arrivalEndDate != null and arrivalEndDate != ''">
        and #{arrivalEndDate}>=arrival_time
      </if>
      <if test="aoCode != null and aoCode != ''">
        and ao_code like concat('%',#{aoCode,jdbcType=VARCHAR},'%')
      </if>
      <if test="orderStatus != null and orderStatus != ''">
        and order_status<![CDATA[ <> ]]>#{orderStatus}
      </if>
      <if test="receiver != null and receiver != ''">
        and receiver=#{receiver}
      </if>
      <if test="receiptCode != null and receiptCode != ''">
        and receipt_code like concat('%',#{receiptCode},'%')
      </if>
      <if test="materialCode != null and materialCode != ''">
        and id IN ( SELECT DISTINCT ao_id FROM wms_arrival_order_item WHERE material_code LIKE concat('%',#{materialCode,jdbcType=VARCHAR},'%') )
      </if>
      <if test="materialName != null and materialName != ''">
        and id IN ( SELECT DISTINCT ao_id FROM wms_arrival_order_item WHERE material_name LIKE concat('%',#{materialName,jdbcType=VARCHAR},'%') )
      </if>
      <if test="searchKey != null and searchKey != ''">
        and ( ao_code like concat('%',#{searchKey,jdbcType=VARCHAR},'%')
        or ao_code IN ( SELECT DISTINCT ao_code FROM wms_arrival_order_item WHERE material_code LIKE concat('%',#{searchKey,jdbcType=VARCHAR},'%') )
        or ao_code IN ( SELECT DISTINCT ao_code FROM wms_arrival_order_item WHERE model_number LIKE concat('%',#{searchKey,jdbcType=VARCHAR},'%') ) )
      </if>
      <if test="materialMarker != null and materialMarker != ''">
        and id IN ( SELECT DISTINCT ao_id FROM wms_arrival_order_item WHERE material_marker LIKE concat('%',#{materialMarker,jdbcType=VARCHAR},'%') )
      </if>
        and ao_code NOT IN ( SELECT DISTINCT arrival_order FROM `qms_in_material_inspection` WHERE arrival_order IS NOT NULL )
    </where>
  </select>
  <select id="countHasQualified" parameterType="java.lang.String" resultType="java.lang.Integer">
    SELECT
      count( id )
    FROM
      `qms_in_material_inspection_detail`
    WHERE
      is_qualified IS NULL
      AND parent_id = #{id}
  </select>
  <insert id="insertSelective" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Wed Jul 22 15:16:27 CST 2020.
    -->
    insert into qms_in_material_inspection
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="arrivalOrder != null">
        arrival_order,
      </if>
      <if test="buyOrder != null">
        buy_order,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="materialCode != null">
        material_code,
      </if>
      <if test="materialName != null">
        material_name,
      </if>
      <if test="temporaryNo != null">
        temporary_no,
      </if>
      <if test="specification != null">
        specification,
      </if>
      <if test="primaryUnit != null">
        unit,
      </if>
      <if test="productName != null">
        product_name,
      </if>
      <if test="inspector != null">
        inspector,
      </if>
      <if test="inspectionNum != null">
        inspection_num,
      </if>
      <if test="unqualifiedNum != null">
        unqualified_num,
      </if>
      <if test="inboundQty != null">
        inbound_qty,
      </if>
      <if test="arrivedQty != null">
        arrived_qty,
      </if>
      <if test="purchaseQty != null">
        purchase_qty,
      </if>
      <if test="inspectionCode != null">
        inspection_code,
      </if>
      <if test="inspectionStatus != null">
        inspection_status,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="isQualified != null">
        is_qualified,
      </if>
      <if test="materialMarker != null">
        material_marker,
      </if>
      <if test="supplierCode != null">
        supplier_code,
      </if>
      <if test="supplierName != null">
        supplier_name,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="activitiId != null">
        activiti_id,
      </if>
      <if test="rejectId != null">
        reject_id,
      </if>
      <if test="reviewer != null">
        reviewer,
      </if>
      <if test="auditResult != null">
        audit_result,
      </if>
      <if test="auditTime != null">
        audit_time,
      </if>
      <if test="inspectionRatio != null">
        inspection_ratio,
      </if>
      <if test="qualifiedRate != null">
        qualified_rate,
      </if>
      <if test="inspectType != null">
        inspect_type,
      </if>
      <if test="aqlCode != null">
        aql_code,
      </if>
      <if test="createOn != null">
        create_on,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateOn != null">
        update_on,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="teamCode != null">
        team_code,
      </if>
      <if test="teamName != null">
        team_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="arrivalOrder != null">
        #{arrivalOrder,jdbcType=VARCHAR},
      </if>
      <if test="buyOrder != null">
        #{buyOrder,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=VARCHAR},
      </if>
      <if test="materialCode != null">
        #{materialCode,jdbcType=VARCHAR},
      </if>
      <if test="materialName != null">
        #{materialName,jdbcType=VARCHAR},
      </if>
      <if test="temporaryNo != null">
        #{temporaryNo,jdbcType=VARCHAR},
      </if>
      <if test="specification != null">
        #{specification,jdbcType=VARCHAR},
      </if>
      <if test="primaryUnit != null">
        #{primaryUnit,jdbcType=VARCHAR},
      </if>
      <if test="productName != null">
        #{productName,jdbcType=VARCHAR},
      </if>
      <if test="inspector != null">
        #{inspector,jdbcType=VARCHAR},
      </if>
      <if test="inspectionNum != null">
        #{inspectionNum,jdbcType=INTEGER},
      </if>
      <if test="unqualifiedNum != null">
        #{unqualifiedNum,jdbcType=INTEGER},
      </if>
      <if test="inboundQty != null">
        #{inboundQty,jdbcType=INTEGER},
      </if>
      <if test="arrivedQty != null">
        #{arrivedQty,jdbcType=INTEGER},
      </if>
      <if test="purchaseQty != null">
        #{purchaseQty,jdbcType=INTEGER},
      </if>
      <if test="inspectionCode != null">
        #{inspectionCode,jdbcType=VARCHAR},
      </if>
      <if test="inspectionStatus != null">
        #{inspectionStatus,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="isQualified != null">
        #{isQualified,jdbcType=VARCHAR},
      </if>
      <if test="materialMarker != null">
        #{materialMarker,jdbcType=INTEGER},
      </if>
      <if test="supplierCode != null">
        #{supplierCode,jdbcType=INTEGER},
      </if>
      <if test="supplierName != null">
        #{supplierName,jdbcType=INTEGER},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=VARCHAR},
      </if>
      <if test="activitiId != null">
        #{activitiId,jdbcType=VARCHAR},
      </if>
      <if test="rejectId != null">
        #{rejectId,jdbcType=VARCHAR},
      </if>
      <if test="reviewer != null">
        #{reviewer,jdbcType=VARCHAR},
      </if>
      <if test="auditResult != null">
        #{auditResult,jdbcType=VARCHAR},
      </if>
      <if test="auditTime != null">
        #{auditTime,jdbcType=TIMESTAMP},
      </if>
      <if test="inspectionRatio != null">
        #{inspectionRatio,jdbcType=DECIMAL},
      </if>
      <if test="qualifiedRate != null">
        #{qualifiedRate,jdbcType=DECIMAL},
      </if>
      <if test="inspectType != null">
        #{inspectType,jdbcType=VARCHAR},
      </if>
      <if test="aqlCode != null">
        #{aqlCode,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="teamCode != null">
        #{teamCode,jdbcType=VARCHAR},
      </if>
      <if test="teamName != null">
        #{teamName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_in_material_inspection
    <set>
      <if test="arrivalOrder != null">
        arrival_order=#{arrivalOrder},
      </if>
      <if test="buyOrder != null">
        buy_order=#{buyOrder},
      </if>
      <if test="orderId != null">
        order_id=#{orderId},
      </if>
      <if test="materialCode != null">
        material_code=#{materialCode},
      </if>
      <if test="materialName != null">
        material_name=#{materialName},
      </if>
      <if test="temporaryNo != null">
        temporary_no=#{temporaryNo},
      </if>
      <if test="specification != null">
        specification=#{specification},
      </if>
      <if test="primaryUnit != null">
        unit=#{primaryUnit},
      </if>
      <if test="productName != null">
        product_name=#{productName},
      </if>
      <if test="inspector != null">
        inspector=#{inspector},
      </if>
      <if test="(inspectionNum != null and inspectionNum != '') or inspectionNum==0">
        inspection_num=#{inspectionNum},
      </if>
      <if test="(unqualifiedNum != null and unqualifiedNum != '') or unqualifiedNum==0">
        unqualified_num=#{unqualifiedNum},
      </if>
      <if test="inboundQty != null">
        inbound_qty=#{inboundQty},
      </if>
      <if test="arrivedQty != null">
        arrived_qty=#{arrivedQty},
      </if>
      <if test="purchaseQty != null">
        purchase_qty=#{purchaseQty},
      </if>
      <if test="inspectionCode != null">
        inspection_code=#{inspectionCode},
      </if>
      <if test="inspectionStatus != null">
        inspection_status=#{inspectionStatus},
      </if>
      <if test="status != null">
        status=#{status},
      </if>
      <if test="isQualified != null">
        is_qualified=#{isQualified},
      </if>
      <if test="materialMarker != null">
        material_marker=#{materialMarker},
      </if>
      <if test="supplierCode != null">
        supplier_code=#{supplierCode},
      </if>
      <if test="supplierName != null">
        supplier_name=#{supplierName},
      </if>
      <if test="unitPrice != null">
        unit_price=#{unitPrice},
      </if>
      <if test="activitiId != null">
        activiti_id=#{activitiId},
      </if>
      <if test="rejectId != null">
        reject_id=#{rejectId},
      </if>
      <if test="reviewer != null">
        reviewer=#{reviewer},
      </if>
      <if test="auditResult != null">
        audit_result=#{auditResult},
      </if>
      <if test="auditTime != null">
        audit_time=#{auditTime},
      </if>
      <if test="inspectionRatio != null">
        inspection_ratio=#{inspectionRatio},
      </if>
      <if test="qualifiedRate != null">
        qualified_rate=#{qualifiedRate},
      </if>
      <if test="inspectType != null">
        inspect_type=#{inspectType},
      </if>
      <if test="aqlCode != null">
        aql_code=#{aqlCode},
      </if>
      <if test="createOn != null">
        create_on=#{createOn},
      </if>
      <if test="createBy != null">
        create_by=#{createBy},
      </if>
      <if test="updateOn != null">
        update_on=#{updateOn},
      </if>
      <if test="updateBy != null">
        update_by=#{updateBy},
      </if>
      <if test="remarks != null">
        remarks=#{remarks},
      </if>
      <if test="teamCode != null">
        team_code=#{teamCode},
      </if>
      <if test="teamName != null">
        team_name=#{teamName},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_in_material_inspection
    set product_name = #{productName,jdbcType=VARCHAR},
    pp_no = #{ppNo,jdbcType=VARCHAR},
    inspection_code = #{inspectionCode,jdbcType=VARCHAR},
    inspection_type = #{inspectionType,jdbcType=VARCHAR},
    inspector = #{inspector,jdbcType=VARCHAR},
    inspection_items = #{inspectionItems,jdbcType=VARCHAR},
    create_on = #{createOn,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_on = #{updateOn,jdbcType=TIMESTAMP},
    update_by = #{updateBy,jdbcType=VARCHAR},
    remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeySelectivedetail" parameterType="com.imes.domain.entities.qms.po.MaterialInspectionDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_in_material_inspection_detail
    <set>
      <if test="childItem != null">
        child_item = #{childItem,jdbcType=VARCHAR},
      </if>
      <if test="realityValue != null">
        reality_value = #{realityValue,jdbcType=VARCHAR},
      </if>
      <if test="isQualified != null">
        is_qualified = #{isQualified,jdbcType=VARCHAR},
      </if>
      <if test="inspectionCode != null">
        inspection_code = #{inspectionCode,jdbcType=VARCHAR},
      </if>
      <if test="createOn != null">
        create_on = #{createOn,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="updateOn != null">
        update_on = #{updateOn,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyDetail" parameterType="com.imes.domain.entities.qms.po.MaterialInspectionDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri Mar 13 17:56:22 CST 2020.
    -->
    update qms_in_material_inspection_detail
    set child_item = #{childItem,jdbcType=VARCHAR},
    reality_value = #{realityValue,jdbcType=VARCHAR},
    is_qualified = #{isQualified,jdbcType=VARCHAR},
    inspection_code = #{inspectionCode,jdbcType=VARCHAR},
    create_on = #{createOn,jdbcType=TIMESTAMP},
    create_by = #{createBy,jdbcType=VARCHAR},
    update_on = #{updateOn,jdbcType=TIMESTAMP},
    update_by = #{updateBy,jdbcType=VARCHAR},
    remarks = #{remarks,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateStatusById" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    update
      qms_in_material_inspection
    set
      inspection_status=#{inspectionStatus}
    where
      id=#{id}
  </update>
  <delete id="deleteMaterialListDetail" parameterType="java.lang.String">
    delete from qms_in_material_inspection_detail where parent_id =#{id}
  </delete>
  <delete id="deleteMaterialList" parameterType="java.lang.String">
    delete from qms_in_material_inspection where id =#{id}
  </delete>
  <update id="updateReject" parameterType="java.lang.String">
    update
      qms_in_material_inspection
    set
      inspection_status=#{inspectionStatus}
    where
      id=#{id}
  </update>
  <update id="updateRalationStatus" parameterType="java.lang.String">
    update
    qms_in_material_relation
    set
    inspection_status=#{inspectionStatus}
    where
    parent_id=#{id}
  </update>
  <update id="updateStatusByActivitiId" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    update
      qms_in_material_inspection
    set
      status=#{status},
      inspection_status=#{inspectionStatus},
      reviewer=#{reviewer},
      audit_time=#{auditTime},
      audit_result=#{auditResult},
      remarks=#{remarks}
    where
      activiti_id=#{activitiId}
  </update>
  <update id="updateByActivitiId" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    update
      qms_in_material_inspection
    set
      status=#{status},
      reviewer=#{reviewer},
      inspection_status=#{inspectionStatus},
      reviewer=#{reviewer},
      audit_time=#{auditTime},
      audit_result=#{auditResult},
      remarks=#{remarks}
    where
      activiti_id=#{activitiId}
  </update>
  <select id="queryListByArrivalOrder" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_in_material_inspection
    where arrival_order = #{arrivalOrder}
  </select>
  <delete id="deleteInspection" parameterType="java.lang.String">
    delete from qms_in_material_inspection where arrival_order = #{arrivalOrder}
  </delete>

  <update id="updateById" parameterType="com.imes.domain.entities.qms.po.MaterialInspection">
    update
      qms_in_material_inspection
    set
      status=#{status},
      inspection_status=#{inspectionStatus},
      reviewer=#{reviewer},
      audit_time=#{auditTime},
      audit_result=#{auditResult},
      remarks=#{remarks}
    where
      id=#{id}
  </update>

  <delete id="deleteByOrderId" parameterType="java.lang.String">
    delete from qms_in_material_inspection where order_id =#{orderId}
  </delete>

  <select id="queryListByOrderId" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from qms_in_material_inspection
    where order_id = #{orderId}
  </select>

  <select id="selectByInspectionCode" parameterType="java.lang.String" resultType="java.lang.String">
    select
    order_id
    from qms_in_material_inspection
    where inspection_code = #{inspectionCode}
  </select>

  <update id="setSomeByOrderId" parameterType="com.imes.domain.entities.qms.vo.UpdateReverseInspectionVo">
    update qms_in_material_inspection set arrival_order=#{arrivalOrder},arrived_qty=#{arrivedQty},inbound_qty=#{inboundQty},purchase_qty=#{purchaseQty} where order_id = #{orderId}
  </update>

  <update id="roBackSomeByOrderId" parameterType="java.lang.String">
    update qms_in_material_inspection set arrival_order=null ,arrived_qty=null ,inbound_qty=null ,purchase_qty=null where order_id = #{orderId}
  </update>

  <update id="updateStatusAndInspectionStatus" parameterType="java.lang.String">
    update qms_in_material_inspection set inspection_status = '6' ,status = '1'  where id = #{id}
  </update>

  <update id="setSomeNullByOrderId" parameterType="java.lang.String">
    update qms_in_material_inspection set arrival_order=null ,arrived_qty=null ,inbound_qty=null  where order_id = #{orderId}
  </update>

</mapper>