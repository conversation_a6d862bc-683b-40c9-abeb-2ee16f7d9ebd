<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.DefectCodeManagementDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcQmDefectCode">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        <id column="id" jdbcType="VARCHAR" property="id" />
        <result column="defect_code" jdbcType="VARCHAR" property="defectCode" />
        <result column="defect_name" jdbcType="VARCHAR" property="defectName" />
        <result column="defect_status" jdbcType="VARCHAR" property="defectStatus" />
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn" />
        <result column="create_by" jdbcType="VARCHAR" property="createBy" />
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn" />
        <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
        <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    </resultMap>
    <sql id="Base_Column_List">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 16:24:58 CST 2019.
        -->
        id, defect_code, defect_name, defect_status, create_on, create_by, update_on, update_by,
        remarks
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 16:24:58 CST 2019.
        -->
        select
        <include refid="Base_Column_List" />
        from ppc_qm_defect_code
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="selectStatus"  resultType="string">
        select defect_status from ppc_qm_defect_code group by defect_status
    </select>

    <select id="queryByParam"  resultMap="BaseResultMap" >
        select
        <include refid="Base_Column_List" />
        from ppc_qm_defect_code
        <where>
            <if test="defectCode != null and defectCode != ''">
                and defect_code = #{defectCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryList" resultMap="BaseResultMap" parameterType="java.util.Map">
        select
        <include refid="Base_Column_List"></include>
        from ppc_qm_defect_code
        where 1=1
        <if test="id != null and id != ''">
            and id = #{id,jdbcType=VARCHAR}
        </if>
        <if test="defectCode != null and defectCode != ''">
            and defect_code like concat('%', #{defectCode,jdbcType=VARCHAR},'%')
        </if>
        <if test="defectName != null and defectName != ''">
            and defect_name like concat('%', #{defectName,jdbcType=VARCHAR},'%')
        </if>
        <if test="defectStatus != null and defectStatus != ''">
            and defect_status = #{defectStatus,jdbcType=VARCHAR}
        </if>
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        delete from ppc_qm_defect_code
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcQmDefectCode">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        insert into ppc_qm_defect_code (id, defect_code, defect_name,
        defect_status, create_on,
        create_by, update_on, update_by,
         remarks)
        values (#{id,jdbcType=VARCHAR}, #{defectCode,jdbcType=VARCHAR}, #{defectName,jdbcType=VARCHAR},
        #{defectStatus,jdbcType=VARCHAR},#{createOn,jdbcType=TIMESTAMP},
        #{createBy,jdbcType=VARCHAR}, #{updateOn,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
         #{remarks,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcQmDefectCode">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        insert into ppc_qm_defect_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="defectCode != null">
                defect_code,
            </if>
            <if test="defectName != null">
                defect_name,
            </if>
            <if test="defectStatus != null">
                defect_status,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remarks != null">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="defectCode != null">
                #{defectCode,jdbcType=VARCHAR},
            </if>
            <if test="defectName != null">
                #{defectName,jdbcType=VARCHAR},
            </if>
            <if test="defectStatus != null">
                #{defectStatus,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                #{remarks,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcQmDefectCode">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        update ppc_qm_defect_code
        <set>
            <if test="defectCode != null">
                defect_code = #{defectCode,jdbcType=VARCHAR},
            </if>
            <if test="defectName != null">
                defect_name = #{defectName,jdbcType=VARCHAR},
            </if>
            <if test="defectStatus != null">
                defect_status = #{defectStatus,jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.imes.domain.entities.ppc.po.PpcQmDefectCode">
        <!--
          WARNING - @mbg.generated
          This element is automatically generated by MyBatis Generator, do not modify.
          This element was generated on Fri Oct 18 17:58:44 CST 2019.
        -->
        update ppc_qm_defect_code
        set defect_code = #{defectCode,jdbcType=VARCHAR},
        defect_name = #{defectName,jdbcType=VARCHAR},
        defect_status = #{defectStatus,jdbcType=VARCHAR},
        create_on = #{createOn,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        update_on = #{updateOn,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR},
        remarks = #{remarks,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
</mapper>