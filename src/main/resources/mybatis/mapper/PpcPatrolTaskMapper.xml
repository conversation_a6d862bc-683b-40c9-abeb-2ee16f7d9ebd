<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcPatrolTaskDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcPatrolTask">
        <!--@Table ppc_patrol_task-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_id" jdbcType="VARCHAR" property="planId"/>
        <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="execute_status" jdbcType="VARCHAR" property="executeStatus"/>
        <result column="execute_department_code" jdbcType="VARCHAR" property="executeDepartmentCode"/>
        <result column="execute_department_name" jdbcType="VARCHAR" property="executeDepartmentName"/>
        <result column="execute_user_code" jdbcType="VARCHAR" property="executeUserCode"/>
        <result column="execute_user_name" jdbcType="VARCHAR" property="executeUserName"/>
        <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime"/>
        <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime"/>
        <result column="actual_complete_time" jdbcType="TIMESTAMP" property="actualCompleteTime"/>
        <result column="overdue_status" jdbcType="VARCHAR" property="overdueStatus"/>
        <result column="send_message_status" jdbcType="VARCHAR" property="sendMessageStatus"/>
        <result column="template_table_code" jdbcType="VARCHAR" property="templateTableCode"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>

    <resultMap id="BaseResultMap1" type="com.imes.domain.entities.ppc.vo.PpcPatrolTaskVo">
        <!--@Table ppc_patrol_task-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="plan_id" jdbcType="VARCHAR" property="planId"/>
        <result column="task_no" jdbcType="VARCHAR" property="taskNo"/>
        <result column="task_name" jdbcType="VARCHAR" property="taskName"/>
        <result column="execute_status" jdbcType="VARCHAR" property="executeStatus"/>
        <result column="execute_department_code" jdbcType="VARCHAR" property="executeDepartmentCode"/>
        <result column="execute_department_name" jdbcType="VARCHAR" property="executeDepartmentName"/>
        <result column="execute_user_code" jdbcType="VARCHAR" property="executeUserCode"/>
        <result column="execute_user_name" jdbcType="VARCHAR" property="executeUserName"/>
        <result column="plan_start_time" jdbcType="TIMESTAMP" property="planStartTime"/>
        <result column="plan_end_time" jdbcType="TIMESTAMP" property="planEndTime"/>
        <result column="actual_complete_time" jdbcType="TIMESTAMP" property="actualCompleteTime"/>
        <result column="overdue_status" jdbcType="VARCHAR" property="overdueStatus"/>
        <result column="send_message_status" jdbcType="VARCHAR" property="sendMessageStatus"/>
        <result column="template_table_code" jdbcType="VARCHAR" property="templateTableCode"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="task_effect_hour" jdbcType="INTEGER" property="taskEffectHour"/>
        <result column="template_table_name" jdbcType="VARCHAR" property="templateTableName"/>
        <result column="fine_report_template" jdbcType="VARCHAR" property="fineReportTemplate"/>
    </resultMap>

    <sql id="Base_Column_List">
    id    ,plan_id    ,task_no    ,task_name    ,execute_status    ,execute_department_code    ,execute_department_name    ,execute_user_code    ,execute_user_name    ,plan_start_time    ,plan_end_time    ,actual_complete_time    ,overdue_status    ,send_message_status     ,template_table_code    ,create_on    ,create_by    ,update_on    ,update_by    ,remarks
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_task
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryByTaskNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_task
        where task_no = #{taskNo,jdbcType=VARCHAR}
    </select>

    <select id="findTop1ByPlanIdOrderByCreatedOnDesc" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_task
        where plan_id = #{planId,jdbcType=VARCHAR}
        order by
         create_on desc
    </select>

    <select id="getRecordDate" parameterType="string" resultType="string">
        select left(created_on, 10) createDate from ppc_patrol_task
        where plan_id = #{planId}
        order by created_on desc
    </select>


    <select id="queryByCond" resultMap="BaseResultMap" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolTask">
        select
        <include refid="Base_Column_List"/>
        from ppc_patrol_task
        <where>
            <if test="id != null and id != ''">
                and id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="planId != null and planId != ''">
                and plan_id = #{planId, jdbcType=VARCHAR}
            </if>
            <if test="taskNo != null and taskNo != ''">
                and task_no like concat('%', #{taskNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="taskName != null and taskName != ''">
                and task_name = #{taskName, jdbcType=VARCHAR}
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                and execute_status = #{executeStatus, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                and execute_department_code = #{executeDepartmentCode, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                and execute_department_name = #{executeDepartmentName, jdbcType=VARCHAR}
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                and execute_user_code = #{executeUserCode, jdbcType=VARCHAR}
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                and execute_user_name like concat('%', #{executeUserName,jdbcType=VARCHAR},'%')
            </if>

            <if test="planStartTime != null">
                and plan_start_time  <![CDATA[ >= ]]>  #{planStartTime,jdbcType=VARCHAR}
            </if>
            <if test="planEndTime != null">
                and plan_start_time  <![CDATA[ <= ]]>  #{planEndTime,jdbcType=VARCHAR}
            </if>
            <if test="actualCompleteTime != null">
                and actual_complete_time = #{actualCompleteTime, jdbcType=TIMESTAMP}
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                and overdue_status = #{overdueStatus, jdbcType=VARCHAR}
            </if>
            <if test="sendMessageStatus != null and sendMessageStatus != ''">
                and send_message_status = #{sendMessageStatus, jdbcType=VARCHAR}
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                and template_table_code = #{templateTableCode, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and remarks = #{remarks, jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="queryByMap" resultMap="BaseResultMap1" parameterType="java.lang.String">
        select
        t1.id    ,t1.plan_id    ,t1.task_no    ,t1.task_name    ,t1.execute_status    ,t1.execute_department_code    ,t1.execute_department_name    ,t1.execute_user_code    ,t1.execute_user_name    ,
        t1.plan_start_time    ,t1.plan_end_time    ,t1.actual_complete_time    ,t1.overdue_status    ,t1.send_message_status     ,t1.template_table_code    ,t1.create_on    ,t1.create_by    ,
        t1.update_on    ,t1.update_by    ,t1.remarks,t2.fine_report_template,t2.fine_report_template
        from ppc_patrol_task t1
        left join ppc_patrol_plan p1 on p1.id = t1.plan_id
        left join sys_template_table t2 on p1.template_table_code = t2.table_code
        <where>
            <if test="id != null and id != ''">
                and t1.id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="planId != null and planId != ''">
                and t1.plan_id = #{planId, jdbcType=VARCHAR}
            </if>
            <if test="taskNo != null and taskNo != ''">
                and t1.task_no like concat('%', #{taskNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="taskName != null and taskName != ''">
                and t1.task_name = #{taskName, jdbcType=VARCHAR}
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                and t1.execute_status = #{executeStatus, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                and t1.execute_department_code = #{executeDepartmentCode, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                and t1.execute_department_name = #{executeDepartmentName, jdbcType=VARCHAR}
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                and t1.execute_user_code = #{executeUserCode, jdbcType=VARCHAR}
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                and t1.execute_user_name like concat('%', #{executeUserName,jdbcType=VARCHAR},'%')
            </if>

            <if test="planStartTime != null and planStartTime != ''">
                and t1.plan_start_time  <![CDATA[ >= ]]>  #{planStartTime,jdbcType=VARCHAR}
            </if>
            <if test="planEndTime != null and planEndTime != ''">
                and t1.plan_start_time  <![CDATA[ <= ]]>  #{planEndTime,jdbcType=VARCHAR}
            </if>
            <if test="actualCompleteTime != null">
                and t1.actual_complete_time = #{actualCompleteTime, jdbcType=TIMESTAMP}
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                and t1.overdue_status = #{overdueStatus, jdbcType=VARCHAR}
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                and t1.template_table_code = #{templateTableCode, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and t1.create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and t1.create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and t1.update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and t1.update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and t1.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
        </where>
        order by t1.plan_start_time desc ,t1.create_on desc
    </select>

    <select id="queryByH5" resultMap="BaseResultMap1" parameterType="java.lang.String">

        select
        t1.*,
        t2.task_effect_hour,
        t2.plan_name template_table_name
        from ppc_patrol_task t1
        left join ppc_patrol_plan t2 on t1.plan_id = t2.id
        where 1=1
            <if test="id != null and id != ''">and t1.id = #{id, jdbcType=VARCHAR}
            </if>
        <if test="
        planId != null and planId != ''">and t1.plan_id = #{planId, jdbcType=VARCHAR}
        </if>
            <if test="taskNo != null and taskNo != ''">
                AND CONCAT(t1.task_no,t1.task_name) like concat('%', #{taskNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="taskName != null and taskName != ''">
                and t1.task_name = #{taskName, jdbcType=VARCHAR}
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                and t1.execute_status = #{executeStatus, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                and t1.execute_department_code = #{executeDepartmentCode, jdbcType=VARCHAR}
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                and t1.execute_department_name = #{executeDepartmentName, jdbcType=VARCHAR}
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                and t1.execute_user_code = #{executeUserCode, jdbcType=VARCHAR}
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                and t1.execute_user_name like concat('%', #{executeUserName,jdbcType=VARCHAR},'%')
            </if>

            <if test="planStartTime != null">
                and t1.plan_start_time  <![CDATA[ >= ]]>  #{planStartTime,jdbcType=VARCHAR}
            </if>
            <if test="planEndTime != null">
                and t1.plan_start_time  <![CDATA[ <= ]]>  #{planEndTime,jdbcType=VARCHAR}
            </if>
            <if test="actualCompleteTime != null">
                and t1.actual_complete_time = #{actualCompleteTime, jdbcType=TIMESTAMP}
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                and t1.overdue_status = #{overdueStatus, jdbcType=VARCHAR}
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                and t1.template_table_code = #{templateTableCode, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and t1.create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and t1.create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and t1.update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and t1.update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and t1.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
            <if test="deptCodeList != null">
                and t1.execute_department_code in
                <foreach item="item" index="index" collection="deptCodeList.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

        order by plan_start_time desc
    </select>

    <select id="getPpcPatrolTaskTemplate"
            resultType="com.imes.domain.entities.query.template.ppc.PpcPatrolTaskTemplate">
        select *
        from (
                 SELECT t1.id,
                        t1.plan_id,
                        t1.task_no,
                        t1.task_name,
                        t1.execute_status,
                        t1.execute_department_code,
                        t1.execute_department_name,
                        t1.execute_user_code,
                        t1.execute_user_name,
                        t1.plan_start_time,
                        t1.plan_end_time,
                        t1.actual_complete_time,
                        (
                            CASE

                                WHEN t1.actual_complete_time IS NOT NULL
                                    AND t1.actual_complete_time > t1.plan_end_time THEN
                                    1
                                WHEN t1.actual_complete_time IS NULL
                                    AND NOW() > t1.plan_end_time THEN
                                    1
                                ELSE 0
                                END
                            ) AS overdue_status,
                        t1.send_message_status,
                        t1.template_table_code,
                        t1.create_on,
                        t1.create_by,
                        t1.update_on,
                        t1.update_by,
                        t1.remarks,
                        t2.fine_report_template
                 FROM ppc_patrol_task t1
                          LEFT JOIN ppc_patrol_plan p1 ON p1.id = t1.plan_id
                          LEFT JOIN sys_template_table t2 ON p1.template_table_code = t2.table_code
             ) t1
            ${ew.customSqlSegment}
    </select>

    <select id="getPpcPatrolTaskRecords" resultType="com.imes.domain.entities.ppc.vo.PpcPatrolTaskRecordVo">
        select task.id id,
        plan.id plan_id,
        plan.plan_name,
        plan.template_table_code,
        plan.template_table_name,
        plan.execute_department,
        department.depart_name execute_department_name,
        plan.plan_start_time,
        plan.plan_end_time,
        plan.task_moment_code,
        plan.interval_type,
        plan.interval_qty,
        plan.is_calendar,
        plan.calendar_code,
        sys_calendar.calendar_name,
        plan.task_effect_hour,
        task.task_no,
        task.task_name,
        task.execute_status,
        task.execute_user_code,
        task.plan_start_time task_start_time,
        task.plan_end_time task_end_time,
        user1.user_name execute_user_name,
        task.actual_complete_time,
        (
        CASE
        WHEN task.actual_complete_time IS NOT NULL
        AND task.actual_complete_time > task.plan_end_time THEN
        1
        WHEN task.actual_complete_time IS NULL
        AND NOW() > task.plan_end_time THEN
        1
        ELSE 0
        END
        ) AS overdue_status,
        concat('{', ifnull(tt.custom, ''), '}') custom
        from ppc_patrol_plan plan
        left join ppc_patrol_task task on task.plan_id = plan.id
        left join sys_calendar on plan.calendar_code = sys_calendar.calendar_code
        left join co_department department on department.depart_code = execute_department
        left join pe_user user1 on user1.user_code = task.execute_user_code
        left join (select group_concat(concat('"', item_code, '"'), ':',
        concat('"', ifnull(item_value, ''), '"')) custom,task_id
        from ppc_patrol_task_table
        group by task_id) tt on tt.task_id = task.id
        where 1=1 and plan.template_table_code = #{templateTableCode}
        <if test="taskName != null and taskName != ''">
            and plan.plan_name like concat('%', #{taskName,jdbcType=VARCHAR} ,'%')
        </if>
        <if test="executeDepartmentName != null and executeDepartmentName != ''">
            and department.depart_name like concat('%', #{executeDepartmentName,jdbcType=VARCHAR} ,'%')
        </if>
        <if test="executeUserName != null and executeUserName != ''">
            and user1.user_name like concat('%', #{executeUserName,jdbcType=VARCHAR} ,'%')
        </if>
        <if test="overdueStatusList != null">
            AND (
            CASE
            WHEN task.actual_complete_time IS NOT NULL
            AND task.actual_complete_time > task.plan_end_time THEN
            1
            WHEN task.actual_complete_time IS NULL
            AND NOW() > task.plan_end_time THEN
            1
            ELSE 0
            END
            ) IN
            <foreach collection="overdueStatusList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="startDate != null and startDate != '' ">
            AND task.actual_complete_time <![CDATA[ >= ]]> #{startDate,jdbcType=VARCHAR}
            AND task.actual_complete_time <![CDATA[ <= ]]> #{endDate,jdbcType=VARCHAR}
        </if>
        order by plan.id, task.id desc
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from ppc_patrol_task where id = #{id, jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolTask">
        insert into ppc_patrol_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="planId != null and planId != ''">
                plan_id,
            </if>
            <if test="taskNo != null and taskNo != ''">
                task_no,
            </if>
            <if test="taskName != null and taskName != ''">
                task_name,
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                execute_status,
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                execute_department_code,
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                execute_department_name,
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                execute_user_code,
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                execute_user_name,
            </if>
            <if test="planStartTime != null">
                plan_start_time,
            </if>
            <if test="planEndTime != null">
                plan_end_time,
            </if>
            <if test="actualCompleteTime != null">
                actual_complete_time,
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                overdue_status,
            </if>
            <if test="sendMessageStatus != null and sendMessageStatus != ''">
                send_message_status,
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                template_table_code,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and planId != ''">
                #{id, jdbcType=VARCHAR},
            </if>
            <if test="planId != null and planId != ''">
                #{planId, jdbcType=VARCHAR},
            </if>
            <if test="taskNo != null and taskNo != ''">
                #{taskNo, jdbcType=VARCHAR},
            </if>
            <if test="taskName != null and taskName != ''">
                #{taskName, jdbcType=VARCHAR},
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                #{executeStatus, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                #{executeDepartmentCode, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                #{executeDepartmentName, jdbcType=VARCHAR},
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                #{executeUserCode, jdbcType=VARCHAR},
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                #{executeUserName, jdbcType=VARCHAR},
            </if>
            <if test="planStartTime != null">
                #{planStartTime, jdbcType=TIMESTAMP},
            </if>
            <if test="planEndTime != null">
                #{planEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="actualCompleteTime != null">
                #{actualCompleteTime, jdbcType=TIMESTAMP},
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                #{overdueStatus, jdbcType=VARCHAR},
            </if>
            <if test="sendMessageStatus != null and sendMessageStatus != ''">
                #{sendMessageStatus, jdbcType=VARCHAR},
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                #{templateTableCode, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks, jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcPatrolTask">
        update ppc_patrol_task
        <set>
            <if test="planId != null and planId != ''">
                plan_id = #{planId, jdbcType=VARCHAR},
            </if>
            <if test="taskNo != null and taskNo != ''">
                task_no = #{taskNo, jdbcType=VARCHAR},
            </if>
            <if test="taskName != null and taskName != ''">
                task_name = #{taskName, jdbcType=VARCHAR},
            </if>
            <if test="executeStatus != null and executeStatus != ''">
                execute_status = #{executeStatus, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartmentCode != null and executeDepartmentCode != ''">
                execute_department_code = #{executeDepartmentCode, jdbcType=VARCHAR},
            </if>
            <if test="executeDepartmentName != null and executeDepartmentName != ''">
                execute_department_name = #{executeDepartmentName, jdbcType=VARCHAR},
            </if>
            <if test="executeUserCode != null and executeUserCode != ''">
                execute_user_code = #{executeUserCode, jdbcType=VARCHAR},
            </if>
            <if test="executeUserName != null and executeUserName != ''">
                execute_user_name = #{executeUserName, jdbcType=VARCHAR},
            </if>
            <if test="planStartTime != null">
                plan_start_time = #{planStartTime, jdbcType=TIMESTAMP},
            </if>
            <if test="planEndTime != null">
                plan_end_time = #{planEndTime, jdbcType=TIMESTAMP},
            </if>
            <if test="actualCompleteTime != null">
                actual_complete_time = #{actualCompleteTime, jdbcType=TIMESTAMP},
            </if>
            <if test="overdueStatus != null and overdueStatus != ''">
                overdue_status = #{overdueStatus, jdbcType=VARCHAR},
            </if>
            <if test="sendMessageStatus != null and sendMessageStatus != ''">
                send_message_status = #{sendMessageStatus, jdbcType=VARCHAR},
            </if>
            <if test="templateTableCode != null and templateTableCode != ''">
                template_table_code = #{templateTableCode, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                remarks = #{remarks, jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>

</mapper>