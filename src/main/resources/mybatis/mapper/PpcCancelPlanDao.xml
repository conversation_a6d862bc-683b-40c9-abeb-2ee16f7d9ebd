<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.imes.ppc.dao.PpcCancelPlanDao">
    <resultMap id="BaseResultMap" type="com.imes.domain.entities.ppc.po.PpcCancelPlan">
        <!--@Table ppc_cancel_plan-->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="cancel_no" jdbcType="VARCHAR" property="cancelNo"/>
        <result column="plan_source" jdbcType="VARCHAR" property="planSource"/>
        <result column="document_type" jdbcType="VARCHAR" property="documentType"/>
        <result column="sale_no" jdbcType="VARCHAR" property="saleNo"/>
        <result column="customer_code" jdbcType="VARCHAR" property="customerCode"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="operator_code" jdbcType="VARCHAR" property="operatorCode"/>
        <result column="operator_name" jdbcType="VARCHAR" property="operatorName"/>
        <result column="link_person_code" jdbcType="VARCHAR" property="linkPersonCode"/>
        <result column="link_person_name" jdbcType="VARCHAR" property="linkPersonName"/>
        <result column="link_phone" jdbcType="VARCHAR" property="linkPhone"/>
        <result column="addr_detail" jdbcType="VARCHAR" property="addrDetail"/>
        <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason"/>
        <result column="cancel_date" jdbcType="TIMESTAMP" property="cancelDate"/>
        <result column="delivery_date" jdbcType="TIMESTAMP" property="deliveryDate"/>
        <result column="dep_code" jdbcType="VARCHAR" property="depCode"/>
        <result column="dep_name" jdbcType="VARCHAR" property="depName"/>
        <result column="sales_person_code" jdbcType="VARCHAR" property="salesPersonCode"/>
        <result column="sales_person_name" jdbcType="VARCHAR" property="salesPersonName"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="create_on" jdbcType="TIMESTAMP" property="createOn"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_on" jdbcType="TIMESTAMP" property="updateOn"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="business_status" jdbcType="VARCHAR" property="businessStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        id    ,cancel_no    ,plan_source    ,document_type    ,sale_no    ,customer_code    ,customer_name    ,operator_code    ,operator_name    ,link_person_code    ,link_person_name    ,link_phone    ,addr_detail    ,cancel_reason    ,cancel_date    ,delivery_date    ,dep_code    ,dep_name    ,sales_person_code    ,sales_person_name    ,plan_status    ,create_on    ,create_by    ,update_on    ,update_by    ,remarks    ,business_status
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_cancel_plan
        where id = #{id,jdbcType=VARCHAR}
    </select>

    <select id="queryByCond" resultType="com.imes.domain.entities.ppc.vo.PpcCancelPlanVo"
            parameterType="com.imes.domain.entities.ppc.vo.PpcCancelPlanVo">
        select
        a.id as id,
        a.cancel_no as cancelNo,
        a.plan_source as planSource,
        a.document_type as documentType,
        a.sale_no as saleNo,
        a.customer_code as customerCode,
        a.customer_name as customerName,
        a.operator_code as operatorCode,
        a.operator_name as operatorName,
        a.link_person_code as linkPersonCode,
        a.link_person_name as linkPersonName,
        a.link_phone as linkPhone,
        a.addr_detail as addrDetail,
        a.cancel_reason as cancelReason,
        a.cancel_date as cancelDate,
        a.delivery_date as deliveryDate,
        a.dep_code as depCode,
        a.dep_name as depName,
        a.sales_person_code as salesPersonCode,
        a.sales_person_name as salesPersonName,
        a.plan_status as planStatus,
        a.create_on as createOn,
        a.create_by as createBy,
        a.update_on as updateOn,
        a.update_by as updateBy,
        a.remarks as remarks,
        a.business_status as businessStatus
        from ppc_cancel_plan a
        <where>
            <if test="id != null and id != ''">
                and a.id = #{id, jdbcType=VARCHAR}
            </if>
            <if test="businessStatusList != null and businessStatusList.size()>0">
                and a.business_status in
                <foreach collection="businessStatusList" open="(" item="businessStatus" separator="," close=")">
                    #{businessStatus,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="planSourceLists != null and planSourceLists.size()>0">
                and a.plan_source in
                <foreach collection="planSourceLists" open="(" item="planSourceList" separator="," close=")">
                    #{planSourceList,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="documentTypeLists != null and documentTypeLists.size()>0">
                and a.document_type in
                <foreach collection="documentTypeLists" open="(" item="documentType" separator="," close=")">
                    #{documentType,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="startData != null and startData!= '' ">
                and a.delivery_date  <![CDATA[ >= ]]>  #{startData, jdbcType=VARCHAR}
            </if>
            <if test="endData != null and endData!= ''">
                and a.delivery_date <![CDATA[ <= ]]>  #{endData, jdbcType=VARCHAR}
            </if>
            <if test="planStatusList != null and planStatusList.size()>0">
                and a.plan_status in
                <foreach collection="planStatusList" open="(" item="planStatus" separator="," close=")">
                    #{planStatus,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="cancelNo != null and cancelNo != ''">
                and a.cancel_no like concat('%',#{cancelNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="saleNo != null and saleNo != ''">
                and a.sale_no like concat('%',#{saleNo,jdbcType=VARCHAR},'%')
            </if>
            <if test="customerCode != null and customerCode != ''">
                and a.customer_code = #{customerCode, jdbcType=VARCHAR}
            </if>
            <if test="customerName != null and customerName != ''">
                and a.customer_name like concat('%',#{customerName,jdbcType=VARCHAR},'%')
            </if>
            <if test="operatorCode != null and operatorCode != ''">
                and a.operator_code = #{operatorCode, jdbcType=VARCHAR}
            </if>
            <if test="operatorName != null and operatorName != ''">
                and a.operator_name = #{operatorName, jdbcType=VARCHAR}
            </if>
            <if test="linkPersonCode != null and linkPersonCode != ''">
                and a.link_person_code = #{linkPersonCode, jdbcType=VARCHAR}
            </if>
            <if test="linkPersonName != null and linkPersonName != ''">
                and a.link_person_name like concat('%',#{linkPersonName,jdbcType=VARCHAR},'%')
            </if>
            <if test="linkPhone != null and linkPhone != ''">
                and a.link_phone = #{linkPhone, jdbcType=VARCHAR}
            </if>
            <if test="addrDetail != null and addrDetail != ''">
                and a.addr_detail = #{addrDetail, jdbcType=VARCHAR}
            </if>
            <if test="cancelReason != null and cancelReason != ''">
                and a.cancel_reason = #{cancelReason, jdbcType=VARCHAR}
            </if>
            <if test="cancelDate != null">
                and a.cancel_date = #{cancelDate, jdbcType=TIMESTAMP}
            </if>
            <if test="deliveryDate != null">
                and a.delivery_date = #{deliveryDate, jdbcType=TIMESTAMP}
            </if>
            <if test="depCode != null and depCode != ''">
                and a.dep_code = #{depCode, jdbcType=VARCHAR}
            </if>
            <if test="depName != null and depName != ''">
                and a.dep_name = #{depName, jdbcType=VARCHAR}
            </if>
            <if test="salesPersonCode != null and salesPersonCode != ''">
                and a.sales_person_code = #{salesPersonCode, jdbcType=VARCHAR}
            </if>
            <if test="salesPersonName != null and salesPersonName != ''">
                and a.sales_person_name = #{salesPersonName, jdbcType=VARCHAR}
            </if>
            <if test="planStatus != null and planStatus != ''">
                and a.plan_status = #{planStatus, jdbcType=VARCHAR}
            </if>
            <if test="createOn != null">
                and a.create_on = #{createOn, jdbcType=TIMESTAMP}
            </if>
            <if test="createBy != null and createBy != ''">
                and a.create_by = #{createBy, jdbcType=VARCHAR}
            </if>
            <if test="updateOn != null">
                and a.update_on = #{updateOn, jdbcType=TIMESTAMP}
            </if>
            <if test="updateBy != null and updateBy != ''">
                and a.update_by = #{updateBy, jdbcType=VARCHAR}
            </if>
            <if test="remarks != null and remarks != ''">
                and a.remarks = #{remarks, jdbcType=VARCHAR}
            </if>
            <if test="businessStatus != null and businessStatus != ''">
                and a.business_status = #{businessStatus, jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ppc_cancel_plan
        where id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="queryList" resultType="com.imes.domain.entities.ppc.vo.PpcCancelPlanDetailVo">
        select a.id,
               b.id              as detail_id,
               b.main_id,
               a.cancel_no,
               a.create_on,
               a.sale_no,
               a.plan_source,
               a.document_type,
               a.customer_name,
               a.link_person_name,
               a.link_phone,
               a.dep_name,
               a.cancel_reason,
               a.sales_person_name,
               a.sales_person_code,
               a.addr_detail,
               a.cancel_date,
               a.delivery_date,
               a.operator_name,
               a.remarks,
               a.plan_status,
               a.business_status,
               b.sd_no,
               b.material_name,
               b.material_code,
               b.specification,
               b.model_number,
               b.qty,
               b.cancel_qty,
               b.sum_sended_qty,
               b.sale_main_unit,
               b.business_status as detail_business_status,
               b.remarks         as detail_remarks
        from ppc_cancel_plan a
                 left join ppc_cancel_plan_detail b on b.main_id = a.id
            ${ew.customSqlSegment}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from ppc_cancel_plan
        where id = #{id, jdbcType=VARCHAR}
    </delete>

    <insert id="insertSelective" parameterType="com.imes.domain.entities.ppc.po.PpcCancelPlan">
        insert into ppc_cancel_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">
                id,
            </if>
            <if test="cancelNo != null and cancelNo != ''">
                cancel_no,
            </if>
            <if test="planSource != null and planSource != ''">
                plan_source,
            </if>
            <if test="documentType != null and documentType != ''">
                document_type,
            </if>
            <if test="saleNo != null and saleNo != ''">
                sale_no,
            </if>
            <if test="customerCode != null and customerCode != ''">
                customer_code,
            </if>
            <if test="customerName != null and customerName != ''">
                customer_name,
            </if>
            <if test="operatorCode != null and operatorCode != ''">
                operator_code,
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name,
            </if>
            <if test="linkPersonCode != null and linkPersonCode != ''">
                link_person_code,
            </if>
            <if test="linkPersonName != null and linkPersonName != ''">
                link_person_name,
            </if>
            <if test="linkPhone != null and linkPhone != ''">
                link_phone,
            </if>
            <if test="addrDetail != null and addrDetail != ''">
                addr_detail,
            </if>
            <if test="cancelReason != null and cancelReason != ''">
                cancel_reason,
            </if>
            <if test="cancelDate != null">
                cancel_date,
            </if>
            <if test="deliveryDate != null">
                delivery_date,
            </if>
            <if test="depCode != null and depCode != ''">
                dep_code,
            </if>
            <if test="depName != null and depName != ''">
                dep_name,
            </if>
            <if test="salesPersonCode != null and salesPersonCode != ''">
                sales_person_code,
            </if>
            <if test="salesPersonName != null and salesPersonName != ''">
                sales_person_name,
            </if>
            <if test="planStatus != null and planStatus != ''">
                plan_status,
            </if>
            <if test="createOn != null">
                create_on,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="updateOn != null">
                update_on,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="remarks != null and remarks != ''">
                remarks,
            </if>
            <if test="businessStatus != null and businessStatus != ''">
                business_status
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="cancelNo != null and cancelNo != ''">
                #{cancelNo, jdbcType=VARCHAR},
            </if>
            <if test="planSource != null and planSource != ''">
                #{planSource, jdbcType=VARCHAR},
            </if>
            <if test="documentType != null and documentType != ''">
                #{documentType, jdbcType=VARCHAR},
            </if>
            <if test="saleNo != null and saleNo != ''">
                #{saleNo, jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null and customerCode != ''">
                #{customerCode, jdbcType=VARCHAR},
            </if>
            <if test="customerName != null and customerName != ''">
                #{customerName, jdbcType=VARCHAR},
            </if>
            <if test="operatorCode != null and operatorCode != ''">
                #{operatorCode, jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null and operatorName != ''">
                #{operatorName, jdbcType=VARCHAR},
            </if>
            <if test="linkPersonCode != null and linkPersonCode != ''">
                #{linkPersonCode, jdbcType=VARCHAR},
            </if>
            <if test="linkPersonName != null and linkPersonName != ''">
                #{linkPersonName, jdbcType=VARCHAR},
            </if>
            <if test="linkPhone != null and linkPhone != ''">
                #{linkPhone, jdbcType=VARCHAR},
            </if>
            <if test="addrDetail != null and addrDetail != ''">
                #{addrDetail, jdbcType=VARCHAR},
            </if>
            <if test="cancelReason != null and cancelReason != ''">
                #{cancelReason, jdbcType=VARCHAR},
            </if>
            <if test="cancelDate != null">
                #{cancelDate, jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryDate != null">
                #{deliveryDate, jdbcType=TIMESTAMP},
            </if>
            <if test="depCode != null and depCode != ''">
                #{depCode, jdbcType=VARCHAR},
            </if>
            <if test="depName != null and depName != ''">
                #{depName, jdbcType=VARCHAR},
            </if>
            <if test="salesPersonCode != null and salesPersonCode != ''">
                #{salesPersonCode, jdbcType=VARCHAR},
            </if>
            <if test="salesPersonName != null and salesPersonName != ''">
                #{salesPersonName, jdbcType=VARCHAR},
            </if>
            <if test="planStatus != null and planStatus != ''">
                #{planStatus, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null and remarks != ''">
                #{remarks, jdbcType=VARCHAR},
            </if>
            <if test="businessStatus != null and businessStatus != ''">
                #{businessStatus, jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <insert id="insert" parameterType="com.imes.domain.entities.ppc.po.PpcCancelPlan">
        insert into ppc_cancel_plan
        (id,
         cancel_no,
         plan_source,
         document_type,
         sale_no,
         customer_code,
         customer_name,
         operator_code,
         operator_name,
         link_person_code,
         link_person_name,
         link_phone,
         addr_detail,
         cancel_reason,
         cancel_date,
         delivery_date,
         dep_code,
         dep_name,
         sales_person_code,
         sales_person_name,
         plan_status,
         create_on,
         create_by,
         update_on,
         update_by,
         remarks,
         business_status)
        values (#{id, jdbcType=VARCHAR},
                #{cancelNo, jdbcType=VARCHAR},
                #{planSource, jdbcType=VARCHAR},
                #{documentType, jdbcType=VARCHAR},
                #{saleNo, jdbcType=VARCHAR},
                #{customerCode, jdbcType=VARCHAR},
                #{customerName, jdbcType=VARCHAR},
                #{operatorCode, jdbcType=VARCHAR},
                #{operatorName, jdbcType=VARCHAR},
                #{linkPersonCode, jdbcType=VARCHAR},
                #{linkPersonName, jdbcType=VARCHAR},
                #{linkPhone, jdbcType=VARCHAR},
                #{addrDetail, jdbcType=VARCHAR},
                #{cancelReason, jdbcType=VARCHAR},
                #{cancelDate, jdbcType=TIMESTAMP},
                #{deliveryDate, jdbcType=TIMESTAMP},
                #{depCode, jdbcType=VARCHAR},
                #{depName, jdbcType=VARCHAR},
                #{salesPersonCode, jdbcType=VARCHAR},
                #{salesPersonName, jdbcType=VARCHAR},
                #{planStatus, jdbcType=VARCHAR},
                #{createOn, jdbcType=TIMESTAMP},
                #{createBy, jdbcType=VARCHAR},
                #{updateOn, jdbcType=TIMESTAMP},
                #{updateBy, jdbcType=VARCHAR},
                #{remarks, jdbcType=VARCHAR},
                #{businessStatus, jdbcType=VARCHAR})
    </insert>

    <insert id="insertBatchList" parameterType="java.util.List">
        insert into ppc_cancel_plan
        ( id,
        cancel_no,
        plan_source,
        document_type,
        sale_no,
        customer_code,
        customer_name,
        operator_code,
        operator_name,
        link_person_code,
        link_person_name,
        link_phone,
        addr_detail,
        cancel_reason,
        cancel_date,
        delivery_date,
        dep_code,
        dep_name,
        sales_person_code,
        sales_person_name,
        plan_status,
        create_on,
        create_by,
        update_on,
        update_by,
        remarks,
        business_status
        ) values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id, jdbcType=VARCHAR},
            #{item.cancelNo, jdbcType=VARCHAR},
            #{item.planSource, jdbcType=VARCHAR},
            #{item.documentType, jdbcType=VARCHAR},
            #{item.saleNo, jdbcType=VARCHAR},
            #{item.customerCode, jdbcType=VARCHAR},
            #{item.customerName, jdbcType=VARCHAR},
            #{item.operatorCode, jdbcType=VARCHAR},
            #{item.operatorName, jdbcType=VARCHAR},
            #{item.linkPersonCode, jdbcType=VARCHAR},
            #{item.linkPersonName, jdbcType=VARCHAR},
            #{item.linkPhone, jdbcType=VARCHAR},
            #{item.addrDetail, jdbcType=VARCHAR},
            #{item.cancelReason, jdbcType=VARCHAR},
            #{item.cancelDate, jdbcType=TIMESTAMP},
            #{item.deliveryDate, jdbcType=TIMESTAMP},
            #{item.depCode, jdbcType=VARCHAR},
            #{item.depName, jdbcType=VARCHAR},
            #{item.salesPersonCode, jdbcType=VARCHAR},
            #{item.salesPersonName, jdbcType=VARCHAR},
            #{item.planStatus, jdbcType=VARCHAR},
            #{item.createOn, jdbcType=TIMESTAMP},
            #{item.createBy, jdbcType=VARCHAR},
            #{item.updateOn, jdbcType=TIMESTAMP},
            #{item.updateBy, jdbcType=VARCHAR},
            #{item.remarks, jdbcType=VARCHAR},
            #{item.businessStatus, jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="com.imes.domain.entities.ppc.po.PpcCancelPlan">
        update ppc_cancel_plan
        <set>
            <if test="cancelNo != null">
                cancel_no = #{cancelNo, jdbcType=VARCHAR},
            </if>
            <if test="planSource != null">
                plan_source = #{planSource, jdbcType=VARCHAR},
            </if>
            <if test="documentType != null">
                document_type = #{documentType, jdbcType=VARCHAR},
            </if>
            <if test="saleNo != null">
                sale_no = #{saleNo, jdbcType=VARCHAR},
            </if>
            <if test="customerCode != null">
                customer_code = #{customerCode, jdbcType=VARCHAR},
            </if>
            <if test="customerName != null">
                customer_name = #{customerName, jdbcType=VARCHAR},
            </if>
            <if test="operatorCode != null">
                operator_code = #{operatorCode, jdbcType=VARCHAR},
            </if>
            <if test="operatorName != null">
                operator_name = #{operatorName, jdbcType=VARCHAR},
            </if>
            <if test="linkPersonCode != null">
                link_person_code = #{linkPersonCode, jdbcType=VARCHAR},
            </if>
            <if test="linkPersonName != null">
                link_person_name = #{linkPersonName, jdbcType=VARCHAR},
            </if>
            <if test="linkPhone != null">
                link_phone = #{linkPhone, jdbcType=VARCHAR},
            </if>
            <if test="addrDetail != null">
                addr_detail = #{addrDetail, jdbcType=VARCHAR},
            </if>
            <if test="cancelReason != null">
                cancel_reason = #{cancelReason, jdbcType=VARCHAR},
            </if>
            <if test="cancelDate != null">
                cancel_date = #{cancelDate, jdbcType=TIMESTAMP},
            </if>
            <if test="deliveryDate != null">
                delivery_date = #{deliveryDate, jdbcType=TIMESTAMP},
            </if>
            <if test="depCode != null">
                dep_code = #{depCode, jdbcType=VARCHAR},
            </if>
            <if test="depName != null">
                dep_name = #{depName, jdbcType=VARCHAR},
            </if>
            <if test="salesPersonCode != null">
                sales_person_code = #{salesPersonCode, jdbcType=VARCHAR},
            </if>
            <if test="salesPersonName != null">
                sales_person_name = #{salesPersonName, jdbcType=VARCHAR},
            </if>
            <if test="planStatus != null">
                plan_status = #{planStatus, jdbcType=VARCHAR},
            </if>
            <if test="createOn != null">
                create_on = #{createOn, jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy, jdbcType=VARCHAR},
            </if>
            <if test="updateOn != null">
                update_on = #{updateOn, jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy, jdbcType=VARCHAR},
            </if>
            <if test="remarks != null">
                remarks = #{remarks, jdbcType=VARCHAR},
            </if>
            <if test="businessStatus != null">
                business_status = #{businessStatus, jdbcType=VARCHAR}
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <update id="updateStatusByIds">
        update ppc_cancel_plan
        set plan_status = #{status},update_by=#{userCode},update_on=#{nowTime19}
        where
        id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <update id="updateBusStatusByIds">
        update ppc_cancel_plan
        set business_status =#{businessStatus},update_on =#{nowTime19},update_by =#{userCode}
        where id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>
    </update>

    <delete id="deleteByIds">
        delete
        from ppc_cancel_plan
        where
        id in
        <foreach collection="idList" open="(" item="id" separator="," close=")">
            #{id,jdbcType=VARCHAR}
        </foreach>

    </delete>
</mapper>