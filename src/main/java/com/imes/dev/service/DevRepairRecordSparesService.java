package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevRepairRecordSpares;
import com.imes.domain.entities.query.template.dev.DevRepairSpareRecordQueryVo;

import java.util.List;
import java.util.Map;

public interface DevRepairRecordSparesService {

    void businessSaveSpare(List<DevRepairRecordSpares> list) throws Exception;

    void businessUpdateSpare(List<DevRepairRecordSpares> list);

    Integer deleteByPrimaryKey(String id) throws Exception;

    Integer save(DevRepairRecordSpares record) throws Exception;

    DevRepairRecordSpares selectById(String id);

    Integer update(DevRepairRecordSpares record) throws Exception;

    List<DevRepairRecordSpares> selectByCond(Map<String, Object> cond) throws Exception;

    List<DevRepairRecordSpares> findAllByCond(Map<String, Object> cond);

    List<DevRepairRecordSpares> findByConditionLifeCycle(Map<String, Object> cond);

    /**
     * 批量保存
     * */
    void saveAll(List<DevRepairRecordSpares> sparesList);

    Integer batchSave(List<DevRepairRecordSpares> records) throws Exception;

    List<DevRepairRecordSpares> findAll(DevRepairSpareRecordQueryVo queryVo);

    void deleteByRepairNoAndType(String recordId, String repairNo, String receiveType);
}
