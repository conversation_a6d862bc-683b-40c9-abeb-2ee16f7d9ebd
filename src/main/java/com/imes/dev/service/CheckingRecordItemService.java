package com.imes.dev.service;


import com.imes.common.exception.CommonException;
import com.imes.domain.entities.dev.po.CheckingRecordItem;
import com.imes.domain.entities.dev.po.EquipFilePO;
import com.imes.domain.entities.dev.vo.CheckingRecordItemVO;
import com.imes.domain.entities.dev.vo.CommonRecordItemVO;
import com.imes.domain.entities.dev.vo.CommonRecordVO;

import java.util.List;

public interface CheckingRecordItemService {


    CheckingRecordItem findRecordItem(String id) throws Exception;

    List<List<CheckingRecordItem>> findGourpByPart(String recordNo, String devCode) throws Exception;

    void add(CheckingRecordItem checkingRecordItem) throws Exception;

    void update(CheckingRecordItem item) throws Exception;

    void submitBatch(List<CheckingRecordItem> itemList) throws Exception;

    void deleteById(String id) throws Exception;

    CheckingRecordItem findById(String id);

    List<CheckingRecordItem> findAll(CheckingRecordItem item);

    List<CommonRecordItemVO> findGroupByDevCode(String checkingRecordNo) throws Exception;

    void savePhoto(EquipFilePO equipFile, String id, String photo) throws CommonException;

    int report2Ops(List<CheckingRecordItemVO> itemVOs) throws Exception;

    List<CommonRecordVO> selectRecordGroupByCode(String devCode);

    List<CheckingRecordItem> findByDevCodeAndRecordNos(String devCode, List<String> recordNos);

    void cancleFileUpload(String itemId, String fileId)throws Exception;


}
