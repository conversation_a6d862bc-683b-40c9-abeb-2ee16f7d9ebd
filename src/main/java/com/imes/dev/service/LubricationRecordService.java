/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.domain.entities.dev.po.LubricationRecord;
import com.imes.domain.entities.dev.vo.DevRecordVo;
import com.imes.domain.entities.query.template.dev.DevLubricationRecordQueryVo;

import java.util.List;
import java.util.Map;

public interface LubricationRecordService {


    long getRecordTotal();

    /**
     * 润滑确认
     * @param id
     */
    void confirm(String id) throws Exception; 

    void update(LubricationRecord record) throws Exception;

    /**
     * 变更主单
     * */
    void modifyRecord(LubricationRecord record) throws Exception; 

    LubricationRecord findByRecordNo(String recordNo); 
    
    LubricationRecord findByIdAndClaimSign(String id, Integer claimSign); 

    /**
     * 认领任务,将设备全部认领
     * */
    void claimRecord(LubricationRecord record, String executor, String executorName) throws Exception; 

    List<LubricationRecord> getByUserAndStatus(DevRecordVo recordVo); 

    /**
     * 保存以后返回更新的数据
     * */
    LubricationRecord replaceData(LubricationRecord record); 


    Map<String, Object> findLubricationRecordNum () throws Exception; 

    List<LubricationRecord> queryRecordByCond(DevLubricationRecordQueryVo cond); 

    /**
     * 更新报修状态
     * */
    void updateIsReport(Integer isReport, String recordNo); 

}
