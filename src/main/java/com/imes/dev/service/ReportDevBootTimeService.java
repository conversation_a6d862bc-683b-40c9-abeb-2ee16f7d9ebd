package com.imes.dev.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imes.domain.entities.dev.po.ReportDevBootTime;
import com.imes.domain.entities.query.template.dev.ReportDevBootTimeSearchVo;

import java.util.List;

/**
 * 开机时长报表(ReportDevBootTime)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-19 16:30:22
 */
public interface ReportDevBootTimeService extends IService<ReportDevBootTime> {
    List<ReportDevBootTimeSearchVo> queryList(ReportDevBootTimeSearchVo vo) throws Exception;

    String insert(ReportDevBootTime reportDevBootTime) throws Exception;

    Integer update(ReportDevBootTime reportDevBootTime) throws Exception;

    Integer batchDelete(List<String> idList) throws Exception;

    void batchReplaceInto(List<ReportDevBootTime> list);

    List<String> findDevCodeIn(List<String> devCodeList , String startTime);
    List<ReportDevBootTime> selectReportDevBootTimeInCode(List<String> devCodeList ,String startTime);
}
