/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.common.exception.CommonException;
import com.imes.domain.entities.dev.po.EquipFilePO;
import com.imes.domain.entities.dev.po.LubricationRecordItem;
import com.imes.domain.entities.dev.vo.CommonRecordItemVO;
import com.imes.domain.entities.dev.vo.CommonRecordVO;
import com.imes.domain.entities.dev.vo.LubricationRecordItemVO;

import java.util.List;

public interface LubricationRecordItemService {

    LubricationRecordItem findRecordItem(String id) throws Exception;

    List<List<LubricationRecordItem>> findGourpByPart(String recordNo, String devCode) throws Exception;

    /**
     * 更新
     * 1、润滑在每个子任务提交时判断是否还有未完成的。
     * 2、润滑在子任务提交时有异常则润滑结果有异常
     * 3、实际开始时间为第一个润滑项目提交时间，结束时间为最后一个提交时间
     */
    void update(LubricationRecordItem item) throws Exception;

    /**
     * 批量提交
     * */
    void submitBatch(List<LubricationRecordItem> itemList) throws Exception;

    /**
     * 查询列表
     */
    List<LubricationRecordItem> findAll(LubricationRecordItem params); 

    List<CommonRecordItemVO> findGroupByDevCode(String inspectionRecordNo); 

    List<LubricationRecordItem> getRecordByDevNoAndRecordNo(String recordNo, String devCode); 

    void savePhoto(EquipFilePO equipFile, String id) throws CommonException; 

    List<CommonRecordVO> selectRecordGroupByCode(String devCode); 

    int report2Ops(List<LubricationRecordItemVO> itemVOs) throws Exception;

    LubricationRecordItem findById(String id); 

    void cancleFileUpload(String itemId, String fileId)throws Exception;

    List<LubricationRecordItem> findByDevCodeAndRecordNos(String devCode, List<String> recordNos);
}
