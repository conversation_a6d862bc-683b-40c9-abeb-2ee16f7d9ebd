package com.imes.dev.service;


import com.imes.domain.entities.dev.po.MaintainTempItem;
import com.imes.domain.entities.dev.vo.CommonTempItemVO;
import com.imes.domain.entities.dev.vo.MaintainTempItemVO;

import java.util.List;


public interface MaintainTempItemService {

    void saveAll(List<MaintainTempItem> items) throws Exception;

    void deleteById(String id);

    int deleteByDevNoAndTempNo(MaintainTempItem item);

    List<MaintainTempItemVO> findAllByCond(MaintainTempItem params);

    List<CommonTempItemVO> getTempGroupByDev(String tempNo);

    Integer deleteByTempNos(String[] tempNos);

    Object appFindAllMaintainInfo(String tempNo);
}
