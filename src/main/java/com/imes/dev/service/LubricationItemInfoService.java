/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.domain.entities.dev.po.LubricationItemInfo;
import com.imes.domain.entities.query.plugin.imports.template.dev.DevLubricationItemInfoWholeAddImportVo;
import com.imes.domain.entities.query.template.dev.DevLubricationItemInfoQueryVo;

import java.util.List;

public interface LubricationItemInfoService {


    long getLubricationItemTotal();

    List<LubricationItemInfo> findQueryAll(DevLubricationItemInfoQueryVo queryVo);

    List<LubricationItemInfo> findAllByCondition(LubricationItemInfo params);

    /**
     * 新增润滑项
     *
     * @param itemInfo
     */
    LubricationItemInfo save(LubricationItemInfo itemInfo) throws Exception;

    /**
     * 修改润焕项
     *
     * @param itemInfo
     */
    LubricationItemInfo update(LubricationItemInfo itemInfo);

    /**
     * 删除润滑项
     *
     * @param idList
     */
    void delete(List<String> idList) throws Exception;

    /**
     * 导入
     *
     * @return
     */
    void importCheckingItemInfo(List<List<Object>> items) throws Exception;

    List<DevLubricationItemInfoWholeAddImportVo> importAdd(List<DevLubricationItemInfoWholeAddImportVo> importVoList);



}
