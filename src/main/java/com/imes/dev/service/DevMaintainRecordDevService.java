package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevMaintainRecordDev;
import com.imes.domain.entities.query.template.dev.DevMaintainDevQueryVo;

import java.util.List;
/**
 * (DevMaintainRecordDev)表服务接口
 *
 * <AUTHOR> @since 2023-08-07 15:15:33
 */
public interface DevMaintainRecordDevService {

    List<DevMaintainDevQueryVo> queryAll(DevMaintainDevQueryVo queryVo);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevMaintainRecordDev queryById(String id);

    List<DevMaintainRecordDev> queryByCond(DevMaintainRecordDev devMaintainRecordDev);

    /**
     * 修改数据
     *
     * @param devMaintainRecordDev 实例对象
     * @return 实例对象
     */
    int update(DevMaintainRecordDev devMaintainRecordDev);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);


    void payoutDev(List<DevMaintainRecordDev> recordDevList) throws Exception;


    /**
     * 领取设备
     * */
    void claimDev(DevMaintainRecordDev recordDev) throws Exception;


    void antiSubmit(String id);


    /**
     * 变更
     * */
    void modify(List<DevMaintainRecordDev> recordDevList) throws Exception;



}