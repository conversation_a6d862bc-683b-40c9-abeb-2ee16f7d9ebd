package com.imes.dev.service;

import com.imes.common.exception.CommonException;
import com.imes.domain.entities.dev.po.EquipFilePO;
import com.imes.domain.entities.dev.po.MaintainRecordItem;
import com.imes.domain.entities.dev.vo.CommonRecordItemVO;
import com.imes.domain.entities.dev.vo.CommonRecordVO;
import com.imes.domain.entities.dev.vo.MaintainRecordItemVO;

import java.util.List;


public interface MaintainRecordItemService {

    MaintainRecordItem findRecordItem(String id) throws Exception; 

    List<List<MaintainRecordItem>> findGourpByPart(String recordNo, String devCode) throws Exception; 

    void update(MaintainRecordItem item) throws Exception; 
    /**
     * 批量提交
     * */
    void submitBatch(List<MaintainRecordItem> itemList) throws Exception; 

    List<MaintainRecordItem> findAllByCond(MaintainRecordItem param); 

    List<CommonRecordItemVO> findGroupByDevCode(String recordNo); 

    void savePhoto(EquipFilePO equipFile, String id) throws CommonException;

    List<CommonRecordVO> selectRecordGroupByCode(String devCode); 

    int report2Ops(List<MaintainRecordItemVO> itemVOs) throws Exception; 

    MaintainRecordItem findById(String id); 

    void cancleFileUpload(String itemId, String fileId)throws Exception;

    List<MaintainRecordItem> findByDevCodeAndRecordNos(String devCode, List<String> recordNos); 
}
