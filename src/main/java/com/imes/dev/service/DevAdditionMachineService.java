package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevAdditionMachine;

import java.util.List;

public interface DevAdditionMachineService {


    List<DevAdditionMachine> findByCondition(DevAdditionMachine devAdditionMachine);

    void save(DevAdditionMachine devAdditionMachine) throws Exception;

    void delete(String id) throws Exception;

    Integer findByAdditionCodeAndDevCode(String additionCode, String devCode);


}
