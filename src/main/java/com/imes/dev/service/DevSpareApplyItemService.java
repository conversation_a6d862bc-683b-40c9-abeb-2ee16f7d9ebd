package com.imes.dev.service;
import com.imes.domain.entities.dev.po.DevSpareApplyItem;

import java.util.List;
/**
 * 备件领料申请详情表(DevSpareApplyItem)表服务接口
 *
 * <AUTHOR> @since 2023-08-02 17:18:56
 */
public interface DevSpareApplyItemService {

    List<DevSpareApplyItem> findByApplyNos(List<String> applyNos);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevSpareApplyItem queryById(String id);

    List<DevSpareApplyItem> queryByCond(DevSpareApplyItem devSpareApplyItem);

    /**
     * 新增数据
     *
     * @param devSpareApplyItem 实例对象
     * @return 实例对象
     */
    int insert(DevSpareApplyItem devSpareApplyItem);

    /**
     * 修改数据
     *
     * @param devSpareApplyItem 实例对象
     * @return 实例对象
     */
    int update(DevSpareApplyItem devSpareApplyItem);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);


    void saveItemList(List<DevSpareApplyItem> list, String applyNo);

    void deleteByApplyNo(String applyNo);

}