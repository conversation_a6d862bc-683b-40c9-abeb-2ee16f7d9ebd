/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.domain.entities.dev.po.InspectionRecord;
import com.imes.domain.entities.dev.vo.DevRecordVo;
import com.imes.domain.entities.query.template.dev.DevInspectRecordQueryVo;

import java.util.List;
import java.util.Map;

public interface InspectionRecordService {

    long getRecordTotal();

    void update(InspectionRecord inspectionRecord) throws Exception; 

    /**
     * 变更主单
     * */
    void modifyRecord(InspectionRecord record) throws Exception; 

    InspectionRecord findByRecordNo(String recordNo); 

    InspectionRecord findByIdAndClaimSign(String id, Integer claimSign); 

    /**
     * 认领任务,将设备全部认领
     * */
    void claimRecord(InspectionRecord record, String executor, String executorName) throws Exception; 

    List<InspectionRecord> getByUserAndStatus(DevRecordVo recordVo); 

    /**
     * 保存以后返回更新的数据
     * */
    InspectionRecord replaceData(InspectionRecord record); 

    Map<String, Object> findInspectRecordNum () throws Exception;

    List<InspectionRecord> queryRecordByCond(DevInspectRecordQueryVo cond); 
 
    /**
     * 更新报修状态
     * */
    void updateIsReport(Integer isReport, String recordNo); 
}
