package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevCheckingRecordDev;
import com.imes.domain.entities.query.template.dev.DevCheckingDevQueryVo;

import java.util.List;
/**
 * (DevCheckingRecordDev)表服务接口
 *
 * <AUTHOR> @since 2023-08-07 15:11:37
 */
public interface DevCheckingRecordDevService {

    List<DevCheckingDevQueryVo> queryAll(DevCheckingDevQueryVo queryVo);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevCheckingRecordDev queryById(String id);

    List<DevCheckingRecordDev> queryByCond(DevCheckingRecordDev devCheckingRecordDev);


    void payoutDev(List<DevCheckingRecordDev> recordDevList) throws Exception;

    /**
     * 领取设备
     * */
    void claimDev(DevCheckingRecordDev recordDev) throws Exception;

    void antiSubmit(String id);


    /**
     * 变更
     * */
    void modify(List<DevCheckingRecordDev> recordDevList) throws Exception;


    /**
     * 修改数据
     *
     * @param devCheckingRecordDev 实例对象
     * @return 实例对象
     */
    int update(DevCheckingRecordDev devCheckingRecordDev);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);
    
    

}