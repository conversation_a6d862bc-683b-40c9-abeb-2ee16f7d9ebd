package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevMsaCalibrationPlan;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * (DevMsaCalibrationPlan)表服务接口
 *
 * <AUTHOR> @since 2022-03-09 16:06:47
 */
public interface DevMsaCalibrationPlanService {

    List<DevMsaCalibrationPlan> selectByTemplateCode(List<String> codes);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevMsaCalibrationPlan queryById(String id);

    List<DevMsaCalibrationPlan> queryByCond(DevMsaCalibrationPlan devMsaCalibrationPlan);

    /**
     * 新增数据
     *
     * @param devMsaCalibrationPlan 实例对象
     * @return 实例对象
     */
    int insertOrUpdate(DevMsaCalibrationPlan devMsaCalibrationPlan) throws Exception;


    int update(DevMsaCalibrationPlan devMsaCalibrationPlan) throws Exception;



    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);
    


   /**
    * 查询执行中的校准计划 并且小于当前时间
    * */
   List<DevMsaCalibrationPlan> findByStatusAndLessNow(Date now, Integer status);

   /**
    * 查询大于小于当前时间的计划
    * */
    List<DevMsaCalibrationPlan> findByStatusAndLessThan(Date  start,Date end, Integer status);

    int countByExecutorAndStatusNotIn(String executor);

    List<Map<String, String>> getPlanMap();
}