package com.imes.dev.service;

import com.imes.domain.entities.dev.po.DevAttrExtendMaterage;
import com.imes.domain.entities.query.template.dev.DevMeasureQueryVo;

import java.util.Date;
import java.util.List;

public interface DevAttrExtendMaterageService {

    List<DevMeasureQueryVo> queryMeasureQueryVo(DevMeasureQueryVo measureQueryVo);

    int countMsaBook(String departCode);

    int deleteById(String id);

    DevAttrExtendMaterage insert(DevAttrExtendMaterage record) throws Exception;

    DevAttrExtendMaterage selectById(String id);

    DevAttrExtendMaterage updateById(DevAttrExtendMaterage record) throws Exception;

    void deleteByIds(String ids);

    DevAttrExtendMaterage selectByDevCode(String code);

    void deleteByDevCode(String devCode);

    void deleteByDevCodes(List<String> devCodes);

    void updateMaterage(String devCode, String accuracy, String measuringState, Date date);

}