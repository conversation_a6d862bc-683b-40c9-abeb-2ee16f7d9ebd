package com.imes.dev.service;

import com.alibaba.fastjson.JSONObject;
import com.imes.domain.entities.dev.po.DevMsaCalibrationTask;
import com.imes.domain.entities.dev.po.DevMsaCalibrationTaskDevice;
import com.imes.domain.entities.dev.vo.DevMsaCalibrationTaskVO;
import com.imes.domain.entities.query.template.dev.DevMsaTaskQueryVo;
import com.imes.domain.entities.query.template.dev.DevMsaTaskResultQueryVo;

import java.util.List;
/**
 * (DevMsaCalibrationTask)表服务接口
 *
 * <AUTHOR> @since 2022-03-10 10:50:15
 */
public interface DevMsaCalibrationTaskService {

    List<DevMsaCalibrationTask> findByStatus(List<Integer> status);

    /**
     * 根据计划id查询最新的一条任务
     * */
    DevMsaCalibrationTask findByPlanIdLastTask(String planId);

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevMsaCalibrationTask queryById(String id);

    List<DevMsaCalibrationTask> queryByCond(DevMsaCalibrationTask devMsaCalibrationTask);

    List<DevMsaCalibrationTask> findByQueryVo(DevMsaTaskQueryVo queryVo);

    List<DevMsaCalibrationTask> findResultByQueryVo(DevMsaTaskResultQueryVo queryVo);

    /**
     * 新增数据
     *
     * @param devMsaCalibrationTask 实例对象
     * @return 实例对象
     */
    int insert(DevMsaCalibrationTask devMsaCalibrationTask);

    /**
     * 修改数据
     *
     * @param devMsaCalibrationTask 实例对象
     * @return 实例对象
     */
    int update(DevMsaCalibrationTask devMsaCalibrationTask);

    /**
     * 提交
     * */
    DevMsaCalibrationTask submit(DevMsaCalibrationTask task, List<DevMsaCalibrationTaskDevice> taskDeviceList) throws Exception;

    /**
     * 审核
     * */
    DevMsaCalibrationTask approval(JSONObject jsonObject) throws Exception;


    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);
    
    /**
     * 通过Map条件查询
     * @param taskVO 查询字段
     * @return 是否成功
     */
   List<DevMsaCalibrationTask> findListByCondition(DevMsaCalibrationTaskVO taskVO) ;
    
   List<DevMsaCalibrationTask> getByUserAndStatus(List<String> departmantCodes, Integer status, Integer claimSign, String executor);

    void fullCheck(String taskNo) throws Exception;

    int countByExecutorAndStatusNot(String executor);

    void saveAll(List<DevMsaCalibrationTaskDevice> taskDeviceList) throws Exception;

    void submitAll(List<DevMsaCalibrationTaskDevice> taskDeviceList) throws Exception;
}