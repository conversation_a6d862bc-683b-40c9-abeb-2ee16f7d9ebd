package com.imes.dev.service;

import com.alibaba.fastjson.JSONObject;
import com.imes.domain.entities.dev.po.DevRepairDescription;
import com.imes.domain.entities.dev.vo.DevRepairDescriptionFlowVo;
import com.imes.domain.entities.dev.vo.DevRepairDescriptionVO;
import com.imes.domain.entities.dev.vo.RepairDescriptionVO;
import com.imes.domain.entities.query.plugin.imports.template.dev.DevFaultDescriptionDevImportVo;
import com.imes.domain.entities.query.plugin.imports.template.dev.DevFaultDescriptionImportVo;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * 报修描述(DevRepairDescription)表服务接口
 *
 * <AUTHOR> @since 2023-02-23 16:02:50
 */
public interface DevRepairDescriptionService {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevRepairDescription queryById(String id);

    List<DevRepairDescriptionVO> queryByCond(DevRepairDescriptionVO repairDescriptionVO);



    /**
     * 新增数据
     *
     * @param devRepairDescription 实例对象
     * @return 实例对象
     */
    DevRepairDescription insert(DevRepairDescription devRepairDescription) throws Exception;


    void batchSubmit(List<String> idList) throws Exception;


    public void approval(JSONObject jsonObject) throws Exception;

    DevRepairDescriptionFlowVo findByActiviIdAndBusinessKey(String activitiId,
                                                            String businessKey) throws InvocationTargetException, IllegalAccessException, Exception;

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    void deleteById(String id) throws Exception;

    void deleteByIds(List<String> id) throws Exception;

    Map<String, List<String>> findByHseCon();

    List<RepairDescriptionVO> findByDevCodeAndDescription(String devCode, String description);

    void antiSubmit(String id);

    Boolean checkExist(String repairDescription, String id);

    String checkExistByApp(String repairDescription);

    List<RepairDescriptionVO> findRepairKonwledgeByDevCode(String devCode, String description);

    List<DevFaultDescriptionImportVo> checkDecriptionImport(List<DevFaultDescriptionImportVo> importVoList) throws Exception;

    List<DevFaultDescriptionImportVo> imporDecriptiontData(List<DevFaultDescriptionImportVo> importVoList) throws Exception;

    List<DevFaultDescriptionDevImportVo> checkDevImport(List<DevFaultDescriptionDevImportVo> importVoList) throws Exception;

    List<DevFaultDescriptionDevImportVo> imporDevData(List<DevFaultDescriptionDevImportVo> importVoList) throws Exception;

}