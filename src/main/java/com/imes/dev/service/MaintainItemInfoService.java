package com.imes.dev.service;

import com.imes.domain.entities.dev.po.MaintainItemInfo;
import com.imes.domain.entities.dev.vo.MaintainItemInfoVO;
import com.imes.domain.entities.query.plugin.imports.template.dev.DevMaintainItemInfoWholeAddImportVo;
import com.imes.domain.entities.query.template.dev.DevMaintainItemInfoQueryVo;

import java.util.List;

public interface MaintainItemInfoService {

    long getMaintainItemTotal();

    List<MaintainItemInfo> findQueryAll(DevMaintainItemInfoQueryVo queryVo);

    MaintainItemInfo add(MaintainItemInfo info) throws Exception;

    MaintainItemInfo update(MaintainItemInfo info) throws Exception;

    void deleteById(List<String> idList) throws Exception;

    MaintainItemInfo findById(String id);

    List<MaintainItemInfoVO> findAllByCond(MaintainItemInfoVO params);

    /**
     * 导入
     *
     * @return
     */
    void importMaintainItemInfo(List<List<Object>> items) throws Exception;

    List<DevMaintainItemInfoWholeAddImportVo> importAdd(List<DevMaintainItemInfoWholeAddImportVo> importVoList);


}
