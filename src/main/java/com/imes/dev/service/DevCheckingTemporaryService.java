package com.imes.dev.service;


import com.imes.domain.entities.dev.po.DevCheckingTemporary;
import com.imes.domain.entities.query.template.dev.DevCheckTemporaryItemQueryVo;
import com.imes.domain.entities.query.template.dev.DevCheckTemporaryQueryVo;

import java.util.List;
import java.util.Map;

/**
 * (DevCheckingTemporary)表服务实现类
 *
 * <AUTHOR> @since 2022-01-14 10:00:28
 */
public interface DevCheckingTemporaryService {


    /**
     * 临时点检任务名称
     */
    List<Map<String, String>> getPlanMap();


    /**
     * 移动端列表接口
     * */
    List<DevCheckingTemporary> appList(Integer status, String condition, String executor);

    /**
     * 执行
     * */
    int execute(String id);


    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    DevCheckingTemporary queryById(String id);


    List<DevCheckTemporaryQueryVo> findQueryAll(DevCheckTemporaryQueryVo queryVo) throws Exception;


    List<DevCheckTemporaryItemQueryVo> findQueryAllApi(DevCheckTemporaryItemQueryVo queryVo) throws Exception;

    DevCheckTemporaryItemQueryVo findQueryAllDetailApi(String checkingTemporaryNo) throws Exception;

     
    List<DevCheckingTemporary> queryByCond(DevCheckingTemporary devCheckingTemporary);

    /**
     * 新增数据
     *
     * @param devCheckingTemporary 实例对象
     * @return 实例对象
     */
    int insert(DevCheckingTemporary devCheckingTemporary);

    /**
     * 修改数据
     *
     * @param devCheckingTemporary 实例对象
     * @return 实例对象
     */
     
    int update(DevCheckingTemporary devCheckingTemporary);

    /**
     * 通过主键删除数据
     *
     * @param id 主键
     * @return 是否成功
     */
    int deleteById(String id);


    Map<String, Object> findTemporaryCheckRecordNum() throws Exception;

}