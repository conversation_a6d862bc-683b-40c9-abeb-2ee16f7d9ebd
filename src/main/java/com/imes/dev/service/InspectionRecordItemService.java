/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.common.exception.CommonException;
import com.imes.domain.entities.dev.po.EquipFilePO;
import com.imes.domain.entities.dev.po.InspectionRecordItem;
import com.imes.domain.entities.dev.vo.CommonRecordItemVO;
import com.imes.domain.entities.dev.vo.CommonRecordVO;
import com.imes.domain.entities.dev.vo.InspectionRecordItemVO;

import java.util.List;


public interface InspectionRecordItemService {
    
    InspectionRecordItem findRecordItem(String id) throws Exception;

    List<List<InspectionRecordItem>> findGourpByPart(String recordNo, String devCode) throws Exception;

    List<InspectionRecordItem> findAll(InspectionRecordItem params);

    List<CommonRecordItemVO> findGroupByDevCode(String inspectionRecordNo, String devName);

    /**
     * 更新
     * 1、巡检在每个子任务提交时判断是否还有未完成的。
     * 2、巡检在子任务提交时有异常则巡检结果有异常
     * 3、实际开始时间为第一个巡检项目提交时间，结束时间为最后一个提交时间
     */
    void update(InspectionRecordItem item) throws Exception;

    /**
     * 批量提交
     * */
    void submitBatch(List<InspectionRecordItem> itemList) throws Exception;

    List<InspectionRecordItem> getRecordByDevNoAndRecordNo(String inspectionRecordNo, String devCode);

    void savePhoto(EquipFilePO equipFile, String id) throws CommonException;

    List<CommonRecordVO> selectRecordGroupByCode(String devCode);

    int report2Ops(List<InspectionRecordItemVO> itemVOs) throws Exception;

    InspectionRecordItem findById(String id);

    void cancleFileUpload(String itemId, String fileId)throws Exception;

    List<InspectionRecordItem> findByDevCodeAndRecordNos(String devCode, List<String> recordNos);
}
