/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.domain.entities.dev.po.InspectionItemInfo;
import com.imes.domain.entities.query.plugin.imports.template.dev.DevInspectionItemInfoWholeAddImportVo;
import com.imes.domain.entities.query.template.dev.DevInspectionItemInfoQueryVo;

import java.util.List;

public interface InspectionItemInfoService {

    long getInspectionItemTotal(); 

    List<InspectionItemInfo> findQueryAll(DevInspectionItemInfoQueryVo queryVo); 

    List<InspectionItemInfo> findAllByCondition(InspectionItemInfo params);

    InspectionItemInfo save(InspectionItemInfo inspectionItemInfo) throws Exception;
; 

    InspectionItemInfo update(InspectionItemInfo inspectionItemInfo) throws Exception;

    void delete(List<String> idList) throws Exception;

    /**
     * 导入
     *
     * @return
     */
    void importCheckingItemInfo(List<List<Object>> items) throws Exception;

    List<DevInspectionItemInfoWholeAddImportVo> importAdd(List<DevInspectionItemInfoWholeAddImportVo> importVoList); 



}
