package com.imes.dev.service;

import com.aliyun.iot20180120.models.QueryDevicePropertyStatusResponseBody;
import com.imes.domain.entities.dev.vo.DevCustomizedKanbanStatusRateVO;
import com.imes.domain.entities.dev.vo.DevCustomizedKanbanVO;

import java.util.List;

/**
 * <AUTHOR>
 * @version V1.0
 * @date 2022/6/15 17:45
 */
public interface CustomizedKanbanService {

    /**
     * 获取实时数据
     *
     * @param deviceList 设备名称
     * @return 实时数据列表
     */
    List<QueryDevicePropertyStatusResponseBody.QueryDevicePropertyStatusResponseBodyDataListPropertyStatusInfo> getRealData(List<String> deviceList) throws Exception;

    /**
     * 获取日产量
     *
     * @return 日产量
     */
    DevCustomizedKanbanVO getDailyOutput(String deviceName);

    /**
     * 获取时产量
     *
     * @return 24小时产量
     */
    DevCustomizedKanbanVO getHourlyOutput(String deviceName);

    /**
     * 设备态势感知
     *
     * @return 设备态势感知率
     */
    DevCustomizedKanbanStatusRateVO getStatusRate();

    /**
     * 获取七日OEE
     *
     * @return 七日OEE
     */
    DevCustomizedKanbanVO getDailyOee();

    /**
     * 点位写值
     *
     * @param value 值
     * @param tagName 点位名称
     */
    void tagWriteValue(String tagName, Integer value) throws Exception;
}
