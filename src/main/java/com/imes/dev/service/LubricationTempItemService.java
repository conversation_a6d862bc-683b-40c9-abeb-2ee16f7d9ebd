/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.dev.service;

import com.imes.domain.entities.dev.po.LubricationTempItem;
import com.imes.domain.entities.dev.vo.CommonTempItemVO;

import java.util.List;

public interface LubricationTempItemService {

    void saveAll(List<LubricationTempItem> lubricationTempItem) throws Exception;

    void delete(String id);

    void deleteByDevNoAndTempNo(LubricationTempItem item);

    List<CommonTempItemVO> getTempGroupByDev(String tempNo);

    List<LubricationTempItem> findByDevCodeAndTempNo(String tempNo, String devCode);

    Object appFindAllLubricationInfo(String tempNo);
}
