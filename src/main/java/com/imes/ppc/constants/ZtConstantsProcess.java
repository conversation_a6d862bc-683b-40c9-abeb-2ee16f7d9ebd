package com.imes.ppc.constants;

/**
 * So 订单回复模块Constants
 *
 * <AUTHOR>
 */
public interface ZtConstantsProcess {

    public static final String PROCESS_S011_1 = "S011-1";// 开料
    public static final String PROCESS_S011_2 = "S011-2";// 剖板
    public static final String PROCESS_S011_3 = "S011-3";// 切角（双头锯
    public static final String PROCESS_S011_5 = "S011-5";// 开企口
    public static final String PROCESS_S011_4 = "S011-4";// 开料-铣槽
    public static final String PROCESS_S011_6 = "S011-6";// 开企口
    public static final String PROCESS_S011_7 = "S011-7";// 开料(EC/R角)
    public static final String PROCESS_S011_8 = "S011-8";// 剖板(EC/R角)


    public static final String PROCESS_S012_1 = "S012-1";// 冲孔
    public static final String PROCESS_S012_2 = "S012-2";// 冲孔-铣槽
    public static final String PROCESS_S012_3 = "S012-3";// 冲孔(EC/R角)


    public static final String PROCESS_S013_1 = "S013-1";// 焊接-铣槽
    public static final String PROCESS_S013_2 = "S013-2";// 焊接
    public static final String PROCESS_S013_3 = "S013-3";// 搅拌摩擦焊
    public static final String PROCESS_S013_4 = "S013-4";// 机器焊接
//    public static final String PROCESS_S013_5 = "S013-5";// 手工焊接

    public static final String PROCESS_S0131_1 = "S0131-1";// 抛丸喷粉


    public static final String PROCESS_S014_1 = "S014-1";// 编号
    public static final String PROCESS_S014_2 = "S014-2";// 校正
    public static final String PROCESS_S014_3 = "S014-3";// 打磨
    public static final String PROCESS_S014_4 = "S014-4";// 打螺杆孔
    public static final String PROCESS_S014_5 = "S014-5";// 装企口


    public static final String PROCESS_S015_1 = "S015-1";// 分拣
    public static final String PROCESS_S015_2 = "S015-2";// 打包


}
