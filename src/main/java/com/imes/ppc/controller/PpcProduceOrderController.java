package com.imes.ppc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.common.exception.CommonException;
import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.po.PpcOrderChangeProcessFollow;
import com.imes.domain.entities.ppc.po.PpcProduceOrder;
import com.imes.domain.entities.ppc.po.PpcProducePlanAzt;
import com.imes.domain.entities.ppc.po.PpcProduceProcessAzt;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.query.template.ppc.*;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.ppc.constants.OrderEnum;
import com.imes.ppc.dao.PpcProduceOrderDao;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcPieceTeamMonthGroupDetailService;
import com.imes.ppc.service.PpcProduceOrderService;
import com.imes.ppc.service.PpcProducePlanAztService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产工单-志特(PpcProduceOrder)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-06 16:24:34
 */
@RestController
@RequestMapping("/api/ppc/ppcProduceOrder")
@Api(tags = "生产工单-志特")
public class PpcProduceOrderController {
    /**
     * 服务对象
     */
    @Autowired
    private PpcProduceOrderService ppcProduceOrderService;
    @Autowired
    private PpcProducePlanAztService ppcProducePlanAztService;
    @Autowired
    private PpcProduceOrderDao ppcProduceOrderDao;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result<PpcProduceOrder> queryById(String id) {
        return new Result<>(ResultCode.SUCCESS, (ppcProduceOrderService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param ppcProduceOrder 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
        String result = ppcProduceOrderService.insert(ppcProduceOrder);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 新增大工单
     *
     * @param ppcProduceOrder 实体
     * @return 新增结果
     */
    @PostMapping("/insertBig")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insertBig(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
        String result = ppcProduceOrderService.insertBig(ppcProduceOrder);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 新增子工单
     * filterType 1贴片信息非空，2两种钻孔非空，3附属EC非空
     *
     * @return 新增结果
     */
    @PostMapping("/insertSub")
    @ApiOperation(value = "新增子工单", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insertSub(@RequestBody List<String> planAztIdList, @RequestParam() String productNo, @RequestParam() String build,
                            @RequestParam() String partType, @RequestParam() String filterType) throws Exception {
        String orderType = "";
        switch (filterType) {
            case "1":
                orderType = OrderEnum.ORDER_TYPE_SUB_PATCH.getCode();
                break;
            case "2":
                orderType = OrderEnum.ORDER_TYPE_SUB_GROOVE.getCode();
                break;
            case "3":
                orderType = OrderEnum.ORDER_TYPE_SUB_EC_A.getCode();
                break;
            case "4":
                orderType = OrderEnum.ORDER_TYPE_SUB_EC_B.getCode();
                break;
            case "5":
                orderType = OrderEnum.ORDER_TYPE_SUB_R.getCode();
                break;
        }
        ppcProduceOrderService.insertSub(productNo, build, partType, planAztIdList, orderType);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 新增数据
     *
     * @param id id
     * @return 新增结果
     */
    @GetMapping("/getMainAndDetailById")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result getMainAndDetailById(@RequestParam String id,@RequestParam(required = false) String processCode) throws Exception {
        Map result = ppcProduceOrderService.getMainAndDetailById(id, processCode);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 编辑数据
     *
     * @param ppcProduceOrder 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新（单号也可更新）", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
        String id = ppcProduceOrderService.updatePpcProduceOrder(ppcProduceOrder);
        return new Result(ResultCode.SUCCESS, id);
    }


    /**
     * 编辑数据
     *
     * @param ppcProduceOrder 实体
     * @return 编辑结果
     */
    @PostMapping("/updateChangeRemarks")
    @ApiOperation(value = "更新变更进度表备注", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result updateChangeRemarks(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
        UpdateWrapper<PpcProduceOrder> update = new UpdateWrapper<>();
        update.eq("id", ppcProduceOrder.getId());
        update.set("change_remarks", ppcProduceOrder.getRemarks());
        ppcProduceOrderService.update(update);
        return new Result(ResultCode.SUCCESS, ppcProduceOrder.getId());
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcProduceOrderService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }

    /**
     * 删除子工单数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/deleteSubOrder")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteSubOrder(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcProduceOrderService.deleteSubOrder(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    /**
     * 删除子工单行物料数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/deleteSubOrderDetail")
    @ApiOperation(value = "批量删除子工单行物料", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteSubOrderDetail(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcProduceOrderService.deleteSubOrderDetail(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(PpcProduceOrderSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        if (OrderEnum.ORDER_TYPE_FIX.getCode().equals(vo.getOrderType())) {
            vo.setOrderBy("createOn:desc");
        }
        List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        if (!orderIdList.isEmpty()) {
            vo.setId(StringUtils.listToString(orderIdList, ','));
        }
        vo.setOrderId(null);
        vo.setLocalOrderId(null);
        vo.setLocalApplicationId(null);
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.queryList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/queryListStd")
    @ApiOperation(value = "高级查询列表标准件")
    @Transactional(rollbackFor = Exception.class)
    public Result<StatPageResult<PpcProduceOrderSearchVo>> queryListStd(PpcProduceOrderSearchVo vo) throws Exception {
        List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        if (!orderIdList.isEmpty()) {
            vo.setId(StringUtils.listToString(orderIdList, ','));
        }
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.queryListStd(vo);
        return Result.SUCCESS(new StatPageResult<>(page.getTotal(), list, map));
    }

    @GetMapping("/queryListSubWork")
    @ApiOperation(value = "高级查询列表子工单")
    @Transactional(rollbackFor = Exception.class)
    public Result queryListSubWork(PpcProduceOrderSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.queryListSubWork(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/transferStdOrder")
    @ApiOperation(value = "转标准工单")
    @Transactional(rollbackFor = Exception.class)
    public Result transferStdOrder(@RequestParam(value = "planIds") String planIds) throws Exception {
        List<String> planIdList = Arrays.asList(planIds.split(","));
        ppcProduceOrderService.transferStdOrder(planIdList);
        return Result.SUCCESS();
    }


    @GetMapping("/readyReleaseProduct")
    @ApiOperation(value = "准备标准件下达数据")
    @Transactional(rollbackFor = Exception.class)
    public Result readyReleaseProduct(@RequestParam(value = "orderIds") String orderIds) throws Exception {
        List<PpcProduceProcessAzt> result = ppcProduceOrderService.readyReleaseProduct(orderIds);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/releaseProduct")
    @ApiOperation(value = "标准件下达")
    @Transactional(rollbackFor = Exception.class)
    public Result releaseProduct(@RequestParam(value = "orderIds") String orderIds) throws Exception {
        ppcProduceOrderService.releaseProduct(orderIds);
        return Result.SUCCESS();
    }


    @GetMapping("/cancelProduct")
    @ApiOperation(value = "取消生产")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelProduct(@RequestParam(value = "planIds") String planIds) throws Exception {
        ppcProduceOrderService.cancelProduct(planIds);
        return new Result(ResultCode.SUCCESS);
    }



    @GetMapping("/cancelProductOrder")
    @ApiOperation(value = "取消生产工单")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelProductOrder(@RequestParam(value = "orderIds") String orderIds) throws Exception {
        ppcProduceOrderService.cancelProductOrder(orderIds);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/handleRevokeStandardParts")
    @ApiOperation(value = "标准件撤回")
    @Transactional(rollbackFor = Exception.class)
    public Result handleRevokeStandardParts(@RequestParam(value = "orderIds") String orderIds) throws Exception {
        ppcProduceOrderService.handleRevokeStandardParts(orderIds);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/cancelRelease")
    @ApiOperation(value = "取消下达")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelRelease(@RequestParam(value = "orderIds") String orderIds) throws Exception {
        ppcProduceOrderService.cancelRelease(orderIds);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 普通生产、增加、补料单、修改单下达
     * @param ids
     * @return
     * @throws Exception
     */
    @GetMapping("/issueFromOrder")
    @ApiOperation(value = "工单管理页面下达")
    @Transactional(rollbackFor = Exception.class)
    public Result issueFromOrder(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        //校验行物料状态，只有正常状态可以下达
        QueryWrapper<PpcProducePlanAzt> queryWrapper = new QueryWrapper<PpcProducePlanAzt>().in("order_id", list);
        List<PpcProducePlanAzt> ppcProducePlanAztList = ppcProducePlanAztService.list(queryWrapper);
        for (PpcProducePlanAzt azt : ppcProducePlanAztList) {
            if (!"正常".equals(azt.getHandleStatus())) {
                throw new CommonException("工单号为【%s】，行号为【%s】的行物料处于【%s】状态，不可生成子工单", azt.getOrderNo(), azt.getLineNo(), azt.getHandleStatus());
            }
        }
        List<PpcProduceOrder> orderList = new ArrayList<>();
        String orderType="";
        for (String s : list) {
            PpcProduceOrder order = ppcProduceOrderService.issueFromOrder(s);
            orderList.add(order);
            orderType = order.getOrderType();
        }
        if (!OrderEnum.ORDER_TYPE_FIX.getCode().equals(orderType) && !OrderEnum.ORDER_TYPE_BIG.getCode().equals(orderType)) {
            // 反写中间库已备料
            List<String> collect = orderList.stream().map(PpcProduceOrder::getSyncOrderSerialId).collect(Collectors.toList());
            ppcProduceOrderService.writeBackErpBatch(collect, 1);
        }
        return new Result(ResultCode.SUCCESS);
    }
    @GetMapping("/issueFromOrderPrepare")
    @ApiOperation(value = "子工单下达备料")
    @Transactional(rollbackFor = Exception.class)
    public Result issueFromOrderPrepare(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        List<PpcProduceOrder> orderList = new ArrayList<>();
        for (String s : list) {
            PpcProduceOrder order = ppcProduceOrderService.issueFromOrderPrepare(s);
            orderList.add(order);
        }
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/issueBackFromOrder")
    @ApiOperation(value = "工单管理页面反下达")
    @Transactional(rollbackFor = Exception.class)
    public Result issueBackFromOrder(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        List<PpcProduceOrder> orderList = new ArrayList<>();
        String orderType="";
        for (String s : list) {
            PpcProduceOrder order = ppcProduceOrderService.issueBackFromOrder(s);
            orderList.add(order);
            orderType = order.getOrderType();
        }
        if(!OrderEnum.ORDER_TYPE_FIX.getCode().equals(orderType)) {
            // 反写中间库未处理
            List<String> collect = orderList.stream().map(PpcProduceOrder::getSyncOrderSerialId).collect(Collectors.toList());
            ppcProduceOrderService.writeBackErpBatchCancel(collect);
        }
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/getBigProcessListByOrderId")
    @ApiOperation(value = "获取工单的工艺路线的大工序列表，注意有拼装工段，为了备料下达用，如果需要单纯的大工序，请领写")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<PpcRouteLineZtProcessBigAndSmallVo>> getBigProcessListByOrderId(@RequestParam String orderId) throws Exception {
        List<PpcRouteLineZtProcessBigAndSmallVo> voList = ppcProduceOrderService.getBigProcessListByOrderId(orderId);
        return Result.SUCCESS(voList);
    }

    /**
     * 处理根据关键字查询工单的请求
     *
     * @return 符合条件的工单列表
     */
    @GetMapping("/searchWorkOrders")
    public Result searchWorkOrders(@RequestParam(defaultValue = "") String keyWord,@RequestParam(required = false) String projectName,@RequestParam(required = false) String orderNo) {
        return Result.SUCCESS(ppcProduceOrderService.searchWorkOrdersByKeyWord(keyWord,null,null,null,projectName,orderNo));
    }

    /**
     * 处理根据关键字查询工单的请求
     *
     * @return 符合条件的工单列表
     */
    @PostMapping("/getByIdList")
    public Result getByIdList(@RequestBody List<String> idList) {
        if (StringUtils.isNullOrBlank(idList)) {
            return Result.SUCCESS();
        } else {
            return Result.SUCCESS(ppcProduceOrderService.getByIdList(idList));
        }
    }

    /**
     * 处理根据关键字查询工单的请求
     *
     * @return 符合条件的工单列表
     */
    @GetMapping("/ics/searchWorkOrders")
    public Result icsSearchWorkOrders(@RequestParam(defaultValue = "") String keyWord,
                                      @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "0") Integer pageNum,
                                      @RequestParam(required = false) String productNo,@RequestParam(required = false) String productShortName,
                                      @RequestParam(required = false) String build,@RequestParam(required = false) String orderNo
                                      ) {

        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(Integer.valueOf(pageNum), Integer.valueOf(pageSize));
        List<PpcProduceOrder> list = ppcProduceOrderService.searchWorkOrdersByKeyWord(keyWord,productNo,productShortName,build,null,orderNo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }


    @GetMapping("/queryListByProductNoAndPartType")
    @ApiOperation(value = "根据项目号跟构建类型查询工单")
    @Transactional(rollbackFor = Exception.class)
    public Result queryListByProductNoAndPartType(PpcProduceOrder order) throws Exception {
        List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(order.getLocalOrderId(), order.getLocalApplicationId(), order.getOrderId());
        order.setOrderIdIns(StringUtils.list2SrtIn(orderIdList));
        order.setOrderType(StringUtils.list2SrtIn(Arrays.asList(order.getOrderType().split(","))));
        Page page = PageHelper.startPage(order.getPageNum(), order.getPageSize());
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.queryListByProductNoAndPartType(order);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    @Autowired
    FeignService feignService;

    /**
     * @param productNo
     * @param build
     * @param bigProcessCode
     * @param status         10 未派工 / 20已派工 / 30工段完工
     * @param orderType
     * @return
     * @throws Exception
     */
    @GetMapping("/getOrderAndProcessByProductNo")
    @ApiOperation(value = "")
    @Transactional(rollbackFor = Exception.class)
    public Result<PageResult<PpcProduceOrderAndBigProcessVo>> getOrderAndProcessByProductNo(@RequestParam String productNo,
                                                                                            @RequestParam() String build,
                                                                                            @RequestParam() String bigProcessCode,
                                                                                            @RequestParam(required = false) String factoryLineName,
                                                                                            @RequestParam(required = false) String status,
                                                                                            @RequestParam(required = false) String orderType,
                                                                                            @RequestParam(required = false) String localOrderId,
                                                                                            @RequestParam(required = false) String localApplicationId,
                                                                                            @RequestParam(required = false) String orderId,
                                                                                            @RequestParam(required = false) String orderNo,
                                                                                            @RequestParam() int pageSize,
                                                                                            @RequestParam() int pageNum
    ) throws Exception {
        String userCode = RedisUtils.getUserCode();
        String orderIdIn = null;
        if (StringUtils.isNotNullOrBlank(localOrderId) || StringUtils.isNotNullOrBlank(localApplicationId) || StringUtils.isNotNullOrBlank(orderId)) {
            List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId, orderId);
            if (!orderIdList.isEmpty()) {
                orderIdIn = StringUtils.list2SrtIn(orderIdList);
            }
        }
        List<String> teamCodeList = null;
        if (StringUtils.isNotNullOrBlank(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> teamList = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            teamCodeList = teamList.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (teamCodeList.isEmpty()) {
                return Result.SUCCESS(new PageResult<>(0L, new ArrayList<>()));
            }
        }
        Page page = PageHelper.startPage(Integer.valueOf(pageNum), Integer.valueOf(pageSize));
        List<PpcProduceOrderAndBigProcessVo> list = ppcProduceOrderService.getOrderAndProcessByProductNo(orderIdIn, productNo, build, bigProcessCode, orderType, status, userCode, teamCodeList, orderNo,factoryLineName);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }



    /**
     * @return
     * @throws Exception
     */
    @GetMapping("/getAllOrderAndProcessByProductNo")
    @ApiOperation(value = "")
    @Transactional(rollbackFor = Exception.class)
    public Result<PageResult<PpcProduceOrderAndBigProcessVo>> getAllOrderAndProcessByProductNo(
                                                                                            @RequestParam() String bigProcessCode,
                                                                                            @RequestParam() String status,
                                                                                            @RequestParam() int pageSize,
                                                                                            @RequestParam() int pageNum,
                                                                                            @RequestParam(required = false) String factoryLineName,
                                                                                            @RequestParam(required = false) String localOrderId,
                                                                                            @RequestParam(required = false) String localApplicationId,
                                                                                            @RequestParam(required = false) String orderId,
                                                                                            @RequestParam(required = false) String orderNo

    ) throws Exception {
        String userCode = RedisUtils.getUserCode();
        String orderIdIn = null;
        if (StringUtils.isNotNullOrBlank(localOrderId) || StringUtils.isNotNullOrBlank(localApplicationId) || StringUtils.isNotNullOrBlank(orderId)) {
            List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId, orderId);
            if (!orderIdList.isEmpty()) {
                orderIdIn = StringUtils.list2SrtIn(orderIdList);
            }
        }
        List<String> teamCodeList = null;
        if (StringUtils.isNotNullOrBlank(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> teamList = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            teamCodeList = teamList.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (teamCodeList.isEmpty()) {
                return Result.SUCCESS(new PageResult<>(0L, new ArrayList<>()));
            }
        }
        Page page = PageHelper.startPage(Integer.valueOf(pageNum), Integer.valueOf(pageSize));
        List<PpcProduceOrderAndBigProcessVo> list = ppcProduceOrderService.getAllOrderAndProcessByProductNo(status, bigProcessCode, orderIdIn, teamCodeList, orderNo, factoryLineName);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    @GetMapping("/dispatchByOrderIdAndProcessCode")
    @ApiOperation(value = "单个工序派工")
    @Transactional(rollbackFor = Exception.class)
    public Result dispatchByOrderIdAndProcessCode(@RequestParam String orderId, @RequestParam() String processCode, @RequestParam() String teamCode, @RequestParam() String userCode) throws Exception {
        ppcProduceOrderService.dispatchByOrderIdAndProcessCode(orderId, processCode, teamCode, userCode);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/batchDispatchByOrderIdAndProcessCode")
    @ApiOperation(value = "批量派工or更新派工人")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDispatchByOrderIdAndProcessCode(@RequestBody List<Map> list, @RequestParam(required = false) String isChangeExecute) throws Exception {
        ppcProduceOrderService.batchDispatchByOrderIdAndProcessCode(list, isChangeExecute);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/batchDispatchByOrderIdAndProcessCodeForApp")
    @ApiOperation(value = "批量派工")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDispatchByOrderIdAndProcessCodeForApp(@RequestBody BatchDispatchForAppVo vo) throws Exception {
        ppcProduceOrderService.batchDispatchByOrderIdAndProcessCodeForApp(vo);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/checkBatchEndProcessCodeByOrderIdList")
    @ApiOperation(value = "工段完检查")
    @Transactional(rollbackFor = Exception.class)
    public Result checkBatchEndProcessCodeByOrderIdList(@RequestBody List<String> orderIdList, @RequestParam() String bigProcessCode) throws Exception {
        Boolean flag = ppcProduceOrderService.checkBatchEndProcessCodeByOrderIdList(orderIdList, bigProcessCode);
        return new Result(ResultCode.SUCCESS,flag);
    }

    @PostMapping("/batchEndProcessCodeByOrderIdList")
    @ApiOperation(value = "工段完工")
    @Transactional(rollbackFor = Exception.class)
    public Result batchEndProcessCodeByOrderIdList(@RequestBody List<String> orderIdList, @RequestParam() String bigProcessCode) throws Exception {
        ppcProduceOrderService.batchEndProcessCodeByOrderIdList(orderIdList, bigProcessCode);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/appointBigProcessByOrderIdList")
    @ApiOperation(value = "指定下工段")
    @Transactional(rollbackFor = Exception.class)
    public Result appointBigProcessByOrderIdList(@RequestBody List<String> orderIdList, @RequestParam() String bigProcessCode, @RequestParam() String bigProcessName) throws Exception {
        UpdateWrapper<PpcProduceOrder> orderQueryWrapper = new UpdateWrapper<>();
        orderQueryWrapper.in("id", orderIdList);
        orderQueryWrapper.set("appoint_big_process_code", bigProcessCode);
        orderQueryWrapper.set("appoint_big_process_name", bigProcessName);
        ppcProduceOrderService.update(orderQueryWrapper);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/backDispatchByOrderId")
    @ApiOperation(value = "反派工")
    @Transactional(rollbackFor = Exception.class)
    public Result backDispatchByOrderId(@RequestParam String orderIds, @RequestParam String bigProcessCode) throws Exception {
        ppcProduceOrderService.backDispatchByOrderId(orderIds, bigProcessCode);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/teamOperationIcs")
    @ApiOperation(value = "工控端班组作业")
    @Transactional(rollbackFor = Exception.class)
    public Result<StatPageResult<PpcWorkReportSearchVo>> teamOperationIcs(PpcWorkReportSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkReportSearchVo> list = ppcProduceOrderService.teamOperationIcs(vo);
        return Result.SUCCESS(new StatPageResult<>(page.getTotal(), list, map));
    }

    @GetMapping("/teamOperationIcsScanCode")
    @ApiOperation(value = "工控端班组作业扫码")
    @Transactional(rollbackFor = Exception.class)
    public Result teamOperationIcsScanCode(
            @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "0") Integer pageNum,
            @RequestParam(required = false) String localOrderId,
            @RequestParam(required = false) String localApplicationId,
            @RequestParam(required = false) String orderId,
            @RequestParam(required = false) String orderNo) throws Exception {
        List<String> orderIdIn = new ArrayList<>();
        if (StringUtils.isNotNullOrBlank(localOrderId) || StringUtils.isNotNullOrBlank(localApplicationId) || StringUtils.isNotNullOrBlank(orderId)) {
            orderIdIn = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId, orderId);
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcWorkReportSearchVo> list = ppcProduceOrderService.teamOperationIcsScanCode(orderIdIn, orderNo,"0");
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    @GetMapping("/teamOperationIcsScanCodeForApp")
    @ApiOperation(value = "移动端班组作业扫码")
    @Transactional(rollbackFor = Exception.class)
    public Result teamOperationIcsScanCodeForApp(
                                                @RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "0") Integer pageNum,
                                                @RequestParam(required = false) String localOrderId,
                                                 @RequestParam(required = false) String localApplicationId,
                                                 @RequestParam(required = false) String orderId, @RequestParam(required = false) String orderNo) throws Exception {
        List<String> orderIdIn = new ArrayList<>();
        if (StringUtils.isNotNullOrBlank(localOrderId) || StringUtils.isNotNullOrBlank(localApplicationId) || StringUtils.isNotNullOrBlank(orderId)) {
            orderIdIn = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId, orderId);
        }
        if (!StringUtils.isNullOrBlank(orderId)) {
            orderIdIn.addAll(Arrays.asList(orderId.split(",")));
        }
        if (!orderIdIn.isEmpty()) {
            orderIdIn = new ArrayList<>(new HashSet<>(orderIdIn));
        }
        Page page = PageHelper.startPage(pageNum, pageSize, false);
        List<PpcWorkReportSearchVo> list = ppcProduceOrderService.teamOperationIcsScanCodeForApp(orderIdIn, orderNo ,"1");
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    @GetMapping("/teamOperationIcsScanCodeForAppS0131")
    @ApiOperation(value = "移动端班喷粉报工扫码")
    @Transactional(rollbackFor = Exception.class)
    public Result teamOperationIcsScanCodeForAppS0131(@RequestParam() String orderNo) throws Exception {
        List<PpcWorkReportSearchVo> list = ppcProduceOrderService.teamOperationIcsScanCodeForAppS0131( orderNo);
        return Result.SUCCESS(new PageResult<>(Long.valueOf(list.size()), list));
    }


    @GetMapping("/getOrderByVo")
    @Transactional(rollbackFor = Exception.class)
    public Result<PageResult<PpcProduceOrderSearchVo>> getOrderByVo(PpcProduceOrderSearchVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.getOrderByVo(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));

    }


    @GetMapping("/getDispatchedForApp")
    @Transactional(rollbackFor = Exception.class)
    public Result getDispatchedForApp(@RequestParam String productNo, @RequestParam String build,
                                      @RequestParam String bigProcessCode, @RequestParam String status,
                                      @RequestParam(required = false) String factoryLineName,
                                      @RequestParam(required = false) String orderNo,
                                      @RequestParam(required = false) String orderType,
                                      @RequestParam(required = false) String localOrderId,
                                      @RequestParam(required = false) String localApplicationId,
                                      @RequestParam(required = false) String orderId
    ) throws Exception {
        String userCode = RedisUtils.getUserCode();
        List<String> teamCodeList = null;
        if (StringUtils.isNotNullOrBlank(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> teamList = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            teamCodeList = teamList.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (teamCodeList.isEmpty()) {
                return new Result(ResultCode.SUCCESS);
            }
        }
        String orderIdIn = null;
        if (StringUtils.isNotNullOrBlank(localOrderId) || StringUtils.isNotNullOrBlank(localApplicationId) || StringUtils.isNotNullOrBlank(orderId)) {
            List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId, orderId);
            if (!orderIdList.isEmpty()) {
                orderIdIn = StringUtils.list2SrtIn(orderIdList);
            }
        }
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.getDispatchedForApp(orderType, orderIdIn, productNo, build, bigProcessCode, status, orderNo, teamCodeList,factoryLineName);
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/dispatchForApp")
    @ApiOperation(value = "移动端班组长任务管理派工按钮")
    @Transactional(rollbackFor = Exception.class)
    public Result dispatchForApp(@RequestParam String orderId, @RequestParam String bigProcessCode, @RequestParam(required = false) String isDispatch) throws Exception {
        PpcProduceOrderAndBigProcessVo result = ppcProduceOrderService.dispatchForApp(orderId, bigProcessCode, isDispatch);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/confirmDispatchForApp")
    @ApiOperation(value = "动端班组长任务管理派工确定按钮")
    @Transactional(rollbackFor = Exception.class)
    public Result confirmDispatchForApp(@RequestBody AppDispatchConfirmForZhiTe vo) throws Exception {
        ppcProduceOrderService.confirmDispatchForApp(vo);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/sendProjectStoreBack")
    @ApiOperation(value = "撤回生产入库数据")
    @Transactional(rollbackFor = Exception.class)
    public Result sendProjectStoreBack(@RequestParam String orderId) throws Exception {
        ppcProduceOrderService.sendProjectStoreBack(orderId);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/printStandardOrder")
    @ApiOperation(value = "帆软打印标准工单")
    @Transactional(rollbackFor = Exception.class)
    public Result printStandardOrder(@RequestParam String id) throws Exception {
        return new Result(ResultCode.SUCCESS, ppcProduceOrderService.printStandardOrder(id));
    }

    @GetMapping("/printOrdinaryOrder")
    @ApiOperation(value = "帆软打印普通工单")
    @Transactional(rollbackFor = Exception.class)
    public Result printOrdinaryOrder(@RequestParam String id) throws Exception {
        return new Result(ResultCode.SUCCESS, ppcProduceOrderService.printOrdinaryOrder(id));
    }


    @GetMapping("/printSonStandardOrder")
    @ApiOperation(value = "帆软打印子工单")
    @Transactional(rollbackFor = Exception.class)
    public Result printSonStandardOrder(@RequestParam String id) throws Exception {
        return new Result(ResultCode.SUCCESS, ppcProduceOrderService.printSonStandardOrder(id));
    }

    @GetMapping("/printModifyStandardOrder")
    @ApiOperation(value = "帆软打印修改单")
    @Transactional(rollbackFor = Exception.class)
    public Result printModifyStandardOrder(@RequestParam String id) throws Exception {
        return new Result(ResultCode.SUCCESS, ppcProduceOrderService.printModifyStandardOrder(id));
    }


    @GetMapping("/inspectByOrderId")
    @ApiOperation(value = "勾选工单生成全检")
    @Transactional(rollbackFor = Exception.class)
    public Result inspectByOrderId(@RequestParam String orderId, @RequestParam String bigProcessCode, @RequestParam Integer isInspectCount) throws Exception {
        ppcProduceOrderService.inspectByOrderId(orderId, bigProcessCode, isInspectCount);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/updateBatchNoAndIsNewMaterial")
    @ApiOperation(value = "动端班组长任务管理派工确定按钮")
    @Transactional(rollbackFor = Exception.class)
    public Result updateStatusAndIsNewMaterial(@RequestBody PpcProduceOrder vo) throws Exception {
        ppcProduceOrderService.updateStatusAndIsNewMaterial(vo);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/cancelOrderClose")
    @ApiOperation(value = "取消单关闭处理")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelOrderClose(@RequestParam String id) throws Exception {
        ppcProduceOrderService.cancelOrderClose(id);
        return new Result(ResultCode.SUCCESS);
    }
    @GetMapping("/cancelOrderCloseBack")
    @ApiOperation(value = "取消单反关闭处理")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelOrderCloseBack(@RequestParam String id) throws Exception {
        ppcProduceOrderService.cancelOrderCloseBack(id);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/fixComplete")
    @ApiOperation(value = "修改单完工")
    @Transactional(rollbackFor = Exception.class)
    public Result fixComplete(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        for (String s : list) {
            PpcProduceOrder order = ppcProduceOrderService.fixComplete(s);
        }
        return new Result(ResultCode.SUCCESS);
    }
    @GetMapping("/fixCompleteBack")
    @ApiOperation(value = "修改单反完工")
    @Transactional(rollbackFor = Exception.class)
    public Result fixCompleteBack(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        for (String s : list) {
            PpcProduceOrder order = ppcProduceOrderService.fixCompleteBack(s);
        }
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/getOrderProgress")
    @ApiOperation(value = "工控机-获取工单进度")
    @Transactional(rollbackFor = Exception.class)
    public Result queryStandardPartsForIcs(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                           @RequestParam(required = false) String orderNo,
                                           @RequestParam(required = false) String productNo,
                                           @RequestParam(required = false) String productName,
                                           @RequestParam(required = false) String build,
                                           @RequestParam(required = false) String factoryLineName,
                                           @RequestParam(required = false) String localOrderId,
                                           @RequestParam(required = false) String localApplicationId,
                                           @RequestParam(required = false) String orderId
    ) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        List<String> orderIdList = ppcProduceOrderService.getIdListByMuiltyScan(localOrderId, localApplicationId,orderId);
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProduceOrderSearchVo> list = ppcProduceOrderService.getOrderProgress(orderIdList,orderNo,productNo,productName,build,factoryLineName);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }



    @GetMapping("/jumpBigProcessCode")
    @ApiOperation(value = "工控机-获取工单进度")
    @Transactional(rollbackFor = Exception.class)
    public Result jumpBigProcessCode(@RequestParam(required = false) String orderId,@RequestParam(required = false) String bigProcessCode) throws Exception {
        ppcProduceOrderService.jumpBigProcessCode(orderId,bigProcessCode);
        return new Result(ResultCode.SUCCESS);
    }



    /**
     * 更新单号
     *
     * @param ppcProduceOrder 实体
     * @return 更新单号
     */
    @PostMapping("/updateOrderNo")
    @ApiOperation(value = "更新单号", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result updateOrderNo(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
         ppcProduceOrderService.updateOrderNo(ppcProduceOrder);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 修改/取消单下发企口压槽接口
     *
     * @param id 修改/取消单id
     * @return 修改/取消单下发企口压槽接口
     */
    @GetMapping("/issueChangeGrooveOrder")
    @ApiOperation(value = "修改/取消单下发企口压槽接口", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result issueChangeGrooveOrder(@RequestParam() String id) throws Exception {
        ppcProduceOrderService.issueChangeGrooveOrder(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 撤回修改/取消单下发企口压槽接口
     *
     * @param id 修改单id
     * @return 取消修改单下发企口压槽接口
     */
    @GetMapping("/issueChangeGrooveOrderBack")
    @ApiOperation(value = "取消修改单下发企口压槽接口", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result issueChangeGrooveOrderBack(@RequestParam() String id) throws Exception {
        ppcProduceOrderService.issueChangeGrooveOrderBack(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 更新修改单产线
     */
    @PostMapping("/updateChangeOrderInfo")
    @ApiOperation(value = "更新修改单产线", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result updateChangeOrderInfo(@RequestBody PpcProduceOrder ppcProduceOrder) throws Exception {
        ppcProduceOrderService.updateChangeOrderInfo(ppcProduceOrder);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 特殊工单下发
     */
    @GetMapping("/getSpecialOrder")
    @ApiOperation(value = "特殊工单下发查询", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result getSpecialOrder() throws Exception {
        List<Map> specialOrder = ppcProduceOrderService.getSpecialOrder();
        return new Result(ResultCode.SUCCESS, specialOrder);
    }


    /**
     * 工单变更进度跟进表
     */
    @GetMapping("/getChangeOrderProgress")
    @ApiOperation(value = "工单变更进度跟进表", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getChangeOrderProgress(PpcOrderChangeProcessFollowSearchVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcOrderChangeProcessFollow> changeOrderProgress = ppcProduceOrderService.getChangeOrderProgress(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), changeOrderProgress));
    }


    /**
     * 工单变更进度跟进表
     */
    @GetMapping("/projectProcessDetail")
    @ApiOperation(value = "工单变更进度跟进表", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result projectProcessDetail(@RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(defaultValue = "0") Integer pageNum,
                                       @RequestParam(required = false) String year,@RequestParam(required = false) String status, @RequestParam(required = false) String productNo, @RequestParam(required = false) String productName,
                                       @RequestParam(required = false) String build
    ) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProjectProcessDetail> ppcProjectProcessDetails = ppcProduceOrderService.projectProcessDetail(productNo, productName, build, year,status, pageSize,pageNum);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), ppcProjectProcessDetails));
    }

    /**
     * 勾选工单返回合并预览的标准件
     */
    @GetMapping("/stdOrderMergePreview")
    @ApiOperation(value = "勾选工单返回合并预览的标准件", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result stdOrderMergePreview(@RequestParam(required = false) String orderIds, @RequestParam(required = false) String planIds) throws Exception {
        AssertUtil.isFalse(StringUtils.isNullOrBlank(orderIds) && StringUtils.isNullOrBlank(planIds), "请勾选工单或行");
        ProduceOrderStdMergePreview re = ppcProduceOrderService.stdOrderMergePreview(orderIds, planIds,false);
        return Result.SUCCESS(re);
    }

    @Value("${imes.ppc.template.downloadStdOrderData}")
    private String downloadStdOrderData;

    @GetMapping("/downloadStdOrderData")
    @ApiOperation(value = "下载整单导入标准工单模板", httpMethod = "GET")
    public void downloadStdOrderData(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, downloadStdOrderData);
    }


    @Value("${imes.ppc.template.downloadOrdinaryOrderData}")
    private String downloadOrdinaryOrderData;
    @GetMapping("/downloadOrdinaryOrderData")
    @ApiOperation(value = "下载整单导入普通工单工单模板", httpMethod = "GET")
    public void downloadOrdinaryOrderData(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, downloadOrdinaryOrderData);
    }

    @PostMapping("/importStdOrderData")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "整单导入标准工单", httpMethod = "POST")
    public Result importStdOrderData(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ppcProduceOrderService.importExcel(uploadFile);
        return new Result(ResultCode.SUCCESS, "成功导入");
    }

    @PostMapping("/importOrdinaryOrderData")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "整单导入普通工单", httpMethod = "POST")
    public Result importOrdinaryOrderData(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ppcProduceOrderService.importOrdinaryExcel(uploadFile);
        return new Result(ResultCode.SUCCESS, "成功导入");
    }

    /**
     * 标准件删除
     *
     * @return 标准件删除
     */
    @GetMapping("/batchDeleteStdOrder")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDeleteStdOrder(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcProduceOrderService.batchDeleteStdOrder(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }

    /**
     * 子工单合并单
     *
     * @param list
     * @return
     * @throws Exception
     */
    @PostMapping("/mergeSubOrder")
    @ApiOperation(value = "子工单合并单", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result mergeSubOrder(@RequestBody List<String> list) throws Exception {
        ppcProduceOrderService.mergeSubOrder(list);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 完工
     * @param ids
     * @return
     * @throws Exception
     */
    @GetMapping("/endOrder")
    @ApiOperation(value = "完工")
    @Transactional(rollbackFor = Exception.class)
    public Result endOrder(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        ppcProduceOrderService.endOrder(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 反完工
     * @param ids
     * @return
     * @throws Exception
     */
    @GetMapping("/backEndOrder")
    @ApiOperation(value = "反完工")
    @Transactional(rollbackFor = Exception.class)
    public Result backEndOrder(@RequestParam(value = "ids") String ids) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(ids), "传入参数id空");
        String[] list = ids.split(",");
        ppcProduceOrderService.backEndOrder(list);
        return new Result(ResultCode.SUCCESS);
    }


    @Value("${imes.ppc.template.exportSubOrderTemplate}")
    String exportSubOrderTemplate;

    @GetMapping("/exportSubOrder")
    @ApiOperation(value = "导出子工单")
    public Result exportSubOrder(PpcProduceOrderSearchVo vo,
                                 HttpServletRequest request,
                                 HttpServletResponse response) throws Exception {
        List<ProduceOrderExportVo> orders = ppcProduceOrderService.queryExportSubOrder(vo);
        List<String> collect = orders.stream().map(x -> x.getProductNo() + "-" + x.getBuild()).distinct().collect(Collectors.toList());
        AssertUtil.isFalse(collect.size() > 1, "相同项目、楼栋的工单才可以导出");
        Map<String, List<ProduceOrderExportVo>> groupList = orders.stream().collect(Collectors.groupingBy(ProduceOrderExportVo::getPartType));

        InputStream in = null;
        ClassPathResource resource = new ClassPathResource(exportSubOrderTemplate);
        String fileName = resource.getFilename();
        response.setContentType("application/octet-stream");
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
        }
        in = resource.getInputStream();
        String[] keys = {"productNo", "orderType", "productName", "productShortName", "issueDirection", "prepareStatus", "build"

                , "partType", "sprayTypeName", "orderNo", "isIssueGroove", "remarks", "createOn", "status", "lineNo"
                , "handleStatus", "isRelease", "wide", "name", "length", "materialMarker", "produceQty", "area", "unitArea"
                , "attachEc", "raCorner", "patchInfo", "templateHole", "grooveHole", "millingGroove", "screwWidth", "screwHeigh"
                , "samplePic", "partPic", "mainMaterial", "isBroke", "isAttachmentCut", "detailRemarks", "chartFileUrl"};
        ppcProduceOrderService.exportSubOrder(response, request, groupList, keys, fileName, in, 1, null);
        return null;
    }

    /**
     * 获取工单详情 工单工艺路线
     * @param orderId
     * @return
     * @throws Exception
     */
    @GetMapping("/getOrderRouLine")
    @ApiOperation(value = "获取工单详情 工单工艺路线")
    @Transactional(rollbackFor = Exception.class)
    public Result getOrderRouLine(@RequestParam(value = "orderId") String orderId) throws Exception {
        List<Map> result = ppcProduceOrderDao.getOrderRouLine(orderId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @Autowired
    private PpcPieceTeamMonthGroupDetailService ppcPieceTeamMonthGroupDetailService;

    @GetMapping("/exportWorkOrderPiecework")
    @ApiOperation(value = "计件项明细导出")
    public Result exportWorkOrderPiecework(@RequestParam(value = "ids") String ids,
                                           HttpServletRequest request,
                                           HttpServletResponse response) throws Exception {
        String[] keys = {"*报工日期", "*工段名称", "*计件项目名称", "*人员工号-比例/工号-比例", "*计件数量", "*计件金额",
                "面积", "单价", "单位", "单位面积", "工序名称", "项目名称", "楼栋", "工单号", "行号", "长", "名称", "宽", "异型", "系统项目代码", "市场类型", "新/旧料", "备注"};
        String[] heads = {"*报工日期", "*工段名称", "*计件项目名称", "*人员" + "工号-比例/工号-比例", "*计件数量", "*计件金额",
                "面积", "单价", "单位", "单位面积", "工序名称", "项目名称", "楼栋", "工单号", "行号", "长", "名称", "宽", "异型", "系统项目代码", "市场类型", "新/旧料", "备注"};
        List<String> orderIdList = Arrays.asList(ids.split(","));
        List<PpcProduceOrder> orderList = ppcProduceOrderService.getByIdList(orderIdList);
         Map<String, PpcProduceOrder> orderMap = orderList.stream().collect(Collectors.toMap(PpcProduceOrder::getId, obj -> obj, (k1, k2) -> k1));
        List<PpcProducePlanAzt> ppcProducePlanAzts = ppcProducePlanAztService.queryByOrderIdList(orderIdList);

        // 方法一：使用 Stream API
        List<Map<String, Object>> dataMap = ppcProducePlanAzts.stream()
                .map(item -> {
                    Map<String, Object> map = new HashMap<>();
                    // 将对象的属性放入 map 中
                    map.put("id", item.getId());
//                    map.put("*报工日期", item.getFinishedDate());
//                    map.put("*工段名称", item.getBigProcessName());
//                    map.put("*计件项目名称", item.getItemName());
//                    map.put("*人员工号-比例/工号-比例", processExecuteUserName(item.getExecuteUserName()));
                    map.put("*计件数量", item.getProduceQty());
//                    map.put("*计件金额", item.getCalcMoney());
                    map.put("面积", item.getArea());
//                    map.put("单价", item.getSinglePrice());
//                    map.put("单位", item.getUnit());
                    map.put("单位面积", item.getUnitArea());
//                    map.put("工序名称", item.getProcessName());
                    map.put("项目名称", orderMap.get(item.getOrderId()).getProductName());
                    map.put("楼栋", orderMap.get(item.getOrderId()).getBuild());
                    map.put("工单号", item.getOrderNo());
                    map.put("行号", item.getLineNo());
                    map.put("长", item.getLength());
                    map.put("名称", item.getName());
                    map.put("宽", item.getWide());
                    map.put("异型", item.getMaterialMarker());
                    map.put("系统项目代码", orderMap.get(item.getOrderId()).getProjectMaterialCode());
                    map.put("市场类型", orderMap.get(item.getOrderId()).getProductType());
                    //备料信息开新料有值 则为新料，无值为旧
                    map.put("新/旧料", StringUtils.isNotNullOrBlank(item.getNewMaterialQty()) ? "新" : "旧");
                    map.put("备注", item.getRemarks());
                    return map;
                })
                .collect(Collectors.toList());
        ExcelUtils.exportExcelForImport(response, request, dataMap, keys, heads, "计件项明细导出");
        return null;
    }

//    private String processExecuteUserName(String executeUserName) {
//        if (executeUserName == null || executeUserName.isEmpty()) {
//            return executeUserName;
//        }
//
//        // 处理多个用户的情况，用逗号分隔
//        if (executeUserName.contains(",")) {
//            String[] users = executeUserName.split(",");
//            StringBuilder result = new StringBuilder();
//            for (int i = 0; i < users.length; i++) {
//                if (i > 0) {
//                    result.append(",");
//                }
//                result.append(processExecuteUserName(users[i].trim()));
//            }
//            return result.toString();
//        }
//
//        // 处理单个用户的情况
//        String[] parts = executeUserName.split("-");
//        if (parts.length >= 3) {
//            // 格式为：工号-姓名-比例
//            return parts[0] + "-" + parts[parts.length - 1];
//        }
//
//        // 如果格式不符合预期，则返回原始值
//        return executeUserName;
//    }
}

