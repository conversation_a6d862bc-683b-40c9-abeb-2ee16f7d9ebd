package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.imes.common.exception.CommonException;
import com.imes.common.support.Message;
import com.imes.common.support.Resume;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.Constants;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcSaleMain;
import com.imes.domain.entities.ppc.po.PpcSaleMainChange;
import com.imes.domain.entities.ppc.vo.PpcDeliveryPlanVo;
import com.imes.domain.entities.ppc.vo.PpcSaleDetailChangeVo;
import com.imes.domain.entities.ppc.vo.PpcSaleMainChangeNewVo;
import com.imes.domain.entities.query.model.base.Model;
import com.imes.domain.entities.query.model.base.MsgAction;
import com.imes.domain.entities.query.model.vo.OperateType;
import com.imes.domain.entities.query.template.ppc.PpcContractSearchVo;
import com.imes.domain.entities.query.template.ppc.PpcSaleMainChangeQueryVo;
import com.imes.domain.entities.ppc.vo.PpcSaleMainChangeVo;
import com.imes.ppc.dao.PpcSaleMainChangeDao;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcSaleMainChangeService;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 销售变更单(PpcSaleMainChange)表控制层
 *
 * <AUTHOR> @since 2022-05-19 13:15:52
 */
@Slf4j
@RestController
@Api(tags = "销售变更单主单接口")
@RequestMapping("/api/ppc/ppcSaleMainChange")
public class PpcSaleMainChangeController {

    @Autowired
    private PpcSaleMainChangeService ppcSaleMainChangeService;
    @Autowired
    private FeignService feignService;
    @Autowired
    private PpcSaleMainChangeDao ppcSaleMainChangeDao;

    @GetMapping("/queryByCond")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "分页查询")
    public Result queryByCond(PpcSaleMainChangeQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcSaleMainChangeVo> result = ppcSaleMainChangeService.queryByCond(vo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById/{id}")
    @ApiOperation(value = "根据id查详情")
    public Result queryById(@PathVariable("id") String id) {
        PpcSaleMainChange result = ppcSaleMainChangeService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcSaleMainChange ppcSaleMainChange) throws Exception {
        String result = ppcSaleMainChangeService.insert(ppcSaleMainChange);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcSaleMainChange ppcSaleMainChange) throws Exception {
        Integer result = ppcSaleMainChangeService.update(ppcSaleMainChange);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation(value = "根据Id删除")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(@PathVariable("id") String id) throws Exception {
        Integer result = ppcSaleMainChangeService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 批量删除
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "销售变更单批量删除")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        List<Resume> tjNos = new ArrayList<>();
        List<PpcSaleMainChange> ppcSaleMains = ppcSaleMainChangeDao.selectByIds(idList);
        Integer result = ppcSaleMainChangeService.batchDelete(idList);
        if (result > 0) {
            for (PpcSaleMainChange main : ppcSaleMains) {
                tjNos.add(Resume.build("0495", OperateType.DELETE, main.getChangeNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @GetMapping("/findChangeStatus")
    @ApiOperation(value = "变更状态", httpMethod = "GET")
    @ApiIgnore
    public Result findCommodityType() throws Exception {
        Map map = new HashMap();
        map.put("SALE_CHANGE_STATUS", feignService.getDictCode("SALE_CHANGE_STATUS"));
        return new Result(ResultCode.SUCCESS, map);
    }

    @PostMapping("/saveAll")
    @ApiOperation(value = "选择后的保存接口(主细同时保存)")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result saveAll(@RequestBody PpcSaleMainChangeVo ppcSaleMainChangeVo) throws Exception {
        //添加同销售单号 校验
        List<PpcSaleMainChange> ppcSaleMainChanges = ppcSaleMainChangeDao.selectBySoNoStatus(ppcSaleMainChangeVo.getSoNo(), Constants.SALE_CHANGE_STATUS_10);
        if (CollectionUtils.isNotEmpty(ppcSaleMainChanges)) {
            AssertUtil.throwException("销售单号:【{}】存在还未提交的变更单,无法新增新变更单!!",ppcSaleMainChangeVo.getSoNo());
        }
        if (!StringUtils.isNullOrBlank(ppcSaleMainChangeVo.getOldId())) {
            List<String> idList = new ArrayList<>();
            idList.add(ppcSaleMainChangeVo.getOldId());
            ppcSaleMainChangeService.batchDelete(idList);
        }
        String result = ppcSaleMainChangeService.saveAll(ppcSaleMainChangeVo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "批量提交")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result submit(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSaleMainChangeService.submit(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    //2023-12-07 重构一下变更单部分逻辑
    @PostMapping("/saveAllSales")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "销售变更单同时保存主细表数据", httpMethod = "POST")
    public Result saveAllSales(@RequestBody PpcSaleMainChangeNewVo ppcSaleMainChangeNewVo) throws Exception {
        String i = ppcSaleMainChangeService.saveAllSales(ppcSaleMainChangeNewVo);
        return new Result(ResultCode.SUCCESS, i);
    }

    @GetMapping("/getDataByIds")
    @ApiOperation(value = "根据销售订单明细id集合获取对应的销售变更数据")
    @Transactional(rollbackFor = Exception.class)
    public Result getDataByIds(@ApiParam("销售订单明细id 带逗号的") String ids,@ApiParam("原销售变更单主单ID") String oldId) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        PpcSaleMainChangeNewVo result = ppcSaleMainChangeService.getDataByIds(idList,oldId);
        return new Result(ResultCode.SUCCESS, result);
    }
    @GetMapping("/getDataByIdsForApp")
    @ApiOperation(value = "根据销售订单id集合获取对应的销售变更数据")
    @Transactional(rollbackFor = Exception.class)
    public Result getDataByIdsForApp(String ids,String oldId) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        PpcSaleMainChangeNewVo result = ppcSaleMainChangeService.getDataByIdsForApp(idList,oldId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/saveMainForApp")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "销售变更单app主单保存", httpMethod = "POST")
    public Result saveMainForApp(@RequestBody PpcSaleMainChangeNewVo ppcSaleMainChangeNewVo) throws Exception {
        String i = ppcSaleMainChangeService.saveMainForApp(ppcSaleMainChangeNewVo);
        return new Result(ResultCode.SUCCESS, i);
    }

    @PostMapping("/saveDetailForApp")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "销售变更单app子单保存", httpMethod = "POST")
    public Result saveDetailForApp(@RequestBody List<PpcSaleDetailChangeVo> ppcSaleDetailChangeVo) throws Exception {
        ppcSaleMainChangeService.saveDetailForApp(ppcSaleDetailChangeVo);
        return new Result(ResultCode.SUCCESS, "ok");
    }


    @PostMapping("/submitAll")
    @ApiOperation(value = "批量提交所有")
    @Transactional(rollbackFor = Exception.class)
    public Result submitAll(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSaleMainChangeService.submitAll(idList);
        if (result > 0) {
            // 上传操作履历
            List<PpcSaleMainChange> ppcSaleMains = ppcSaleMainChangeDao.selectByIds(idList);
            List<Resume> tjNos = new ArrayList<>();
            for (PpcSaleMainChange main : ppcSaleMains) {
                tjNos.add(Resume.build("0495", OperateType.APPROVE, main.getChangeNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @GetMapping("/getPpcSaleMainChangeCurrency")
    @ApiIgnore
    @ApiOperation(value = "查询销售订单变更单币种信息是否存在", httpMethod = "GET")
    public Result getPpcSaleMainChangeCurrency(@RequestParam("currency")String currency) throws Exception {
        Integer i = ppcSaleMainChangeService.getPpcSaleMainChangeCurrency(currency);
        return new Result(ResultCode.SUCCESS, i);
    }

    @GetMapping("/rejectSubmit")
    @ApiOperation(value = "反提交")
    @Transactional(rollbackFor = Exception.class)
    public Result rejectSubmit(String id) throws Exception {
        if (!StringUtils.isNullOrBlank(id)) {
            Integer result = ppcSaleMainChangeService.rejectSubmit(id);
            return new Result(ResultCode.SUCCESS, result);
        }
        return new Result(ResultCode.FAIL);
    }

}
