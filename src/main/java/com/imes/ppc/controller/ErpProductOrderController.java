package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.utils.Constants;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.ppc.po.PpcWorkFinish;
import com.imes.domain.entities.ppc.po.erp.ErpProductOrder;
import com.imes.domain.entities.ppc.po.erp.KingdeeProductinbill;
import com.imes.domain.entities.ppc.po.erp.ProductPackageSortOrder;
import com.imes.domain.entities.query.template.ppc.ErpProductOrderQueryVo;
import com.imes.domain.entities.query.template.ppc.KingdeeProductinbillSearchVo;
import com.imes.domain.entities.query.template.ppc.PpcWorkFinishQueryAllVo;
import com.imes.domain.entities.query.template.ppc.ProductPackageSortOrderSearchVo;
import com.imes.ppc.service.impl.ErpProductOrderServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报工单(PpcWorkFinish)表控制层
 *
 * <AUTHOR> @since 2022-01-12 15:16:30
 */
@Slf4j
@RestController
@Api(tags = "中间接口表控制层")
@RequestMapping("/api/ppc/erpOrder")
public class ErpProductOrderController {
    @Autowired
    ErpProductOrderServiceImpl erpProductOrderService;
    @GetMapping("/queryAll")
    @ApiOperation(value = "中间接口表高级查询", httpMethod = "GET")
    public Result queryAll(ErpProductOrderQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ErpProductOrderQueryVo> list = erpProductOrderService.queryAll(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @GetMapping("/queryKingdeeProductinbillSearchVo")
    @ApiOperation(value = "金蝶完工入库表高级查询", httpMethod = "GET")
    public Result queryKingdeeProductinbillSearchVo(KingdeeProductinbillSearchVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<KingdeeProductinbill> list = erpProductOrderService.queryKingdeeProductinbillSearchVo(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @GetMapping("/queryProductPackageSortOrderSearchVo")
    @ApiOperation(value = "自动化分拣工单高级查询", httpMethod = "GET")
    public Result queryProductPackageSortOrderSearchVo(ProductPackageSortOrderSearchVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ProductPackageSortOrder> list = erpProductOrderService.queryProductPackageSortOrderSearchVo(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }
}
