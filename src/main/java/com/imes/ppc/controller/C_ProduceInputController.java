package com.imes.ppc.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.domain.entities.ppc.po.PpcProduceInput;
import com.imes.domain.entities.ppc.vo.ProduceInputVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.C_ProduceInputService;
import com.imes.ppc.service.ProduceInputService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/ppc/c_planMaterial")
@Api(tags = "行业版生产投料相关接口")
@ApiIgnore
public class C_ProduceInputController {

    @Autowired
    private C_ProduceInputService c_produceInputService;

    @Autowired
    FeignService feignService;

    @PostMapping("/kdBatchInsertOrUpdate")
    @ApiOperation(value = "金蝶新增用料清单", httpMethod = "POST")
    public Result kdBatchInsertOrUpdate(@RequestBody List<PpcProduceInput> inputs) throws Exception {
        c_produceInputService.kdBatchInsertOrUpdate(inputs);
        return new Result(ResultCode.SUCCESS);
    }

}
