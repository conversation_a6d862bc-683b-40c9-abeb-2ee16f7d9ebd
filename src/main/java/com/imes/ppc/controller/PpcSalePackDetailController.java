package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.imes.domain.entities.ppc.po.PpcProduceSchedulPack;
import com.imes.domain.entities.ppc.po.PpcSalePackDetail;
import com.imes.domain.entities.ppc.po.PpcSalePackDetailBox;
import com.imes.domain.entities.ppc.vo.PpcDeliveryPlanDetailVo;
import com.imes.domain.entities.ppc.vo.PpcSalePackDetailVo;
import com.imes.ppc.service.*;
import net.sf.jsqlparser.expression.CollateExpression;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Collection;
import java.util.List;

/**
 * 订单装箱记录物料明细表(PpcSalePackDetail)表控制层
 *
 * <AUTHOR> @since 2023-03-22 09:18:49
 */
@Slf4j
@RestController
@Api(tags = "订单装箱记录物料明细表控制层")
@RequestMapping("/api/ppc/ppcSalePackDetail")
public class PpcSalePackDetailController {

    @Autowired
    private PpcSalePackDetailService ppcSalePackDetailService;

    @Autowired
    private PpcSalePackService ppcSalePackService;

    @Autowired
    private PpcSalePackDetailBoxService boxService;

    @Autowired
    private WorkFinishService workFinishService;

    @Autowired
    private PpcProduceSchedulPackService ppcProduceSchedulPackService;

    @Autowired
    private PpcProducePlanSchedulService ppcProducePlanSchedulService;



    @GetMapping("/queryByCond")
    @ApiIgnore
    @ApiOperation(value = "分页查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "current") int current,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              PpcSalePackDetail ppcSalePackDetail) throws Exception {
        Page page = PageHelper.startPage(current, pageSize);
        List<PpcSalePackDetail> result = ppcSalePackDetailService.queryByCond(ppcSalePackDetail);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById/{id}")
    @ApiIgnore
    @ApiOperation(value = "根据id查询")
    public Result queryById(@PathVariable("id") String id) {
        PpcSalePackDetail result = ppcSalePackDetailService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcSalePackDetail record) throws Exception {
        Integer result = ppcSalePackDetailService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcSalePackDetail record) throws Exception {
        Integer result = ppcSalePackDetailService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation(value = "根据id删除")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(@PathVariable("id") String id) throws Exception {
        Integer result = ppcSalePackDetailService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSalePackDetailService.batchDelete(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }


    @PostMapping("/packReset")
    @ApiOperation(value = "装箱重置")
    @Transactional(rollbackFor = Exception.class)
    public Result packReset(String id) throws Exception {
        Integer result = ppcSalePackDetailService.packReset(id);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }


    @PostMapping("/singlePackCreate")
    @ApiOperation(value = "单装批量生成箱码")
    @Transactional(rollbackFor = Exception.class)
    public Result singlePackCreate(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSalePackDetailService.singlePackCreate(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }


    @PostMapping("/mixPackCreate")
    @ApiOperation(value = "混装批量生成箱码")
    @Transactional(rollbackFor = Exception.class)
    public Result mixPackCreate(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSalePackDetailService.mixPackCreate(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @GetMapping("/getDetailByPackNo")
    @ApiOperation(value = "根据主单单号查询明细")
    @Transactional(rollbackFor = Exception.class)
    public Result getDetailByPackNo(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String packNo) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcSalePackDetailVo> result = ppcSalePackDetailService.getDetailByPackNo(packNo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }


    @PostMapping("/branchSplit")
    @ApiOperation(value = "拆分行")
    @Transactional(rollbackFor = Exception.class)
    public Result branchSplit(String id, BigDecimal qty, String precision) throws Exception {
        Integer result = ppcSalePackService.branchSplit(id,qty,precision);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/reviseDetailValue")
    @ApiOperation(value = "实时修改明细修改值")
    @Transactional(rollbackFor = Exception.class)
    public Result reviseDetailValue(@RequestBody PpcSalePackDetailVo ppcSalePackDetailVo) throws Exception {
        Integer result = ppcSalePackService.reviseDetailValue(ppcSalePackDetailVo);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @GetMapping("/findPpcSalePackDetailByBoxNo")
    @ApiOperation(value = "根据箱号获取装箱打印信息")
    public Result findPpcSalePackDetailByBoxNo(@RequestParam("boxNos") String boxNos) throws Exception {
        List<String> boxNoList = Arrays.asList(boxNos.split(","));
        List<String> ids = ppcSalePackService.selectIdsByBoxNos(boxNoList);
        String string = "";
        for (String id: ids) {
            //produce_schedul
           if("1".equals(id.split(",")[0])) {
               List<String> idList = new ArrayList<>();
               idList.add(id.split(",")[1]);
               StringBuilder print = ppcProduceSchedulPackService.packPrint(idList);
               string=print.toString();

           }
           //sale_pack
           else  if("2".equals(id.split(",")[0])) {
               List<String> idList = new ArrayList<>();
               idList.add(id.split(",")[1]);
               StringBuilder builder = boxService.packPrintNotEfficacy(idList);
               string = builder.toString();
           }
           //work_finish
           else  if("3".equals(id.split(",")[0])) {
               List<String> idList = new ArrayList<>();
               idList.add(id.split(",")[1]);
               StringBuilder builder = workFinishService.packPrint(idList);
               string = builder.toString();
           }
           //produce_sn_box
           else  if("4".equals(id.split(",")[0])) {

           }
        }
        return new Result(ResultCode.SUCCESS,string);
    }
}
