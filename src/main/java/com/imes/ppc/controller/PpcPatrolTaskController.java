package com.imes.ppc.controller;


import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.exception.CommonException;
import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.dto.PpcPatrolTaskRecordDTO;
import com.imes.domain.entities.ppc.po.PpcPatrolTask;
import com.imes.domain.entities.ppc.vo.PpcPatrolTaskRecordVo;
import com.imes.domain.entities.ppc.vo.PpcPatrolTaskVo;
import com.imes.domain.entities.ppc.vo.PpcProducePlanSchedulVo;
import com.imes.domain.entities.query.template.ppc.PpcPatrolTaskTemplate;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcPatrolTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产巡视计划(PpcPatrolTask)表控制层
 *
 * <AUTHOR> @since 2022-03-31 10:40:54
 */
@Slf4j
@RestController
@Api(tags = "生产巡视任务控制层")
@RequestMapping("/api/ppc/ppcPatrolTask")
public class PpcPatrolTaskController {

    @Resource
    private PpcPatrolTaskService ppcPatrolTaskService;

    @Autowired
    private FeignService feignService;

    @GetMapping("/getPpcPatrolTaskTemplate")
    @ApiOperation(value = "巡检任务查询", httpMethod = "GET")
    public Result getPpcPatrolTaskTemplate(PpcPatrolTaskTemplate vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPatrolTaskTemplate> result = ppcPatrolTaskService.getPpcPatrolTaskTemplate(vo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }


    @GetMapping("/getPpcPatrolTaskRecords")
    @ApiOperation(value = "生产巡检记录统计报表", httpMethod = "GET")
    public Result getPpcPatrolTaskRecords(PpcPatrolTaskRecordDTO DTO) throws Exception {
        Page page = PageHelper.startPage(DTO.getCurrent(), DTO.getSize());
        List<PpcPatrolTaskRecordVo> result = ppcPatrolTaskService.getPpcPatrolTaskRecords(DTO);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/exportPpcPatrolTaskRecords")
    @ApiOperation(value = "生产巡检记录统计报表", httpMethod = "GET")
    public Result exportBadStatisticsList(PpcPatrolTaskRecordDTO DTO, HttpServletRequest request, HttpServletResponse response) throws Exception {
        String[] keys = {"planName", "templateTableName", "executeDepartmentName", "planStartTime", "planEndTime",
                "taskMomentName", "intervalType", "intervalQty", "isCalendar", "calendarName", "taskEffectHour", "taskNo"
                , "taskName", "executeStatus", "taskStartTime", "taskEndTime", "executeUserName", "actualCompleteTime", "overdueStatus"
        };
        String[] heads = {"计划名称", "记录模板", "执行部门", "计划开始", "计划结束", "生成时刻", "间隔类型",
                "间隔周期", "是否启用日历", "日历", "任务有效期（小时）", "生产巡检单号", "任务名称", "执行状态", "任务开始", "任务结束", "执行人员", "实际完成时间", "超期状态"};

        List<String> keyList = new ArrayList<>(Arrays.asList(keys));
        List<String> headList = new ArrayList<>(Arrays.asList(heads));
        List<PpcPatrolTaskRecordVo> list = ppcPatrolTaskService.getPpcPatrolTaskRecords(DTO);
        for (PpcPatrolTaskRecordVo vo : list) {
            if ("0".equals(vo.getIntervalType())) {
                vo.setIntervalType("无");
            } else if ("1".equals(vo.getIntervalType())) {
                vo.setIntervalType("天");
            } else if ("2".equals(vo.getIntervalType())) {
                vo.setIntervalType("周");
            } else {
                vo.setIntervalType("月");
            }
            if ("10".equals(vo.getExecuteStatus())) {
                vo.setExecuteStatus("待领取");
            } else if ("20".equals(vo.getExecuteStatus())) {
                vo.setExecuteStatus("待执行");
            } else if ("30".equals(vo.getExecuteStatus())) {
                vo.setExecuteStatus("已完成");
            } else if ("40".equals(vo.getExecuteStatus())) {
                vo.setExecuteStatus("超期完成");
            } else {
                vo.setExecuteStatus("强制关闭");
            }
            if ("0".equals(vo.getOverdueStatus())) {
                vo.setOverdueStatus("正常");
            } else {
                vo.setOverdueStatus("超期");
            }
            if ("0".equals(vo.getIsCalendar())) {
                vo.setIsCalendar("否");
            } else {
                vo.setIsCalendar("是");
            }

        }
        List<List<Map<String, Object>>> lists1 = ppcPatrolTaskService.preView(DTO.getTemplateTableCode(), null);
        if (!lists1.isEmpty()) {
            List<Map<String, Object>> lists = (List<Map<String, Object>>) lists1.get(0).get(0).get("children");
            for (Map<String, Object> map : lists) {
                List<Map<String, Object>> children = (List<Map<String, Object>>) map.get("children");
                if (children.isEmpty()) {
                    keyList.add(map.get("code").toString());
                    headList.add(map.get("label").toString());
                } else {
                    for (Map<String, Object> map1 : children) {
                        keyList.add(map1.get("code").toString());
                        headList.add(map1.get("label").toString());
                    }
                }
            }
        }
        keys = keyList.toArray(new String[keyList.size()]);
        heads = headList.toArray(new String[headList.size()]);
        List<Map<String, Object>> mapList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map<String, Object> map1 = BeanMapUtils.beanToMap(list.get(i));
            Map map = JSON.parseObject(list.get(i).getCustom(), Map.class);
            Map newMap = new HashMap();
            newMap.putAll(map1);
            newMap.putAll(map);
            mapList.add(newMap);
        }
        ExcelUtils.exportExcel(response, request, mapList, keys, heads, "生产巡检记录统计报表");
        return null;
    }


    @GetMapping("")
    @ApiOperation(value = "")
    @ApiIgnore
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              @RequestParam Map<String, Object> map) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcPatrolTaskVo> result = ppcPatrolTaskService.queryByMap(map);
        for (PpcPatrolTaskVo ppcPatrolTask : result) {
            if (ppcPatrolTask.getActualCompleteTime() == null) {
                if (ppcPatrolTask.getPlanEndTime().before(new Date())) {
                    ppcPatrolTask.setOverdueStatus("1");
                }
            } else {
                if (ppcPatrolTask.getPlanEndTime().before(ppcPatrolTask.getActualCompleteTime())) {
                    ppcPatrolTask.setOverdueStatus("1");
                }
            }
        }
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据id获取生产巡检任务", httpMethod = "GET")
    public Result queryById(@ApiParam("生产巡检任务id") @PathVariable("id") String id) {
        PpcPatrolTask result = ppcPatrolTaskService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/forceEndTask")
    @ApiOperation(value = "强制结束生产巡检任务", httpMethod = "GET")
    public Result forceEndTask(@ApiParam("生产巡检任务id") @RequestParam(value = "id") String id, @ApiParam("备注") @RequestParam(value = "remarks", required = false) String remarks) throws Exception {
        PpcPatrolTask ppcPatrolTask = ppcPatrolTaskService.queryById(id);
        ppcPatrolTask.setExecuteStatus("90");
        ppcPatrolTask.setRemarks(remarks);
        ppcPatrolTask.setExecuteUserCode(RedisUtils.getUserCode());
        ppcPatrolTask.setExecuteUserName(RedisUtils.getUserName());
        ppcPatrolTask.setActualCompleteTime(new Date());
        RecordUtils.updateData(ppcPatrolTask);
        ppcPatrolTaskService.update(ppcPatrolTask);
        return new Result(ResultCode.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/batchEndTask"})
    @ApiOperation(value = "批量强制结束生产巡检任务", httpMethod = "POST")
    public Result batchDeletePpcProducePlanById(@ApiParam("生产巡检任务id数组") String[] ids) throws Exception {
        for (String id : ids) {
            PpcPatrolTask ppcPatrolTask = ppcPatrolTaskService.queryById(id);
            //如果是已完成的执行批量强制关闭操作，则直接跳过
            if (ppcPatrolTask != null) {
                if (!StringUtils.isNullOrBlank(ppcPatrolTask.getExecuteStatus())) {
                    if ("30".equals(ppcPatrolTask.getExecuteStatus())) {
                        AssertUtil.throwException("任务名称:【{}】单据的执行状态是已完成，不能强制关闭",ppcPatrolTask.getTaskName());
                    }
                }
            }
            ppcPatrolTask.setExecuteStatus("90");
            ppcPatrolTask.setExecuteUserCode(RedisUtils.getUserCode());
            ppcPatrolTask.setExecuteUserName(RedisUtils.getUserName());
            ppcPatrolTask.setActualCompleteTime(new Date());
            RecordUtils.updateData(ppcPatrolTask);
            ppcPatrolTaskService.update(ppcPatrolTask);
        }
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("")
    @ApiOperation(value = "")
    @ApiIgnore
    public Result save(@RequestBody PpcPatrolTask record) {
        Integer result = ppcPatrolTaskService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PutMapping("")
    @ApiOperation(value = "")
    @ApiIgnore
    public Result update(@RequestBody PpcPatrolTask record) {
        Integer result = ppcPatrolTaskService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "")
    @ApiIgnore
    public Result deleteById(@PathVariable("id") String id) {
        Integer result = ppcPatrolTaskService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getPlanNameSelect")
    @ApiIgnore
    public Result getPlanNameSelect() throws Exception {
        List<Map<String, String>> result = ppcPatrolTaskService.getPlanNameSelect();
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/queryByH5")
    @ApiOperation(value = "生产巡检任务查询移动端", httpMethod = "GET")
    public Result queryByH5(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            @RequestParam Map<String, Object> map) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcPatrolTaskVo> result = ppcPatrolTaskService.queryByH5(map);
        for (PpcPatrolTask ppcPatrolTask : result) {
            if (ppcPatrolTask.getActualCompleteTime() == null) {
                if (ppcPatrolTask.getPlanEndTime().before(new Date())) {
                    ppcPatrolTask.setOverdueStatus("1");
                } else {
                    ppcPatrolTask.setOverdueStatus("0");
                }
            } else {
                if (ppcPatrolTask.getActualCompleteTime().after(ppcPatrolTask.getPlanEndTime())) {
                    ppcPatrolTask.setOverdueStatus("1");
                } else {
                    ppcPatrolTask.setOverdueStatus("0");
                }
            }
        }
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }


    @GetMapping("/receiveTask")
    @ApiOperation(value = "生产巡检任务查询移动端", httpMethod = "GET")
    public Result receiveTask(@ApiParam("生产巡检任务id") @RequestParam("id") String id) throws Exception {
        ppcPatrolTaskService.receiveTask(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 预览
     */
    @GetMapping("/preView")
    @ApiOperation(value = "预览", httpMethod = "GET")
    public Result preView(@ApiParam("巡检模版编码") String tableCode, @ApiParam("生产巡检任务id") @RequestParam(value = "taskId", required = false) String taskId) {
        List<List<Map<String, Object>>> result = ppcPatrolTaskService.preView(tableCode, taskId);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 预览
     */
    @GetMapping("/tableDetail")
    @ApiOperation(value = "预览", httpMethod = "GET")
    public Result tableDetail(@ApiParam("巡检模版编码") String tableCode, @ApiParam("生产巡检任务id") @RequestParam(value = "taskId", required = false) String taskId) throws Exception {
        List<List<Map>> result = ppcPatrolTaskService.tableDetail(tableCode, taskId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @Transactional(rollbackFor = Exception.class)
    @GetMapping({"/patrolTaskPrint"})
    @ApiOperation(value = "巡检任务打印", httpMethod = "GET")
    public Result patrolTaskPrint(@ApiParam("生产巡检任务编码") @RequestParam("taskNo") String taskNo, @ApiParam("帆软模版") @RequestParam("fineReportTemplate") String fineReportTemplate) throws Exception {
        ppcPatrolTaskService.patrolTaskPrint(taskNo, fineReportTemplate);
        return new Result(ResultCode.SUCCESS, fineReportTemplate);
    }
}
