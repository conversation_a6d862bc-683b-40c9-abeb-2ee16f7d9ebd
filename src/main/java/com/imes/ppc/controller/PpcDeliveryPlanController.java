package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.support.Query;
import com.imes.common.utils.Constants;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcDeliveryPlan;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.query.model.vo.OperateType;
import com.imes.common.support.Resume;
import com.imes.domain.entities.query.template.ppc.PpcDeliveryPlanSearchVo;
import com.imes.domain.entities.query.template.ppc.PpcSaleReverseSelectSearchVo;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import com.imes.ppc.dao.PpcDeliveryPlanDao;
import com.imes.ppc.dao.PpcSaleMainDao;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcDeliveryInvoiceService;
import com.imes.ppc.service.PpcDeliveryPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;

/**
 * 发货计划(PpcDeliveryPlan)表控制层
 *
 * <AUTHOR> @since 2022-05-05 10:32:11
 */
@Slf4j
@RestController
@Api(tags = "发货计划主单")
@RequestMapping("/api/ppc/ppcDeliveryPlan")
public class PpcDeliveryPlanController {

    @Autowired
    private PpcDeliveryPlanService ppcDeliveryPlanService;
    @Autowired
    private FeignService feignService;
    @Autowired
    private PpcDeliveryInvoiceService ppcDeliveryInvoiceService;

    @Autowired
    private PpcSaleMainDao ppcSaleMainDao;

    @Autowired
    private PpcDeliveryPlanDao ppcDeliveryPlanDao;

    @GetMapping("/queryByCond")
    @ApiIgnore
    @ApiOperation(value = "分页查询", httpMethod = "GET")
    public Result queryByCond(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "current") int current,
                              @ApiParam("分页条数")  @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              PpcDeliveryPlanVo ppcDeliveryPlanVo) throws Exception {
        if (!StringUtils.isNullOrBlank(ppcDeliveryPlanVo.getPlanStatus())) {
            ppcDeliveryPlanVo.setPlanStatusList(Arrays.asList(ppcDeliveryPlanVo.getPlanStatus().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcDeliveryPlanVo.getPlanSource())) {
            ppcDeliveryPlanVo.setPlanSourceLists(Arrays.asList(ppcDeliveryPlanVo.getPlanSource().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcDeliveryPlanVo.getDocumentType())) {
            ppcDeliveryPlanVo.setDocumentTypeLists(Arrays.asList(ppcDeliveryPlanVo.getDocumentType().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcDeliveryPlanVo.getBusinessStatus())) {
            ppcDeliveryPlanVo.setBusinessStatusList(Arrays.asList(ppcDeliveryPlanVo.getBusinessStatus().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcDeliveryPlanVo.getOrder())) {
            if (ppcDeliveryPlanVo.getOrder().equals("ASC")) {
                PageHelper.orderBy("a.order_date ASC,a.create_on DESC,a.delivery_no DESC");
            } else if (ppcDeliveryPlanVo.getOrder().equals("DESC")) {
                PageHelper.orderBy("a.order_date DESC,a.create_on DESC,a.delivery_no DESC");
            }
        } else {
            PageHelper.orderBy("a.create_on DESC,a.delivery_no DESC,a.order_date DESC");
        }
        Page page = PageHelper.startPage(current, pageSize);
        List<PpcDeliveryPlanVo> result = ppcDeliveryPlanService.queryByCond(ppcDeliveryPlanVo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/getPlanSource")
    @ApiOperation(value = "获取计划来源", httpMethod = "GET")
    public Result getPlanSource(  @ApiParam("1的是发货计划的计划来源接口  2是发货单的计划来源接口 3是出库单的计划来源接口  5 供应链发货单计划来源 6退货计划的接口") String type) throws Exception {
        //1的是发货计划的计划来源接口  2是发货单的计划来源接口 3是出库单的计划来源接口  5 供应链发货单计划来源 6退货计划的接口
        Map map = new HashMap();
        if ("1".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("DELIVERY_PLAN_SOURCE");
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_2.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_3.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_4.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_5.equals(vo.getCode()));

            map.put("DELIVERY_PLAN_SOURCE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else if ("2".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("DELIVERY_PLAN_SOURCE");
            //TODO 如果是走供应链发货单
            if (ppcDeliveryInvoiceService.canInvoiceCTB()) {
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_1.equals(vo.getCode()));
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_2.equals(vo.getCode()));
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_3.equals(vo.getCode()));
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_4.equals(vo.getCode()));
            } else {
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_3.equals(vo.getCode()));
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_4.equals(vo.getCode()));
                planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_5.equals(vo.getCode()));
            }
            map.put("DELIVERY_PLAN_SOURCE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else if ("3".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("DELIVERY_PLAN_SOURCE");
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_4.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_5.equals(vo.getCode()));
            map.put("DELIVERY_PLAN_SOURCE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else if ("4".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("DELIVERY_PLAN_SOURCE");
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_2.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_3.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_4.equals(vo.getCode()));
            planSource.removeIf(vo -> Constants.DELIVERY_PLAN_SOURCE_5.equals(vo.getCode()));
            map.put("DELIVERY_PLAN_SOURCE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else {
            List<SysDictDetailVo> planSource = feignService.getDictCode("DELIVERY_PLAN_SOURCE");
            map.put("DELIVERY_PLAN_SOURCE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        }

    }

    @GetMapping("/getFhlxByType")
    @ApiOperation(value = "发货类型", httpMethod = "GET")
    public Result getFhlxByType(@ApiParam("1的是发货计划的计划来源接口  2是发货单的计划来源接口 3是出库单的计划来源接口") String type) throws Exception {
        //1的是发货计划的计划来源接口  2是发货单的计划来源接口 3是出库单的计划来源接口
        Map map = new HashMap();
        if ("1".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("SALE_DOC_TYPE");
            planSource.removeIf(vo -> "1001".equals(vo.getCode()));
            planSource.removeIf(vo -> "1002".equals(vo.getCode()));
            map.put("SALE_DOC_TYPE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else if ("2".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("SALE_DOC_TYPE");
            planSource.removeIf(vo -> "1001".equals(vo.getCode()));
            planSource.removeIf(vo -> "1003".equals(vo.getCode()));
            map.put("SALE_DOC_TYPE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else if ("3".equals(type)) {
            List<SysDictDetailVo> planSource = feignService.getDictCode("SALE_DOC_TYPE");
            map.put("SALE_DOC_TYPE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        } else {
            List<SysDictDetailVo> planSource = feignService.getDictCode("SALE_DOC_TYPE");
            map.put("SALE_DOC_TYPE", planSource);
            return new Result(ResultCode.SUCCESS, map);
        }

    }

    @GetMapping("/getPlanStatus")
    @ApiIgnore
    @ApiOperation(value = "计划状态", httpMethod = "GET")
    public Result getPlanStatus() throws Exception {
        Map map = new HashMap();
        map.put("DELIVERY_PLAN_STATUS", feignService.getDictCode("DELIVERY_PLAN_STATUS"));
        return new Result(ResultCode.SUCCESS, map);
    }


    @GetMapping("/queryById/{id}")
    @ApiOperation(value = "发货计划根据id查详情", httpMethod = "GET")
    public Result queryById(@ApiParam("发货计划id") @PathVariable("id") String id) {
        PpcDeliveryPlan result = ppcDeliveryPlanService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiIgnore
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcDeliveryPlan ppcDeliveryPlan) throws Exception {
        String result = ppcDeliveryPlanService.insert(ppcDeliveryPlan);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiIgnore
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcDeliveryPlan ppcDeliveryPlan) throws Exception {
        Integer result = ppcDeliveryPlanService.update(ppcDeliveryPlan);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation(value = "发货计划根据Id删除", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(@ApiParam("发货计划id") @PathVariable("id") String id) throws Exception {
        Integer result = ppcDeliveryPlanService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/submit")
    @ApiOperation(value = "发货计划批量提交", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result submit(@ApiParam("发货计划id字符串，带逗号") String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcDeliveryPlanService.submit(idList);
        if (result > 0) {
            List<PpcDeliveryPlan> ppcSaleMains = ppcDeliveryPlanDao.selectByIds(idList);
            List<Resume> tjNos = new ArrayList<>();
            for (PpcDeliveryPlan main : ppcSaleMains) {
                tjNos.add(Resume.build("0493", OperateType.APPROVE, main.getDeliveryNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/unSubmit")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "发货计划反审核", httpMethod = "POST")
    public Result unSubmit(String id) throws Exception {
        Integer result = ppcDeliveryPlanService.unSubmit(id);
        if (result > 0) {
            //上传操作履历
            List<String> idList = new ArrayList<>();
            idList.add(id);
            List<PpcDeliveryPlan> ppcSaleMains = ppcDeliveryPlanDao.selectByIds(idList);
            List<Resume> tjNos = new ArrayList<>();
            for (PpcDeliveryPlan main : ppcSaleMains) {
                tjNos.add(Resume.build("0493", OperateType.DISAPPROVE, main.getDeliveryNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }

    @PostMapping("/planClose")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "发货计划主单关闭", httpMethod = "POST")
    public Result planClose(@ApiParam("发货计划id") String id, String isNeedClose) throws Exception {
        Integer result = ppcDeliveryPlanService.planClose(id, isNeedClose);
        if (result > 0) {
            List<String> idList = new ArrayList<>();
            idList.add(id);
            //上传操作履历
            List<PpcDeliveryPlan> ppcSaleMains = ppcDeliveryPlanDao.selectByIds(idList);
            List<Resume> tjNos = new ArrayList<>();
            for (PpcDeliveryPlan main : ppcSaleMains) {
                tjNos.add(Resume.build("0493", OperateType.CLOSE, main.getDeliveryNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }


    @PostMapping("/planUnClose")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "发货计划主单反关闭", httpMethod = "POST")
    public Result planUnClose(@ApiParam("发货计划id字符串，带逗号") String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcDeliveryPlanService.planUnClose(idList);
        if (result > 0) {
            //上传操作履历
            List<PpcDeliveryPlan> ppcSaleMains = ppcDeliveryPlanDao.selectByIds(idList);
            List<Resume> tjNos = new ArrayList<>();
            for (PpcDeliveryPlan main : ppcSaleMains) {
                tjNos.add(Resume.build("0493", OperateType.DISCLOSE, main.getDeliveryNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }

    @GetMapping("/getInverseSelect")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    @ApiOperation(value = "反选弹框查询(通用)", httpMethod = "GET")
    public Result getInverseSelect(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "current") int current,
                                   @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                   PpcDeliverySelectVo ppcDeliverySelectVo) throws Exception {
        Page page = PageHelper.startPage(current, pageSize);
        List<PpcDeliverySelectVo> result = ppcDeliveryPlanService.getInverseSelect(ppcDeliverySelectVo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/getInverseSelectQueryList")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    @ApiOperation(value = "通用反选弹框主细拉平", httpMethod = "GET")
    public Result getInverseSelectQueryList(PpcSaleReverseSelectSearchVo ppcSaleReverseSelectSearchVo) throws Exception {
        Page page = PageHelper.startPage(ppcSaleReverseSelectSearchVo.getPageNum(), ppcSaleReverseSelectSearchVo.getPageSize());
        List<PpcSaleReverseSelectVo> result = ppcDeliveryPlanService.getInverseSelectQueryList(ppcSaleReverseSelectSearchVo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/getInverseSelectForOut")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "出库单用反选主细数据", httpMethod = "GET")
    public Result getInverseSelectForOut(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "current") int current,
                                         @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                         PpcDeliverySelectVo ppcDeliverySelectVo) throws Exception {
        Page page = PageHelper.startPage(current, pageSize);
        List<PpcDeliverySelectVo> result = ppcDeliveryPlanService.getInverseSelectForOut(ppcDeliverySelectVo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);

    }


    /**
     * 批量删除
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "发货计划批量删除", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(@ApiParam("发货计划id字符串，带逗号") String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        //上传操作履历
        List<PpcDeliveryPlan> ppcSaleMains = ppcDeliveryPlanDao.selectByIds(idList);
        List<Resume> tjNos = new ArrayList<>();
        Integer result = ppcDeliveryPlanService.batchDelete(idList);
        if (result > 0) {
            for (PpcDeliveryPlan main : ppcSaleMains) {
                tjNos.add(Resume.build("0493", OperateType.DELETE, main.getDeliveryNo()));
            }
            feignService.saveResume(tjNos);
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/saveDetailAll")
    @ApiIgnore
    @ApiOperation(value = "选择后的保存接口(主细同时保存)", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result saveAll(@RequestBody PpcSelectDataVo ppcSelectDataVo) throws Exception {
        if (CollectionUtils.isNotEmpty(ppcSelectDataVo.getData())) {
            if (!StringUtils.isNullOrBlank(ppcSelectDataVo.getData().get(0))) {
                if (!StringUtils.isNullOrBlank(ppcSelectDataVo.getData().get(0).getOldId())) {
                    List<String> idList = new ArrayList<>();
                    idList.add(ppcSelectDataVo.getData().get(0).getOldId());
                    ppcDeliveryPlanService.batchDelete(idList);
                }
            }
        }
        String result = ppcDeliveryPlanService.saveDetailAll(ppcSelectDataVo);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/queryList")
    @ApiOperation(value = "发货计划高级查询列表", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(PpcDeliveryPlanSearchVo vo) throws Exception {
        //TODO 用于获取整单上查对应的 这个单号可能是销售出库单号 发货计划单号 发货单号 销售出库的上查还没好
        List<String> uniqueCodes = new ArrayList<>();
        if (!StringUtils.isNullOrBlank(vo.getProcessFlag())) {
            List<String> soNos = Arrays.asList(vo.getProcessFlag().split(Query.SPLIT_CHAR));
            if (CollectionUtils.isNotEmpty(soNos)) {
                uniqueCodes = ppcSaleMainDao.selectForsoNos(soNos);
            }
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcDeliveryPlanDetailVo> list = ppcDeliveryPlanService.queryList(vo, uniqueCodes);
        Map resultMap = new HashMap();
        resultMap.put("list", list);
        resultMap.put("size", page.getTotal());
        resultMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    @PostMapping("/saveAllSales")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "发货计划同时保存主细表数据", httpMethod = "POST")
    public Result saveAllSales(@RequestBody PpcDeliveryPlanVo ppcDeliveryPlanVo) throws Exception {
        String i = ppcDeliveryPlanService.saveAllSales(ppcDeliveryPlanVo);
        return new Result(ResultCode.SUCCESS, i);
    }


    @PostMapping("/saveAllSalesAndSubmit")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "发货计划同时保存主细表数据并提交", httpMethod = "POST")
    public Result saveAllSalesAndSubmit(@RequestBody PpcDeliveryPlanVo ppcDeliveryPlanVo) throws Exception {
        String i = ppcDeliveryPlanService.saveAllSalesAndSubmit(ppcDeliveryPlanVo);
        return new Result(ResultCode.SUCCESS, i);
    }

    @PostMapping("/reviewMomDeliveryPlan")
    @ApiOperation(value = "MOM标准api发货计划反审核接口", httpMethod = "POST")
    public Result reviewMomDeliveryPlan(@ApiParam("发货计划单号") @RequestParam("deliveryNo") String deliveryNo) throws Exception {
        ppcDeliveryPlanService.reviewMomDeliveryPlan(deliveryNo);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/deleteMomDeliveryPlan")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "MOM标准api删除发货计划接口", httpMethod = "POST")
    public Result deleteMomDeliveryPlan(@ApiParam("发货计划单号") @RequestParam("deliveryNo") String deliveryNo) throws Exception {
        ppcDeliveryPlanService.deleteMomDeliveryPlan(deliveryNo);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/queryByDeliveryPlanNo")
    @ApiOperation(value = "根据deliveryPlanNo查询", httpMethod = "GET")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result queryByDeliveryPlanNo(@RequestParam Map map) throws Exception {
        PpcDeliveryPlanVo list = ppcDeliveryPlanService.queryByDeliveryPlanNo(map);
        return new Result(ResultCode.SUCCESS, list);
    }
}
