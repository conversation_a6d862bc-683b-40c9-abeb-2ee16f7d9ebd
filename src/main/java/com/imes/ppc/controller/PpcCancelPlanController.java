package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.po.PpcCancelPlan;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.query.template.ppc.PpcCancelPlanSearchVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcCancelPlanService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

import java.util.*;

/**
 * 退货计划(PpcCancelPlan)表控制层
 *
 * <AUTHOR> @since 2022-06-13 10:23:01
 */
@Slf4j
@RestController
@Api(tags = "退货计划控制层")
@RequestMapping("/api/ppc/ppcCancelPlan")
@ApiIgnore
public class PpcCancelPlanController {

    @Autowired
    private PpcCancelPlanService ppcCancelPlanService;
    @Autowired
    private FeignService feignService;

    @GetMapping("/queryByCond")
    @ApiOperation(value = "分页查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "current") int current,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              PpcCancelPlanVo ppcCancelPlanVo) throws Exception {
        if (!StringUtils.isNullOrBlank(ppcCancelPlanVo.getPlanStatus())) {
            ppcCancelPlanVo.setPlanStatusList(Arrays.asList(ppcCancelPlanVo.getPlanStatus().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcCancelPlanVo.getPlanSource())) {
            ppcCancelPlanVo.setPlanSourceLists(Arrays.asList(ppcCancelPlanVo.getPlanSource().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcCancelPlanVo.getDocumentType())) {
            ppcCancelPlanVo.setDocumentTypeLists(Arrays.asList(ppcCancelPlanVo.getDocumentType().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcCancelPlanVo.getBusinessStatus())) {
            ppcCancelPlanVo.setBusinessStatusList(Arrays.asList(ppcCancelPlanVo.getBusinessStatus().split(",")));
        }
        if (!StringUtils.isNullOrBlank(ppcCancelPlanVo.getOrder())) {
            if (ppcCancelPlanVo.getOrder().equals("ASC")) {
                PageHelper.orderBy("cancel_date ASC");
            } else if (ppcCancelPlanVo.getOrder().equals("DESC")) {
                PageHelper.orderBy("cancel_date DESC");
            }
        }

        Page page = PageHelper.startPage(current, pageSize);
        List<PpcCancelPlanVo> result = ppcCancelPlanService.queryByCond(ppcCancelPlanVo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById/{id}")
    @ApiOperation(value = "根据id查询")
    public Result queryById(@PathVariable("id") String id) {
        PpcCancelPlan result = ppcCancelPlanService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcCancelPlan record) throws Exception {
        String result = ppcCancelPlanService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcCancelPlan record) throws Exception {
        Integer result = ppcCancelPlanService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation(value = "根据id删除")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(@PathVariable("id") String id) throws Exception {
        Integer result = ppcCancelPlanService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcCancelPlanService.batchDelete(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/submit")
    @ApiOperation(value = "批量提交")
    @Transactional(rollbackFor = Exception.class)
    public Result submit(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcCancelPlanService.submit(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/unSubmit")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "退货计划反审核", httpMethod = "POST")
    public Result unSubmit(String id) throws Exception {
        Integer result = ppcCancelPlanService.unSubmit(id);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }

    @PostMapping("/planClose")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "退货计划主单关闭", httpMethod = "POST")
    public Result planClose(String id, String isNeedClose) throws Exception {
        Integer result = ppcCancelPlanService.planClose(id, isNeedClose);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }


    @PostMapping("/planUnClose")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "退货计划主单反关闭", httpMethod = "POST")
    public Result planUnClose(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcCancelPlanService.planUnClose(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.BIZ_ERROR);
        }
    }




    @GetMapping("/queryList")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(PpcCancelPlanSearchVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcCancelPlanDetailVo> list = ppcCancelPlanService.queryList(vo);
        Map resultMap = new HashMap();
        resultMap.put("list", list);
        resultMap.put("size", page.getTotal());
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    @PostMapping("/saveDetailAll")
    @ApiOperation(value = "选择后的保存接口(主细同时保存)")
    @Transactional(rollbackFor = Exception.class)
    public Result saveAll(@RequestBody PpcSelectDataVo ppcSelectDataVo) throws Exception {
        if (CollectionUtils.isNotEmpty(ppcSelectDataVo.getData())) {
            if (!StringUtils.isNullOrBlank(ppcSelectDataVo.getData().get(0))) {
                if (!StringUtils.isNullOrBlank(ppcSelectDataVo.getData().get(0).getOldId())) {
                    List<String> idList = new ArrayList<>();
                    idList.add(ppcSelectDataVo.getData().get(0).getOldId());
                    ppcCancelPlanService.batchDelete(idList);
                }
            }
        }
        String result = ppcCancelPlanService.saveDetailAll(ppcSelectDataVo);
        return new Result(ResultCode.SUCCESS, result);
    }

}
