package com.imes.ppc.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.ppc.service.PpcSaleStatisticsService;
import com.imes.ppc.service.PpcStatisticsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("api/ppc/ppcStatistics")
@Api(tags = "移动端统计数据中心-生产数据中心")
public class PpcStatisticsController {
    @Autowired
    PpcStatisticsService statisticsService;

    @GetMapping("/getOneFinishedQty")
    @ApiOperation(value = "获取登录人报工总数", httpMethod = "GET")
    public Result getOneFinishedQty(@ApiParam("开始时间") String startTime,@ApiParam("截止时间") String endTime) throws Exception {
        BigDecimal qty = statisticsService.getOneFinishedQty(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getOneBadQty")
    @ApiOperation(value = "获取登录人不良总数", httpMethod = "GET")
    public Result getOneBadQty(@ApiParam("开始时间") String startTime,@ApiParam("截止时间") String endTime) throws Exception {
        BigDecimal qty = statisticsService.getOneBadQty(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getOneWorkTime")
    @ApiOperation(value = "获取登录人总工时", httpMethod = "GET")
    public Result getOneWorkTime(@ApiParam("开始时间")String startTime,@ApiParam("截止时间")String endTime) throws Exception {
        BigDecimal qty = statisticsService.getOneWorkTime(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getOnePieceWorkPrice")
    @ApiOperation(value = "获取登录人总报工金额", httpMethod = "GET")
    public Result getOnePieceWorkPrice(@ApiParam("开始时间")String startTime,@ApiParam("截止时间")String endTime) throws Exception {
        BigDecimal qty = statisticsService.getOnePieceWorkPrice(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getAllPlan")
    @ApiOperation(value = "获取计划单总数", httpMethod = "GET")
    public Result getAllPlan(@ApiParam("开始时间")String startTime,@ApiParam("截止时间")String endTime) throws Exception {
        BigDecimal qty = statisticsService.getAllPlan(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getFinishPlan")
    @ApiOperation(value = "获取完成计划总数", httpMethod = "GET")
    public Result getFinishPlan(@ApiParam("开始时间")String startTime,@ApiParam("截止时间") String endTime) throws Exception {
        BigDecimal qty = statisticsService.getFinishPlan(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getAllPlanProduceQty")
    @ApiOperation(value = "计划总产量", httpMethod = "GET")
    public Result getAllPlanProduceQty(@ApiParam("开始时间")String startTime,@ApiParam("截止时间") String endTime) throws Exception {
        BigDecimal qty = statisticsService.getAllPlanProduceQty(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }

    @GetMapping("/getPlanFinishQty")
    @ApiOperation(value = "完工总数", httpMethod = "GET")
    public Result getPlanFinishQty(@ApiParam("开始时间")String startTime,@ApiParam("截止时间") String endTime) throws Exception {
        BigDecimal qty = statisticsService.getPlanFinishQty(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toString());
    }

    @GetMapping("/getPlanCompletionRate")
    @ApiOperation(value = "计划完成率", httpMethod = "GET")
    public Result getPlanCompletionRate(@ApiParam("开始时间")String startTime,@ApiParam("截止时间")String endTime) throws Exception {
        BigDecimal qty = statisticsService.getPlanCompletionRate(startTime,endTime);
        return new Result(ResultCode.SUCCESS, qty.stripTrailingZeros().toPlainString());
    }






}
