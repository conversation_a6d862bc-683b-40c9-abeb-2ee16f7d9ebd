package com.imes.ppc.controller;

import java.util.List;
import java.util.Map;

import com.imes.domain.entities.ppc.po.PpcProduceProcess;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.ppc.vo.PpcProduceProcessVo;
import com.imes.ppc.service.ProduceProcessService;

import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

@Slf4j
@RestController
@RequestMapping("api/ppc/planProcess")
@Api(tags = "工序相关接口")
@ApiIgnore
public class produceProcessController {


    @Autowired
    private ProduceProcessService produceProcessService;

    /**
     * 生产计划工序
     *
     * @param id 生产计划Id
     * @return
     */
    @GetMapping("/getPlanProcess/{id}")
    @ApiOperation(value = "生产计划工序", httpMethod = "GET")
    public Result query(@PathVariable("id") String id) throws Exception {
        List<PpcProduceProcessVo> processList = produceProcessService.findListByPpid(id);
        return new Result(ResultCode.SUCCESS, processList);
    }


    /**
     * 排产单工序
     *
     * @param productionNo 排产单号
     * @return
     */
    @GetMapping("/getPlanScheduleProcess")
    @ApiOperation(value = "排产工序", httpMethod = "GET")
    public Result getPlanScheduleProcess(@RequestParam("productionNo") String productionNo) throws Exception {
        List<PpcProduceProcessVo> processList = produceProcessService.findListByPlanScheduleProcess(productionNo);
        return new Result(ResultCode.SUCCESS, processList);
    }

    /**
     * 通过生产id获取工序和车间产线信息
     *
     * @param id 生产计划Id
     * @return
     */
    @GetMapping("/getPlanProcess")
    @ApiOperation(value = "通过生产id获取工序和车间产线信息", httpMethod = "GET")
    public Result getPlanProcess(@RequestParam("id") String id) throws Exception {
        List<Map<String, Object>> byId = produceProcessService.findById(id);
        return new Result(ResultCode.SUCCESS, byId);
    }

    @PostMapping("/selectByMap")
    @ApiOperation(value = "条件查询工序流转卡", httpMethod = "POST")
    public Result selectByMap(@RequestBody Map<String, Object> map) throws Exception {
        List<PpcProduceProcess> codeList = produceProcessService.selectByMap(map);
        return new Result(ResultCode.SUCCESS, codeList);
    }
}
