package com.imes.ppc.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.ExcuteResult;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.support.Query;
import com.imes.common.support.Resume;
import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.dto.PpcBulletinBoardVo;
import com.imes.domain.entities.ppc.dto.PpcProducePlanDTO;
import com.imes.domain.entities.ppc.dto.PpcProducePlanVoByMobileDTO;
import com.imes.domain.entities.ppc.dto.productSchedulStatisticsDTO;
import com.imes.domain.entities.ppc.po.*;
import com.imes.domain.entities.ppc.vo.PlanSchedulSingleSNExportParamsVO;
import com.imes.domain.entities.ppc.vo.PpcProducePlanVo;
import com.imes.domain.entities.ppc.vo.ProjectStoreVo;
import com.imes.domain.entities.query.model.base.Model;
import com.imes.domain.entities.query.model.vo.QueryModelConfigWrap;
import com.imes.domain.entities.query.plugin.imports.template.ppc.PpcProducePlanOrdinaryImportVo;
import com.imes.domain.entities.query.plugin.imports.template.ppc.PpcProducePlanSimpleImportVo;
import com.imes.domain.entities.query.plugin.imports.template.ppc.PpcProducePlanSimpleImportVoNew;
import com.imes.domain.entities.query.template.ppc.*;
import com.imes.domain.entities.system.params.QueryProducePlanParams;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import com.imes.domain.entities.system.vo.UserTableFieldVO;
import com.imes.domain.entities.wms.WmsStock;
import com.imes.ppc.dao.ProducePlanDao;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.*;
import com.imes.ppc.utils.ResumeUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("api/ppc/plan")
@Api(tags = "生产计划相关接口")
public class ProducePlanController {


    @Autowired
    private SaleDetailService saleDetailService;

    @Autowired
    private SaleMainService saleMainService;

    @Autowired
    private ProducePlanService planService;
    @Autowired
    FeignService feignService;
    @Autowired
    private PpcProducePlanSchedulSnService ppcProducePlanSchedulSnService;
    @Autowired
    private PpcProducePlanSchedulService ppcProducePlanSchedulService;
    @Autowired
    private PpcProducePlanSchedulRePlanService ppcProducePlanSchedulRePlanService;
    @Autowired
    private ProducePlanDao planDao;
    @Autowired
    private PpcTransactionService ppcTransactionService;
    @Autowired
    PpcProjectErpInputService erpInputService;

    @Value("${imes.ppc.template.ppcProducePlanTemplateSimple}")
    private String ppcProducePlanTemplateSimple;

    @Value("${imes.ppc.template.ppcProducePlanTemplateOrdinary}")
    private String ppcProducePlanTemplateOrdinary;


    @GetMapping("/initDataPlanOrder")
    @ApiOperation(value = "初始化数据", httpMethod = "GET")
    public Result initDataPlanOrder() throws Exception {
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("WORKSHOP_ALL", feignService.getALLWorkshopList());
        reMap.put("PP_STATUS", feignService.getDictCode("PP_STATUS"));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 生产计划查询全部
     *
     * @return
     */
    @GetMapping("/queryAll")
    @ApiOperation(value = "生产计划查询全部", httpMethod = "GET")
    public Result query(@ApiParam("分页页数") @RequestParam("pageNum") Integer pageNum,
                        @ApiParam("分页条数")  @RequestParam("pageSize") Integer pageSize,
                        @RequestParam Map<String, Object> map) throws Exception {

        Map resultMap = new HashMap();
        UserTableFieldVO userTableFields = feignService.queryUserTableField(RedisUtils.getUserCode(), "plan_sale");
        if (StringUtils.isNullOrBlank(userTableFields.getColumns())) {
            AssertUtil.throwException("无权限访问！需管理员配置用户字段权限！");
        }
        map.put("queryFields", userTableFields.getColumns().replace("ppc_sale_detail.id,", ""));// 去除默认补充的子表id，否则sql报错
        map.put("statusIn", StringUtils.str2In(map.get("statusIn")));
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProducePlanVo> planList = planService.queryListByFields(map);

        resultMap.put("userFields", userTableFields.getUserFields());
        resultMap.put("showFields", userTableFields.getShowFields());
        resultMap.put("list", planList);
        resultMap.put("total", page.getTotal());
        List<SysDictDetailVo> sysDictDetailVos = feignService.getDictCode("PLAN_SCHEDUL_PRINT_TYPE");
        if (!sysDictDetailVos.isEmpty()) {
            resultMap.put("printType", sysDictDetailVos.get(0).getLabel());
        }
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    @GetMapping("/findProducePlanVo")
    @ApiOperation(value = "查询生产情况信息", response = Result.class)
    public Result findProducePlanVo(ProductionStatusQueryVo vo) throws Exception {
        List<String> uniqueCodes = new ArrayList<>();
        if (!StringUtils.isNullOrBlank(vo.getProcessFlag())) {
            List<String> soNos = Arrays.asList(vo.getProcessFlag().split(Query.SPLIT_CHAR));
            if (CollectionUtils.isNotEmpty(soNos)) {
                uniqueCodes = planDao.selectForsoNos(soNos);
            }
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProducePlanVo> producePlanVoList = planService.findProducePlanVo(vo,  uniqueCodes,false);
        producePlanVoList.forEach(item -> {
            item.setWorkTime(new BigDecimal(item.getWorkTime()).stripTrailingZeros().toPlainString());
            item.setPlanId(item.getId());
            item.setShowComplete(!StringUtils.isNullOrBlank(item.getScheStatus()) && !item.getScheStatus().contains(Constants.PP_STATUS_10)
                    && !item.getScheStatus().contains(Constants.PP_STATUS_20) && !item.getScheStatus().contains(Constants.PP_STATUS_25)
                    && !item.getScheStatus().contains(Constants.PP_STATUS_30));
        });
        return Result.SUCCESS(new PageResult(null, producePlanVoList));
    }


    @GetMapping("/findProducePlanVoStat")
    @ApiOperation(value = "查询生产计划列表页面合计值", response = Result.class)
    public Result findProducePlanVoSumProduceQty(ProductionStatusQueryVo vo) throws Exception {
        Map<String, Object> stat = new HashMap();
        //是否只有生产计划的条件 0 否  1 是
        String paramBelongPlan = null;
        if (StrUtil.isBlank(vo.getStatus())) {
            vo.setStatus("10∫20∫30∫40∫90");
        }
        vo.setOrderBy(null);
        vo.setPageNum(null);
        vo.setPageSize(null);
        Query q = Query.build().initParams(vo);
        List<PpcProducePlanVo> ppcProducePlanVoList = planDao.selectSumAll(q);
        BigDecimal sumProduceQty = ppcProducePlanVoList.isEmpty() ? BigDecimal.ZERO : ppcProducePlanVoList.get(0).getProduceQty();
        String sumWorkTime = ppcProducePlanVoList.isEmpty() ? "0" : ppcProducePlanVoList.get(0).getWorkTime();
        BigDecimal sumGoodQty = ppcProducePlanVoList.isEmpty() ? BigDecimal.ZERO : ppcProducePlanVoList.get(0).getScheGoodQty();
        stat.put("produceQty", sumProduceQty.stripTrailingZeros());
        stat.put("workTime", new BigDecimal(sumWorkTime).stripTrailingZeros());
        stat.put("scheGoodQty", sumGoodQty.stripTrailingZeros());
        return Result.SUCCESS(new PageResult(null, ListUtil.toList(stat)));
    }


    @GetMapping("/findProducePlanVoCount")
    @ApiOperation(value = "查询生产计划列表页面总页数", response = Result.class)
    public Result findProducePlanVoTotal(ProductionStatusQueryVo vo) throws Exception {
        long count = 0;
        try {
            List<String> uniqueCodes = new ArrayList<>();
            if (!StringUtils.isNullOrBlank(vo.getProcessFlag())) {
                List<String> soNos = Arrays.asList(vo.getProcessFlag().split(Query.SPLIT_CHAR));
                if (CollectionUtils.isNotEmpty(soNos)) {
                    uniqueCodes = planDao.selectForsoNos(soNos);
                }
            }
            vo.setOrderBy(null);
            count = planService.findProducePlanVoCount(vo, uniqueCodes);
        } catch (Exception e) {

        }
        return new Result(ResultCode.SUCCESS, count);
    }


    @GetMapping("/findProducePlanTreeVo")
    @ApiOperation(value = "根据计划单号查询下级计划", response = Result.class)
    public Result findProducePlanTreeVo(@ApiParam("父计划单号") @RequestParam("parentPpNo") String parentPpNo) throws Exception {
        List<PpcProducePlanVo> list = planService.findProducePlanTreeVo(parentPpNo);
        list.forEach(vo -> {
            vo.setPlanId(vo.getId());
        });
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/findPlanAllQueryVo")
    @ApiOperation(value = "查询生产记录列表", response = Result.class)
    public Result findPlanAllQueryVo(planAllQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<planAllQueryVo> list = planService.findPlanAllQueryVo(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }


    /**
     * 移动端查看计划
     *
     * @param saleDetailId
     * @return
     */
    @GetMapping("/queryPlanMessage")
    @ApiOperation(value = "查看生产计划", httpMethod = "GET")
    public Result queryPlanMessage(@ApiParam("订单行号")  String saleDetailId) throws Exception {
        return new Result(ResultCode.SUCCESS, planService.getPlanMessage(saleDetailId));
    }


    /***
     * 移动端 计划进度查询
     * @param statsIn
     * @return
     */
    @GetMapping("/planScheduleByMobile")
    @ApiOperation(value = "计划进度查询", httpMethod = "GET")
    public Result planScheduleByMobile(@ApiParam("状态集合")  String[] statsIn) throws Exception {
        List<Map<String, Object>> result = planService.planScheduleByMobile(statsIn);
        return new Result(ResultCode.SUCCESS, result);
    }


    /***
     * 移动端 订单计划进度查询
     * @param statsIn
     * @return
     */
    @GetMapping("/sailPlanScheduleByMobile")
    @ApiOperation(value = "订单计划进度查询", httpMethod = "GET")
    public Result sailPlanScheduleByMobile(@ApiParam("状态集合") String[] statsIn,@ApiParam("产线编码") String lineCode) throws Exception {
        List<Map<String, Object>> result = planService.sailplanScheduleByMobile(statsIn, lineCode);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 产线下拉
     *
     * @return
     */
    @GetMapping("/selectLine")
    @ApiOperation(value = "产线下拉", httpMethod = "GET")
    public Result selectLine() throws Exception {
        List<Map<String, Object>> result = feignService.selectLine();
        return new Result(ResultCode.SUCCESS, result);
    }

    /***
     * 移动端 订单计划优先级修改
     * @param id 生产计划id
     * @param priorityParam 0,优先 1,延后
     * @return
     */
    @GetMapping("/modifySailPlanPriority")
    @ApiOperation(value = "订单计划优先级修改", httpMethod = "GET")
    public Result modifySailPlanPriority(@ApiParam("生产计划id") @RequestParam("id") String id,@ApiParam(" 0 下移   1 上移")  @RequestParam("priorityParam") Integer priorityParam) throws Exception {
        planService.modifySailPlanPriority(id, priorityParam);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 移动端 计划进度详情查询
     *
     * @param planId
     * @return
     */
    @GetMapping("/prodecePlanInfo/{planId}")
    @ApiOperation(value = "计划进度详情查询", httpMethod = "GET")
    public Result prodecePlanInfo(@ApiParam("生产计划id") @PathVariable("planId") String planId) throws Exception {
        Map<String, Object> result = planService.prodecePlanInfo(planId);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * PDA生产计划查询全部
     *
     * @return
     */
    @GetMapping("/queryByMobile")
    @ApiOperation(value = "PDA生产计划查询全部", httpMethod = "GET")
    public Result queryByMobile(@ApiParam("分页页数") @RequestParam("pageNum") Integer pageNum,
                                @ApiParam("分页条数") @RequestParam("pageSize") Integer pageSize,
                                @ApiParam("用户编码") @RequestParam("userCode") String userCode,
                                @ApiParam("状态") @RequestParam("statuss") String statuss,
                                @ApiParam("计划单号") @RequestParam("ppNo") String ppNo,
                                @ApiParam("订单行号")  @RequestParam(defaultValue = "", value = "saleDetailId") String saleDetailId) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> map = new HashMap();
        if (!StringUtils.isNullOrBlank(statuss)) {
            map.put("statusIn", StringUtils.str2In(statuss));
        }
        map.put("planerCode", userCode);
        map.put("ppNo", ppNo);
        map.put("saleDetailId", saleDetailId);
        List<PpcProducePlan> planList = planService.queryList(map);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("list", planList);
        result.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, result);

    }

    /**
     * 批量新增计划
     *
     * @param planList 计划集合
     * @return
     */
    @PostMapping("/batchSavePlan")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "批量新增计划", httpMethod = "POST")
    public Result batchSavePlan(@RequestBody List<PpcProducePlan> planList) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        planService.updateProducePlan(planList,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    @DeleteMapping("/dltProducePlan/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "删除计划", httpMethod = "GET")
    public Result dltProducePlan(@ApiParam("生产计划id") @PathVariable("id") String planId) throws Exception {
//        planService.dltProducePlan(planId);
        planService.batchDeletePlanById(Collections.singletonList(planId), true,new ArrayList<>());
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/checkEnableChangeWorkshop/{id}")
    @ApiIgnore
    public Result checkEnableChangeWorkshop(@ApiParam("生产计划id") @PathVariable("id") String planId) throws Exception {
        boolean boo = planService.checkEnableChangeWorkshop(planId);
        return new Result(ResultCode.SUCCESS, boo);
    }


    @PutMapping("/issuePp/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "下达计划", httpMethod = "GET")
    public Result issuePp(@ApiParam("生产计划id") @PathVariable("id") String id) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        if (StringUtils.isNullOrBlank(id)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        planService.issuePp(id,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    @PutMapping("/batchIssuePp")
    @ApiOperation(value = "批量下达计划", httpMethod = "GET")
    public Result batchIssuePp(@ApiParam("生产计划id集合") @RequestParam("ids") List<String> ids) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        String result = planService.batchIssuePp(ids,resumeList);
        feignService.saveResume(resumeList);
        if ("1".equals(result)) {
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(99998, result, false);
        }
    }

    @PutMapping("/reverseIssued/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "反下达计划", httpMethod = "GET")
    public Result reverseIssued(@ApiParam("生产计划id") @PathVariable("id") String id) throws Exception {
        if (StringUtils.isNullOrBlank(id)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        planService.reverseIssued(id);
        return new Result(ResultCode.SUCCESS);
    }

    @PutMapping("/batchReverseIssuePp")
    @ApiOperation(value = "批量反下达计划", httpMethod = "GET")
    public Result batchReverseIssuePp(@RequestParam("ids") List<String> ids) throws Exception {
        Vector<Resume> resumeList = new Vector<>();
        String result = planService.batchReverseIssuePp(ids,resumeList);
        if ("1".equals(result)) {
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(99998, result, false);
        }
    }

    /**
     * 批量生成计划排产信息
     *
     * @param ppcProducePlanVos
     * @return
     */
    @PostMapping("/batchInsetProduction")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    public Result batchInsetProduction(@RequestBody List<PpcProducePlanVo> ppcProducePlanVos,
                                       @RequestParam("planStart") String planStart,
                                       @RequestParam("planEnd") String planEnd,
                                       @RequestParam("routeCode") String routeCode,
                                       @RequestParam("isRelease") String isRelease,
                                       @RequestParam(required = false, value = "remark") String remarks
    ) throws Exception {
        String result = planService.batchInsetProduction(ppcProducePlanVos, planStart, planEnd, routeCode, remarks, isRelease);
        if ("1".equals(result)) {
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(99998, result, false);
        }
    }


    @PutMapping("/forceComplete/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "强制完工计划", httpMethod = "POST")
    @ApiIgnore
    public Result forceComplete(@PathVariable("id") String id) throws Exception {
        planService.forceComplete(id);
        return new Result(ResultCode.SUCCESS);
    }

    @PutMapping("/planReverseCompletion/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产计划反完工", httpMethod = "POST")
    public Result planReverseCompletion(@ApiParam("生产计划id") @PathVariable("id") String id) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        planService.planReverseCompletion(id,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    @PutMapping("/planCompletion/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产计划完工", httpMethod = "POST")
    public Result planCompletion(@ApiParam("生产计划id") @PathVariable("id") String id) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        planService.planCompletion(id,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/planFinish")
    @ApiIgnore(value = "自动产线生产完工，该方法不要设置事务，service被多方使用，事务在service层")
    public Result planFinish(@RequestBody Map map) throws Exception {
        ExcuteResult excuteResult = planService.planFinish(map);
        if (!excuteResult.isSuccess()) {
            return new Result(ResultCode.BIZ_ERROR.code(), excuteResult.getMessage(), false);
        }
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/getProeucePlan/{id}")
    @ApiOperation(value = "通过id查询计划", httpMethod = "GET")
    public Result getProeucePlan(@ApiParam("生产计划id") @PathVariable("id") String id) throws Exception {
        PpcProducePlan plan = planService.getById(id);
        Map<String, Object> stringObjectMap = BeanMapUtils.beanToMap(plan);
        if (!StringUtils.isNullOrBlank(stringObjectMap.get("materialCode"))) {
            Promaterial material = feignService.findMaterialByCode(stringObjectMap.get("materialCode").toString());
            stringObjectMap.put("specification", material.getSpecification());
        }
        BigDecimal goodQty = new BigDecimal("0");
        BigDecimal badQty = new BigDecimal("0");
        List<PpcProducePlanSchedul> ppcProducePlanScheduls = ppcProducePlanSchedulService.queryByProducePlanId(id);
        for (PpcProducePlanSchedul ppcProducePlanSchedul : ppcProducePlanScheduls) {
            goodQty = goodQty.add(ppcProducePlanSchedul.getGoodQty());
            badQty = badQty.add(ppcProducePlanSchedul.getBadQty());
        }
        stringObjectMap.put("goodQty", goodQty);
        stringObjectMap.put("badQty", badQty);
        return new Result(ResultCode.SUCCESS, stringObjectMap);
    }

    @PostMapping("/uptProducePlan")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "修改计划", httpMethod = "POST")
    public Result uptProducePlan(@RequestBody PpcProducePlan plan) throws Exception {
        List<PpcProducePlan> list = new ArrayList<>();
        list.add(plan);
        planService.updateProducePlan(list,new ArrayList<>());
        return new Result(ResultCode.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/saveProducePlan"})
    @ApiOperation(value = "保存计划", httpMethod = "POST")
    public Result saveProducePlan(@RequestBody PpcProducePlan ppcProducePlan) throws Exception {
        Map map = new HashMap();
        map.put("id", this.planService.saveProducePlan(ppcProducePlan));
        return new Result(ResultCode.SUCCESS, map);
    }

    @Transactional(rollbackFor = Exception.class)
    @GetMapping({"/deletePpcProducePlanById"})
    @ApiOperation(value = "删除计划", httpMethod = "GET")
    public Result deletePpcProducePlanById(@ApiParam("生产计划id") @RequestParam("id") String id) throws Exception {
        this.planService.dltProducePlan(id);
        return new Result(ResultCode.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/batchDeletePpcProducePlanById"})
    @ApiOperation(value = "批量删除计划", httpMethod = "POST")
    public Result batchDeletePpcProducePlanById(String[] ids) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        this.planService.batchDeletePlanById(Arrays.asList(ids), true,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/batchDeletePlanById"})
    @ApiOperation(value = "批量删除计划（新）", httpMethod = "POST")
    public Result batchDeletePlanById(@ApiParam("生产计划id集合")@RequestBody List<String> ids) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        this.planService.batchDeletePlanById(ids, true,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }


    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/planSchedulExport"})
    @ApiOperation(value = "排产单二维码导出通过帆软方式", httpMethod = "POST")
    public Result planSchedulExport(@RequestBody List<String> ids) throws Exception {
        StringBuilder stringBuilder = this.planService.planSchedulExport(ids);
        return new Result(ResultCode.SUCCESS, stringBuilder);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/planSchedulSNExport"})
    @ApiOperation(value = "排产单SN码导出通过帆软方式", httpMethod = "POST")
    public Result planSchedulSNExport(@ApiParam("生产计划id集合") @RequestBody List<String> ids) throws Exception {
        StringBuilder stringBuilder = this.planService.planSchedulSNExport(ids);
        return new Result(ResultCode.SUCCESS, stringBuilder);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/planSchedulSingleSNExport"})
    @ApiOperation(value = "排产单单个SN码导出通过帆软方式", httpMethod = "POST")
    public Result planSchedulSingleSNExport(@RequestBody PlanSchedulSingleSNExportParamsVO params) throws Exception {
        StringBuilder stringBuilder = this.planService.planSchedulSingleSNExport(params.getPlanId(), params.getSnNo());
        return new Result(ResultCode.SUCCESS, stringBuilder);
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/planSchedulExportByWord"})
    @ApiOperation(value = "排产单二维码导出通过Word方式", httpMethod = "POST")
    public Result planSchedulExportByWord(@ApiParam("生产计划id集合") @RequestBody List<String> ids, HttpServletResponse response, HttpServletRequest request) throws Exception {
        this.planService.planSchedulExportByWord(ids, response, request);
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/planSchedulSNExportByWord"})
    @ApiOperation(value = "排产单SN码导出通过Word方式", httpMethod = "POST")
    public Result planSchedulSNExportByWord(@ApiParam("生产计划id集合") @RequestBody List<String> ids, HttpServletResponse response, HttpServletRequest request) throws Exception {
        this.planService.planSchedulSNExportByWord(ids, response, request);
        return null;
    }


    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/updateStatus"})
    @ApiOperation(value = "修改计划装", httpMethod = "POST")
    public Result updateStatus(@ApiParam("生产计划id") @RequestParam("id") String id) throws Exception {
        this.planService.updateStatus(id);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping({"/getPpcProducePlanById"})
    @ApiOperation(value = "通过id查询计划", httpMethod = "GET")
    public Result getPpcProducePlanById(@ApiParam("生产计划id") @RequestParam("id") String id) throws Exception {
        Map map = new HashMap();
        map.put("data", this.planService.getPpcProducePlanById(id));
        return new Result(ResultCode.SUCCESS, map);
    }

    @PostMapping({"/updateProducePlan"})
    @ApiOperation(value = "修改计划", httpMethod = "POST")
    @ApiIgnore
    public Result updateProducePlan(@RequestBody PpcProducePlan ppcProducePlan) throws Exception {
        this.planService.updateProducePlan(ppcProducePlan);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping({"/getCompleteInfo"})
    public Result getCompleteInfo(@ApiParam("生产计划id") @RequestParam("id") String id) throws Exception {
        Map<String, Object> map = planService.getCompleteInfo(id);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * from mobile
     * 下达命令
     *
     * @param id
     * @return
     */
    @GetMapping({"/planIssue"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "下达命令", httpMethod = "GET")
    @ApiIgnore
    public Result planIssue(@RequestParam("id") String id) throws Exception {
        planService.planStart(id);
        return new Result(ResultCode.SUCCESS);
    }

    //实验室MOM闭环逻辑
    @PostMapping("/closeLoop/{workOrderId}")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    public Result closeLoop(@ApiParam("派工单id")  @PathVariable("workOrderId") String workOrderId, @RequestParam("number") String number) throws Exception {
        planService.closeLoop(workOrderId, new BigDecimal(number));
        return new Result(ResultCode.SUCCESS);
    }

    public Result queryForDistributeTask() throws Exception {
        Map<String, Object> map = new HashMap();
        List<PpcProducePlan> planList = planService.queryList(map);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping({"/processFinish"})
    public Result processFinish(@RequestParam Map map) throws Exception {
        planService.processFinishTest(map);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping({"/teaProcessFinish"})
    public Result teaProcessFinish(@RequestParam Map map) throws Exception {
        planService.teaProcessFinish(map);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/businessPushDownPlan")
    @ApiOperation(value = "销售订单下推(生产计划)", httpMethod = "POST")
    public Result businessPushDownPlan(@RequestBody List<PpcSaleMainSearchVo> voList) throws Exception {
        if (!CollectionUtils.isNotEmpty(voList)) {
            AssertUtil.throwException("下推生产计划失败,未选中行数据!");
        }
        Map<String, String> result = new HashMap<>();
        //整单下推将主单id替换为明细id
        Set<String> detailIdList = new HashSet<>();
        if (!StringUtils.isNullOrBlank(voList.get(0).getShowMode()) && "main".equals(voList.get(0).getShowMode())) {
            for (PpcSaleMainSearchVo ppcSaleMainSearchVo : voList) {
                List<PpcSaleDetail> detailList = saleDetailService.findByMainId(ppcSaleMainSearchVo.getId());
                detailList.forEach(e -> {
                    detailIdList.add(e.getId());
                });
            }
        } else {
            voList.forEach(e -> {
                if (!StringUtils.isNullOrBlank(e.getDetailId())) {
                    detailIdList.add(e.getDetailId());
                } else {
                    if (!StringUtils.isNullOrBlank(e.getId())) {
                        detailIdList.add(e.getId());
                    }
                }
            });
        }
        if (detailIdList.isEmpty()) {
            AssertUtil.throwException("没有明细无法下推!");
        }
        QueryModelConfigWrap userConfig = feignService.getUserConfig("1742075366094147584", RedisUtils.getUserCode());//下推生产默认类型
        QueryModelConfigWrap subPlanConfig = feignService.getUserConfig("1742075366094147583", RedisUtils.getUserCode());//是否生产子计划
        StringBuffer msg = new StringBuffer();
        CountDownLatch countDownLatch = new CountDownLatch(detailIdList.size());//计数器
        List<String> planIdList = new Vector<>();
        RequestContextHolderUtils.setThreadLocalSchemaAndTraceId();
        ArrayList<Resume> resumeList = new ArrayList<>();
        for (String detailId : detailIdList) {
            ppcTransactionService.detailConvertPlan(detailId, userConfig.getConfigValue(), msg, planIdList, countDownLatch, "", "", subPlanConfig.getConfigValue(),resumeList);
        }
        countDownLatch.await();
        if (!planIdList.isEmpty()) {
            result.put("id", planIdList.stream().collect(Collectors.joining(",")));
        }
        result.put("msg", msg.toString());
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS, result);
    }


    @PostMapping("/selectSaleDetailToPlan")
    @ApiOperation(value = "生产计划反选订单生成计划", httpMethod = "POST")
    public Result selectSaleDetailToPlan(@ApiParam("订单行号id")  @RequestBody List<String> detailIdList) throws Exception {
        Map<String, String> result = new HashMap<>();
        StringBuffer msg = new StringBuffer();
        CountDownLatch countDownLatch = new CountDownLatch(detailIdList.size());//计数器
        List<String> planIdList = new Vector<>();
        String userCode = RedisUtils.getUserCode();
        String userName = RedisUtils.getUserName();

        List<SysDictDetailVo> sysDictDetailVoList = feignService.getDictCode("ADD_PLAN_PARAMS");
        QueryModelConfigWrap subPlanConfig = feignService.getUserConfig("1742075366094147582", RedisUtils.getUserCode());//是否生产子计划
        String orderType = "1";
        for (SysDictDetailVo sysDictDetailVo : sysDictDetailVoList) {
            if ("0".equals(sysDictDetailVo.getCode()) && "1".equals(sysDictDetailVo.getIsDefault())) {
                orderType = "0";
            }
        }
        RequestContextHolderUtils.setThreadLocalSchemaAndTraceId();
        ArrayList<Resume> resumeList = new ArrayList<>();
        for (String detailId : detailIdList) {
            ppcTransactionService.detailConvertPlan(detailId, orderType, msg, planIdList, countDownLatch, userCode, userName, subPlanConfig.getConfigValue(),resumeList);
        }
        countDownLatch.await();
        if (!StringUtils.isNullOrBlank(msg.toString())) {
            AssertUtil.throwException(msg.toString());
        }
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 生产计划新增/更新
     *
     * @param map
     * @return
     * @throws Exception
     */
    @PostMapping({"/insertProducePlan"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产计划新增/更新", httpMethod = "POST")
    public Result insertProducePlan(@RequestBody Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        if (map.get("orderType") == null || "1".equals(map.get("orderType").toString())) {
            planService.insertProducePlan(map);
        } else {
            //简单生产计划
            planService.insertProduceSimplePlan(map, null);
        }
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping({"/insertProducePlanSimple"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "简单生产计划新增/更新", httpMethod = "POST")
    @ApiIgnore
    public Result insertProducePlanSimple(@RequestPart Map<String, Object> data, @RequestPart(required = false, value = "file") MultipartFile[] file) throws Exception {
        if (data.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        if (data.get("orderType") == null || "1".equals(data.get("orderType").toString())) {
            planService.insertProducePlan(data);
        } else {
            planService.insertProduceSimplePlan(data, file);
        }
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 生产计划新增/更新
     *
     * @param ppcProducePlanDTO
     * @return
     * @throws Exception
     */
    @PostMapping({"/insertProducePlanByDTO"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产计划新增/更新", httpMethod = "POST")
    public Result insertProducePlan(@RequestBody PpcProducePlanDTO ppcProducePlanDTO) throws Exception {
        if (StringUtils.isNullOrBlank(ppcProducePlanDTO.getPlanerCode())){
            ppcProducePlanDTO.setPlanerCode("");
            ppcProducePlanDTO.setPlanerName("");
        }
        ArrayList<Resume> resumeList = new ArrayList<>();
        CustomUtil.handle(Model.$0267, ppcProducePlanDTO, true);
        String id = "";
        if (StringUtils.isNullOrBlank(ppcProducePlanDTO.getSkuCode())) {
            planService.checkSkuCode(ppcProducePlanDTO.getMaterialCode());
        }
        if (ppcProducePlanDTO.getOrderType() == null || "1".equals(ppcProducePlanDTO.getOrderType())) {
            id = planService.insertProducePlanByDTO(ppcProducePlanDTO, true,resumeList);
        } else {
            id = planService.insertProduceSimplePlanByDTO(ppcProducePlanDTO,resumeList);
        }
        List<Resume> result = ResumeUtils.removeRepeat(resumeList);
        feignService.saveResume(result);
        return new Result(ResultCode.SUCCESS, id);
    }

    /**
     * 生产计划变更
     *
     * @param ppcProducePlanDTO
     * @return
     * @throws Exception
     */
    @PostMapping({"/changeProducePlanByDTO"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产计划变更", httpMethod = "POST")
    public Result changeProducePlanByDTO(@RequestBody PpcProducePlanDTO ppcProducePlanDTO) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        planService.changeProducePlanByDTO(ppcProducePlanDTO,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS, ppcProducePlanDTO.getId());
    }


    /**
     * 订单生成简单生产计划新增
     *
     * @param map
     * @return
     * @throws Exception
     */
    @PostMapping({"/insertSimpleProducePlanBySale"})
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "订单生成简单生产计划新增", httpMethod = "GET")
    public Result insertSimpleProducePlanBySale(@RequestBody Map<String, Object> map) throws Exception {
        planService.insertSimpleProducePlanBySale(map);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 生产计划新增
     *
     * @param map
     * @return
     * @throws Exception
     */
    @PostMapping({"/findSaleBySn"})
    @ApiOperation(value = "根据sn码，查询订单信息", httpMethod = "POST")
    public Result selectSaleBySn(@RequestBody Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<JSONObject> data = ppcProducePlanSchedulSnService.selectSaleInfo(map);
        return ResultUtils.SUCCESS(data);
    }


    @PostMapping({"/processCardExport"})
    @ApiOperation(value = "工序转序卡导出", httpMethod = "POST")
    public Result processCardExport(@ApiParam("生产计划id集合") @RequestBody List<String> ids) throws Exception {
        return ResultUtils.SUCCESS(planService.processCardExportFr(ids));
    }


    @PostMapping({"/processCardExportForKaiyu"})
    @ApiOperation(value = "铠宇通用工序转序卡导出", httpMethod = "POST")
    public Result processCardExportForKaiyu(@ApiParam("生产计划id集合") @RequestBody List<String> ids) throws Exception {
        return ResultUtils.SUCCESS(planService.processCardExportFrKaiyu(ids, "1"));
    }

    @PostMapping({"/processCardCustomExportForKaiyu"})
    @ApiOperation(value = "铠宇定制工序转序卡导出", httpMethod = "POST")
    public Result processCardCustomExportForKaiyu(@ApiParam("生产计划id集合") @RequestBody List<String> ids) throws Exception {
        return ResultUtils.SUCCESS(planService.processCardExportFrKaiyu(ids, "2"));
    }

    @PostMapping({"/processCardCustomByCustomerExportForKaiyu"})
    @ApiOperation(value = "铠宇定制客户订单工序转序卡导出", httpMethod = "POST")
    public Result processCardCustomExportForKaiyuprocessCardCustomByCustomerExportForKaiyu(@ApiParam("生产计划id集合") @RequestBody List<String> ids) throws Exception {
        return ResultUtils.SUCCESS(planService.processCardExportFrKaiyu(ids, "3"));
    }

    /**
     * 1-精车车间；2-压铸车间；3-注塑107车间；4-注塑H27
     * */
    @PostMapping({"/processCardByCustomerExportForKaiyu"})
    @ApiOperation(value = "铠宇工序转序卡导出", httpMethod = "POST")
    public Result processCardByCustomerExportForKaiyu(@ApiParam("生产计划id集合") @RequestBody List<String> ids, @RequestParam("cardType") String cardType) throws Exception {
        return ResultUtils.SUCCESS(planService.processCardExportFrKaiyuByType(ids, cardType));
    }

    /**
     * 检查BOM下是否有未完工的生产计划
     *
     * @param bomCode
     * @param bomVer
     * @return
     * @throws Exception
     */
    @GetMapping({"/checkProducePlanByBomCodeAndVer"})
    @ApiOperation(value = "检查BOM下是否有未完工的生产计划", httpMethod = "GET")
    public Result checkProducePlanByBomCodeAndVer(@ApiParam("BOM编码")@RequestParam(value = "bomCode") String bomCode,@ApiParam("BOM版本") @RequestParam(value = "bomVer") String bomVer) throws Exception {
        Boolean flag = planService.checkProducePlanByBomCodeAndVer(bomCode, bomVer);
        return new Result(ResultCode.SUCCESS, flag);
    }

    @PostMapping({"/countProducePlanByBomCodeIn"})
    @ApiOperation(value = "统计BOM下是否有未完工的生产计划", httpMethod = "POST")
    public Result checkProducePlanByBomCodeAndVer(@RequestBody List<String> bomCodeList) {
        return new Result(ResultCode.SUCCESS, planService.countProducePlanByBomCodeIn(bomCodeList));
    }

    /**
     * 同步ERP生产数据
     *
     * @return
     * @throws Exception
     */
    @PostMapping({"/addProducePlanByERP"})
    @ApiOperation(value = "同步ERP生产数据", httpMethod = "POST")
    public Result addProducePlanByERP(@RequestBody List<Map> planList) throws Exception {
        String reMsg = planService.addProducePlanByERP(planList);
        return new Result(ResultCode.SUCCESS.code(),reMsg,true);
    }


    /**
     * 同步ERP生产数据
     *
     * @return
     * @throws Exception
     */
    @PostMapping({"/addProducePlanNocheck"})
    @ApiOperation(value = "同步生产数据,只校验计划单号，其他不校验，直接保存", httpMethod = "POST")
    public Result addProducePlanNocheck(@RequestBody PpcProducePlan plan) throws Exception {
        planService.addProducePlanNocheck(plan);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/getProducePlanTree/{planId}")
    @ApiOperation(value = "获取计划树", httpMethod = "GET")
    public Result getProducePlanTree(@ApiParam("生产计划id") @PathVariable("planId") String planId) throws Exception {
        ArrayList<Map<String, Object>> result = planService.getProducePlanTree(planId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getPlanStatusDistribution")
    @ApiOperation(value = "获取计划状态分布", httpMethod = "GET")
    public Result getPlanStatusDistribution() {
        return new Result(ResultCode.SUCCESS, planService.getPlanStatusDistribution());
    }

    @GetMapping("/getCompletionRateOnSchedule")
    @ApiOperation(value = "获取按期完成率", httpMethod = "GET")
    public Result getCompletionRateOnSchedule() {
        return new Result(ResultCode.SUCCESS, planService.getCompletionRateOnSchedule());
    }

    @GetMapping("/getProduceByProcess")
    @ApiOperation(value = "每月产出", httpMethod = "GET")
    public Result getProduceByProcess() {
        return new Result(ResultCode.SUCCESS, planService.getProduceByProcess());
    }

    @GetMapping("/getPlanMessage")
    @ApiOperation(value = "获取计划总数,计划完成率，完成计划数，按期完成率，总工时，总生产", httpMethod = "GET")
    public Result getPlanMessage() throws ParseException {
        return new Result(ResultCode.SUCCESS, planService.getPlanMessage());
    }

    @GetMapping("/getAnnualReport")
    @ApiOperation(value = "年度报表", httpMethod = "GET")
    public Result getAnnualReport() {
        return new Result(ResultCode.SUCCESS, planService.getAnnualReport());
    }

    @GetMapping("/getPlanLineMessage")
    @ApiOperation(value = "产线产量相关数据", httpMethod = "GET")
    public Result getPlanLineMessage() {
        return new Result(ResultCode.SUCCESS, planService.getPlanLineMessage());
    }


    @GetMapping("/getProduceMassAnalysis")
    @ApiOperation(value = "生产质量分析", httpMethod = "GET")
    public Result getProduceMassAnalysis() {
        return new Result(ResultCode.SUCCESS, planService.getProduceMassAnalysis());
    }

    @GetMapping("/getWorkFinishBadCodeDistribution")
    @ApiOperation(value = "不良项分布", httpMethod = "GET")
    public Result getWorkFinishBadCodeDistribution() {
        return new Result(ResultCode.SUCCESS, planService.getWorkFinishBadCodeDistribution());
    }

    @GetMapping("/getProduceSchedule")
    @ApiOperation(value = "生产进度查看-排产单维度", httpMethod = "GET")
    public Result getProduceSchedule() {
        return new Result(ResultCode.SUCCESS, planService.getProduceSchedule());
    }

    @GetMapping("/getProduceProgress")
    @ApiOperation(value = "生产进度查看-工序维度", httpMethod = "GET")
    public Result getProduceProgress() {
        return new Result(ResultCode.SUCCESS, planService.getProduceProgress());
    }

    @PostMapping("/saveMomProducePlan")
    @ApiOperation(value = "MOM标准api保存生产计划单接口（插件接口）", httpMethod = "POST")
    public Result saveMomProducePlan(@RequestBody Map map) throws Exception {
        String result = planService.saveMomProducePlan(map);
        return new Result(ResultCode.SUCCESS, result);
    }
    @PostMapping("/saveMomProducePlanApi")
    @ApiOperation(value = "MOM标准api保存生产计划单接口（Web API）,返回计划单号", httpMethod = "POST")
    public Result saveMomProducePlanApi(@RequestBody PpcProducePlan plan) throws Exception {
        String result = planService.saveMomProducePlanApi(plan);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getMomProducePlanMessage")
    @ApiOperation(value = "MOM标准api获取生产计划相关信息", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getMomProducePlanMessage(@ApiParam("开始时间") @RequestParam(required = false, value = "startDate") String startDate, @ApiParam("截止时间")@RequestParam(required = false, value = "endDate") String endDate) throws Exception {
        List<Map> result = ppcProducePlanSchedulService.getSchedulMessageSendToErp(startDate, endDate);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getMomProducePlanFeeding")
    @ApiOperation(value = "MOM标准api获取生产计划投料信息", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getMomProducePlanFeeding(@ApiParam("开始时间") @RequestParam(required = false, value = "startDate") String startDate,@ApiParam("截止时间") @RequestParam(required = false, value = "endDate") String endDate) throws Exception {
        List<Map> result = ppcProducePlanSchedulService.getMomProducePlanFeeding(startDate, endDate);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/queryMomProducePlanList")
    @ApiOperation(value = "MOM标准api查询生产计划单接口", httpMethod = "POST")
    public Result queryMomProducePlanList(@RequestBody QueryProducePlanParams params) {
        return new Result(ResultCode.SUCCESS, planService.queryMomProducePlanList(params));
    }

    @PostMapping("/queryList")
    @ApiOperation(value = "MOM查询生产计划单接口", httpMethod = "POST")
    public Result queryList(@RequestBody Map params) throws Exception {
        return new Result(ResultCode.SUCCESS, planService.queryList(params));
    }

    @PostMapping("/checkPlanNoIsExist")
    @ApiOperation(value = "MOM通过ppNo查询生产计划是否存在", httpMethod = "POST")
    public Result queryMomProducePlanList(@ApiParam("计划单号集合") @RequestBody List<String> ppNoIn) {
        return new Result(ResultCode.SUCCESS, planService.checkPlanNoIsExist(ppNoIn));
    }

    @PostMapping("/getPlanByPlanNoList")
    @ApiOperation(value = "MOM通过ppNo查询生产计划是否存在", httpMethod = "POST")
    public Result queryMomProducePlanListByPlanNoList(@RequestBody List<String> ppNoList) {
        return new Result(ResultCode.SUCCESS, planService.getPlanByPlanNoList(ppNoList));
    }

    @PostMapping("/queryAllMomProducePlanList")
    @ApiOperation(value = "MOM标准api查询所有生产计划单接口", httpMethod = "POST")
    public Result queryAllMomProducePlanList() {
        return new Result(ResultCode.SUCCESS, planService.queryAllMomProducePlanList());
    }

    @PostMapping("/reverseMomProducePlan")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "MOM标准api生产计划反下达接口", httpMethod = "POST")
    public Result reverseMomProducePlan(@ApiParam("计划单号") @RequestParam("ppNo") String ppNo) throws Exception {
        planService.reverseMomProducePlan(ppNo);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/deleteMomProducePlan")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "MOM标准api删除生产计划接口", httpMethod = "POST")
    public Result deleteMomProducePlan(@ApiParam("计划单号") @RequestParam("ppNo") String ppNo) throws Exception {
        planService.deleteMomProducePlan(ppNo);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/copyProducePlan")
    @ApiOperation(value = "复制计划", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result copyProducePlan(@ApiParam("计划单号集合")  @RequestParam("planId") String planId) throws Exception {
        planService.copyProducePlan(planId);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/getLineCompletionRate")
    @ApiOperation(value = "产线完工情况", httpMethod = "GET")
    public Result getLineCompletionRate() {
        return new Result(ResultCode.SUCCESS, planService.getLineCompletionRate());
    }

    @GetMapping("/getCustomerLineMessage")
    @ApiOperation(value = "产线报表", httpMethod = "GET")
    public Result getCustomerLineMessage() {
        return new Result(ResultCode.SUCCESS, planService.getCustomerLineMessage());
    }

    @GetMapping("/getWorkOrderMessage")
    @ApiOperation(value = "工单生产情况", httpMethod = "GET")
    public Result getWorkOrderMessage() {
        return new Result(ResultCode.SUCCESS, planService.getWorkOrderMessage());
    }

    @GetMapping("/getProductPlanByPcNo")
    @ApiOperation(value = "根据排产单号获取生产订单", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getProductPlanByPcNo(@ApiParam("排产单号")  @RequestParam(value = "pcNo") String pcNo) throws Exception {
        PpcProducePlan ppcProducePlan = planService.getProductPlanByPcNo(pcNo);
        return new Result(ResultCode.SUCCESS, ppcProducePlan);
    }

//    @GetMapping("/planUrgent")
//    @ApiOperation(value = "生产计划加急/反加急", httpMethod = "GET")
//    @Transactional(rollbackFor = Exception.class)
//    public Result planUrgent(@RequestParam("planId") String planId, @RequestParam("isUrgent") String isUrgent) throws Exception {
//        Integer result = planService.planUrgent(planId, isUrgent);
//        return new Result(ResultCode.SUCCESS);
//    }

    @GetMapping("/batchPlanUrgent")
    @ApiOperation(value = "批量生产计划加急/反加急", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchPlanUrgent(@ApiParam("计划id列表") @RequestParam("ids") List<String> ids,@ApiParam("0 反加急  1 加急")  @RequestParam("isUrgent") String isUrgent) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        Integer result = planService.batchPlanUrgent(ids, isUrgent,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 模板导出
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/templateSimple")
    @ApiOperation(value = "简单模板导出", httpMethod = "GET")
    public void downloadTemplateSimple(HttpServletRequest request, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            ClassPathResource resource = new ClassPathResource(ppcProducePlanTemplateSimple);
            String fileName = resource.getFilename();
            response.setContentType("application/octet-stream");
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            }
            in = resource.getInputStream();
            out = response.getOutputStream();
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (out != null) out.close();
                if (in != null) in.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /**
     * 模板导出
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/templateOrdinary")
    @ApiOperation(value = "普通模板导出", httpMethod = "GET")
    public void downloadTemplateOrdinary(HttpServletRequest request, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            ClassPathResource resource = new ClassPathResource(ppcProducePlanTemplateOrdinary);
            String fileName = resource.getFilename();
            response.setContentType("application/octet-stream");
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            }
            in = resource.getInputStream();
            out = response.getOutputStream();
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            out.flush();
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (out != null) out.close();
                if (in != null) in.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    /*
     * 简单模式导入
     * */
    @PostMapping("/simpleImport")
    @ApiOperation(value = "简单模式导入", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result simpleImport(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<PpcProducePlanSimpleImportVo> importList = ImportUtil.getData(PpcProducePlanSimpleImportVo.class, uploadFile);
        Map<String, Map> map = planService.checkSimpleImport(importList);
        for (String key : map.keySet()) {
            Map planMap = map.get(key);
            LinkedHashMap<String, PpcWorkOrder> processMap = (LinkedHashMap<String, PpcWorkOrder>) planMap.get("processList");
            //生成工序信息
            ArrayList<LinkedHashMap<String, Object>> processList = new ArrayList<>();
            for (String processKey : processMap.keySet()) {
                PpcWorkOrder ppcWorkOrder = processMap.get(processKey);
                LinkedHashMap<String, Object> process = new LinkedHashMap();
                process.put("processCode", ppcWorkOrder.getProcessCode());
                process.put("processName", ppcWorkOrder.getProcessName());
                process.put("processCode", ppcWorkOrder.getProcessCode());
                process.put("produceQty", ppcWorkOrder.getProduceQty());
                process.put("processCode", ppcWorkOrder.getProcessCode());
                process.put("pieceworkHour", ppcWorkOrder.getPieceworkHour());
                process.put("unitConversionRatio", ppcWorkOrder.getUnitConversionRatio());
                processList.add(process);
            }
            planMap.put("orderType", "0");
            planMap.put("processList", processList);
            planService.insertProduceSimplePlan(planMap, null);
        }
        return new Result(ResultCode.SUCCESS, "成功导入【" + 9 + "】条数据");
    }


    /*
     * 普通模式导入
     * */
    @PostMapping("/ordinaryImport")
    @ApiOperation(value = "普通模式导入", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result ordinaryImport(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<PpcProducePlanOrdinaryImportVo> importList = ImportUtil.getData(PpcProducePlanOrdinaryImportVo.class, uploadFile);
        StringBuffer sb = new StringBuffer();
        List<PpcProducePlan> ppcProducePlanList = planService.checkOrdinaryImport(importList, sb);
        int i = 0;
        for (PpcProducePlan plan : ppcProducePlanList) {
            Map<String, Object> map = BeanMapUtils.beanToMap(plan);
            if ("1".equals(map.get("subPlan").toString())) {
                map.put("subPlan", "true");
            } else {
                map.put("subPlan", "false");
            }
            map.put("planStartDate", DateUtils.transDate2Str10(plan.getPlanStartDate()));
            map.put("planEndDate", DateUtils.transDate2Str10(plan.getPlanEndDate()));
            planService.insertProducePlan(map);
            i++;
        }
        return new Result(ResultCode.SUCCESS, "成功导入【" + i + "】条数据");
    }

    @GetMapping("/getModelEnableTime")
    @ApiOperation(value = "获取生产模块启用时间  统计第一条订单/生产计划的数据时间", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getModelEnableTime() throws Exception {
        String result = planService.getModelEnableTime();
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getPlanDistribution")
    @ApiOperation(value = "计划分布", httpMethod = "GET")
    public Result getPlanDistribution(PpcBulletinBoardVo vo) throws Exception {
        Map<String, Object> result = planService.getPlanDistribution(vo);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getPlanCompletionRate")
    @ApiOperation(value = "计划达成率", httpMethod = "GET")
    public Result getPlanCompletionRate(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getPlanCompletionRate(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getPlanLateRate")
    @ApiOperation(value = "计划逾期分布", httpMethod = "GET")
    public Result getPlanLateRate(PpcBulletinBoardVo vo) throws Exception {
        Map<String, Object> result = planService.getPlanLateRate(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getProductionPlanningStatistics")
    @ApiOperation(value = "生产计划统计", httpMethod = "GET")
    public Result getProductionPlanningStatistics(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getProductionPlanningStatistics(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getDayPlanAndOutputStatistics")
    @ApiOperation(value = "日计划与产出", httpMethod = "GET")
    public Result getDayPlanAndOutputStatistics(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getDayPlanAndOutputStatistics(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getSchedulStatistics")
    @ApiOperation(value = "排产计划与产出", httpMethod = "GET")
    public Result getSchedulStatistics(PpcBulletinBoardVo vo) throws Exception {
        Map<String, Object> result = planService.getSchedulStatisticsNew(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getDaySchedulStatistics")
    @ApiOperation(value = "排产计划与统计", httpMethod = "GET")
    public Result getDaySchedulStatistics(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getDaySchedulStatistics(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getPlanStandardStatistics")
    @ApiOperation(value = "计划正非标", httpMethod = "GET")
    public Result getPlanStandardStatistics(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getPlanStandardStatistics(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getSchedulList")
    @ApiOperation(value = "排产计划列表", httpMethod = "GET")
    public Result getSchedulList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getSchedulList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getProcessExecutionList")
    @ApiOperation(value = "工序执行情况", httpMethod = "GET")
    public Result getProcessExecutionList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getProcessExecutionList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getEmployeePerformanceTop5")
    @ApiOperation(value = "员工绩效Top5", httpMethod = "GET")
    public Result getEmployeePerformanceTop5(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getEmployeePerformanceTop5(vo);
        return new Result(ResultCode.SUCCESS, result);
    }




    @GetMapping("/getMonthPlanNumList")
    @ApiOperation(value = "月计划生产数", httpMethod = "GET")
    public Result getMonthPlanNumList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getMonthPlanNumList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getPlanList")
    @ApiOperation(value = "生产计划列表", httpMethod = "GET")
    public Result getPlanList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getPlanList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getWorkshopPlanningAndProductionQualificationList")
    @ApiOperation(value = "车间计划及生产合格统计", httpMethod = "GET")
    public Result getWorkshopPlanningAndProductionQualificationList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getWorkshopPlanningAndProductionQualificationList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getWorkshopCompletionRate")
    @ApiOperation(value = "车间完成率统计", httpMethod = "GET")
    public Result getWorkshopCompletionRate(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getWorkshopCompletionRate(vo);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/getWorkshopPlanNumList")
    @ApiOperation(value = "车间计划数", httpMethod = "GET")
    public Result getWorkshopPlanNumList(PpcBulletinBoardVo vo) throws Exception {
        List<Map<String, Object>> result = planService.getWorkshopPlanNumList(vo);
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("/getPlanById")
    @ApiOperation(value = "根据计划id查询计划", httpMethod = "GET")
    public Result getPlanById(@ApiParam("生产计划id") @RequestParam("id") String id,@ApiParam("true 明细模式  false 非明细模式") @RequestParam(required = false, value = "isDetail") String isDetail) throws Exception {
        if (!StringUtils.isNullOrBlank(isDetail) && "true".equals(isDetail)) {
            ProductionStatusQueryVo vo = new ProductionStatusQueryVo();
            vo.setId(id);
            List<PpcProducePlanVo> producePlanVoList = planService.findProducePlanVo(vo,  new ArrayList<>(),true);
            if (!producePlanVoList.isEmpty() && StringUtils.isNullOrBlank(producePlanVoList.get(0).getEnableAuxiliaryProp())) {
                producePlanVoList.get(0).setEnableAuxiliaryProp("0");
            }

            BigDecimal scheQty = BigDecimal.ZERO;
            BigDecimal outsourcingScheQty = BigDecimal.ZERO;
            if(producePlanVoList.isEmpty()){
                return new Result(ResultCode.SUCCESS, new ProductionStatusQueryVo());
            }
            Promaterial material = feignService.findMaterialByCode(producePlanVoList.get(0).getMaterialCode());
            List<PpcProducePlanSchedul> ppcProducePlanScheduls = ppcProducePlanSchedulService.queryAllByProducePlanId(producePlanVoList.get(0).getId());
            for (PpcProducePlanSchedul ppcProducePlanSchedul : ppcProducePlanScheduls) {
                if ("10".equals(ppcProducePlanSchedul.getOrderType())) {
                    scheQty = scheQty.add(ppcProducePlanSchedul.getProduceQty());
                } else {
                    outsourcingScheQty = outsourcingScheQty.add(ppcProducePlanSchedul.getProduceQty());
                }
            }
            ArrayList<String> materialList = new ArrayList<>();
            materialList.add(material.getMaterialCode());
            List<WmsStock> availableQty = feignService.getAvailableQty(materialList);
            if (!availableQty.isEmpty()) {
                producePlanVoList.get(0).setAvailableQty(availableQty.get(0).getAvailableQty());
            } else {
                producePlanVoList.get(0).setAvailableQty(BigDecimal.ZERO);
            }
            producePlanVoList.get(0).setScheQty(scheQty);
            producePlanVoList.get(0).setOutsourcingScheQty(outsourcingScheQty);
            return new Result(ResultCode.SUCCESS, producePlanVoList.get(0));
        } else {
            PpcProducePlan plan = planService.getById(id);
            if (!"0".equals(plan.getOrderType())) {
                plan.setOrderType("1");
            }
            if (!StringUtils.isNullOrBlank(plan.getSaleDetailId())) {
                PpcSaleDetail saleDetail = saleDetailService.findById(plan.getSaleDetailId());
                PpcSaleMain saleMain = saleMainService.findById(saleDetail.getMainId());
                plan.setProduceStandard(saleMain.getSoNo());
            }
            BigDecimal scheQty = BigDecimal.ZERO;
            BigDecimal outsourcingScheQty = BigDecimal.ZERO;
            List<PpcProducePlanSchedul> ppcProducePlanScheduls = ppcProducePlanSchedulService.queryAllByProducePlanId(plan.getId());
            for (PpcProducePlanSchedul ppcProducePlanSchedul : ppcProducePlanScheduls) {
                if ("10".equals(ppcProducePlanSchedul.getOrderType())) {
                    scheQty = scheQty.add(ppcProducePlanSchedul.getProduceQty());
                } else {
                    outsourcingScheQty = outsourcingScheQty.add(ppcProducePlanSchedul.getProduceQty());
                }
            }
            Promaterial material = feignService.findMaterialByCode(plan.getMaterialCode());
            plan.setUnitCode(material.getPrimaryUnit());
            plan.setUnitName(material.getPrimaryUnitName());
            ArrayList<String> materialList = new ArrayList<>();
            materialList.add(material.getMaterialCode());
            planService.getAvailableQty(plan,materialList);
            plan.setScheQty(scheQty);
            plan.setOutsourcingScheQty(outsourcingScheQty);
            return new Result(ResultCode.SUCCESS, plan);
        }
    }

    @GetMapping("/getPlanAndScheById")
    @ApiOperation(value = "根据计划id查询生产计划以及排产单信息", httpMethod = "GET")
    public Result getPlanAndScheById(@ApiParam("生产计划id") @RequestParam("id") String id, @RequestParam(required = false, value = "isDetail") String isDetail) throws Exception {
        PpcProducePlan plan = planDao.selectById(id);
        if (!"0".equals(plan.getOrderType())) {
            plan.setOrderType("1");
        }
        if (!StringUtils.isNullOrBlank(plan.getSaleDetailId())) {
            PpcSaleDetail saleDetail = saleDetailService.findById(plan.getSaleDetailId());
            PpcSaleMain saleMain = saleMainService.findById(saleDetail.getMainId());
            plan.setProduceStandard(saleMain.getSoNo());
        }
        BigDecimal scheQty = BigDecimal.ZERO;
        BigDecimal outsourcingScheQty = BigDecimal.ZERO;
        List<PpcProducePlanSchedul> ppcProducePlanScheduls = ppcProducePlanSchedulService.queryAllByProducePlanId(plan.getId());
        for (PpcProducePlanSchedul ppcProducePlanSchedul : ppcProducePlanScheduls) {
            if ("10".equals(ppcProducePlanSchedul.getOrderType())) {
                scheQty = scheQty.add(ppcProducePlanSchedul.getProduceQty());
            } else {
                outsourcingScheQty = outsourcingScheQty.add(ppcProducePlanSchedul.getProduceQty());
            }
        }
        Promaterial material = feignService.findMaterialByCode(plan.getMaterialCode());
        ArrayList<String> materialList = new ArrayList<>();
        materialList.add(material.getMaterialCode());
        List<WmsStock> availableQty = feignService.getAvailableQty(materialList);
        if (!availableQty.isEmpty()) {
            plan.setAvailableQty(availableQty.get(0).getAvailableQty());
        }
        plan.setScheQty(scheQty);
        plan.setOutsourcingScheQty(outsourcingScheQty);
        plan.setUnitCode(material.getPrimaryUnit());
        plan.setUnitName(material.getPrimaryUnitName());
        return new Result(ResultCode.SUCCESS, plan);
    }


    @GetMapping("/findProducePlanVoByMobile")
    @ApiOperation(value = "查询生产情况信息", response = Result.class)
    public Result findProducePlanVoByMobile(PpcProducePlanVoByMobileDTO dto) throws Exception {
        Page page = PageHelper.startPage(dto.getCurrent(), dto.getSize());

        List<PpcProducePlanVo> producePlanVoList = planService.findProducePlanVoByMobile(dto);
        producePlanVoList.forEach(item -> {
            item.setWorkTime(new BigDecimal(item.getWorkTime()).stripTrailingZeros().toPlainString());
            item.setPlanId(item.getId());
            item.setShowComplete(!StringUtils.isNullOrBlank(item.getScheStatus()) && !item.getScheStatus().contains(Constants.PP_STATUS_10)
                    && !item.getScheStatus().contains(Constants.PP_STATUS_20) && !item.getScheStatus().contains(Constants.PP_STATUS_25)
                    && !item.getScheStatus().contains(Constants.PP_STATUS_30));
        });
        return Result.SUCCESS(new PageResult(page.getTotal(), producePlanVoList));
    }

    @GetMapping("/getPpcPlanQueryReportVoList")
    @ApiOperation(value = "计划查询报表", response = Result.class)
    public Result getPpcPlanQueryReportVoList(PpcPlanQueryReportVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize(), false);
        //排序字段是否是组合字段
        boolean orderByBelongPlan = true;
        if (StrUtil.isBlank(vo.getOrderBy())) {
            vo.setOrderBy("createOn:desc");
        } else {
            if (vo.getOrderBy().contains("issuedQty") || vo.getOrderBy().contains("scheGoodQty") || vo.getOrderBy().contains("sdNo")
                    || vo.getOrderBy().contains("produceStandard") || vo.getOrderBy().contains("produceStandard")
                    || vo.getOrderBy().contains("productionMessage") || vo.getOrderBy().contains("productionNo")
                    || vo.getOrderBy().contains("isLate") || vo.getOrderBy().contains("issuedStatus") || vo.getOrderBy().contains("specification") || vo.getOrderBy().contains("materialMarker") || vo.getOrderBy().contains("materialName")
                    || vo.getOrderBy().contains("planerName") || vo.getOrderBy().contains("unitName") || vo.getOrderBy().contains("workTime")
            ) {
                orderByBelongPlan = false;
            }
        }
        //是否只有生产计划的条件 0 否  1 是
        String paramBelongPlan = null;
        /**
         * 起始条数
         */
        Integer firstIndex = null;
        /**
         * 截止条数
         */
        Integer lastIndex = null;
        if (StringUtils.isNullOrBlank(vo.getIssuedQty()) && StringUtils.isNullOrBlank(vo.getScheGoodQty()) && StringUtils.isNullOrBlank(vo.getWorkTime()) && StringUtils.isNullOrBlank(vo.getSdNo())
                && StringUtils.isNullOrBlank(vo.getProduceStandard()) && StringUtils.isNullOrBlank(vo.getProduceStandard())
                && StringUtils.isNullOrBlank(vo.getProductionMessage()) && StringUtils.isNullOrBlank(vo.getProductionNo())
                && StringUtils.isNullOrBlank(vo.getIsLate()) && StringUtils.isNullOrBlank(vo.getIssuedStatus()) && StringUtils.isNullOrBlank(vo.getSpecification()) && StringUtils.isNullOrBlank(vo.getMaterialMarker())
                && StringUtils.isNullOrBlank(vo.getMaterialName()) && StringUtils.isNullOrBlank(vo.getPlanerName()) && orderByBelongPlan
        ) {
            paramBelongPlan = "1";
            firstIndex = (vo.getPageNum() - 1) * vo.getPageSize();
            lastIndex = vo.getPageSize();
        }
        List<PpcProducePlanVo> producePlanVoList = planService.getPpcPlanQueryReportVoList(vo, paramBelongPlan, firstIndex, lastIndex);
        producePlanVoList.forEach(item -> {
            item.setWorkTime(new BigDecimal(item.getWorkTime()).stripTrailingZeros().toPlainString());
            item.setPlanId(item.getId());
        });
        return Result.SUCCESS(new PageResult(null, producePlanVoList));
    }


    @GetMapping("/getPpcPlanQueryReportVoListStat")
    @ApiOperation(value = "查询生产计划列表页面合计值", response = Result.class)
    public Result getPpcPlanQueryReportVoListStat(PpcPlanQueryReportVo vo) throws Exception {
        Map<String, Object> stat = new HashMap();
        //是否只有生产计划的条件 0 否  1 是
        String paramBelongPlan = null;
        if (StrUtil.isBlank(vo.getStatus())) {
            vo.setStatus("10∫20∫30∫40∫90");
        }
        vo.setOrderBy(null);
        vo.setPageNum(null);
        vo.setPageSize(null);
        Query q = Query.build().initParams(vo);
        List<PpcProducePlanVo> ppcProducePlanVoList = planDao.selectSumAll(q);
        BigDecimal sumProduceQty = ppcProducePlanVoList.isEmpty() ? BigDecimal.ZERO : ppcProducePlanVoList.get(0).getProduceQty();
        String sumWorkTime = ppcProducePlanVoList.isEmpty() ? "0" : ppcProducePlanVoList.get(0).getWorkTime();
        BigDecimal sumGoodQty = ppcProducePlanVoList.isEmpty() ? BigDecimal.ZERO : ppcProducePlanVoList.get(0).getScheGoodQty();
        stat.put("produceQty", sumProduceQty.stripTrailingZeros());
        stat.put("workTime", new BigDecimal(sumWorkTime).stripTrailingZeros());
        stat.put("scheGoodQty", sumGoodQty.stripTrailingZeros());
        return Result.SUCCESS(new PageResult(null, ListUtil.toList(stat)));
    }


    @GetMapping("/getPpcPlanQueryReportVoListCount")
    @ApiOperation(value = "查询生产计划列表页面总页数", response = Result.class)
    public Result getPpcPlanQueryReportVoListTotal(PpcPlanQueryReportVo vo) throws Exception {
        vo.setOrderBy(null);
        //是否只有生产计划的条件 0 否  1 是
        String paramBelongPlan = null;
        /**
         * 起始条数
         */
        Integer firstIndex = null;
        /**
         * 截止条数
         */
        Integer lastIndex = null;
        if (StringUtils.isNullOrBlank(vo.getIssuedQty()) && StringUtils.isNullOrBlank(vo.getScheGoodQty()) && StringUtils.isNullOrBlank(vo.getWorkTime()) && StringUtils.isNullOrBlank(vo.getSdNo())
                && StringUtils.isNullOrBlank(vo.getProduceStandard()) && StringUtils.isNullOrBlank(vo.getProduceStandard())
                && StringUtils.isNullOrBlank(vo.getProductionMessage()) && StringUtils.isNullOrBlank(vo.getProductionNo())
                && StringUtils.isNullOrBlank(vo.getIsLate()) && StringUtils.isNullOrBlank(vo.getIssuedStatus()) && StringUtils.isNullOrBlank(vo.getSpecification()) && StringUtils.isNullOrBlank(vo.getMaterialMarker())
                && StringUtils.isNullOrBlank(vo.getMaterialName()) && StringUtils.isNullOrBlank(vo.getPlanerName())
        ) {
            paramBelongPlan = "1";
            firstIndex = (vo.getPageNum() - 1) * vo.getPageSize() + 1;
            lastIndex = vo.getPageNum() * vo.getPageSize();
        }
        Long count = planService.getPpcPlanQueryReportVoListCount(vo, paramBelongPlan, firstIndex, lastIndex);
        return new Result(ResultCode.SUCCESS, count);
    }

    @GetMapping("/productSchedulStatistics")
    @ApiOperation(value = "产品排产情况统计", response = Result.class)
    public Result productSchedulStatistics(productSchedulStatisticsDTO dto) throws Exception {
        Page page = PageHelper.startPage(dto.getCurrent(), dto.getSize());
        List<Map<String, Object>> list = planService.productSchedulStatistics(dto);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @GetMapping("/productSchedulStatisticsSum")
    @ApiOperation(value = "产品排产情况统计合计", response = Result.class)
    public Result productSchedulStatisticsSum(productSchedulStatisticsDTO dto) throws Exception {
        Map map = planService.productSchedulStatisticsSum(dto);
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/productSchedulComprehensiveStatistics")
    @ApiOperation(value = "产品排产情况综合统计", response = Result.class)
    public Result productSchedulComprehensiveStatistics(productSchedulStatisticsDTO dto) throws Exception {
        Map<String, Object> map = planService.productSchedulComprehensiveStatistics(dto);
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/exportProcessDefectStatisticsDetailList")
    @ApiOperation(value = "产品排产情况导出", httpMethod = "GET")
    public Result exportProcessDefectStatisticsDetailList(productSchedulStatisticsDTO dto, HttpServletRequest request, HttpServletResponse response) throws Exception {
        List<Map<String, Object>> list = planService.productSchedulStatistics(dto);
        String dateType = "日";
        if ("month".equals(dto.getTimeSift())) {
            dateType = "月";
        } else if ("year".equals(dto.getTimeSift())) {
            dateType = "年";
        }
        String[] keys = {"materialCode", "materialName", "specification", "materialMarker", "planQty", "schedulQty", "allFinishProduceQty", "goodQty", "lastGoodQty", "oldGoodQty", "badQty", "goodRate", "FirstPassRate", "badRate", "lastGoodRate", "oldGoodRate", "primaryUnitName"};
        String[] heads = {"物料编码", "物料名称", "规格", "型号", "计划生产数量", "排产数量", "报工总数量", "合格数量", "去【" + dateType + "】合格数", "上【" + dateType + "】合格数", "不良数量", "合格率", "直通率", "不良率", "合格数同比", "合格数环比", "基本单位"};
        ExcelUtils.exportExcel(response, request, list, keys, heads, "产品排产情况");

        return null;
    }


    @PostMapping("/ordinaryImportNew")
    @ApiOperation(value = "计划导入-普通模式（返回导入失败集合）", httpMethod = "POST")
    public void ordinaryImportNew(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ImportUtil.getData(PpcProducePlanOrdinaryImportVo.class, uploadFile, (x, y) -> planService.ordinaryImportNew(x), "1");
    }

    @PostMapping("/simpleImportNew")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "计划导入-简单模式（返回导入失败集合）", httpMethod = "POST")
    public void simpleImportNew(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ImportUtil.getData(PpcProducePlanSimpleImportVoNew.class, uploadFile, (x, y) -> planService.simpleImportNew(x), "1");
    }

    @PostMapping("/findProducePlanUserByUserCodeIn")
    @ApiOperation(value = "查询生产计划关联人员列表", httpMethod = "POST")
    public Result findProducePlanUserByUserCodeIn(@ApiParam("用户编码集合") @RequestBody List<String> userCodeList) {
        return Result.SUCCESS(planService.findProducePlanUserByUserCodeIn(userCodeList));
    }

    @PostMapping("/findProcessCodeInForPpcByType")
    @ApiOperation(value = "查询计划业务相关工序无法删除数据", httpMethod = "POST")
    @ApiIgnore
    public Result findUserCodeInForPpcByType(@ApiParam("工序编码集合") @RequestBody List<String> processCodeList, @ApiParam("用户编码集合") @RequestParam("number") String number) throws Exception {
        return Result.SUCCESS(planService.findProcessCodeInForPpcByType(processCodeList, number));
    }

    @PostMapping("/findBomInForPpcByType")
    @ApiOperation(value = "查询计划业务相关BOM无法删除数据", httpMethod = "POST")
    @ApiIgnore
    public Result findBomInForPpcByType(@ApiParam("BOM集合") @RequestBody List<Bom> bomList, @ApiParam("用户编码集合") @RequestParam("number") String number) throws Exception {
        return Result.SUCCESS(planService.findBomInForPpcByType(bomList, number));
    }

    @PostMapping("/findRouteInForPpcByType")
    @ApiOperation(value = "查询计划业务相关BOM无法删除数据", httpMethod = "POST")
    @ApiIgnore
    public Result findRouteInForPpcByType(@ApiParam("BOM集合") @RequestBody List<String> routeCodes, @ApiParam("用户编码集合") @RequestParam("number") String number) throws Exception {
        return Result.SUCCESS(planService.findRouteInForPpcByType(routeCodes, number));
    }

    @GetMapping("/findProducePlanPrint")
    @ApiOperation(value = "排产单二维码套打", response = Result.class)
    public Result findProducePlanPrintSchedule(ProductionStatusPrintModel vo) throws Exception {
        List<ProductionStatusPrintModel> roductionStatusPrintModelList = ppcProducePlanSchedulService.findProducePlanPrintSchedule(vo.getId());
        return Result.SUCCESS( roductionStatusPrintModelList);
    }


    @GetMapping("/findProducePlanProcessCardPrint")
    @ApiOperation(value = "工序流转卡套打", response = Result.class)
    public Result findProducePlanProcessCardPrint(ProductionStatusPrintProcessCardModel vo) throws Exception {
        List<ProductionStatusPrintProcessCardModel> producePlanProcessCardPrint = ppcProducePlanSchedulService.findProducePlanProcessCardPrint(vo.getId());
        return Result.SUCCESS( producePlanProcessCardPrint);
    }

    @GetMapping("/findProducePlanMaterialSn")
    @ApiOperation(value = "物料SN码套打", response = Result.class)
    public Result findProducePlanMaterialSn(ProductionStatusPrintModelMaterialSN vo) throws Exception {
        List<ProductionStatusPrintModelMaterialSN> productionStatusPrintModelMaterialSNList = ppcProducePlanSchedulService.findProducePlanMaterialSn(vo.getId());
        return Result.SUCCESS( productionStatusPrintModelMaterialSNList);
    }


    @GetMapping("/findProducePlanCodeByCreateOn")
    @ApiOperation(value = "根据创建时间查询计划单号", response = Result.class)
    public Result findProducePlanCodeByCreateOn(@RequestParam String start, @RequestParam String end) throws Exception {
        Map<String, String> map = new HashMap<>();
        map.put("start", start);
        map.put("end", end);
        List<PpcProducePlan> planList = planService.queryList(map);
        return Result.SUCCESS(planList);
    }

    @GetMapping("/addAssemblyPlan")
    @ApiOperation(value = "新增拼装计划", response = Result.class)
    public Result addAssemblyPlan(@RequestParam String projectCode, @RequestParam String projectName,
    @RequestParam  String addresss, @RequestParam String buildType, @RequestParam BigDecimal sortingArea) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        planService.addAssemblyPlan(projectCode,projectName,addresss,buildType,sortingArea,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/storeByPlanId")
    @ApiOperation(value = "生产入库组装数据", response = Result.class)
    public Result storeByPlanId(@RequestBody List<String> planIds) throws Exception {
        List<Map<String, Object>> result = planService.storeByPlanId(planIds);
        return new Result(ResultCode.SUCCESS,result);
    }

    @PostMapping("/sendProjectStore")
    @ApiOperation(value = "发送生产入库数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result sendProjectStore(@RequestBody ProjectStoreVo vo) throws Exception {
        erpInputService.sendProjectStore(vo);
        return new Result(ResultCode.SUCCESS);
    }
    @RequestMapping("/sendProjectStoreCheck")
    @ApiOperation(value = "生产入库校验", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> sendProjectStoreCheck(@RequestParam String id) throws Exception {
        return new Result<>(ResultCode.SUCCESS,erpInputService.sendProjectStoreCheck(id));
    }
}
