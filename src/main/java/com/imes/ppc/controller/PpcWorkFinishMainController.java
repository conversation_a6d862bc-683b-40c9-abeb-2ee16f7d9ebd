package com.imes.ppc.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.constant.ConstantsSys;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.exception.CommonException;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.Constants;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcProduceOrder;
import com.imes.domain.entities.ppc.po.PpcProduceOrderProcess;
import com.imes.domain.entities.ppc.po.PpcWorkFinishMain;
import com.imes.domain.entities.ppc.vo.DefauleItemNamePlanAztVo;
import com.imes.domain.entities.ppc.vo.checkWorkReportVo;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.query.template.ppc.PpcWorkFinishDetailMaterialQueryVo;
import com.imes.domain.entities.query.template.ppc.PpcWorkFinishMainQueryVo;
import com.imes.domain.entities.query.template.ppc.PpcWorkFinishMainSearchVo;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.ppc.constants.OrderEnum;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcProduceOrderProcessService;
import com.imes.ppc.service.PpcProduceOrderService;
import com.imes.ppc.service.PpcWorkFinishMainService;
import com.imes.ppc.service.PpcWorkInspectCalcService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报工单-主表-志特(PpcWorkFinishMain)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-17 09:32:16
 */
@RestController
@Api(tags = "报工单-主表-志特")
@RequestMapping("/api/ppc/ppcWorkFinishMain")
public class PpcWorkFinishMainController {

    @Autowired
    private PpcWorkFinishMainService ppcWorkFinishMainService;

    /**
     * 查询详情
     *
     * @param id 主单id
     * @return 主单
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result<PpcWorkFinishMain> info(String id) {
        return Result.SUCCESS((ppcWorkFinishMainService.getById(id)));
    }

    @GetMapping("/infoWithSub")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result<PpcWorkFinishMainQueryVo> infoWithSub(String id) throws Exception {
        return Result.SUCCESS((ppcWorkFinishMainService.infoWithSub(id)));
    }

    /**
     * 保存数据
     *
     * @param ppcWorkFinishMain 实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(@RequestBody PpcWorkFinishMain ppcWorkFinishMain) throws Exception {
        return new Result<>(ppcWorkFinishMainService.save(ppcWorkFinishMain));
    }

    /**
     * 更新保存
     *
     * @param ppcWorkFinishMain 实体
     * @return 更新保存结果
     */
//    @PostMapping("/saveWithAudit")
//    @ApiOperation(value = "报工审核时修改", httpMethod = "POST")
//    @Transactional(rollbackFor = Exception.class)
//    public Result saveWithAudit(@RequestBody PpcWorkFinishMainQueryVo ppcWorkFinishMain) throws Exception {
//        ppcWorkFinishMainService.saveWithAudit(ppcWorkFinishMain);
//        return Result.SUCCESS();
//    }

    /**
     * 更新保存2-含计件项
     *
     * @param ppcWorkFinishMain 实体
     * @return 更新保存结果
     */
    @PostMapping("/saveWithAudit2")
    @ApiOperation(value = "报工审核时修改2-含计件项", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result saveWithAudit2(@RequestBody PpcWorkFinishMainQueryVo ppcWorkFinishMain) throws Exception {
        ppcWorkFinishMainService.saveWithAudit2(ppcWorkFinishMain);
        return Result.SUCCESS();
    }

    /**
     * 查询列表
     *
     * @param vo 查询条件
     * @return 明细
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result queryList(PpcWorkFinishMainQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 高级查询
     *
     * @param vo 查询条件
     * @return 明细
     */
    @GetMapping("/advancedQueryList")
    @ApiOperation(value = "高级查询", httpMethod = "GET")
    public Result queryList(PpcWorkFinishMainSearchVo vo) throws Exception {
        if(StringUtils.isNullOrBlank(vo.getFinishedDate())){
            throw new CommonException("报工日期必填");
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainSearchVo> list = ppcWorkFinishMainService.advancedQueryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 查询工单下可报工行物料列表
     *
     * @param vo 查询条件
     * @return 工单下可报工行物料列表
     */
    @GetMapping("/queryEnableReportListByOrderId")
    @ApiOperation(value = "查询工单下可报工行物料列表", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishDetailMaterialQueryVo>> queryEnableReportListByOrderNo(PpcWorkFinishMainQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishDetailMaterialQueryVo> list = ppcWorkFinishMainService.queryEnableReportListByOrderNo(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    @Autowired
    PpcProduceOrderService produceOrderService;

    @Autowired
    PpcProduceOrderProcessService ppcProduceOrderProcessService;

    /**
     * 查询groupList列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditGroupList")
    @ApiOperation(value = "查询分组列表", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditGroupList(PpcWorkFinishMainQueryVo vo) throws Exception {
        String userCode = RedisUtils.getUserCode();
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        vo.setOrderIds(StringUtils.list2SrtIn(orderIdList));

        /**
         * 租户管理查询所有
         * 非租户管理员：查自己本班组非修改单数据，如果兼职了内协则也可以查询本班组修改单数据
         */
        if (!feignService.isTenantAdmin(userCode)) {
            if (feignService.isRoleByRoleName(userCode, ConstantsSys.IC_NAME)) {
                vo.setIsIc("1");
            }
            List<CoDepartment> leaderTeamCodeByUserCode = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            List<String> teamList = leaderTeamCodeByUserCode.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (!teamList.isEmpty()) {
                vo.setAuditTeamCodeIn(StringUtils.list2SrtIn(teamList));
            }
        } else {
            vo.setIsTenantAdmin("1");
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryGroupList(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    /**
     * 查询报工审核拉平信息
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditByWfNoList")
    @ApiOperation(value = "查询报工审核拉平信息", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditByWfNoList(PpcWorkFinishMainQueryVo vo) throws Exception {
        String userCode = RedisUtils.getUserCode();
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        vo.setOrderIds(StringUtils.list2SrtIn(orderIdList));

        /**
         * 租户管理查询所有
         * 非租户管理员：查自己本班组非修改单数据，如果兼职了内协则也可以查询本班组修改单数据
         */
        if (!feignService.isTenantAdmin(userCode)) {
            if (feignService.isRoleByRoleName(userCode, ConstantsSys.IC_NAME)) {
                vo.setIsIc("1");
            }
            List<CoDepartment> leaderTeamCodeByUserCode = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            List<String> teamList = leaderTeamCodeByUserCode.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (!teamList.isEmpty()) {
                vo.setAuditTeamCodeIn(StringUtils.list2SrtIn(teamList));
            }
        } else {
            vo.setIsTenantAdmin("1");
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryAuditByWfNoList(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    @GetMapping("/queryWorkReportAdjustmentForClerk")
    @ApiOperation(value = "报工调整（文员）", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryWorkReportAdjustmentForClerk(PpcWorkFinishMainQueryVo vo) throws Exception {
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        vo.setOrderIds(StringUtils.list2SrtIn(orderIdList));
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryWorkReportAdjustmentForClerk(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    @GetMapping("/workReportBackForClerk")
    @ApiOperation(value = "报工调整页面反审核按钮（文员）", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result workReportBackForClerk(String ids, String status) throws Exception {
        ppcWorkFinishMainService.workReportBackForClerk(ids, status);
        return Result.SUCCESS();
    }

    @Autowired
    FeignService feignService;

    /**
     * 查询groupListApp列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditGroupListApp")
    @ApiOperation(value = "查询分组列表", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditGroupListApp(PpcWorkFinishMainQueryVo vo) throws Exception {
        String userCode = RedisUtils.getUserCode();
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        vo.setOrderIds(StringUtils.list2SrtIn(orderIdList));
        if (!feignService.isTenantAdmin(userCode)) {
            if (feignService.isRoleByRoleName(userCode, ConstantsSys.IC_NAME)) {
                vo.setIsIc("1");
            }
            List<CoDepartment> leaderTeamCodeByUserCode = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            List<String> teamList = leaderTeamCodeByUserCode.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (!teamList.isEmpty()) {
                vo.setAuditTeamCodeIn(StringUtils.list2SrtIn(teamList));
            }

        } else {
            vo.setIsTenantAdmin("1");
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryGroupListApp(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    @GetMapping("/queryAuditGroupByUserCodeListApp")
    @ApiOperation(value = "查询个人报工", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditGroupByUserCodeListApp(PpcWorkFinishMainQueryVo vo) throws Exception {
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        vo.setOrderIds(StringUtils.list2SrtIn(orderIdList));
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        vo.setExecuteUserCode(RedisUtils.getUserCode());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryAuditGroupByUserCodeListApp(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * 查询项目下报工列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditList")
    @ApiOperation(value = "查询项目下报工列表", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditList(PpcWorkFinishMainQueryVo vo) throws Exception {
//        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getProductNo()), "项目编号productNo不可传空");
//        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getPartType()), "构件partType不可传空");
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getBigProcessCode()), "工段bigProcessCode不可传空");
        String userCode = RedisUtils.getUserCode();
        if (!feignService.isTenantAdmin(userCode)) {
            if (feignService.isRoleByRoleName(userCode, ConstantsSys.IC_NAME)) {
                vo.setIsIc("1");
            }
            List<CoDepartment> leaderTeamCodeByUserCode = feignService.getLeaderTeamCodeByUserCode(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            List<String> teamList = leaderTeamCodeByUserCode.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (!teamList.isEmpty()) {
                vo.setAuditTeamCodeIn(StringUtils.list2SrtIn(teamList));
            }
        } else {
            vo.setIsTenantAdmin("1");
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryAuditList(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * 查询项目下报工列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditListApp")
    @ApiOperation(value = "查询项目下报工列表app", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditListApp(PpcWorkFinishMainQueryVo vo) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getOrderId()), "工单号orderId不可传空");
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getBigProcessCode()), "工段编码bigProcessCode不可传空");

        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        if (!feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            if (feignService.isRoleByRoleName(RedisUtils.getUserCode(), ConstantsSys.IC_NAME)) {
                vo.setOrderType(OrderEnum.ORDER_TYPE_FIX.getCode());
            }
            List<CoDepartment> leaderTeamCodeByUserCode = feignService.getLeaderTeamCodeByUserCode(RedisUtils.getUserCode(), Constants.DEPARTMENT_TYPE_PRODUCE);
            List<String> teamList = leaderTeamCodeByUserCode.stream().map(CoDepartment::getDepartCode).collect(Collectors.toList());
            if (!teamList.isEmpty()) {
                vo.setAuditTeamCodeIn(StringUtils.list2SrtIn(teamList));
            }
        }else {
            vo.setIsTenantAdmin("1");
        }
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryAuditListForApp(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }


    /**
     * 查询项目下报工列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAuditViewListApp")
    @ApiOperation(value = "查询项目下报工列表app", httpMethod = "GET")
    public Result<PageResult<PpcWorkFinishMainQueryVo>> queryAuditViewListApp(PpcWorkFinishMainQueryVo vo) throws Exception {
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getOrderId()), "工单号orderId不可传空");
        AssertUtil.isTrue(!StringUtils.isNullOrBlank(vo.getBigProcessCode()), "工段编码bigProcessCode不可传空");
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        vo.setExecuteUserCode(RedisUtils.getUserCode());
        List<PpcWorkFinishMainQueryVo> list = ppcWorkFinishMainService.queryAuditViewListApp(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * @param vo
     * @param auditFlag    1同意，2驳回
     * @param rejectReason 驳回原因
     * @return
     * @throws Exception
     */
    @PostMapping("/auditInBigProcess")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "外层大工段审核", httpMethod = "GET")
    public Result queryAuditGroupList(@RequestBody List<PpcWorkFinishMainQueryVo> vo,
                                      @RequestParam(value = "auditFlag") String auditFlag,
                                      @RequestParam(value = "rejectReason", required = false) String rejectReason) throws Exception {

        Optional<PpcWorkFinishMainQueryVo> optional = vo.stream().filter(x -> StringUtils.isNullOrBlank(x.getProductNo())).findFirst();
        if (optional.isPresent()) {
            AssertUtil.throwException("项目编号productNo不可传空");
        }
        optional = vo.stream().filter(x -> StringUtils.isNullOrBlank(x.getPartType())).findFirst();
        if (optional.isPresent()) {
            AssertUtil.throwException("构件partType不可传空");
        }
        optional = vo.stream().filter(x -> StringUtils.isNullOrBlank(x.getBigProcessCode())).findFirst();
        if (optional.isPresent()) {
            AssertUtil.throwException("工段bigProcessCode不可传空");
        }
        for (PpcWorkFinishMainQueryVo ppcWorkFinishMainQueryVo : vo) {
            if (StringUtils.isNullOrBlank(ppcWorkFinishMainQueryVo.getFactoryLineName()) &&
                    (OrderEnum.ORDER_TYPE_SUB_EC_A.getCode().equals(ppcWorkFinishMainQueryVo.getOrderType())  || OrderEnum.ORDER_TYPE_SUB_EC_B.getCode().equals(ppcWorkFinishMainQueryVo.getOrderType())
                            || OrderEnum.ORDER_TYPE_SUB_R.getCode().equals(ppcWorkFinishMainQueryVo.getOrderType())
                            || OrderEnum.ORDER_TYPE_SUB_PATCH.getCode().equals(ppcWorkFinishMainQueryVo.getOrderType()) ||
                            OrderEnum.ORDER_TYPE_SUB_GROOVE.getCode().equals(ppcWorkFinishMainQueryVo.getOrderType()))) {
                AssertUtil.throwException("产线factoryLineName不可传空");
            }
        }
        ppcWorkFinishMainService.audit(vo, auditFlag, rejectReason);
        return Result.SUCCESS();
    }

    /**
     * @param idList
     * @param auditFlag    1通过2拒绝
     * @param rejectReason
     * @return
     * @throws Exception
     */
    @PostMapping("/auditWorkFinish")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "第二层报工审核", httpMethod = "GET")
    public Result auditWorkFinish(@RequestBody List<String> idList,
                                  @RequestParam(value = "auditFlag") String auditFlag,
                                  @RequestParam(value = "rejectReason", required = false) String rejectReason) throws Exception {

        if (idList.isEmpty()) {
            AssertUtil.throwException("报工单id不可传空");
        }
        ppcWorkFinishMainService.auditWorkFinish(idList, auditFlag, rejectReason);
        return Result.SUCCESS();
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @DeleteMapping("/batchDel")
    @ApiOperation(value = "批量删除", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDel(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        return new Result<>(ppcWorkFinishMainService.batchDel(idList) > 0);
    }


    /**
     * 检查工单是否可以报工
     *
     * @return 报工数据
     */
    @PostMapping("/checkWorkReport")
    @ApiOperation(value = "检查工单是否可以报工", httpMethod = "POST")
    public Result<Boolean> checkWorkReport(@RequestBody checkWorkReportVo vo) throws Exception {
        PpcWorkFinishMainQueryVo result = ppcWorkFinishMainService.checkWorkReport(vo);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 获取正常报工/补充报工需要填的计件项字段
     *
     * @return 报工数据
     */
    @GetMapping("/getReportColumnByBigProcessAndDate")
    @ApiOperation(value = "获取正常报工/补充报工需要填的计件项字段", httpMethod = "GET")
    public Result<Boolean> getReportColumnByBigProcessAndDate(@RequestParam String orderId,@RequestParam String bigProcessCode,@RequestParam String smallProcessCode, @RequestParam Date finishedDate) throws Exception {
        ArrayList<Map> result = ppcWorkFinishMainService.getReportColumnByBigProcessAndDate(orderId,bigProcessCode,smallProcessCode, finishedDate);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 获取子工单报工需要填的计件项字段
     *
     * @return 报工数据
     */
    @GetMapping("/getSubReportColumnByBigProcessAndDate")
    @ApiOperation(value = "获取子工单报工需要填的计件项字段", httpMethod = "GET")
    public Result<Boolean> getSubReportColumnByBigProcessAndDate(@RequestParam String bigProcessCode,@RequestParam String smallProcessCode, @RequestParam String finishedDate) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        ArrayList<Map> result = ppcWorkFinishMainService.getSubReportColumnByBigProcessAndDate(bigProcessCode,smallProcessCode, sdf.parse(finishedDate));
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/checkWorkReportForApp")
    @ApiOperation(value = "检查工单是否可以报工", httpMethod = "POST")
    public Result<Boolean> checkWorkReportForApp(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        PpcWorkFinishMainQueryVo result = ppcWorkFinishMainService.checkWorkReportForApp(vo);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 班组作业报工
     */
    @PostMapping("/workReport")
    @ApiOperation(value = "班组作业报工", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> workReport(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        ppcWorkFinishMainService.workReport(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 班组作业报工2-含计件
     */
    @PostMapping("/workReport2")
    @ApiOperation(value = "班组作业报工-含计件", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> workReport2(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        String workFinishNo = ppcWorkFinishMainService.workReport2(vo);
        return new Result(ResultCode.SUCCESS, workFinishNo);
    }


    /**
     * 抛丸喷粉自动报工
     */
    @PostMapping("/workReportForS0131")
    @ApiOperation(value = "抛丸喷粉自动报工", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> workReportForS0131(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        List<String> orderIdList = produceOrderService.getIdListByMuiltyScan(vo.getLocalOrderId(), vo.getLocalApplicationId(), vo.getOrderId());
        //校验是否有工单不在抛丸喷粉段
        if (!orderIdList.isEmpty()) {
            QueryWrapper<PpcProduceOrderProcess> selectOrderProcess = new QueryWrapper<>();
            selectOrderProcess.in("order_id", orderIdList);
            selectOrderProcess.eq("big_process_code", "S0131");
            selectOrderProcess.eq("is_dispatch_stage", "1");
            selectOrderProcess.eq("status", "20");
            List<PpcProduceOrderProcess> orderProcessList = ppcProduceOrderProcessService.list(selectOrderProcess);
            List<String> existOrderList = orderProcessList.stream().map(PpcProduceOrderProcess::getOrderId).filter(Objects::nonNull).collect(Collectors.toList());
            Set<String> existOrderSet = new HashSet<>(existOrderList);
            // 过滤出 orderIdList 中存在但 existOrderList 中不存在的元素
            List<String> resultList = orderIdList.stream().filter(orderId -> !existOrderSet.contains(orderId)).collect(Collectors.toList());
            if (!resultList.isEmpty()) {
                QueryWrapper<PpcProduceOrderProcess> message = new QueryWrapper<>();
                message.eq("order_id", resultList.get(0));
                message.eq("big_process_code", "S0131");
                PpcProduceOrderProcess one = ppcProduceOrderProcessService.getOne(message);
                if (one == null) {
                    throw new CommonException("该二维码未匹配到工单");
                }
                if ("10".equals(one.getStatus())) {
                    QueryWrapper<PpcProduceOrderProcess> message1 = new QueryWrapper<>();
                    message1.eq("order_id", resultList.get(0));
                    message1.eq("is_dispatch_stage", "1");
                    List<PpcProduceOrderProcess> list = ppcProduceOrderProcessService.list(message1);
                    if (list.isEmpty()) {
                        throw new CommonException("单号为【" + one.getOrderNo() + "】的工单尚未流转到抛丸喷粉工段");
                    }else {
                        throw new CommonException("单号为【" + one.getOrderNo() + "】的工单处于【" + list.get(0).getBigProcessName() + "】工段");
                    }

                }
            }
        }else {
            throw new CommonException("该二维码未匹配到工单");
        }
        //组织报工数据
        for (String orderId : orderIdList) {
            ppcWorkFinishMainService.workReportForS0131(orderId);
        }

        return new Result(ResultCode.SUCCESS);
    }

    /**z'x
     * 报工调整保存接口
     */
    @PostMapping("/workReportForClerk")
    @ApiOperation(value = "报工调整保存接口", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> workReportForClerk(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        ppcWorkFinishMainService.workReportForClerk(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * z'x
     * 报工调整保存接口2-含计件
     */
    @PostMapping("/workReportForClerk2")
    @ApiOperation(value = "报工调整保存接口", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> workReportForClerk2(@RequestBody PpcWorkFinishMainQueryVo vo) throws Exception {
        String workFinishNo = ppcWorkFinishMainService.workReportForClerk2(vo);
        return new Result(ResultCode.SUCCESS, workFinishNo);
    }


    /**
     * 报工调整更新查询接口
     */
    @GetMapping("/getDetailByMainId")
    @ApiOperation(value = "报工调整更新查询接口", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result getDetailByMainId(@RequestParam String mainId) throws Exception {
        Map result = ppcWorkFinishMainService.getDetailByMainId(mainId);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 根据工单ID查询报工主单
     *
     * @return 报工主单集合
     */
    @DeleteMapping("/queryByOrderIdList")
    @ApiOperation(value = "根据工单ID查询报工主单", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByOrderIdList(List<String> orderIdList) throws Exception {
        List<PpcWorkFinishMain> finishMainList = ppcWorkFinishMainService.queryByOrderIdList(orderIdList);
        return Result.SUCCESS(finishMainList);
    }

    @GetMapping("/queryByOrderIdAndLine")
    @ApiOperation(value = "根据工单ID和行号查询报工主单", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByOrderIdAndLine(@RequestParam("orderId") String orderId, @RequestParam("lineNo") Integer lineNo) throws Exception {
        List<PpcWorkFinishMain> finishMainList = ppcWorkFinishMainService.queryByOrderIdAndLine(orderId, lineNo);
        return Result.SUCCESS(finishMainList);
    }


    @PostMapping("/checkProduceOrderIsFinish")
    @ApiOperation(value = "检查工单是否满足完工条件，满足则直接完工", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result checkProduceOrderIsFinish(@RequestBody List<String> orderIdList) throws Exception {
        ppcWorkFinishMainService.checkProduceOrderIsFinish(orderIdList);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/getWorkReportTeamAndWorkerByOrderId")
    @ApiOperation(value = "全检查询对应工单，对应行号的报工人，报工班组")
    @Transactional(rollbackFor = Exception.class)
    public Result getWorkReportTeamAndWorkerByOrderId(@RequestParam String orderId, @RequestParam String lineNos) throws Exception {
        HashMap<String, Object> result = ppcWorkFinishMainService.getWorkReportTeamAndWorkerByOrderId(orderId, lineNos);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 全检扫码提示优化
     */
    @GetMapping("/checkForQms")
    @ApiOperation(value = "全检扫码提示优化", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public void checkForQms(@RequestParam String keyWord) throws Exception {
        List<PpcProduceOrder> resultList = ppcWorkFinishMainService.checkForQms(keyWord);
        if (!resultList.isEmpty()) {
            QueryWrapper<PpcProduceOrderProcess> message1 = new QueryWrapper<>();
            message1.eq("order_id", resultList.get(0).getId());
            message1.eq("is_dispatch_stage", "1");
            List<PpcProduceOrderProcess> list = ppcProduceOrderProcessService.list(message1);
            if (list.isEmpty()) {
                throw new CommonException("该工单没有处于流转阶段的工段");
            } else {
                PpcProduceOrderProcess process = list.get(0);
                throw new CommonException("该工单处于【%s】工段，状态为【%s】",process.getBigProcessName(),
                        "10".endsWith(process.getStatus()) ? "未派工" : "已派工");
            }
        } else {
            throw new CommonException("该二维码未匹配到工单");
        }
    }


    @GetMapping("/getSumAreaByProcessCodeAndDate")
    @ApiOperation(value = "各工序日面积统计图")
    @Transactional(rollbackFor = Exception.class)
    public Result getSumAreaByProcessCodeAndDate(@RequestParam() int pageSize, @RequestParam() int pageNum,
                                                 @RequestParam() String startDate, @RequestParam() String endDate) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<HashMap<String, Object>> pageMessage = ppcWorkFinishMainService.getSumAreaByProcessCodeAndDatePage(startDate, endDate);
        if (pageMessage.isEmpty()) {
            return Result.SUCCESS(new PageResult<>(0l, null));
        }
        List<HashMap<String, Object>> result = ppcWorkFinishMainService.getSumAreaByProcessCodeAndDate(pageMessage.get(pageMessage.size() - 1).get("finishedDate").toString(), pageMessage.get(0).get("finishedDate").toString() );
        return Result.SUCCESS(new PageResult<>(page.getTotal(), result));
    }

    @Autowired
    PpcWorkInspectCalcService ppcWorkInspectCalcService;

    @PostMapping("/getDefaultItemForPlanAztList")
    @ApiOperation(value = "报工行推荐计件项")
    public Result getDefaultItemForPlanAztList(@RequestParam() String orderId, @RequestParam() String finishDate,
                                               @RequestParam() String bigProcessCode, @RequestParam() String smallProcessCode,
                                               @RequestBody List<DefauleItemNamePlanAztVo> voList) throws Exception {
        if (voList.isEmpty()) {
            return Result.SUCCESS();
        }
        List<DefauleItemNamePlanAztVo> list = ppcWorkFinishMainService.getDefaultItemForPlanAztList(orderId, finishDate, bigProcessCode, smallProcessCode, voList);
        return Result.SUCCESS(list);
    }

    @PostMapping("/generateInspectPieceData")
    @ApiOperation(value = "报工行推荐计件项")
    public Result generateInspectPieceData(@RequestBody QmsWorkInspect qmsWorkInspect) throws Exception {
        if (qmsWorkInspect == null) {
            AssertUtil.throwException("无全检单需要生成计件数据");
        }
        ppcWorkInspectCalcService.generateInspectPieceData(qmsWorkInspect);
        //生产之后校验工单是否需要完工
        ArrayList<String> orderIdList = new ArrayList<>();
        orderIdList.add(qmsWorkInspect.getOrderId());
        ppcWorkFinishMainService.checkProduceOrderIsFinish(orderIdList);
        return Result.SUCCESS();
    }

    @GetMapping("/deleteInspectPieceData")
    @ApiOperation(value = "报工行推荐计件项")
    public Result deleteInspectPieceData(@RequestParam() String inspectNo) throws Exception {
        ppcWorkInspectCalcService.deleteInspectPieceData(inspectNo);
        return Result.SUCCESS();
    }


    @GetMapping("/getPpcWorkFinishMainByOrderIdAndBIgProcessCode")
    @ApiOperation(value = "根据工单id跟大工序编码获取报工单")
    public Result getPpcWorkFinishMainByOrderIdAndBIgProcessCode(@RequestParam() String orderId, @RequestParam() String bigProcessCode) throws Exception {
        List<PpcWorkFinishMain> result = ppcWorkFinishMainService.getPpcWorkFinishMainByOrderIdAndBIgProcessCode(orderId, bigProcessCode);
        return Result.SUCCESS(result);
    }
}

