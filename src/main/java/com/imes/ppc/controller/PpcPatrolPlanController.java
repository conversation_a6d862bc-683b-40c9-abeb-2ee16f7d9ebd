package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.exception.CommonException;
import com.imes.common.jenum.AccurateCronEnum;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.Constants;
import com.imes.common.utils.DateUtils;
import com.imes.domain.entities.pm.po.CoDepartmentOptions;
import com.imes.domain.entities.ppc.po.PpcPatrolPlan;
import com.imes.domain.entities.ppc.po.PpcPatrolTask;
import com.imes.domain.entities.ppc.vo.PpcPatrolPlanVo;
import com.imes.domain.entities.query.template.ppc.PpcPatrolPlanTemplate;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import com.imes.ppc.feignClient.SysInterfaceService;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.InspectionPlanTaskService;
import com.imes.ppc.service.PpcPatrolPlanService;
import com.imes.ppc.service.PpcPatrolTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 生产巡视计划(PpcPatrolPlan)表控制层
 *
 * <AUTHOR> @since 2022-03-21 15:48:19
 */
@Slf4j
@RestController
@Api(tags = "生产巡检计划控制层")
@RequestMapping("/api/ppc/ppcPatrolPlan")
public class PpcPatrolPlanController {

    @Resource
    private PpcPatrolPlanService ppcPatrolPlanService;

    @Autowired
    private FeignService feignService;
    @Autowired
    private SysInterfaceService sysInterfaceService;
    @Autowired
    private InspectionPlanTaskService inspectionPlanTaskService;
    @Autowired
    private PpcPatrolTaskService ppcPatrolTaskService;


    @GetMapping("/getPpcPatrolPlanTemplate")
    @ApiOperation(value = "生产巡检计划列表", httpMethod = "GET")
    public Result getPpcPatrolPlanTemplate(PpcPatrolPlanTemplate vo) throws ParseException {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPatrolPlanTemplate> result = ppcPatrolPlanService.getPpcPatrolPlanTemplate(vo);
        Calendar instance = Calendar.getInstance();
        for (PpcPatrolPlanTemplate ppcPatrolPlanTemplate : result) {
            if (ppcPatrolPlanTemplate.getStatus().equals("20")) {
                PpcPatrolTask ppcPatrolTask = ppcPatrolTaskService.findTop1ByPlanIdOrderByCreatedOnDesc(ppcPatrolPlanTemplate.getId());
                if (ppcPatrolTask != null) {
                    String intervalType = ppcPatrolPlanTemplate.getIntervalType();//间隔类型
                    Integer intervalQty = ppcPatrolPlanTemplate.getIntervalQty();//间隔周期
                    if (intervalType.equals("0")) {
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY, 0);
                        instance.set(Calendar.MINUTE, 0);
                        instance.set(Calendar.SECOND, 0);
                        instance.set(Calendar.MILLISECOND, 0);
                        instance.add(Calendar.DATE, 1);
                        ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                    }
                    if (intervalType.equals("1")) {
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY, 0);
                        instance.set(Calendar.MINUTE, 0);
                        instance.set(Calendar.SECOND, 0);
                        instance.set(Calendar.MILLISECOND, 0);
                        instance.add(Calendar.DATE, intervalQty + 1);
                        ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                    }
                    if (intervalType.equals("2")) {
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY, 0);
                        instance.set(Calendar.MINUTE, 0);
                        instance.set(Calendar.SECOND, 0);
                        instance.set(Calendar.MILLISECOND, 0);
                        Calendar curr = Calendar.getInstance();
                        instance.add(Calendar.DATE, 7 * intervalQty);
                        ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                    }
                    if (intervalType.equals("3")) {
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY, 0);
                        instance.set(Calendar.MINUTE, 0);
                        instance.set(Calendar.SECOND, 0);
                        instance.set(Calendar.MILLISECOND, 0);
                        instance.add(Calendar.MONTH, intervalQty);
                        ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                    }
                } else {
                    instance.setTime(new Date());
                    instance.set(Calendar.HOUR_OF_DAY, 0);
                    instance.set(Calendar.MINUTE, 0);
                    instance.set(Calendar.SECOND, 0);
                    instance.set(Calendar.MILLISECOND, 0);
                    instance.add(Calendar.DATE, 1);
                    ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                }
                if (new Date().after(ppcPatrolPlanTemplate.getNextGeneralTime())) {
                    instance.setTime(new Date());
                    instance.set(Calendar.HOUR_OF_DAY, 0);
                    instance.set(Calendar.MINUTE, 0);
                    instance.set(Calendar.SECOND, 0);
                    instance.set(Calendar.MILLISECOND, 0);
                    instance.add(Calendar.DATE, 1);
                    ppcPatrolPlanTemplate.setNextGeneralTime(instance.getTime());
                }
                Date EndTime = format.parse(ppcPatrolPlanTemplate.getPlanEndTime());
                if (ppcPatrolPlanTemplate.getNextGeneralTime().after(EndTime)) {
                    ppcPatrolPlanTemplate.setNextGeneralTime(null);
                }
            }
//            Result userByDepartCode = sysInterfaceService.findUserByDepartCode(ppcPatrolPlanTemplate.getExecuteDepartment());
//            if (userByDepartCode.isSuccess()) {
//                List data = (List) userByDepartCode.getData();
//                ppcPatrolPlanTemplate.setExecuteDepartmentUserNames(data);
//            }
            if (ppcPatrolPlanTemplate.getIsCalendar().equals("0")) {
                ppcPatrolPlanTemplate.setCalendarCode("");
            }
        }
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("")
    @ApiOperation(value = "")
    @ApiIgnore
    public Result queryAll(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
            PpcPatrolPlan ppcPatrolPlan) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcPatrolPlanVo> result = ppcPatrolPlanService.queryByCond(ppcPatrolPlan);
        Calendar instance = Calendar.getInstance();
        for(PpcPatrolPlanVo ppcPatrolPlanVo : result){
            if(ppcPatrolPlanVo.getStatus().equals("20")){
                PpcPatrolTask ppcPatrolTask = ppcPatrolTaskService.findTop1ByPlanIdOrderByCreatedOnDesc(ppcPatrolPlanVo.getId());
                if(ppcPatrolTask != null){
                    String intervalType = ppcPatrolPlanVo.getIntervalType();//间隔类型
                    Integer intervalQty = ppcPatrolPlanVo.getIntervalQty();//间隔周期
                    if(intervalType.equals("0")){
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY,0);
                        instance.set(Calendar.MINUTE,0);
                        instance.set(Calendar.SECOND,0);
                        instance.set(Calendar.MILLISECOND,0);
                        instance.add(Calendar.DATE,1);
                        ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                    }
                    if(intervalType.equals("1")){
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY,0);
                        instance.set(Calendar.MINUTE,0);
                        instance.set(Calendar.SECOND,0);
                        instance.set(Calendar.MILLISECOND,0);
                        instance.add(Calendar.DATE,intervalQty+1);
                        ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                    }
                    if(intervalType.equals("2")){
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY,0);
                        instance.set(Calendar.MINUTE,0);
                        instance.set(Calendar.SECOND,0);
                        instance.set(Calendar.MILLISECOND,0);
                        Calendar curr = Calendar.getInstance();
                        instance.add(Calendar.DATE,7*intervalQty);
                        ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                    }
                    if(intervalType.equals("3")){
                        instance.setTime(ppcPatrolTask.getPlanStartTime());
                        instance.set(Calendar.HOUR_OF_DAY,0);
                        instance.set(Calendar.MINUTE,0);
                        instance.set(Calendar.SECOND,0);
                        instance.set(Calendar.MILLISECOND,0);
                        instance.add(Calendar.MONTH,intervalQty);
                        ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                    }
                }else {
                    instance.setTime(new Date());
                    instance.set(Calendar.HOUR_OF_DAY,0);
                    instance.set(Calendar.MINUTE,0);
                    instance.set(Calendar.SECOND,0);
                    instance.set(Calendar.MILLISECOND,0);
                    instance.add(Calendar.DATE,1);
                    ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                }
                if(new Date().after(ppcPatrolPlanVo.getNextGeneralTime())){
                    instance.setTime(new Date());
                    instance.set(Calendar.HOUR_OF_DAY,0);
                    instance.set(Calendar.MINUTE,0);
                    instance.set(Calendar.SECOND,0);
                    instance.set(Calendar.MILLISECOND,0);
                    instance.add(Calendar.DATE,1);
                    ppcPatrolPlanVo.setNextGeneralTime(instance.getTime());
                }
                if(ppcPatrolPlanVo.getNextGeneralTime().after(ppcPatrolPlanVo.getPlanEndTime())){
                    ppcPatrolPlanVo.setNextGeneralTime(null);
                }
            }
            Result userByDepartCode = sysInterfaceService.findUserByDepartCode(ppcPatrolPlanVo.getExecuteDepartment());
            if (userByDepartCode.isSuccess()) {
                List data = (List) userByDepartCode.getData();
                ppcPatrolPlanVo.setExecuteDepartmentUserNames(data);
            }
            if(ppcPatrolPlanVo.getIsCalendar().equals("0")){
                ppcPatrolPlanVo.setCalendarCode("");
            }
        }
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/{id}")
    @ApiOperation(value = "根据id获取生产巡检计划",httpMethod = "GET")
    public Result queryById(@ApiParam("生产巡检计划id") @PathVariable("id") String id) {
        PpcPatrolPlan result = ppcPatrolPlanService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("")
    @ApiOperation(value = "保存",httpMethod = "POST")
    public Result save(@RequestBody PpcPatrolPlan record) {
        record.setStatus("10");
        if(record.getIsCalendar().equals("0")){
            record.setCalendarCode("");
        }
        Integer result = ppcPatrolPlanService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PutMapping("")
    @ApiOperation(value = "更新",httpMethod = "PUT")
    public Result update(@RequestBody PpcPatrolPlan record) {
        if(record.getIsCalendar().equals("0")){
            record.setCalendarCode("");
        }
        Integer result = ppcPatrolPlanService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PutMapping("/updateStatus")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "状态更新",httpMethod = "PUT")
    public Result updateStatus(@RequestBody PpcPatrolPlan record) throws Exception {
        if (record.getIntervalQty() > 1000) {
            AssertUtil.throwException("请填写小于1000的间隔数");
        }
        if (record.getIntervalType() == null || record.getTemplateTableCode() == null
                ) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        double max = DateUtils.getHoursGap(record.getPlanStartTime(), record.getPlanEndTime());
        if (record.getTaskEffectHour() > max) {
            AssertUtil.throwException("任务有效期过长，已经超出计划完成日期");
        }
        if(record.getIsCalendar().equals("1")){
            Result result = sysInterfaceService.sltCalendar();
            List<LinkedHashMap> sltCalendar = (List<LinkedHashMap>) result.getData();
            if (!sltCalendar.stream().anyMatch(x -> {
                return record.getCalendarCode().equals(x.get("value"));
            })) {
                AssertUtil.throwException("该日历已经无效，无法继续使用");
            }
        }
        if(record.getStatus().equals("20")){
            inspectionPlanTaskService.generationRecordBYPpc(record);
        }
        ppcPatrolPlanService.update(record);
        return new Result(ResultCode.SUCCESS);
    }

    @DeleteMapping("/{id}")
    @ApiOperation(value = "根据id删除",httpMethod = "DELETE")
    public Result deleteById(@ApiParam("生产巡检计划id") @PathVariable("id") String id) {
        Integer result = ppcPatrolPlanService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 查询初始化数据
     *
     * @return
     */
    @ApiOperation(value = "获取生产巡检部门下拉列表",httpMethod = "GET")
    @GetMapping("/getDepartmentInfo")
    public Result getDepartmentInfo() throws Exception {
        HashMap<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> list = feignService.selectPartment(Constants.INSPECTION_PRODUCE_GROUP);
        List<SysDictDetailVo> rsList = new ArrayList();
        if (list != null && list.size() > 0) {
            for (Map<String, Object> map : list) {
                List<CoDepartmentOptions> coDepartmentOptions = feignService.queryUserByDeptCode(map.get("value").toString());
                if(coDepartmentOptions.isEmpty()){
                    continue;
                }
                SysDictDetailVo vo = new SysDictDetailVo();
                vo.setMainCode(Constants.CHECK_GROUP);
                vo.setId(map.get("id").toString());
                vo.setLabel(map.get("name").toString());
                vo.setCode(map.get("value").toString());
                rsList.add(vo);
            }
        }
        resultMap.put("departList",rsList);
        return new Result(ResultCode.SUCCESS, rsList);
    }


    @GetMapping("/cycle")
    @ApiIgnore
    public Result getCycleMap() {
        Map<String, List<Map<String, Object>>> result = new HashMap<>();
        result.put("cycles", AccurateCronEnum.getCronEnums());
        return new Result(ResultCode.SUCCESS, result);
    }


    @GetMapping("map")
    @ApiIgnore
    public Result getTempSelectMap() throws Exception {
        List<Map<String, String>> result = ppcPatrolPlanService.getTempSelectMap();
        return new Result(ResultCode.SUCCESS, result);

    }
    /**
     * 获取日历
     * */
    @ApiOperation(value = "获取日历",httpMethod = "GET")
    @GetMapping("/sltCalendar")
    public Result sltCalendar() {
        return sysInterfaceService.sltCalendar();
    }
}
