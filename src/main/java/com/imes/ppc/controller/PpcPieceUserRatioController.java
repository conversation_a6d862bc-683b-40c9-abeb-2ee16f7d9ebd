package com.imes.ppc.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.domain.entities.ppc.po.PpcPieceUserRatio;
import com.imes.ppc.service.PpcPieceUserRatioService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.ppc.PpcPieceUserRatioSearchVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 员工工时系数(PpcPieceUserRatio)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 19:55:27
 */
@RestController
@RequestMapping("/api/ppc/ppcPieceUserRatio")
@Api(tags = "员工工时系数")
public class PpcPieceUserRatioController {
    /**
     * 服务对象
     */
    @Autowired
    private PpcPieceUserRatioService ppcPieceUserRatioService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (ppcPieceUserRatioService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param ppcPieceUserRatio 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody PpcPieceUserRatio ppcPieceUserRatio) throws Exception {
        String result = ppcPieceUserRatioService.insert(ppcPieceUserRatio);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param ppcPieceUserRatio 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcPieceUserRatio ppcPieceUserRatio) throws Exception {
        if (ppcPieceUserRatioService.update(ppcPieceUserRatio) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcPieceUserRatioService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result<StatPageResult<PpcPieceUserRatioSearchVo>> queryList(PpcPieceUserRatioSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceUserRatioSearchVo> list = ppcPieceUserRatioService.queryList(vo);
        return Result.SUCCESS(new StatPageResult<>(page.getTotal(), list, map));
    }

}

