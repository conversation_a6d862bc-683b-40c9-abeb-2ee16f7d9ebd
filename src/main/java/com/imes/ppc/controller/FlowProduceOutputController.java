package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.common.utils.ExcelUtils;
import com.imes.domain.entities.ppc.po.PpcFlowProduceOutput;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.FlowProduceOutputService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/ppc/flow/produceOutput")
@Api(tags = "流程mes每日报工相关接口")
@ApiIgnore
public class FlowProduceOutputController {

    @Value("${imes.ppc.template.outputTemplate}")
    private String outputTemplatePath;
    @Autowired
    FeignService feignService;
    @Autowired
    FlowProduceOutputService flowProduceOutputService;

    /**
     * 查询初始化数据
     *
     * @return
     */
    @GetMapping("/initData")
    @ApiOperation(value = "查询初始化数据", httpMethod = "GET")
    public Result initData() throws Exception {
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("WORKSHOP_ALL", feignService.getALLWorkshopList());
        return new Result(ResultCode.SUCCESS, reMap);
    }


    @PostMapping("/queryOutput")
    @ApiOperation(value = "查询数据", httpMethod = "POST")
    public Result queryOutput(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              @RequestParam Map map) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("list", flowProduceOutputService.queryByParam(map));
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @GetMapping("/queryById/{id}")
    @ApiOperation(value = "通过id查询数据", httpMethod = "GET")
    public Result queryById(@PathVariable("id") String id) throws Exception {
        return new Result(ResultCode.SUCCESS, flowProduceOutputService.queryById(id));
    }

    @GetMapping("/deleteByIds/{ids}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "删除数据", httpMethod = "GET")
    public Result deleteByIds(@PathVariable("ids") String ids) throws Exception {
//        flowProduceOutputService.deleteByIds(ids);
        List<String> idList = Arrays.asList(ids.split(","));
        flowProduceOutputService.batchDeleteByIds(idList);
        return new Result(ResultCode.SUCCESS, null);
    }

    @PostMapping("/importFlowOutput")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "导入数据", httpMethod = "POST")
    public Result importFlowOutput(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 3);
        int dataNum = flowProduceOutputService.importData(importList);
        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
    }

    @GetMapping("/downloadTemplateOutput")
    @ApiOperation(value = "导出模版", httpMethod = "GET")
    public void downloadTemplateOutput(HttpServletRequest request, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            ClassPathResource resource = new ClassPathResource(outputTemplatePath);
            String fileName = resource.getFilename();
            response.setContentType("application/octet-stream");
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition", "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            }
            in = resource.getInputStream();
            out = response.getOutputStream();
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            out.flush();
        } catch (Exception e) {
            log.error("流程日报工FlowProduceOutputController.downloadTemplateOutput出错", e);
        } finally {
            try {
                if (out != null) out.close();
                if (in != null) in.close();
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @PostMapping("/saveOutput")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "保存数据", httpMethod = "POST")
    public Result saveOutput(@RequestBody PpcFlowProduceOutput ppcFlowProduceOutput) throws Exception {
        flowProduceOutputService.save(ppcFlowProduceOutput);
        return new Result(ResultCode.SUCCESS, null);
    }

    /**
     * 供能源调用接口
     *
     * @param reportDateStart
     * @param reportDateEnd
     * @param materialCode
     * @return
     */
    @GetMapping("/queryForEne")
    @ApiOperation(value = "供能源调用接口", httpMethod = "GET")
    public Result queryForEne(@RequestParam("reportDateStart") String reportDateStart,
                              @RequestParam("reportDateEnd") String reportDateEnd,
                              @RequestParam("materialCode") String materialCode) throws Exception {
        Map map = new HashMap();
        map.put("reportDateStart", reportDateStart);
        map.put("reportDateEnd", reportDateEnd);
        map.put("materialCode", materialCode);
        return new Result(ResultCode.SUCCESS, flowProduceOutputService.queryForEne(map));
    }
}
