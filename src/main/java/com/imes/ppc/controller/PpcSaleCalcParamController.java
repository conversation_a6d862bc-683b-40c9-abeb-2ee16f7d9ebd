package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import com.imes.ppc.service.PpcSaleCalcParamService;
import com.imes.domain.entities.ppc.po.PpcSaleCalcParam;;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 销售需求计算参数表(PpcSaleCalcParam)表控制层
 *
 * <AUTHOR> @since 2023-03-28 15:19:58
 */
@Slf4j
@RestController
@Api(tags = "销售需求计算参数表控制层")
@RequestMapping("/api/ppc/ppcSaleCalcParam")
public class PpcSaleCalcParamController {

    @Autowired
    private PpcSaleCalcParamService ppcSaleCalcParamService;

    @GetMapping("/queryByCond")
    @ApiOperation(value = "分页查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
            PpcSaleCalcParam ppcSaleCalcParam) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcSaleCalcParam> result = ppcSaleCalcParamService.queryByCond(ppcSaleCalcParam);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById")
    @ApiOperation(value = "根据id查询")
    public Result queryById(String id) {
        PpcSaleCalcParam result = ppcSaleCalcParamService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcSaleCalcParam record) throws Exception {
        Integer result = ppcSaleCalcParamService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcSaleCalcParam record) throws Exception {
        Integer result = ppcSaleCalcParamService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/deleteById")
    @ApiOperation(value = "根据id删除")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(String id) throws Exception {
        Integer result = ppcSaleCalcParamService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }
    
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(@ApiParam("主单id带逗号的") String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSaleCalcParamService.batchDelete(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }
}
