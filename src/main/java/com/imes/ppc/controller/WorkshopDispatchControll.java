package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.constant.ApiVersionConstants;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.common.exception.CommonException;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.common.support.Resume;
import com.imes.common.utils.*;
import com.imes.common.utils.api.ApiVersion;
import com.imes.domain.entities.po.po.PoPurchaseRequest;
import com.imes.domain.entities.po.po.PoPurchaseRequestDetail;
import com.imes.domain.entities.ppc.po.*;
import com.imes.domain.entities.ppc.vo.PpcQueryMaterialStockQty;
import com.imes.domain.entities.ppc.vo.ProduceInputVo;
import com.imes.domain.entities.ppc.vo.WorkOderVo;
import com.imes.domain.entities.query.template.lims.LimsExternalLabEnquiriesQueryVo;
import com.imes.domain.entities.query.template.lims.LimsSassayRecordQueryVo;
import com.imes.domain.entities.query.template.ppc.*;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.domain.entities.system.dept.CoDepartmentLine;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("api/ppc/workshop")
@Api(tags = "车间管理相关接口")
public class WorkshopDispatchControll {

    @Autowired
    ProducePlanSyncOldService oldService;
    @Autowired
    ProducePlanService producePlanService;
    @Autowired
    ProduceProcessService produceProcessService;
    @Autowired
    WorkOrderService workOrderService;
    @Autowired
    ProduceInputService inputService;
    @Autowired
    PpcProduceLockService lockService;
    @Autowired
    WorkOrderProcessParameterService workOrderProcessParameterService;
    @Autowired
    FeignService feignService;
    @Autowired
    PpcProducePlanSchedulService ppcProducePlanSchedulService;
    @Autowired
    PpcProducePlanSchedulRePlanService ppcProducePlanSchedulRePlanService;


    @Autowired
    PpcBomProdService bomProdService;

    /**
     * 查询初始化数据
     *
     * @return
     */
    @GetMapping("/initData")
    @ApiOperation(value = "查询基础数据（所有车间信息，计划状态（数据字典），派工单状态（数据字典），派工单类型（数据字典）），查询当前用户所在的车间以及当前用户是否为车间主任）", httpMethod = "GET")
    public Result initData() throws Exception {
        Map<String, Object> reMap = new HashMap<>();
        reMap.put("WORKSHOP_ALL", feignService.getALLWorkshopList());
        reMap.put("IS_OR_NOT", feignService.getDictCode("IS_OR_NOT"));
        reMap.put("PP_STATUS", feignService.getDictCode("PP_STATUS"));
        reMap.put("WO_STATUS", feignService.getDictCode("WO_STATUS"));
        reMap.put("WO_TYPE", feignService.getDictCode("WO_TYPE"));
        //查询当前用户是否为车间主任 所在的车间
        reMap.put("DFT_WORKSHOP", feignService.getWorkShopByLeaderUser(RedisUtils.getUserCode(), Constants.DEPARTMENT_TYPE_WORKSHOP));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @GetMapping("/queryDispatchInit")
    @ApiIgnore
    public Result queryDispatchInit(@RequestParam Map<String, Object> map) throws Exception {
        //查询设备
        String produceProcessId = map.get("produceProcessId").toString();
        String workId = null;
        if (map.containsKey("id")) {
            workId = map.get("id").toString();
        }
        map = workOrderService.queryDispatchInit(produceProcessId, workId);
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/queryAll")
    @ApiIgnore
    public Result queryAll(@RequestParam Map<String, Object> map) throws Exception {
        List<PpcWorkOrder> ppcWorkOrderList = workOrderService.queryByParam(map);
        return new Result(ResultCode.SUCCESS, ppcWorkOrderList);
    }

    @GetMapping("/queryAllDeduplication")
    @ApiIgnore
    public Result queryAllDeduplication(@RequestParam Map<String, Object> map) throws Exception {
        List<PpcWorkOrder> ppcWorkOrderList = workOrderService.queryAllDeduplication(map);
        return new Result(ResultCode.SUCCESS, ppcWorkOrderList);
    }

    /**
     * 查询工序树
     *
     * @return
     */
    @GetMapping("/queryPlanList")
    @ApiIgnore
    public Result queryPlanList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                @RequestParam Map<String, Object> map) throws Exception {
        //查询树
        Page page = PageHelper.startPage(pageNum, pageSize);
        map.put("statusIn", StringUtils.str2In(map.get("statusIn")));
        map.put("dispatchStage", true);
        List<PpcProducePlan> list = producePlanService.queryPlanList(map);
        Map reMap = new HashMap();
        reMap.put("list", list);
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @GetMapping("/queryPlanProcessList/{producePlanId}")
    @ApiIgnore
    public Result queryPlanProcessList(@PathVariable("producePlanId") String producePlanId) throws Exception {
        List<Map<String, Object>> result = producePlanService.queryPlanProcessList(producePlanId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/saveDispatchOne")
    @ApiIgnore
    public Result saveDispatchOne(@RequestBody PpcWorkOrder ppcWorkOrder) throws Exception {
        workOrderService.save(ppcWorkOrder);
        return new Result(ResultCode.SUCCESS, null);
    }

    /*
    planId,planProcessId,planQty,planStartDate,planEndDate
     */
    @Transactional(rollbackOn = Exception.class)
    @PostMapping("/saveDispatchBatch")
    @ApiIgnore
    public Result saveDispatchBatch(@RequestParam Map<String, Object> map) throws Exception {
        workOrderService.dispatchByProduceProcessIds(map);
        return new Result(ResultCode.SUCCESS, null);
    }

    /*
     */
    @Transactional(rollbackOn = Exception.class)
    @PostMapping("/saveDispatchByProducePlanId/{producePlanId}")
    @ApiIgnore
    public Result saveDispatchByProducePlanId(@PathVariable("producePlanId") String producePlanId) throws Exception {
        workOrderService.dispatchByProducePlanId(producePlanId);
        return new Result(ResultCode.SUCCESS, null);
    }

    @GetMapping("/queryWorkOrderList/{producePlanId}")
    @ApiIgnore
    public Result queryWorkOrderList(@PathVariable("producePlanId") String producePlanId) throws Exception {
        //查询树
        List<PpcWorkOrder> list = workOrderService.queryByProducePlanId(producePlanId);
        return new Result(ResultCode.SUCCESS, list);
    }


    @GetMapping("/queryWorkOrderListByProductionNo")
    @ApiOperation(value = "查询排产下所有派工单", httpMethod = "GET")
    public Result queryWorkOrderListByProductionNo(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        //查询树
        List<PpcWorkOrder> list = workOrderService.queryByProductionNo(productionNo);
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/queryWorkOrderByPIdAndShopCode/{planId}")
    @ApiIgnore
    public Result queryWorkOrderByPIdAndShopCode(@PathVariable("planId") String planId, @RequestParam("workshopCode") String workshopCode) throws Exception {
        Map map = new HashMap();
        map.put("planId", planId);
        map.put("workshopCode", workshopCode);
        List<PpcWorkOrder> result = workOrderService.queryByParam(map);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 查询排产生产情况
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/queryWorkOrderByPIdAndProductionNo")
    @ApiOperation(value = "查询排产生产情况", httpMethod = "GET")
    public Result queryWorkOrderByPIdAndProductionNo(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("工序名称") @RequestParam(required = false, value = "processName") String processName) throws Exception {
        List list = workOrderService.getCompleteDetail(productionNo, processName);
        return new Result(ResultCode.SUCCESS, list);
    }


    @GetMapping("/findListByCondition")
    @ApiOperation(value = "班组长任务管理-工序情况", httpMethod = "GET")
    public Result findListByCondition(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                      @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                      WorkOrderProcessQueryVo vo) throws Exception {

        Page page = PageHelper.startPage(pageNum, pageSize);
        List<WorkOrderProcessQueryVo> workOrderProcessQueryVoList = workOrderService.findListByCondition(vo);
        Map reMap = new HashMap();
        reMap.put("list", workOrderProcessQueryVoList);
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }


    /**
     * 查询排产生产情况
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/queryWorkOrderByPIdAndProductionNoICS")
    @ApiOperation(value = "查询排产生产情况", httpMethod = "GET")
    public Result queryWorkOrderByPIdAndProductionNoICS(@ApiParam("分页页数") @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                                        @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                                        @ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("工序名称") @RequestParam(required = false, value = "processName") String processName) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List list = workOrderService.getCompleteDetail(productionNo, processName);
        Map map = new HashMap();
        map.put("result", list);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/queryMember")
    @ApiIgnore
    public Result queryMember(@RequestParam("teamCode") String teamCode) throws Exception {
        CoDepartment coDepartment = feignService.findByDepartmentCode(teamCode);
        List<Map<String, Object>> result = feignService.sltUserByDepartId(coDepartment.getId());
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/deleteWo/{id}")
    @ApiIgnore
    public Result deleteWo(@PathVariable("id") String id) throws Exception {
        workOrderService.deleteById(id);
        return new Result(ResultCode.SUCCESS, "");
    }

    /**
     * 根据状态查询派工信息
     *
     * @param
     * @return
     */
    @GetMapping("/getWorkOrderList")
    @ApiIgnore
    public Result getWorkOrderList(@ApiParam("分页页数") @RequestParam("current") Integer current, @ApiParam("分页条数") @RequestParam("size") Integer size,
                                   @ApiParam("报工单id") @RequestParam("finishId") String finishId, String[] ids, @ApiParam("工序编码") @RequestParam(required = false, value = "processCode") String processCode,
                                   @ApiParam("排产单号") @RequestParam(required = false, value = "productionNo") String productionNo, @ApiParam("物料编码") @RequestParam(required = false, value = "materialCode") String materialCode,
                                   @ApiParam("物料名称") @RequestParam(required = false, value = "materialName") String materialName, @ApiParam("物料规格") @RequestParam(required = false, value = "specification") String specification,
                                   @ApiParam("付工序名称") @RequestParam(required = false, value = "parentName") String parentName) throws Exception {

        Page page = PageHelper.startPage(current, size);
        Map map = new HashMap();
        List<WorkOderVo> result = workOrderService.selectWorkOderVo(ids, finishId, processCode, productionNo, materialCode, materialName, specification, parentName);
        map.put("result", result);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/findWorkOrderVoList")
    @ApiOperation(value = "查询班组作业信息--单任务列表信息", httpMethod = "GET")
    public Result findWorkOrderVoList(PpcWorkOrderQueryVo vo) throws Exception {
        String custom = vo.getCustom();
        if (!StringUtils.isNullOrBlank(vo.getPickNo())) {
            PpcProducePlan plan = oldService.getPlanByErpLocalApplicationId(vo.getPickNo());
            if (plan == null) {
                AssertUtil.throwException("未找到该送料单id:{}的计划", vo.getPickNo());
            }
            vo.setErpOrderId(plan.getLocalOrderId());
//            custom = CustomUtil.put(custom, "工单号", plan.getOrderNo());
//            vo.setCustom(custom);
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize(), false);
        List<WorkOderVo> list = workOrderService.findWorkOrderVo(vo);
        return Result.SUCCESS(new StatPageResult(null, list, null));
    }

    @GetMapping("/findWorkOrderVoListCount")
    @ApiOperation(value = "查询班组作业信息--单任务总条数", httpMethod = "GET")
    public Result findWorkOrderVoListCount(PpcWorkOrderQueryVo vo) throws Exception {
//        String custom = vo.getCustom();
//        if (!StringUtils.isNullOrBlank(vo.getPickNo())) {
//            PpcProducePlan plan = producePlanService.getPlanByErpLocalApplicationId(vo.getPickNo());
//            vo.setErpOrderId(plan.getLocalOrderId());
//            custom = CustomUtil.put(custom, "工单号", plan.getOrderNo());
//            vo.setCustom(custom);
//        }
        Long count = workOrderService.findWorkOrderVoCount(vo);
        return new Result(ResultCode.SUCCESS, count);
    }


    @GetMapping("/findWorkOrderMergeVoList")
    @ApiOperation(value = "查询班组作业信息--合并任务", httpMethod = "GET")
    public Result findWorkOrderMergeVoList(PpcWorkOrderMergeQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<WorkOderVo> list = workOrderService.findWorkOrderMergeVo(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @GetMapping("/findWorkOrderPAD")
    @ApiOperation(value = "工控机查询生产作业列表", httpMethod = "GET")
    public Result findWorkOrderPAD(PpcWorkOrderICSVO vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcWorkOrderICSVO> list = workOrderService.findWorkOrderPAD(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * @param qrCodeType 1  排产单号  2  SN码
     * @return
     * @throws Exception
     */
    @GetMapping("/getWorkOrderListH5")
    @ApiOperation(value = "移动端生产作业单任务", httpMethod = "GET")
    public Result getWorkOrderList(@ApiParam("分页页数") @RequestParam("current") Integer current, @ApiParam("分页条数") @RequestParam("size") Integer size,
                                   @ApiParam("工序编码") String processCode, @ApiParam("车间编码") String departCode, @ApiParam("排产单号") String productionNo,
                                   @ApiParam("物料名称") String materialName, @ApiParam("物料规格") String specification, @ApiParam("产线编码") String lineCode, @ApiParam("二维码类型 1  排产单号  2  SN码") String qrCodeType) throws Exception {
        //查询登录员工所在的班组
        String userCode = RedisUtils.getUserCode();
        List<String> teamCodes = new ArrayList<>();
        if (!Constants.ADMIN.equals(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到生产班组");
            } else {
                for (CoDepartment department : departmentList) {
                    teamCodes.add(department.getDepartCode());
                }
            }
        }
        Set<String> processSet = new HashSet<>();
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(userCode)) {
            processSet = feignService.queryPermittedProcess(userCode);
            if (processSet.isEmpty()) {
                AssertUtil.throwException("该员工无工序权限！");
            }
            StringBuilder processCodeIn = new StringBuilder();
            processSet.forEach(e -> {
                processCodeIn.append(e).append("∫");
            });
        }
        Page page = PageHelper.startPage(current, size, true, false, null);
        Map map = new HashMap();
        List<WorkOderVo> result = workOrderService.selectWorkOderVoH5(lineCode, teamCodes, processSet, processCode, departCode, productionNo, materialName, specification, qrCodeType, null);
        map.put("result", result);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }


    /**
     * @param qrCodeType 1  排产单号  2  SN码
     * @return
     * @throws Exception
     */
    @GetMapping("/getMergeWorkOrderListH5")
    @ApiOperation(value = "移动端生产作业单任务合并任务", httpMethod = "GET")
    public Result getMergeWorkOrderListH5(@ApiParam("分页页数") @RequestParam("current") Integer current, @ApiParam("分页条数") @RequestParam("size") Integer size,
                                          @ApiParam("工序编码") String processCode, @ApiParam("车间编码") String departCode, @ApiParam("排产单号") String productionNo,
                                          @ApiParam("物料名称") String materialName, @ApiParam("物料规格") String specification, @ApiParam("产线编码") String lineCode, @ApiParam("二维码类型 1  排产单号  2  SN码") String qrCodeType) throws Exception {
        //查询登录员工所在的班组
        String userCode = RedisUtils.getUserCode();
        List<String> teamCodes = new ArrayList<>();
        if (!Constants.ADMIN.equals(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到生产班组");
            } else {
                for (CoDepartment department : departmentList) {
                    teamCodes.add(department.getDepartCode());
                }
            }
        }
        Set<String> processSet = new HashSet<>();
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(userCode)) {
            processSet = feignService.queryPermittedProcess(userCode);
            if (processSet.isEmpty()) {
                AssertUtil.throwException("该员工无工序权限");
            }
            StringBuilder processCodeIn = new StringBuilder();
            processSet.forEach(e -> {
                processCodeIn.append(e).append("∫");
            });
        }
        Page page = PageHelper.startPage(current, size);
        Map map = new HashMap();
        List<WorkOderVo> result = workOrderService.selectWorkOderVoH5(lineCode, teamCodes, processSet, processCode, departCode, productionNo, materialName, specification, qrCodeType, "30");
        map.put("result", result);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }

    @GetMapping("/getWorkOrderListH5AndFirst")
    @ApiIgnore
    public Result getWorkOrderListH5AndFirst(@RequestParam("current") Integer current,
                                             @RequestParam("size") Integer size, String processCode, String productionNo, String materialName, String specification, String lineCode) throws Exception {
        //查询登录员工所在的班组
        String userCode = RedisUtils.getUserCode();
        List<String> teamCodes = new ArrayList<>();
        if (!Constants.ADMIN.equals(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到生产班组");
            } else {
                for (CoDepartment department : departmentList) {
                    teamCodes.add(department.getDepartCode());
                }
            }
        }

        Page page = PageHelper.startPage(current, size);
        Map map = new HashMap();
        List<WorkOderVo> result = workOrderService.getWorkOrderListH5AndFirst(lineCode, teamCodes, processCode, productionNo, materialName, specification);
        for (WorkOderVo vo : result) {
            if (vo.getProduceQty().compareTo(vo.getFinishedQty()) > 0) {
                vo.setSurplusQty(vo.getProduceQty().subtract(vo.getFinishedQty()));
            } else {
                vo.setSurplusQty(new BigDecimal("0"));
            }
        }
        map.put("result", result);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }


    @GetMapping("/getWorkOrderListH5AndBatCh")
    @ApiIgnore
    public Result getWorkOrderListH5AndBatCh(@RequestParam("current") Integer current,
                                             @RequestParam("size") Integer size, String processCode, String lineCode, @RequestParam(required = false, value = "searchKey") String searchKey) throws Exception {
        //查询登录员工所在的班组
        String userCode = RedisUtils.getUserCode();
        List<String> teamCodes = new ArrayList<>();
        if (!Constants.ADMIN.equals(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_TYPE_PRODUCE);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到生产班组");
            } else {
                for (CoDepartment department : departmentList) {
                    teamCodes.add(department.getDepartCode());
                }
            }
        }

        Page page = PageHelper.startPage(current, size);
        Map map = new HashMap();
        List<WorkOderVo> result = workOrderService.getWorkOrderListH5AndBatCh(teamCodes, processCode, lineCode, searchKey);
        for (WorkOderVo vo : result) {
            if (vo.getProduceQty().compareTo(vo.getFinishedQty()) > 0) {
                vo.setSurplusQty(vo.getProduceQty().subtract(vo.getFinishedQty()));
            } else {
                vo.setSurplusQty(new BigDecimal("0"));
            }
        }
        map.put("result", result);
        map.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, map);
    }

    @PostMapping("/updateWorkFinishBatchStatus")
    @Transactional(rollbackOn = Exception.class)
    @ApiIgnore
    public Result updateWorkFinishBatchStatus(@RequestBody WorkOderVo workOderVo, @RequestParam("status") String status) throws Exception {
        workOrderService.updateWorkFinishBatchStatus(workOderVo, status);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/getpickByPlanId/{planId}")
    @ApiIgnore
    public Result getpickByPlanId(@PathVariable("planId") String planId) throws Exception {
        List<ProduceInputVo> result = inputService.queryByPlanId(planId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/findPickByPlanId")
    @ApiIgnore
    public Result findPickByPlanId(@RequestParam("workSchedulNo") String workSchedulNo, @RequestParam(value = "woNo", required = false) String woNo) throws Exception {
        List<ProduceInputVo> result = inputService.findByPlanId(workSchedulNo, woNo);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 排产单齐套性检查
     *
     * @param
     * @return
     */
    @PostMapping("/findCompleteSetByProductionNoList")
    @ApiOperation(value = "排产单齐套性检查", httpMethod = "POST")
    public Result findCompleteSetByProductionNoList(@ApiParam("排产单号集合") @RequestBody List<String> productionNoList) throws Exception {
        Map map = lockService.getCompleteSetByProductionNoList(productionNoList);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 排产单齐套性检查
     *
     * @param
     * @return
     */
    @PostMapping("/findCompleteSetByProductionNoListAndCW")
    @ApiOperation(value = "排产单齐套性检查", httpMethod = "POST")
    public Result findCompleteSetByProductionNoListAndCW(@RequestBody PpcQueryMaterialStockQty vo) throws Exception {
        Map map = lockService.findCompleteSetByProductionNoListAndCW(vo);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 生产计划齐套性检查
     *
     * @param
     * @return
     */
    @PostMapping("/findCompleteSetByPlan")
    @ApiOperation(value = "生产计划齐套性检查", httpMethod = "POST")
    public Result findCompleteSetByPlan(@ApiParam("生产计划id集合") @RequestParam("planIds") List<String> planIds) throws Exception {
        Map map = lockService.getCompleteSetByPlanIds(planIds);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 生产计划用料清单检查检查
     *
     * @param
     * @return
     */
    @PostMapping("/findMaterialsCompleteSetByPlan")
    @ApiOperation(value = "生产计划用料清单检查检查", httpMethod = "POST")
    public Result findMaterialsCompleteSetByPlan(@ApiParam("生产计划id集合") @RequestParam("planIds") List<String> planIds) throws Exception {
        Map map = lockService.findMaterialsCompleteSetByPlan(planIds);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 排产单齐套性检查
     *
     * @param
     * @return
     */
    @PostMapping("/findCompleteSetBySchedule")
    @ApiOperation(value = "排产单齐套性检查", httpMethod = "POST")
    public Result findCompleteSetBySchedule(@ApiParam("排产单号集合") @RequestParam("productionNoList") List<String> productionNoList) throws Exception {
        Map map = lockService.getCompleteSetByProductionNoList(productionNoList);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 缺料导出
     *
     * @param
     * @return
     */

    @PostMapping("/exportLackMaterial")
    @ApiOperation(value = "缺料导出为EXCEl", httpMethod = "POST")
    public Result exportLackMaterial(@RequestBody List<Map<String, Object>> lackMaterialList, HttpServletRequest request,
                                     HttpServletResponse response) throws Exception {
        ArrayList<Map<String, Object>> newLackMaterialList = new ArrayList<>();
        for (Map map : lackMaterialList) {
            if (map.get("lackQty") != null) {
                BigDecimal lackQty = new BigDecimal(map.get("lackQty").toString());
                if (lackQty.compareTo(new BigDecimal("0")) > 0) {
                    newLackMaterialList.add(map);
                }
            }
        }
        String[] keys = {"ppNo", "materialCode", "materialName", "matchingQty", "needQty",
                "stockAvailableQty", "lackQty", "primaryUnit", "category"};
        String[] heads = {"计划单号", "物料编码", "物料名称", "单个用量", "总用量", "库存可用数量", "缺量",
                "单位", "类型"};
        if (newLackMaterialList.size() == 0) {
            AssertUtil.throwException("物料明细中不存在缺量大于0的数据");
        }
        ExcelUtils.exportExcel(response, request, newLackMaterialList, keys, heads, "缺料信息");
        return null;
    }


    /**
     * 缺料导出
     *
     * @param
     * @return
     */

    @PostMapping("/exportLackMaterialBySchedule")
    @ApiOperation(value = "缺料导出为EXCEl", httpMethod = "POST")
    public Result exportLackMaterialBySchedule(@RequestBody List<Map<String, Object>> lackMaterialList, HttpServletRequest request,
                                               HttpServletResponse response) throws Exception {
        ArrayList<Map<String, Object>> newLackMaterialList = new ArrayList<>();
        for (Map map : lackMaterialList) {
            if (map.get("lackQty") != null) {
                BigDecimal lackQty = new BigDecimal(map.get("lackQty").toString());
                if (lackQty.compareTo(new BigDecimal("0")) > 0) {
                    newLackMaterialList.add(map);
                }
            }
        }
        String[] keys = {"productionNo", "materialCode", "materialName", "matchingQty", "needQty",
                "stockAvailableQty", "lackQty", "primaryUnitName"};
        String[] heads = {"排产单号", "物料编码", "物料名称", "单个用量", "总用量", "库存可用数量", "缺量",
                "单位"};
        if (newLackMaterialList.size() == 0) {
            AssertUtil.throwException("物料明细中不存在缺量大于0的数据");
        }
        ExcelUtils.exportExcel(response, request, newLackMaterialList, keys, heads, "缺料信息");
        return null;
    }


    @GetMapping("/getpickByPlanIdAndWoId")
    @ApiIgnore
    public Result getpickByPlanIdAndWoId(@RequestParam("planId") String planId, @RequestParam("woId") String woId) throws Exception {
        List<ProduceInputVo> result = inputService.queryByPlanIdAndWoId(planId, woId);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PutMapping("/uptStatus/{id}")
    @ApiIgnore
    public Result uptStatus(@PathVariable("id") String id) throws Exception {
        workOrderService.setStatus(id, Constants.WO_STATUS_30, null, null, new ArrayList<>());
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/issueWo/{id}")
    @ApiIgnore
    public Result issueWo(@PathVariable("id") String id) throws Exception {
        workOrderService.issueWo(id);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/splitWo/{id}")
    @Transactional(rollbackOn = Exception.class)
    @ApiIgnore
    public Result splitWo(@PathVariable("id") String id) throws Exception {
        workOrderService.splitWo(id);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/findLineByWorkshopAndProcess")
    @ApiIgnore
    public Result findLineByWorkshopAndProcess(@RequestParam("workshopCode") String workshopCode, @RequestParam("processCode") String processCode) throws Exception {
        List<CoDepartmentLine> list = workOrderService.findLineByWorkshopAndProcess(workshopCode, processCode);
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/queryTeamByWorksho")
    @ApiIgnore
    public Result queryTeamByWorksho(@RequestParam("workshopCode") String workshopCode) throws Exception {
        List<CoDepartment> teamList = feignService.findByParentCodeAndType(workshopCode);
        return new Result(ResultCode.SUCCESS, teamList);
    }

    /**
     * 保存计划排产信息
     *
     * @param map
     * @return
     */
    @PostMapping("/insetProduction")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "保存计划排产信息", httpMethod = "POST")
    public Result insetProduction(@RequestBody Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        ArrayList<Resume> resumeList = new ArrayList<>();
        workOrderService.insetProduction(map, new ArrayList<>());
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 默认计算可排产量
     *
     * @param planId
     * @return
     */
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "默认计算可排产量", httpMethod = "GET")
    @GetMapping("/produceQtyByPlanId")
    public Result produceQtyByPlanId(@ApiParam("生产计划id") @RequestParam("planId") String planId) throws Exception {
        if (planId.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        return new Result(ResultCode.SUCCESS, workOrderService.produceQtyByPlanId(planId));
    }


//    @PostMapping("/insetProduction")
//    @Transactional(rollbackOn = Exception.class)
//    @ApiVersion(value = ApiVersionConstants.FU_JIAN_QIN_QIN)
//    @ApiOperation(value = "福建亲亲-保存-计划排产此信息-增加上报状态", httpMethod = "GET")
//    public Result insetProductionQinQin(@RequestBody Map<String, Object> map) throws Exception {
//        if (map.isEmpty()) {
//            return new Result(ResultCode.ILLEGAL_PARAMETER);
//        }
//        workOrderService.insetProductionQinQin(map);
//        return new Result(ResultCode.SUCCESS);
//    }


    /**
     * 查看生产计划下是否有排产，没有自动生产,生成的排查单默认为录入状态
     *
     * @param planId
     * @return
     */
    @GetMapping("/insetProductionByPlanId")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "查看生产计划下是否有排产，没有自动生产,生成的排查单默认为录入状态", httpMethod = "GET")
    public Result insetProductionByPlanId(@ApiParam("生产计划id") @RequestParam("planId") String planId) throws Exception {
        if (planId.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        ArrayList<Resume> resumeList = new ArrayList<>();
        workOrderService.insetProductionByPlanId(planId, resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 获取计划排产信息
     *
     * @param planId
     * @return
     */
    @GetMapping("/getProduction/{planId}")
    @ApiOperation(value = "根据计划id获取计划排产信息", httpMethod = "GET")
    public Result getProductionByPlanId(@ApiParam("生产计划id") @PathVariable("planId") String planId) throws Exception {
        if (StringUtils.isNullOrBlank(planId)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<List<PpcWorkOrder>> productionByPlan = workOrderService.getProductionByPlan(planId);
        return new Result(ResultCode.SUCCESS, productionByPlan);
    }

    /**
     * 修改计划排产信息
     *
     * @param map
     * @return
     */
    @PostMapping("/updateProduction")
    @Transactional(rollbackOn = Exception.class)
    @ApiIgnore
    public Result updateProduction(@RequestBody Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.updateProduction(map);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 修改计划排产信息
     *
     * @param productionNo  排产单号
     * @param routeCode     工艺路线编码
     * @param planStartDate 计划开始日期
     * @param planEndDate   计划结束日期
     * @return
     */
    @GetMapping("/updateProductionRouteLine")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "修改计划排产信息", httpMethod = "GET")
    public Result updateProductionRouteLine(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("工艺路线编码") @RequestParam(required = false, value = "routeCode") String routeCode,
                                            @ApiParam("计划开始时间") @RequestParam("planStartDate") Date planStartDate, @ApiParam("计划结束时间") @RequestParam("planEndDate") Date planEndDate,
                                            @ApiParam("排产数量") @RequestParam("produceQty") BigDecimal produceQty) throws Exception {
        if (productionNo.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.updateProductionRouteLine(productionNo, routeCode, planStartDate, planEndDate, produceQty);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 排产单拆单
     *
     * @param productionNo     排产单号
     * @param splitQty         拆分批量
     * @param splitIntervalDay 批量拆分间隔天数
     * @return
     */
    @GetMapping("/splitSchedule")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "按照间隔天数，拆分批量对排产单进行拆单", httpMethod = "GET")
    public Result splitSchedule(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("拆分批量") @RequestParam("splitQty") BigDecimal splitQty, @ApiParam("间隔天数") @RequestParam("splitIntervalDay") Integer splitIntervalDay) throws Exception {
        if (productionNo.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<Resume> resumeList = workOrderService.splitSchedule(productionNo, splitQty, splitIntervalDay, new ArrayList<>());
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 删除计划排产信息
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/deletePpcWorkOrderByProductionNo")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "根据排产单号删除计划排产信息", httpMethod = "GET")
    public Result deletePpcWorkOrderByProductionNo(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        if (StringUtils.isNullOrBlank(productionNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.deletePpcWorkOrderByProductionNo(productionNo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取计划排产信息
     *
     * @param plan
     * @return
     */
    @GetMapping("/findPlanProduction")
    @ApiOperation(value = "获取计划排产信息", httpMethod = "GET")
    public Result findPlanProduction(@ApiParam("分页页码") @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @ApiParam("分页条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                     PpcProductionPlanVo plan) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProductionPlanModel> planProdution = workOrderService.findPlanProduction(plan);
        Map reMap = new HashMap();
        reMap.put("list", planProdution);
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取计划排产信息
     *
     * @param map
     * @return
     */
    @GetMapping("/findPlanProductionByTracking")
    @ApiIgnore
    public Result findPlanProductionByTracking(@RequestParam Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        //查询
        if (map.get("statusIn") != null) {
            map.put("statusIn", map.get("statusIn").toString().split(","));
        }
        List<Map<String, String>> planProduction = workOrderService.findPlanProductionByTracking(map);
        Map reMap = new HashMap();
        reMap.put("list", planProduction);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 保存产线车间配置
     *
     * @param ppcWorkOrder
     * @return
     */
    @GetMapping("/saveTeam")
    @Transactional
    @ApiOperation(value = "保存产线车间配置", httpMethod = "GET")
    public Result saveTeam(PpcWorkOrder ppcWorkOrder) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        ppcProducePlanSchedulService.assignTeam(ppcWorkOrder, resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取投料信息
     *
     * @param map
     * @return
     */
    @GetMapping("/findWorkFinish")
    @ApiIgnore
    public Result findWorkFinish(@RequestParam Map<String, String> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<Map<String, Object>> workFinish = workOrderService.findWorkFinish(map);
        return new Result(ResultCode.SUCCESS, workFinish);
    }

    /**
     * 收料
     *
     * @param list
     * @return
     */
    @PostMapping("/updateWorkFinish")
    @ApiIgnore
    @Transactional(rollbackOn = Exception.class)
    public Result updateWorkFinish(@RequestBody List<Map<String, Object>> list) throws Exception {
        if (list.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.updateWorkFinish(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 根据派工单号获取收料数据
     *
     * @param woNo
     * @return
     */
    @GetMapping("/queryReceive")
    @ApiIgnore
    public Result queryReceive(@RequestParam("woNo") String woNo) throws Exception {
        if (StringUtils.isNullOrBlank(woNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<Map<String, Object>> maps = workOrderService.queryReceive(woNo);
        return new Result(ResultCode.SUCCESS, maps);
    }

    /**
     * 根据排产单号查询所有工序
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/queryProcess")
    @ApiOperation(value = "根据排产单号查询所有工序", httpMethod = "GET")
    public Result queryProcess(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        if (StringUtils.isNullOrBlank(productionNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<Map<String, Object>> maps = workOrderService.queryProcess(productionNo);
        return new Result(ResultCode.SUCCESS, maps);
    }

    /**
     * 条件查询排产领料信息
     *
     * @param map
     * @return
     */
    @GetMapping("/queryPickMain")
    @ApiIgnore
    public Result queryPickMain(@RequestParam Map<String, Object> map) throws Exception {
        if (map.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<Map<String, Object>> maps = workOrderService.queryPickMain(map);
        return new Result(ResultCode.SUCCESS, maps);
    }

    /**
     * 保存收料用量信息
     *
     * @param list
     * @return
     */
    @GetMapping("/insertFinishRecive")
    @ApiIgnore
    public Result insertFinishRecive(@RequestBody List<Map<String, Object>> list) throws Exception {
        if (list.isEmpty()) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.insertFinishRecive(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 根据派工单号判断当前工序是否为第一工序
     *
     * @param woNo
     * @return
     */
    @GetMapping("/processIsOne")
    @ApiOperation(value = "根据派工单号判断当前工序是否为第一工序", httpMethod = "GET")
    public Result processIsOne(@ApiParam("派工单号") @RequestParam("woNo") String woNo) throws Exception {
        if (StringUtils.isNullOrBlank(woNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        boolean b = workOrderService.processIsOne(woNo, "order by process_no asc");
        Map<Object, Object> ovj = new HashMap<>();
        ovj.put("type", b);
        return new Result(ResultCode.SUCCESS, ovj);
    }

    /**
     * 根据派工单号判断当前工序是否为末道工序
     *
     * @param woNo
     * @return
     */
    @GetMapping("/processIsEnd")
    @ApiOperation(value = "根据派工单号判断当前工序是否为末道工序", httpMethod = "GET")
    public Result processIsEnd(@ApiParam("派工单号") @RequestParam("woNo") String woNo) throws Exception {
        if (StringUtils.isNullOrBlank(woNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        boolean b = workOrderService.processIsOne(woNo, "order by process_no desc");
        Map<Object, Object> ovj = new HashMap<>();
        ovj.put("type", b);
        return new Result(ResultCode.SUCCESS, ovj);
    }

    /**
     * 查询排产单最后一道工序合格数量
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/queryQualifiedCount")
    @ApiOperation(value = "查询排产单号查询末道工序合格数量", httpMethod = "GET")
    public Result queryQualifiedCount(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        if (StringUtils.isNullOrBlank(productionNo)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        List<PpcWorkFinish> ppcWorkFinishes = workOrderService.queryQualifiedCount(productionNo);
        return new Result(ResultCode.SUCCESS, ppcWorkFinishes);
    }

    /**
     * 更新排产数据状态
     *
     * @param productionNo
     * @param status
     * @return
     */
    @GetMapping("/updateProduceSchedul")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "更新排产数据状态", httpMethod = "GET")
    public Result updateProduceSchedul(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("状态") @RequestParam("status") String status) throws Exception {
        if (StringUtils.isNullOrBlank(productionNo) || StringUtils.isNullOrBlank(status)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.updateProduceSchedulStatus(productionNo, status);
        return new Result(ResultCode.SUCCESS);

    }

    /**
     * 更新派工数据状态
     *
     * @param id
     * @param status
     * @return
     */
    @GetMapping("/updateWorkOrder")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "更新派工数据状态", httpMethod = "GET")
    public Result updateWorkOrder(@ApiParam("派工单id") @RequestParam("id") String id, @ApiParam("状态") @RequestParam("status") String status) throws Exception {

        if (StringUtils.isNullOrBlank(id) || StringUtils.isNullOrBlank(status)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        ArrayList<Resume> resumeList = new ArrayList<>();
        workOrderService.updateByPrimaryKeySelective(id, status, resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 更新派工数据状态
     *
     * @param ids
     * @param status
     * @return
     */
    @GetMapping("/batchUpdateWorkOrder")
    @Transactional(rollbackOn = Exception.class)
    public Result batchUpdateWorkOrder(@RequestParam("ids") List<String> ids, @RequestParam("status") String status) throws Exception {
        Vector<Resume> resumeList = new Vector<>();
        String result = workOrderService.batchUpdateWorkOrder(ids, status, resumeList);
        feignService.saveResume(resumeList);
        if ("1".equals(result)) {
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(99998, result, false);
        }
    }

    /**
     * 更新派工数据状态(班组长任务管理工序任务)  (只用于反完工和反开工)
     *
     * @param id
     * @param status
     * @return
     */
    @GetMapping("/updateWorkOrderByProcess")
    @ApiOperation(value = "更新派工数据状态(班组长任务管理工序任务)", httpMethod = "GET")
    @Transactional(rollbackOn = Exception.class)
    public Result updateWorkOrderByProcess(@ApiParam("派工单id") @RequestParam("id") String id, @ApiParam("状态") @RequestParam("status") String status) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        if (StringUtils.isNullOrBlank(id) || StringUtils.isNullOrBlank(status)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER);
        }
        workOrderService.updateWorkOrderByProcess(id, status, resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 批量更新派工数据状态(班组长任务管理工序任务)  (只用于反完工和反开工)
     *
     * @param ids
     * @param status
     * @return
     */
    @GetMapping("/batchUpdateWorkOrderByProcess")
    @Transactional(rollbackOn = Exception.class)
    public Result batchUpdateWorkOrderByProcess(@RequestParam("ids") List<String> ids, @RequestParam("status") String status) throws Exception {
        Vector<Resume> resumeList = new Vector<>();
        String result = workOrderService.batchUpdateWorkOrderByProcess(ids, status, resumeList);
        feignService.saveResume(resumeList);
        if ("1".equals(result)) {
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(99998, result, false);
        }
    }

    /**
     * @param ids
     * @return
     */
    @GetMapping("/batchWorkOrderReverseCompletion")
    @Transactional(rollbackOn = Exception.class)
    @ApiOperation(value = "根据派工单id集合批量返完工派工单", httpMethod = "GET")
    public Result batchWorkOrderReverseCompletion(@ApiParam("派工单id集合") @RequestParam("ids") List<String> ids) throws Exception {
        Vector<Resume> resumeList = new Vector<>();
        workOrderService.batchWorkOrderReverseCompletion(ids, resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 条件查询生产批次号
     *
     * @param map
     * @return
     * @throws Exception
     */
    @GetMapping("/queryPlanBatchNumber")
    @ApiIgnore
    public Result queryPlanBatchNumber(@RequestParam Map<String, Object> map) throws Exception {
        List<Map<String, Object>> list = workOrderService.queryPlanBatchNumber(map);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     * 查询工艺文件
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/findProcessFileByWorkOrderId")
    @ApiOperation(value = "根据派工单id查询工艺文件", httpMethod = "GET")
    public Result findProcessFileByWorkOrderId(@RequestParam("workOrderId") String workOrderId) throws Exception {
        List<Map<String, Object>> processFileByWorkOrderId = workOrderService.findProcessFileByWorkOrderId(workOrderId);
        return new Result(ResultCode.SUCCESS, processFileByWorkOrderId);
    }

    /**
     * 查询标准工艺参数
     *
     * @param productionNo processCode
     * @return
     */
    @GetMapping("/findProcessParameterByWorkOrderId")
    @ApiIgnore
    public Result findProcessParameterByWorkOrderId(@RequestParam("productionNo") String productionNo, @RequestParam("processCode") String processCode) throws Exception {
        List<Map<String, Object>> ProcessParameterMap = workOrderService.findProcessParameterByWorkOrderId(productionNo, processCode);
        return new Result(ResultCode.SUCCESS, ProcessParameterMap);
    }

    /**
     * 查询标准工艺参数
     *
     * @param productionNo processCode
     * @return
     */

    @GetMapping("/findAllByWorkOrderId")
    @ApiOperation(value = "根据排产单号查询标准工艺参数", httpMethod = "GET")
    public Result findAllByWorkOrderId(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("工序编码") @RequestParam("processCode") String processCode) throws Exception {
        if (productionNo.contains(",")) {
            productionNo = productionNo.split(",")[0];
        }
        List<Map<String, Object>> ProcessParameterMap = workOrderService.findAllByWorkOrderId(productionNo, processCode);
        return new Result(ResultCode.SUCCESS, ProcessParameterMap);
    }

    /**
     * 查询实际工艺参数采集时间
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/findActualProcessParameterByTime")
    @ApiOperation(value = "根据派工单id查询实际工艺参数采集时间", httpMethod = "GET")
    public Result findActualProcessParameterByTime(@ApiParam("派工单id") @RequestParam("workOrderId") String workOrderId) throws Exception {
        List<PpcWorkOrderProcessParameter> actualProcessParameterByWorkOrderList = workOrderProcessParameterService.findActualProcessParameterByTime(workOrderId);
        return new Result(ResultCode.SUCCESS, actualProcessParameterByWorkOrderList);
    }

    /**
     * 根据工单id与创建时间查询实际工艺参数
     *
     * @param workOrderId
     * @param createOn
     * @return
     */
    @GetMapping("/findActualProcessParameterByWorkOrderIdAndCollectTime")
    @ApiOperation(value = "根据工单id与创建时间查询实际工艺参数", httpMethod = "GET")
    public Result findActualProcessParameterByWorkOrderIdAndCollectTime(@ApiParam("派工单id") @RequestParam("workOrderId") String workOrderId, @ApiParam("创建时间") @RequestParam("createOn") String createOn) throws Exception {
        List<PpcWorkOrderProcessParameter> actualProcessParameterByWorkOrderList = workOrderProcessParameterService.findActualProcessParameterByWorkOrderIdAndCollectTime(workOrderId, createOn);
        return new Result(ResultCode.SUCCESS, actualProcessParameterByWorkOrderList);
    }

    /**
     * 根据排产单号与工序查询实际工艺参数
     *
     * @param productionNo
     * @param processCode
     * @return
     */
    @GetMapping("/findActualProcessParameterByProductionNoIdAndProcess")
    @ApiOperation(value = "根据排产单号与工序查询实际工艺参数", httpMethod = "GET")
    public Result findActualProcessParameterByProductionNoIdAndProcess(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo, @ApiParam("工序编码") @RequestParam("processCode") String processCode) throws Exception {
        List<PpcWorkOrderProcessParameter> actualProcessParameterByWorkOrderList = workOrderProcessParameterService.findActualProcessParameterByProductionNoIdAndProcess(productionNo, processCode);
        return new Result(ResultCode.SUCCESS, actualProcessParameterByWorkOrderList);
    }

    /**
     * 保存实际工艺参数
     *
     * @param ppcWorkOrderProcessParameterList
     * @return
     */
    @PostMapping("/saveActualProcessParameter")
    @ApiOperation(value = "保存实际工艺参数", httpMethod = "GET")
    public Result saveActualProcessParameter(@RequestBody List<PpcWorkOrderProcessParameter> ppcWorkOrderProcessParameterList) throws Exception {
        workOrderProcessParameterService.saveActualProcessParameter(ppcWorkOrderProcessParameterList);
        return new Result(ResultCode.SUCCESS, null);
    }

    /**
     * 删除实际工艺信息
     *
     * @param workOrderId
     * @return
     */
    @GetMapping("/delete")
    @ApiOperation(value = "根据派工单id删除实际工艺信息", httpMethod = "GET")
    public Result deleteActualProcessParameterById(@ApiParam("派工单id") @RequestParam("workOrderId") String workOrderId, @ApiParam("创建时间") @RequestParam("createOn") String createOn) throws Exception {
        Integer result = workOrderProcessParameterService.deleteActualProcessParameterById(workOrderId, createOn);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 移动端生产记录-扫码查看 根据排产单号获取排产
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/getPlanSchedulDetailByH5Scan")
    @ApiOperation(value = "移动端生产记录-扫码查看 根据排产单号获取排产", httpMethod = "GET")
    public Result getPlanSchedulDetailByH5Scan(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        //根据排产单查找生产记录信息
        List<PpcPlanSchedulDetail> Detail = ppcProducePlanSchedulService.getPlanSchedulDetailByProductionNo(productionNo);
        if (Detail.size() != 0) {
            if (Detail.size() != 1) {
                Detail.get(0).setMainId(null);
                Detail.get(0).setMainNo(null);
            }
            map.put("main", Detail.get(0));
        }
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 生产记录追溯获取生产进度
     *
     * @param mainId
     * @param planId
     * @return
     * @throws Exception
     */
    @GetMapping("/getProduceProgress")
    @ApiOperation(value = "生产记录追溯获取生产进度", httpMethod = "GET")
    public Result getProduceProgress(@ApiParam("订单明细id") @RequestParam(required = false, value = "mainId") String mainId, @ApiParam("计划id") @RequestParam("planId") String planId) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        map = ppcProducePlanSchedulService.getProduceProgress(mainId, planId);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 移动端生产记录-扫码查看 根据排产单号获取BOM信息
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/getBomItemDetailByH5Scan")
    @ApiOperation(value = "移动端生产记录-扫码查看 根据排产单号获取BOM信息", httpMethod = "GET")
    public Result getBomItemDetailByH5Scan(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        PpcProducePlanSchedul ppcProducePlanSchedul = ppcProducePlanSchedulService.getByProductionNo(productionNo);
        String planId = ppcProducePlanSchedul.getPlanId();
        if (StringUtils.isNullOrBlank(planId)) {
            PpcProducePlanSchedulRePlan re = new PpcProducePlanSchedulRePlan();
            re.setProductionNo(ppcProducePlanSchedul.getProductionNo());
            List<PpcProducePlanSchedulRePlan> ppcProducePlanSchedulRePlans = ppcProducePlanSchedulRePlanService.queryByCond(re);
            planId = ppcProducePlanSchedulRePlans.get(0).getPlanId();
        }
        List<Map<String, Object>> result = bomProdService.getBomProdTreeQuick(planId);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 移动端生产记录-扫码查看 根据排产单号获取工艺文件信息
     *
     * @param productionNo
     * @return
     */
    @GetMapping("/getProcessFileByH5Scan")
    @ApiOperation(value = "移动端生产记录-扫码查看 根据排产单号获取工艺文件信息", httpMethod = "GET")
    public Result getProcessFileByH5Scan(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        PpcProducePlanSchedul ppcProducePlanSchedul = ppcProducePlanSchedulService.getByProductionNo(productionNo);
        if (StringUtils.isNullOrBlank(ppcProducePlanSchedul.getRouteCode())) {
            AssertUtil.throwException("无对应工艺路线信息！");
        }
        PpcRouteLine routeLine = feignService.findRouteLine(ppcProducePlanSchedul.getRouteCode());
        if (StringUtils.isNullOrBlank(routeLine)) {
            AssertUtil.throwException("无对应工艺路线信息！");
        }
        List<PpcWorkOrder> ppcWorkOrders = workOrderService.queryByProductionNo(productionNo);
        List<Map<String, Object>> list = new ArrayList<>();
        for (PpcWorkOrder ppcWorkOrder : ppcWorkOrders) {
            List<Map<String, Object>> processFiles = feignService.findProcessFile(routeLine.getRouteCode(), ppcWorkOrder.getProcessCode());
            list.addAll(processFiles);
        }
        return new Result(ResultCode.SUCCESS, list);
    }


    @GetMapping("/getWorkOrderBySearchKey")
    @ApiOperation(value = "多字段模糊搜索派工单", httpMethod = "GET")
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public Result getWorkOrderBySearchKey(@ApiParam("页数") @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                          @ApiParam("条数") @RequestParam(defaultValue = "10", value = "pageSize") int pageSize, @ApiParam("模糊搜索字段（支持派工单号，物料编码，物料名称）") @RequestParam(required = false, value = "searchKey") String searchKey, @ApiParam("排产单号") @RequestParam(required = false, value = "productionNo") String productionNo, @ApiParam("工序编码") @RequestParam(required = false, value = "processCode") String processCode) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcWorkOrder> list = workOrderService.getWorkOrderBySearchKey(searchKey, productionNo, processCode, "20");
        Map reMap = new HashMap();
        reMap.put("list", list);
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }


    @GetMapping("/getQmsWorkOrderList")
    @ApiOperation(value = "查询派工单列表--qms", response = Result.class)
    public Result getQmsWorkOrderList(QmsWorkOrderQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<WorkOderVo> list = workOrderService.findQmsWorkOrderList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @GetMapping("/getWMSWorkOrderList")
    @ApiOperation(value = "查询派工单列表--wms", response = Result.class)
    public Result getWMSWorkOrderList(WmsWorkOrderQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<WorkOderVo> list = workOrderService.findWmsWorkOrderList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @PostMapping("/saveFileBySimplePlan")
    @ApiOperation(value = "简单模式生产计划保存附件", response = Result.class)
    public Result saveFileBySimplePlan(@ApiParam("文件") @RequestParam("file") MultipartFile file,
                                       @ApiParam("排产单号") @RequestParam(value = "productionNo") String productionNo,
                                       @ApiParam("派工单id") @RequestParam(required = false, value = "workOrderId") String workOrderId,
                                       @ApiParam("工序编码") @RequestParam(value = "processCode") String processCode, @ApiParam("派工数量") @RequestParam(value = "produceQty") BigDecimal produceQty, @ApiParam("计件价格") @RequestParam(required = false, value = "pieceworkHour") BigDecimal pieceworkHour) throws Exception {
        String fileJson = workOrderService.saveFileBySimplePlan(file, productionNo, workOrderId, processCode, produceQty, pieceworkHour);
        return new Result(ResultCode.SUCCESS, fileJson);
    }

    @GetMapping("/deleteFileBySimplePlan")
    @ApiOperation(value = "简单模式生产计划删除附件", response = Result.class)
    public Result deleteFileBySimplePlan(@ApiParam("文件id") @RequestParam(value = "fileId") String fileId, @ApiParam("派工单id") @RequestParam(value = "workOrderId") String workOrderId) throws Exception {
        workOrderService.deleteFileBySimplePlan(fileId, workOrderId);
        return Result.SUCCESS(ResultCode.SUCCESS);
    }

    @PostMapping("/lackMaterialPushPurRequest")
    @ApiOperation(value = "缺料下推采购申请", response = Result.class)
    public Result lackMaterialPushPurRequest(@RequestBody List<PoPurchaseRequestDetail> purchaseRequestDetails) throws Exception {
        PoPurchaseRequest purchaseRequest = workOrderService.lackMaterialPushPurRequest(purchaseRequestDetails);
        return new Result(ResultCode.SUCCESS, purchaseRequest);
    }


    @GetMapping("/getProcessRateByProductionNo")
    @ApiOperation(value = "查询排产单下工序", response = Result.class)
    public Result getProcessRateByProductionNo(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo) throws Exception {
        ArrayList<String> productionNoList = new ArrayList<>();
        productionNoList.add(productionNo);
        List<Map> list = workOrderService.getProcessRateByProductionNo(productionNoList);
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/getResourcesByWorkOrderId")
    @ApiOperation(value = "查询派工单对应资源", response = Result.class)
    public Result getResourcesByWorkOrderId(@ApiParam("派工单id") @RequestParam("workOrderId") String workOrderId) throws Exception {
        List<PpcRouteProcessResources> list = workOrderService.getResourcesByWorkOrderId(workOrderId);
        return new Result(ResultCode.SUCCESS, list);
    }

    @GetMapping("/findWorkOrderDetail")
    @ApiOperation(value = "查询班组作业-详细信息", response = Result.class)
    public Result findWorkOrderDetail(@ApiParam("派工单id") @RequestParam("workOrderId") String workOrderId) throws Exception {
        WorkOderVo result = workOrderService.findWorkOrderDetail(workOrderId);
        return new Result(ResultCode.SUCCESS, result);
    }
}
