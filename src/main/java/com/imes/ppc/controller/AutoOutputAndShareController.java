package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.ppc.vo.AutoOutputAndShareVo;
import com.imes.ppc.service.AutoOutputAndShareService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("api/ppc/autoOutputAndShare")
@Api(tags = "自动投料相关接口")
@ApiIgnore
public class AutoOutputAndShareController {
    @Autowired
    private AutoOutputAndShareService autoOutputAndShareService;

    @GetMapping("/getAutoOutputAndShare")
    @ApiOperation(value = "获取自动投料记录", httpMethod = "GET")
    public Result getAutoOutputAndShare(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize, AutoOutputAndShareVo vo) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<AutoOutputAndShareVo> result = autoOutputAndShareService.getAutoOutputAndShare(vo);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/getAutoOutputAndShareDetail")
    @ApiOperation(value = "获取自动投料记录分摊明细", httpMethod = "GET")
    public Result getAutoOutputAndShareDetail(@RequestParam("mainId") String id) {
        List<AutoOutputAndShareVo> result = autoOutputAndShareService.getAutoOutputAndShareDetail(id);
        return new Result(ResultCode.SUCCESS, result);
    }
}
