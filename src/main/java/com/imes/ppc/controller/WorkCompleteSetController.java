package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.*;
import com.imes.domain.entities.ppc.vo.PpcProduceLockVo;
import com.imes.domain.entities.ppc.vo.ProduceInputVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping("api/ppc/completeSet")
@Api(tags = "生产锁库控制类")
@ApiIgnore
public class WorkCompleteSetController {

    @Autowired
    PpcProduceLockService lockService;



    /**
     * 领料生效保存
     *
     * @param PpcProduceLockVoList
     * @param workSchedulNo
     * @return
     */
    @PostMapping("saveOrUpdateCompleteSet")
    @Transactional(rollbackFor = Exception.class)
    public Result saveOrUpdateCompleteSet(@RequestBody List<PpcProduceLockVo> PpcProduceLockVoList, @RequestParam("workSchedulNo") String workSchedulNo, @RequestParam("remarks") String remarks) throws Exception {
        lockService.saveOrUpdateCompleteSet(PpcProduceLockVoList,workSchedulNo,remarks);
        return new Result(ResultCode.SUCCESS);
    }

}
