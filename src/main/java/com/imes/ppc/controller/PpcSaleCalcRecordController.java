package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import org.springframework.beans.factory.annotation.Autowired;
import com.imes.domain.entities.ppc.po.PpcSaleCalcRecord;
import com.imes.ppc.service.PpcSaleCalcRecordService;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 销售需求计算记录表(PpcSaleCalcRecord)表控制层
 *
 * <AUTHOR> @since 2023-03-28 14:05:32
 */
@Slf4j
@RestController
@Api(tags = "销售需求计算记录表控制层")
@RequestMapping("/api/ppc/ppcSaleCalcRecord")
public class PpcSaleCalcRecordController {

    @Autowired
    private PpcSaleCalcRecordService ppcSaleCalcRecordService;

    @GetMapping("/queryByCond")
    @ApiOperation(value = "分页查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              PpcSaleCalcRecord ppcSaleCalcRecord) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcSaleCalcRecord> result = ppcSaleCalcRecordService.queryByCond(ppcSaleCalcRecord);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById")
    @ApiOperation(value = "根据id查询")
    public Result queryById(String id) {
        PpcSaleCalcRecord result = ppcSaleCalcRecordService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody PpcSaleCalcRecord record) throws Exception {
        Integer result = ppcSaleCalcRecordService.insert(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcSaleCalcRecord record) throws Exception {
        Integer result = ppcSaleCalcRecordService.update(record);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/deleteById")
    @ApiOperation(value = "根据id删除")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(String id) throws Exception {
        Integer result = ppcSaleCalcRecordService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        Integer result = ppcSaleCalcRecordService.batchDelete(idList);
        if (result > 0) {
            return new Result(ResultCode.SUCCESS, result);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }
}
