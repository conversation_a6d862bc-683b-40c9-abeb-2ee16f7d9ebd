package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.Constants;
import com.imes.common.utils.ExcelUtils;
import com.imes.domain.entities.ppc.vo.AutoOutputAndShareVo;
import com.imes.ppc.service.AutoOutputAndShareService;
import com.imes.ppc.service.PpcFakeDataImportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
@RestController
@RequestMapping("api/ppc/fakeDataImport")
@Api(tags = "自动投料相关接口")
@ApiIgnore
public class PpcFakeDataImportController {
    @Autowired
    PpcFakeDataImportService fakeDataImportService;

    @ApiOperation("报工数据模板下载")
    @GetMapping("/templateWorkFinish")
    public void templateWorkFinish(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, "template/fakeData/报工假数据导入模板.xlsx");
    }

    @ApiOperation("报工数据导入")
    @PostMapping("/templateWorkFinishImport")
    public Result templateWorkFinishImport(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 7, 3);
        fakeDataImportService.checkImportWorkFinish(importList);
        ConcurrentLinkedQueue<String> errorList = fakeDataImportService.importDataWorkFinish(importList);
        StringBuilder errorMessage = new StringBuilder();
        int i = 0;
        for (String error : errorList) {
            errorMessage.append(error).append("\n");
            if (i++ > 10) {
                break;
            }
        }
        return new Result(ResultCode.SUCCESS.code(), errorMessage.toString(), true);
    }
    @ApiOperation("报工数据导入2")
    @PostMapping("/templateWorkFinishImport2")
    public Result templateWorkFinishImport2(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 7, 3);
        fakeDataImportService.checkImportWorkFinish(importList);
        ConcurrentLinkedQueue<String> errorList = fakeDataImportService.importDataWorkFinish2(importList);
        StringBuilder errorMessage = new StringBuilder();
        int i = 0;
        for (String error : errorList) {
            errorMessage.append(error).append("\n");
            if (i++ > 10) {
                break;
            }
        }
        return new Result(ResultCode.SUCCESS.code(), errorMessage.toString(), true);
    }

    @ApiOperation("生产入库数据模板下载")
    @GetMapping("/templateProduceInStorage")
    public void templateProduceInStorage(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, "template/fakeData/报工假数据导入模板.xlsx");
    }

    @ApiOperation("生产入库数据导入")
    @PostMapping("/templateImportProduceInStorage")
    @Transactional(rollbackFor = Exception.class)
    public Result templateImportProduceInStorage(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 7, 3);
        fakeDataImportService.checkImportProduceInStorage(importList);
        int dataNum = fakeDataImportService.importDataProduceInStorage(importList);
        return new Result(ResultCode.SUCCESS, "成功导入");
    }
}
