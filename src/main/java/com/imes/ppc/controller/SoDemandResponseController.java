package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.SoDemandResponse;
import com.imes.domain.entities.ppc.vo.SoDemandResponseVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.SoDemandResponseService;
import org.springframework.web.bind.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import springfox.documentation.annotations.ApiIgnore;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 采购预测回复(SoDemandResponse)表控制层
 *
 * <AUTHOR> @since 2022-03-28 11:08:38
 */
@Slf4j
@RestController
@Api(tags = "采购预测回复")
@RequestMapping("/api/ppc/soDemandResponse")
@ApiIgnore
public class SoDemandResponseController {

    @Autowired
    private SoDemandResponseService soDemandResponseService;
    @Autowired
    FeignService feignService;

    @GetMapping("/queryByCond")
    @ApiOperation(value = "分页查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              SoDemandResponseVo soDemandResponse) throws Exception {
        //判断订单状态是否为空
        if (!StringUtils.isNullOrBlank(soDemandResponse.getOrderStatus())) {
            List<String> oderStatuslist = Arrays.asList(soDemandResponse.getOrderStatus().split(","));
            soDemandResponse.setOrderStatusList(oderStatuslist);
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<SoDemandResponseVo> result = soDemandResponseService.queryByCond(soDemandResponse);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    @GetMapping("/queryById/{id}")
    @ApiOperation(value = "根据id查详情")
    public Result queryById(@PathVariable("id") String id) {
        SoDemandResponse result = soDemandResponseService.queryById(id);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "保存")
    @Transactional(rollbackFor = Exception.class)
    public Result save(@RequestBody SoDemandResponse soDemandResponse) throws Exception {
        Integer result = soDemandResponseService.insert(soDemandResponse);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/update")
    @ApiOperation(value = "更新")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody SoDemandResponse soDemandResponse) throws Exception {
        Integer result = soDemandResponseService.update(soDemandResponse);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/deleteById/{id}")
    @ApiOperation(value = "根据Id删除")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteById(@PathVariable("id") String id) throws Exception {
        Integer result = soDemandResponseService.deleteById(id);
        return new Result(ResultCode.SUCCESS, result);
    }


    @PostMapping("/feignSave")
    @ApiOperation(value = "用于采购需求预测单发布的时候调用(主单)")
    @Transactional(rollbackFor = Exception.class)
    public Result feignSave(@RequestBody List<SoDemandResponse> soDemandResponse) throws Exception {
        Integer result = soDemandResponseService.feignSave(soDemandResponse);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/oderReceived")
    @ApiOperation(value = "收到")
    @Transactional(rollbackFor = Exception.class)
    public Result oderReceived(String ids) throws Exception {
        if (!StringUtils.isNullOrBlank(ids)) {
            List<String> idList = Arrays.asList(ids.split(","));
            Integer result = soDemandResponseService.oderReceived(idList);
            if (result > 0) {
                return new Result(ResultCode.SUCCESS, result);
            } else {
                return new Result(ResultCode.FAIL);
            }
        }
        return new Result(ResultCode.FAIL);
    }

    @GetMapping("/getOrderStatusList")
    @ApiOperation(value = "订单回复状态", httpMethod = "GET")
    public Result getOrderStatusList() throws Exception {
        Map map = new HashMap();
        map.put("ORDER_REPLY_STATUS", feignService.getDictCode("ORDER_REPLY_STATUS"));
        return new Result(ResultCode.SUCCESS, map);
    }

    @PostMapping("/oderReply")
    @ApiOperation(value = "需求回复")
    @Transactional(rollbackFor = Exception.class)
    public Result oderReply(String ids) throws Exception {
        if (!StringUtils.isNullOrBlank(ids)) {
            List<String> idList = Arrays.asList(ids.split(","));
            Integer result = soDemandResponseService.oderReply(idList);
            return new Result(ResultCode.SUCCESS, result);
        }
        return new Result(ResultCode.FAIL);
    }

    /**
     * @Description: MOM不同租户调用采购预测数据写入订单回复表中
     * @Param: Map 包含 data  get("data")
     * @return: Result
     * @Author: cshu
     * @Date: 2022/4/2
     */
    @PostMapping("/soOutIntefaceSave")
    @ApiOperation(value = "Mom外部调用接口保存数据")
    @Transactional(rollbackFor = Exception.class)
    public Result soOutIntefaceSave(@RequestBody Map<String, Object> data) throws Exception {
        Integer result = soDemandResponseService.outIntefaceSave(data);
        return new Result(ResultCode.SUCCESS, result);
    }

}
