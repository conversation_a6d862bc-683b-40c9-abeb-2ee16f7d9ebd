package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.po.PpcPieceTeamMonth;
import com.imes.domain.entities.query.template.ppc.PpcPieceTeamMonthSearchVo;
import com.imes.ppc.service.PpcPieceTeamMonthGroupService;
import com.imes.ppc.service.PpcPieceTeamMonthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 班组计件总表(PpcPieceTeamMonth)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-08 14:15:19
 */
@RestController
@RequestMapping("/api/ppc/ppcPieceTeamMonth")
@Api(tags = "班组计件总表")
@Slf4j
public class PpcPieceTeamMonthController {
    /**
     * 服务对象
     */
    @Autowired
    private PpcPieceTeamMonthService ppcPieceTeamMonthService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (ppcPieceTeamMonthService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param ppcPieceTeamMonth 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody PpcPieceTeamMonth ppcPieceTeamMonth) throws Exception {
        String result = ppcPieceTeamMonthService.insert(ppcPieceTeamMonth);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param ppcPieceTeamMonth 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcPieceTeamMonth ppcPieceTeamMonth) throws Exception {
        ppcPieceTeamMonthService.update(ppcPieceTeamMonth);
        return new Result(ResultCode.SUCCESS, 0);
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {

        AssertUtil.isTrue(StringUtils.isNotNullOrBlank(ids), "请选择要删除的记录！");

        List<String> idList = Arrays.asList(ids.split(","));
        PpcPieceTeamMonth teamMonth = ppcPieceTeamMonthService.getById(idList.get(0));
        if ("1".equals(teamMonth.getPieceType())) {
            ppcPieceTeamMonthService.batchDelete(idList);
        } else {
            ppcPieceTeamMonthService.batchDeleteForPack(idList);
        }
        return new Result(ResultCode.SUCCESS, 1);

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result<StatPageResult<PpcPieceTeamMonthSearchVo>> queryList(PpcPieceTeamMonthSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceTeamMonthSearchVo> list = ppcPieceTeamMonthService.queryList(vo);
        return Result.SUCCESS(new StatPageResult<>(page.getTotal(), list, map));
    }

    @GetMapping("/queryPersonSummary")
    @ApiOperation(value = "个人计件汇总")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<Map<String,Object>>> queryPersonSummary(String id) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<Map<String,Object>> list = ppcPieceTeamMonthService.queryPersonSummary(id);
        return new Result<>(ResultCode.SUCCESS,list);
    }

    @GetMapping("/queryPersonSummaryTeam")
    @ApiOperation(value = "个人计件汇总-集体计件")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<List<Map<String, Object>>>> queryPersonSummaryTeam(String id) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<List<Map<String, Object>>> list = ppcPieceTeamMonthService.queryPersonSummaryTeam(id);
        return new Result<>(ResultCode.SUCCESS, list);
    }
    @GetMapping("/effect")
    @ApiOperation(value = "完成")
    @Transactional(rollbackFor = Exception.class)
    public Result effect(String ids) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<String> idList = Arrays.asList(ids.split(","));
        PpcPieceTeamMonth teamMonth = ppcPieceTeamMonthService.getById(idList.get(0));
        if ("1".equals(teamMonth.getPieceType())) {
            for (String id : idList) {
                ppcPieceTeamMonthService.effect(id);
            }
        } else {
            for (String id : idList) {
                ppcPieceTeamMonthService.effectForPack(id);
            }
        }
        //如有需要计算合计对map进行赋值
        return new Result<>(ResultCode.SUCCESS);
    }
    @GetMapping("/unEffect")
    @ApiOperation(value = "反完成")
    @Transactional(rollbackFor = Exception.class)
    public Result unEffect(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        for (String id : idList) {
            ppcPieceTeamMonthService.unEffect(id);
        }
        return new Result<>(ResultCode.SUCCESS);
    }

    @Autowired
    PpcPieceTeamMonthGroupService groupService;
    @Value("${imes.ppc.template.exportPersonalSummaryInfo}")
    String exportPersonalSummaryInfo;
    @GetMapping("/exportPersonalSummaryInfo")
    @ApiOperation(value = "导出个人汇总")
    public Result exportPersonalSummaryInfo(@RequestParam() String id,
                                     HttpServletRequest request,
                                     HttpServletResponse response) throws Exception {
        AssertUtil.isTrue(StringUtils.isNotNullOrBlank(id),"班组计件id不可为空");

        List<Map<String, Object>> mapList = ppcPieceTeamMonthService.queryPersonSummary(id);
        InputStream in = null;
        ClassPathResource resource = new ClassPathResource(exportPersonalSummaryInfo);
        String fileName = resource.getFilename();
        response.setContentType("application/octet-stream");
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
        }
        in = resource.getInputStream();
        // 处理标题
        PpcPieceTeamMonth teamMonth = ppcPieceTeamMonthService.getById(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("MM/dd");

        List<String> groupIdList = groupService.getIdByMainId(id);
        AssertUtil.isTrue(!groupIdList.isEmpty(), "未发现个人计件分组信息");
        Date startDate = sdf.parse(teamMonth.getMonthDate() + "-01");
        Date lastDate = DateUtils.getLastDayInMonth(startDate);
        List<Date> tempDateList = DateUtils.getAscDateList(startDate, lastDate);
        List<String> ascDateList = tempDateList.stream().map(sdf2::format).collect(Collectors.toList());
        List<String> headList = new ArrayList<>();
        headList.add("姓名");
        headList.add("日期");
        headList.addAll(ascDateList);
        headList.add("汇总");

        // 处理列的key
        List<String> columnKey = new ArrayList<>();
        columnKey.add("userCode");
        columnKey.add("item");
        columnKey.addAll(ascDateList);
        columnKey.add("sum");
        String[] keys = columnKey.toArray(new String[0]);

        ppcPieceTeamMonthService.exportMinimalistInventory(response, request, mapList, keys, fileName, in, 1, headList);

        return null;
    }

    @Value("${imes.ppc.template.exportPersonalSummaryInfoTeam}")
    String exportPersonalSummaryInfoTeam;

    @GetMapping("/exportPersonalSummaryInfoTeam")
    @ApiOperation(value = "导出个人汇总")
    public Result exportPersonalSummaryInfoTeam(@RequestParam() String id,
                                                HttpServletRequest request,
                                                HttpServletResponse response) throws Exception {
        AssertUtil.isTrue(StringUtils.isNotNullOrBlank(id), "班组计件id不可为空");
        List<List<Map<String, Object>>> lists = ppcPieceTeamMonthService.queryPersonSummaryTeam(id);
        List<Map<String, Object>> mapList = lists.stream().flatMap(List::stream).collect(Collectors.toList());
        InputStream in = null;
        ClassPathResource resource = new ClassPathResource(exportPersonalSummaryInfoTeam);
        String fileName = resource.getFilename();
        response.setContentType("application/octet-stream");
        if (ExcelUtils.isLowVersionBrowser(request)) {
            fileName = URLEncoder.encode(fileName, "UTF8");
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + fileName);
        } else {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
        }
        in = resource.getInputStream();
        // 处理标题
        String[] keys = {"title", "subTeamName", "filterDate", "totalMoney",
                "totalHourPurge", "moneyPerHour", "userName", "totalHour", "ratio", "userTotalHourPurge", "userTotalMoney"};

        ppcPieceTeamMonthService.exportPersonalSummaryInfoTeam(response, request, mapList, keys, fileName, in, 1, null);

        return null;
    }


    @GetMapping("/queryMonthAndTeam")
    @ApiOperation(value = "班组月度计件")
    @Transactional(rollbackFor = Exception.class)
    public Result<List<PpcPieceTeamMonthSearchVo>> queryMonthAndTeam(@RequestParam() String monthDate,
                                                                     @RequestParam() String teamCode,
                                                                     @RequestParam() String userCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        List<PpcPieceTeamMonthSearchVo> list = ppcPieceTeamMonthService.queryMonthAndTeam(monthDate, teamCode, userCode);
        return Result.SUCCESS();
    }



}

