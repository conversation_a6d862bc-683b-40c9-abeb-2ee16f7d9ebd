package com.imes.ppc.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.common.support.Query;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.vo.PpcMyWorkVo;
import com.imes.domain.entities.ppc.vo.WorkOderVo;
import com.imes.domain.entities.query.template.ppc.PpcScheduleQueryReportVo;
import com.imes.domain.entities.query.template.ppc.ProductionStatusQueryVo;
import com.imes.domain.entities.query.template.ppc.ScheduleJobForemanQueryVo;
import com.imes.domain.entities.query.template.ppc.WorkOrderJobForemanQueryVo;
import com.imes.ppc.dao.WorkOrderDao;
import com.imes.ppc.service.TaskManagementService;
import com.imes.ppc.service.TeamWorkFinishService;
import com.imes.ppc.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/ppc/taskManege")
@Api(tags = "班组长任务管理相关接口")
public class TaskManagementController {

    @Autowired
    TaskManagementService taskManagementService;

    @Autowired
    TeamWorkFinishService teamWorkFinishService;

    @Autowired
    WorkOrderDao workOrderDao;

    /**
     * 根据登录用户 查询权限内的工单
     *
     * @return
     */
    @GetMapping("/queryWorkOrder")
    @ApiOperation(value = "根据登录用户 查询权限内的工单", httpMethod = "GET")
    @ApiIgnore
    public Result queryWorkOrder(@ApiParam("页数")  @RequestParam("pageNum") Integer pageNum,
                                 @ApiParam("条数")  @RequestParam("pageSize") Integer pageSize, @ApiParam("条数") @RequestParam Map map) throws Exception {
        Map result = taskManagementService.queryWorkOrder(pageNum, pageSize, map);
        return new Result(ResultCode.SUCCESS, result);
    }

    @GetMapping("/searchWorkOrder")
    @ApiOperation(value = "班组长任务管理（产线级）列表数据查询", httpMethod = "GET")
    public Result searchWorkOrder(WorkOrderJobForemanQueryVo vo) throws Exception {
        List<String> uniqueCodes = new ArrayList<>();
        if (!StringUtils.isNullOrBlank(vo.getProcessFlag())) {
            List<String> soNos = Arrays.asList(vo.getProcessFlag().split(Query.SPLIT_CHAR));
            if (CollectionUtils.isNotEmpty(soNos)) {
                uniqueCodes = workOrderDao.selectForsoNos(soNos);
            }
        }
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize(), false);
        List<WorkOderVo> list = taskManagementService.searchWorkOrder(vo,uniqueCodes);
        return Result.SUCCESS(new StatPageResult(null, list, null));
    }

    @GetMapping("/searchWorkOrderCount")
    @ApiOperation(value = "班组长任务管理（产线级）列表页面总页数查询", httpMethod = "GET")
    public Result searchWorkOrderCount(WorkOrderJobForemanQueryVo vo) throws Exception {
        List<String> uniqueCodes = new ArrayList<>();
        if (!StringUtils.isNullOrBlank(vo.getProcessFlag())) {
            List<String> soNos = Arrays.asList(vo.getProcessFlag().split(Query.SPLIT_CHAR));
            if (CollectionUtils.isNotEmpty(soNos)) {
                uniqueCodes = workOrderDao.selectForsoNos(soNos);
            }
        }
        vo.setOrderBy(null);
        Long count = taskManagementService.searchWorkOrderCount(vo,uniqueCodes);
        return new Result(ResultCode.SUCCESS, count);
    }

    @GetMapping("/getPpcScheduleQueryReportList")
    @ApiOperation(value = "排产单查询报表 列表数据查询", httpMethod = "GET")
    public Result getPpcScheduleQueryReportList(PpcScheduleQueryReportVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize(), false);
        List<WorkOderVo> list = taskManagementService.getPpcScheduleQueryReportList(vo);
        return Result.SUCCESS(new StatPageResult(null, list, null));
    }

    @GetMapping("/getPpcScheduleQueryReportListCount")
    @ApiOperation(value = "排产单查询报表）列表页面总页数", httpMethod = "GET")
    public Result getPpcScheduleQueryReportListCount(PpcScheduleQueryReportVo vo) throws Exception {
        long count = 0;
        try {
            vo.setOrderBy(null);
            count = taskManagementService.getPpcScheduleQueryReportListCount(vo);
        } catch (Exception e) {

        }
        return new Result(ResultCode.SUCCESS, count);
    }


    @GetMapping("/searchWorkOrderByICS")
    @ApiOperation(value = "班组长任务管理（产线级） 工控机", httpMethod = "GET")
    public Result searchWorkOrderByICS(WorkOrderJobForemanQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<WorkOderVo> list = taskManagementService.searchWorkOrder(vo,null);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, null));
    }

    /**
     * 排产单管理查询
     *
     * @return
     */
    @GetMapping("/searchSchedule")
    @ApiOperation(value = "排产单管理查询", httpMethod = "GET")
    public Result searchSchedule(ScheduleJobForemanQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        vo.setOrderType("10");//只查询自制排产单
        List<WorkOderVo> list = taskManagementService.searchSchedule(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }


    /**
     * 移动端查询派工单生产记录
     *
     * @return
     */
    @GetMapping("/queryWorkOrderByH5")
    @ApiOperation(value = "移动端查询派工单生产记录",httpMethod = "GET")
    public Result queryWorkOrderByH5(@ApiParam("页数") @RequestParam("pageNum") Integer pageNum,
                                     @ApiParam("条数") @RequestParam("pageSize") Integer pageSize, @ApiParam("排产单号") @RequestParam(required = false, value = "productionNo") String productionNo,  @ApiParam("车间编码")@RequestParam(required = false, value = "workshopCode") String workshopCode, @ApiParam("模糊查询字段（支持排产单号，物料名称，物料编码）")  @RequestParam(required = false, value = "searchKey") String searchKey) throws Exception {
        Map result = taskManagementService.queryWorkOrderByH5(pageNum, pageSize, productionNo, searchKey, workshopCode);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 移动端在制品跟踪基本信息
     *
     * @return
     */
    @GetMapping("/queryWIPTrackingBasicInformationByH5")
    @ApiOperation(value = "移动端在制品跟踪基本信息", httpMethod = "GET")
    public Result queryWIPTrackingBasicInformationByH5(@ApiParam("SN码")  @RequestParam(value = "SNNo") String SNNo) throws Exception {
        Map result = taskManagementService.queryWIPTrackingBasicInformationByH5(SNNo);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 移动端在制品跟踪生产信息
     *
     * @return
     */
    @GetMapping("/queryWIPTrackingProductionInformationByH5")
    @ApiOperation(value = "移动端在制品跟踪生产信息", httpMethod = "GET")
    public Result queryWIPTrackingProductionInformationByH5(@ApiParam("SN码")  @RequestParam(value = "SNNo") String SNNo) throws Exception {
        ArrayList<Map<String, Object>> result = taskManagementService.queryWIPTrackingProductionInformationByH5(SNNo);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 移动端在制品跟踪订单信息
     *
     * @return
     */
    @GetMapping("/queryWIPTrackingSaleInformationByH5")
    @ApiOperation(value = "移动端在制品跟踪订单信息", httpMethod = "GET")
    public Result queryWIPTrackingSaleInformationByH5(@ApiParam("SN码")  @RequestParam(value = "SNNo") String SNNo) throws Exception {
        ArrayList<Map<String, Object>> result = taskManagementService.queryWIPTrackingSaleInformationByH5(SNNo);
        return new Result(ResultCode.SUCCESS, result);
    }


    /**
     * 修改工单状态
     *
     * @param map
     * @return
     */
    @PostMapping("/uptOrderStatus")
    @ApiOperation(value = "修改工单状态", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result uptStatus(@RequestParam Map map) throws Exception {
        taskManagementService.uptStatus(map);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/searchMyWorkH5")
    @ApiOperation(value = "移动端查询我的生产任务", httpMethod = "GET")
    public Result searchMyWorkH5(PpcMyWorkVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getCurrent(), vo.getSize(), true, false, (Boolean) null);
        List<WorkOderVo> list = taskManagementService.searchMyWorkH5(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 判断是否为班组长
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/isWorkTeamByLeaderUser")
    @ApiOperation(value = "判断当前登录人是否为班组长", httpMethod = "GET")
    public Result isWorkTeamByLeaderUser() throws Exception {
        Boolean result = taskManagementService.isWorkTeamByLeaderUser();
        return Result.SUCCESS(result);
    }

    /**
     * 我的生产任务首页计数
     *
     * @param
     * @return
     * @throws Exception
     */
    @GetMapping("/searchMyWorkH5Count")
    public Result searchMyWorkH5Count(@RequestParam(required = false, value = "isLeader") Boolean isLeader,@RequestParam(required = false, value = "status") String status) throws Exception {
        Map<String, Object> map = taskManagementService.searchMyWorkH5Count(isLeader,status);
        return Result.SUCCESS(map);
    }

}
