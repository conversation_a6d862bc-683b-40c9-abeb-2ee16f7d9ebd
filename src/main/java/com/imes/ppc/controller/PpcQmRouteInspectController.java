package com.imes.ppc.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcQmRouteProcessInspect;
import com.imes.domain.entities.ppc.po.PpcQmStdInspect;
import com.imes.domain.entities.ppc.po.PpcQmStdInspectDetail;
import com.imes.ppc.constants.PpcQmInspectConstants;
import com.imes.ppc.service.impl.PpcQmRouteProcessInspectDetailServiceImpl;
import com.imes.ppc.service.impl.PpcQmRouteProcessInspectServiceImpl;
import com.imes.ppc.service.impl.PpcQmStdInspectDetailServiceImpl;
import com.imes.ppc.service.impl.PpcQmStdInspectServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/ppc/ppcQmRouteInspect")
@Api(tags = "工序质检信息相关接口")
@ApiIgnore
public class PpcQmRouteInspectController {
    @Autowired
    private PpcQmRouteProcessInspectServiceImpl ppcQmRouteProcessInspectServiceImpl;
    @Autowired
    private PpcQmRouteProcessInspectDetailServiceImpl ppcQmRouteProcessInspectDetailServiceImpl;
    @Autowired
    private PpcQmStdInspectServiceImpl ppcQmStdInspectServiceImpl;
    @Autowired
    private PpcQmStdInspectDetailServiceImpl ppcQmStdInspectDatailServiceImpl;

    /**
     * 保存质检信息到工序表
     *
     * @return
     */
    @GetMapping("/saveInspect")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "保存质检信息到工序表", httpMethod = "GET")
    public Result saveInspect(@RequestParam Map<String, Object> map) throws Exception {
        //先查询数据库当前待新增的质检编码是否已存在
        List<PpcQmRouteProcessInspect> all2 = ppcQmRouteProcessInspectServiceImpl.findAll(map);
        if (all2.isEmpty()) {
            List<PpcQmStdInspect> all = ppcQmStdInspectServiceImpl.findAll(map);
            if (!all.isEmpty()) {
                ppcQmRouteProcessInspectServiceImpl.saveInspect(all.get(0).toMap());
                List<PpcQmStdInspectDetail> all1 = ppcQmStdInspectDatailServiceImpl.findAll(map);
                if (!all1.isEmpty()) {
                    ppcQmRouteProcessInspectDetailServiceImpl.saveInspectDetail(all1.get(0).toMap());
                }
            }
            return new Result(ResultCode.SUCCESS, map);
        } else {
            return new Result(ResultCode.FAIL, "质检编码已存在！");
        }
    }

    /**
     * 质检信息查询全部
     *
     * @return
     */
    @GetMapping("/queryAll")
    @ApiOperation(value = "质检信息查询全部", httpMethod = "GET")
    public Result query(@RequestParam Map<String, Object> map) throws Exception {
        List<PpcQmRouteProcessInspect> all = ppcQmRouteProcessInspectServiceImpl.findAll(map);
        map.put(PpcQmInspectConstants.LIST, all);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 根据id删除质检主信息
     *
     * @param id
     * @return
     */
    @GetMapping("/deletePpcQmRouteProcessInspectById/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "根据id删除质检主信息", httpMethod = "GET")
    public Result deletePpcQmRouteProcessInspectById(@PathVariable("id") String id) throws Exception {
        if (StringUtils.isNullOrBlank(id)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER, null);
        }
        //根据id查询质检信息
        Map<Object, Object> objectObjectHashMap = new HashMap<>();
        objectObjectHashMap.put(PpcQmInspectConstants.ID, id);
        List<PpcQmRouteProcessInspect> all = ppcQmRouteProcessInspectServiceImpl.findAll(objectObjectHashMap);
        //判断需要删除的数据是否存在
        if (!all.isEmpty()) {
            //删除质检信息
            ppcQmRouteProcessInspectServiceImpl.deleteByPrimaryKey(id);
            //删除明细信息
            ppcQmRouteProcessInspectDetailServiceImpl.deleteByInspectCode(all.get(0).getInspectCode().toString());
            return new Result(ResultCode.SUCCESS);
        } else {
            return new Result(ResultCode.ILLEGAL_PARAMETER, null);
        }
    }

    /**
     * 根据质检单号查询bom信息
     *
     * @param
     * @return
     */
    @GetMapping("/findBomByInspectCode")
    @ApiOperation(value = "根据质检单号查询bom信息", httpMethod = "GET")
    public Result findBomByInspectCode(@RequestParam Map<String, Object> map) throws Exception {
        if (!map.isEmpty() && StringUtils.isNullOrBlank(map.get(PpcQmInspectConstants.INSPECT_NO))) {
            List<Map> bomByInspectCode = ppcQmRouteProcessInspectServiceImpl.findBomByInspectCode(map);

            map.put(PpcQmInspectConstants.LIST, bomByInspectCode);
            return new Result(ResultCode.SUCCESS, map);
        } else {
            return new Result(ResultCode.ILLEGAL_PARAMETER, null);
        }
    }

    /**
     * 保存质检信息到工序表
     *
     * @return
     */
    @PostMapping("/copyInspect")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "保存质检信息到工序表", httpMethod = "GET")
    public Result copyInspect(@RequestBody Map<String, Object> map) throws Exception {
        if (!StringUtils.isNullOrBlank(map.get(PpcQmInspectConstants.INSPECT_CODE))) {
            //先查询数据库当前待新增的质检编码是否已存在
            List<PpcQmRouteProcessInspect> all2 = ppcQmRouteProcessInspectServiceImpl.findAll(map);
            if (all2.isEmpty()) {
                List<PpcQmStdInspect> all = ppcQmStdInspectServiceImpl.findAll(map);
                if (!all.isEmpty()) {
                    String routeCode = map.get(PpcQmInspectConstants.ROUTE_CODE).toString();
                    Map mapInspect = all.get(0).toMap();
                    mapInspect.put(PpcQmInspectConstants.ROUTE_CODE, routeCode);
                    ppcQmRouteProcessInspectServiceImpl.saveInspect(mapInspect);
                    List<PpcQmStdInspectDetail> all1 = ppcQmStdInspectDatailServiceImpl.findAll(map);
                    if (!all1.isEmpty()) {
                        Map mapInspectDetail = all1.get(0).toMap();
                        mapInspectDetail.put(PpcQmInspectConstants.ROUTE_CODE, routeCode);
                        ppcQmRouteProcessInspectDetailServiceImpl.saveInspectDetail(mapInspectDetail);
                    }
                }
                return new Result(ResultCode.SUCCESS, map);
            } else {
                return new Result(ResultCode.FAIL, "质检编码已存在！");
            }
        } else {
            return new Result(ResultCode.ILLEGAL_PARAMETER, null);
        }
    }
}
