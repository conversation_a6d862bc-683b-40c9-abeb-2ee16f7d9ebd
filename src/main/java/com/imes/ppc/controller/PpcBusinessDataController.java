package com.imes.ppc.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcProducePlanSchedulService;
import com.imes.ppc.service.ProducePlanService;
import com.imes.ppc.service.SaleMainService;
import com.imes.ppc.service.WorkFinishService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2022-05-19
 */
@Slf4j
@RestController
@RequestMapping("/api/ppc/businessDataCollect")
@Api(tags = "生产业务数据汇总查询")
public class PpcBusinessDataController {
    @Autowired
    FeignService feignService;
    @Autowired
    private ProducePlanService planService;
    @Resource
    private WorkFinishService workFinishService;
    @Autowired
    private PpcProducePlanSchedulService PpcProducePlanSchedulService;
    @Autowired
    private SaleMainService saleMainService;

    /**
     * 生产业务数据采集
     */
    @GetMapping
    @ApiOperation(value = "获取系统物料，BOM，工序，工艺路线，客户，供应商，生产计划，生产记录，已执行工单数，订单数数据汇总", httpMethod = "GET")
    public Result ppcBusinessDataCollect() {
        Map map = feignService.getPpcBusinessDataCollect();
        //生产计划
        long planNum = planService.findPlanNum();
        //生产记录
        long workFinishNum = workFinishService.findWorkFinishNum();
        //已执行工单数
        long planSchedulNum = PpcProducePlanSchedulService.findPlanSchedulNum();
        //订单数
        long saleMainNum = saleMainService.findSaleMainNum();
        LinkedHashMap<Object, Object> dataMap = new LinkedHashMap<>();
        dataMap.put("1", map.get("materialNum") == null ? 0 : Long.parseLong(map.get("materialNum").toString()));
        dataMap.put("2", map.get("bomNum") == null ? 0 : Long.parseLong(map.get("bomNum").toString()));
        dataMap.put("3", map.get("processNum") == null ? 0 : Long.parseLong(map.get("processNum").toString()));
        dataMap.put("4", map.get("ppcRouteLineNum") == null ? 0 : Long.parseLong(map.get("ppcRouteLineNum").toString()));
        dataMap.put("5", planNum);
        dataMap.put("6", workFinishNum);
        dataMap.put("7", map.get("supplierNum") == null ? 0 : Long.parseLong(map.get("supplierNum").toString()));
        dataMap.put("8", map.get("customerNum") == null ? 0 : Long.parseLong(map.get("customerNum").toString()));
        dataMap.put("9", planSchedulNum);
        dataMap.put("10", saleMainNum);
        return new Result(ResultCode.SUCCESS, dataMap);
    }
}
