package com.imes.ppc.controller;

import com.imes.common.constant.ApiVersionConstants;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.common.utils.ExcelUtils;
import com.imes.common.utils.api.ApiVersion;
import com.imes.ppc.service.PpcUserSalaryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @<NAME_EMAIL>
 * @Description:
 * @date 2024/1/4 17:00
 **/

@RequestMapping("/api/ppc/userSalary")
@RestController
@Slf4j
@Api(tags = "员工薪资数据")
@ApiIgnore
public class PpcUserSalaryController {

    @Value("${imes.ppc.template.userSalaryTemplate}")
    private String userSalaryTemplate;

    @Autowired
    private PpcUserSalaryService userSalaryService;

    @GetMapping("downloadUserSalaryModel")
    @ApiOperation(value = "下载员工薪资导入模板", httpMethod = "GET")
    public void downloadQinQinTemplate(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, userSalaryTemplate);
    }

    @PostMapping("importUserSalary")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "Excel导入", httpMethod = "POST")
    public Result importRouteProcessParameter(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        int dataNum = userSalaryService.importExcel(uploadFile);
        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
    }

    @GetMapping("getUserSalaryReport")
    @ApiOperation(value = "查询报表", httpMethod = "GET")
    public Result getUserSalaryReport() throws Exception {
        //int dataNum = userSalaryService.importExcel();
        //return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
        return Result.SUCCESS();
    }


}
