package com.imes.ppc.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;
import com.imes.domain.entities.ppc.po.PpcPieceMainItem;
import com.imes.ppc.service.PpcPieceMainItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.ppc.PpcPieceMainItemQueryVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;

/**
 * 计件项明细表(PpcPieceMainItem)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-21 16:50:18
 */
@RestController
@Api(tags = "计件项明细表")
@RequestMapping("/api/ppc/ppcPieceMainItem")
public class PpcPieceMainItemController {

    @Autowired
    private PpcPieceMainItemService ppcPieceMainItemService;

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result info(String id) {
        return Result.SUCCESS((ppcPieceMainItemService.getById(id)));
    }

    /**
     * 保存数据
     *
     * @param ppcPieceMainItem 实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(@RequestBody PpcPieceMainItem ppcPieceMainItem) throws Exception {
        return new Result<>(ppcPieceMainItemService.save(ppcPieceMainItem));
    }

    /**
     * 查询列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result queryList(PpcPieceMainItemQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceMainItemQueryVo> list = ppcPieceMainItemService.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 查询列表
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/queryItem")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result<List<PpcPieceMainItem>> queryItem(@RequestParam() String finishedDate, @RequestParam() String bigProcessCode, @RequestParam() String smallProcessCode) throws Exception {
        List<PpcPieceMainItem> list = ppcPieceMainItemService.queryItem(finishedDate, bigProcessCode, smallProcessCode);
        return Result.SUCCESS(list);
    }

    /**
     * 根据主表id查询列表
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/queryByMainId")
    @ApiOperation(value = "根据主表id查询列表", httpMethod = "GET")
    public Result<List<PpcPieceMainItem>> queryByMainId(@RequestParam() String mainId) throws Exception {
        List<PpcPieceMainItem> list = ppcPieceMainItemService.queryByMainId(mainId);
        return Result.SUCCESS(list);
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @DeleteMapping("/batchDel")
    @ApiOperation(value = "批量删除", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDel(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        return new Result<>(ppcPieceMainItemService.batchDel(idList) > 0);
    }

    /**
     * 复制item
     *
     * @return 删除是否成功
     */
    @GetMapping("/copyItem")
    @ApiOperation(value = "复制item", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> copyItem(@RequestParam() String id) throws Exception {
        ppcPieceMainItemService.copyItem(id);
        return Result.SUCCESS();
    }
}

