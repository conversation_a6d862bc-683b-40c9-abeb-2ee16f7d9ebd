package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.domain.entities.ppc.po.PpcPieceFormula;
import com.imes.domain.entities.query.template.ppc.PpcPieceFormulaQueryVo;
import com.imes.ppc.service.PpcPieceFormulaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 计件公式表(PpcPieceFormula)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-21 16:50:16
 */
@RestController
@Api(tags = "计件公式表")
@RequestMapping("/api/ppc/ppcPieceFormula")
public class PpcPieceFormulaController {

    @Autowired
    private PpcPieceFormulaService ppcPieceFormulaService;

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result info(String id) {
        return Result.SUCCESS((ppcPieceFormulaService.getById(id)));
    }

    /**
     * 保存数据
     *
     * @param ppcPieceFormula 实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(@RequestBody PpcPieceFormula ppcPieceFormula) throws Exception {
        return new Result<>(ppcPieceFormulaService.save(ppcPieceFormula));
    }

    /**
     * 查询列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result<PageResult<PpcPieceFormulaQueryVo>> queryList(PpcPieceFormulaQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceFormulaQueryVo> list = ppcPieceFormulaService.queryList(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @DeleteMapping("/batchDel")
    @ApiOperation(value = "批量删除", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDel(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        return new Result<>(ppcPieceFormulaService.batchDel(idList) > 0);
    }
    /**
     * 新增个人计件计算金额
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/calcMoney")
    @ApiOperation(value = "新增个人计件计算金额", httpMethod = "GET")
    public Result<BigDecimal> calcMoney(@RequestParam String reportMethod, @RequestParam BigDecimal singlePrice, @RequestParam String unit, @RequestParam BigDecimal calcQty, @RequestParam BigDecimal area) throws Exception {
        BigDecimal b = ppcPieceFormulaService.calcMoney(reportMethod, singlePrice, unit, calcQty, area);
        return Result.SUCCESS(b);
    }
}

