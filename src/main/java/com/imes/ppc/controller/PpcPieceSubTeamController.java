package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.domain.entities.ppc.po.PpcPieceSubTeam;
import com.imes.domain.entities.query.template.ppc.PpcPieceSubTeamSearchVo;
import com.imes.ppc.service.PpcPieceSubTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计件小组配置(PpcPieceSubTeam)表控制层
 *
 * <AUTHOR>
 * @since 2025-05-09 17:07:17
 */
@RestController
@RequestMapping("/api/ppc/ppcPieceSubTeam")
@Api(tags = "计件小组配置")
public class PpcPieceSubTeamController {
    /**
     * 服务对象
     */
    @Autowired
    private PpcPieceSubTeamService ppcPieceSubTeamService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (ppcPieceSubTeamService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param ppcPieceSubTeam 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody PpcPieceSubTeam ppcPieceSubTeam) throws Exception {
        String result = ppcPieceSubTeamService.insert(ppcPieceSubTeam);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param ppcPieceSubTeam 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcPieceSubTeam ppcPieceSubTeam) throws Exception {
        if (ppcPieceSubTeamService.update(ppcPieceSubTeam) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcPieceSubTeamService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result<StatPageResult<PpcPieceSubTeamSearchVo>> queryList(PpcPieceSubTeamSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceSubTeamSearchVo> list = ppcPieceSubTeamService.queryList(vo);
        return Result.SUCCESS(new StatPageResult<>(page.getTotal(), list, map));
    }
    @GetMapping("/getByTeamCode")
    @ApiOperation(value = "通过班组查询小组列表")
    public Result<List<PpcPieceSubTeam>> getByTeamCode(@RequestParam String teamCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        List<PpcPieceSubTeam> list = ppcPieceSubTeamService.getByTeamCode(teamCode);
        return Result.SUCCESS(list);
    }
    @GetMapping("/getByTeamCodeAndBigProcessCode")
    @ApiOperation(value = "通过班组查询小组列表")
    public Result<List<PpcPieceSubTeam>> getByTeamCodeAndBigProcessCode(@RequestParam String teamCode,@RequestParam String bigProcessCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        List<PpcPieceSubTeam> list = ppcPieceSubTeamService.getByTeamCodeAndBigProcessCode(teamCode,bigProcessCode);
        return Result.SUCCESS(list);
    }

    @GetMapping("/getBySubTeamNameAndTeamCode")
    @ApiOperation(value = "通过小组名和班组查询小组")
    public Result<PpcPieceSubTeam> getBySubTeamNameAndTeamCode(@RequestParam String teamCode, @RequestParam String subTeamName) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        PpcPieceSubTeam list = ppcPieceSubTeamService.getBySubTeamNameAndTeamCode(teamCode, subTeamName);
        return Result.SUCCESS(list);
    }

}

