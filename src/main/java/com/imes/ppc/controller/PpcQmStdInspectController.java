package com.imes.ppc.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcQmStdInspect;
import com.imes.domain.entities.ppc.po.PpcQmStdInspectDetail;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.ppc.constants.PpcQmInspectConstants;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.impl.PpcQmStdInspectDetailServiceImpl;
import com.imes.ppc.service.impl.PpcQmStdInspectServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/ppc/ppcQmStdInspect")
@Api(tags = "标准质检项相关接口")
@ApiIgnore
public class PpcQmStdInspectController {
    @Autowired
    private PpcQmStdInspectServiceImpl ppcQmStdInspectServiceImpl;
    @Autowired
    private FeignService feignService;
    @Autowired
    private PpcQmStdInspectDetailServiceImpl ppcQmStdInspectDatailServiceImpl;

    /**
     * 质检信息查询全部
     *
     * @return
     */
    @GetMapping("/queryAll")
    @ApiOperation(value = "质检信息查询全部", httpMethod = "GET")
    public Result query(@RequestParam Map<String, Object> map) throws Exception {
        List<PpcQmStdInspect> all = ppcQmStdInspectServiceImpl.findAll(map);
        map.put(PpcQmInspectConstants.LIST, all);
        return new Result(ResultCode.SUCCESS, map);
    }

    /**
     * 根据质检编码查询生产工序和生产车间
     *
     * @return
     */
    @GetMapping("/queryByInspectCode/{inspectCode}")
    @ApiOperation(value = "根据质检编码查询生产工序和生产车间", httpMethod = "GET")
    public Result queryByInspectCode(@PathVariable("inspectCode") String inspectCode) throws Exception {
        if (StringUtils.isNullOrBlank(inspectCode)) {
            return new Result(ResultCode.ILLEGAL_PARAMETER, null);
        } else {
            Map objHashMap = new HashMap<>();
            objHashMap.put(PpcQmInspectConstants.INSPECT_CODE, inspectCode);
            //查询工序信息
            List<Map> all = ppcQmStdInspectServiceImpl.queryByInspectCode(objHashMap);
            objHashMap.put(PpcQmInspectConstants.LIST_STD_INSPECT, all);
            if (!all.isEmpty()) {
                //查询明细信息，供前台渲染输入框
                List<PpcQmStdInspectDetail> all1 = ppcQmStdInspectDatailServiceImpl.findAll(objHashMap);
                objHashMap.put(PpcQmInspectConstants.LIST_STD_INSPECT_DETAIL, all1);

                //查询车间信息
                List<CoDepartment> coDepartments = feignService.queryDepartByProcess(all.get(0).get(PpcQmInspectConstants.PROCESS_CODE).toString());
                objHashMap.put(PpcQmInspectConstants.LIST_CO_DEPARTMENT, coDepartments);
            }
            return new Result(ResultCode.SUCCESS, objHashMap);
        }
    }
}
