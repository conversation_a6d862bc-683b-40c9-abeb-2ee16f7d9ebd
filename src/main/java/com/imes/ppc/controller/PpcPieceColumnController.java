package com.imes.ppc.controller;

import com.imes.domain.entities.system.SysDictDetail;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;
import com.imes.domain.entities.ppc.po.PpcPieceColumn;
import com.imes.ppc.service.PpcPieceColumnService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.ppc.PpcPieceColumnQueryVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;

/**
 * 计件项字段维护表(PpcPieceColumn)表控制层
 *
 * <AUTHOR>
 * @since 2025-04-21 16:50:17
 */
@RestController
@Api(tags = "计件项字段维护表")
@RequestMapping("/api/ppc/ppcPieceColumn")
public class PpcPieceColumnController {

    @Autowired
    private PpcPieceColumnService ppcPieceColumnService;

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result info(String id) {
        return Result.SUCCESS((ppcPieceColumnService.getById(id)));
    }

    /**
     * 保存数据
     *
     * @param ppcPieceColumn 实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(@RequestBody PpcPieceColumn ppcPieceColumn) throws Exception {
        return new Result<>(ppcPieceColumnService.save(ppcPieceColumn));
    }

    /**
     * 查询列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result<PageResult<PpcPieceColumnQueryVo>> queryList(PpcPieceColumnQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcPieceColumnQueryVo> list = ppcPieceColumnService.queryList(vo);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @DeleteMapping("/batchDel")
    @ApiOperation(value = "批量删除", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDel(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        return new Result<>(ppcPieceColumnService.batchDel(idList) > 0);
    }

    /**
     * 查询判定字段的下拉类型的列的值列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryColumnDropList")
    @ApiOperation(value = "查询判定字段的下拉类型的列的值列表", httpMethod = "GET")
    public Result<List<SysDictDetailVo>> queryColumnDropList(PpcPieceColumn vo) throws Exception {
        List<SysDictDetailVo> list = ppcPieceColumnService.queryColumnDropList(vo);
        return Result.SUCCESS(list);
    }

}

