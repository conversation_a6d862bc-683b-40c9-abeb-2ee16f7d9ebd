package com.imes.ppc.controller;

import com.imes.common.entity.PageResult;
import com.imes.domain.entities.ppc.po.PpcProducePackSyncDay;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.domain.entities.ppc.po.PpcProducePackSum;
import com.imes.ppc.service.PpcProducePackSumService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.ppc.PpcProducePackSumSearchVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 分拣工单-计件汇总表（月）(PpcProducePackSum)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 11:20:33
 */
@RestController
@RequestMapping("/api/ppc/ppcProducePackSum")
@Api(tags = "分拣工单-计件汇总表（月）")
public class PpcProducePackSumController {
    /**
     * 服务对象
     */
    @Autowired
    private PpcProducePackSumService ppcProducePackSumService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (ppcProducePackSumService.getById(id)));
    }

    /**
     * 根据条件查询
     *
     * @return 单条数据
     */
    @GetMapping("/queryByCond")
    @ApiOperation(value = "根据条件查询", httpMethod = "GET")
    public Result queryByCond(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              PpcProducePackSum ppcProducePackSum) {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProducePackSum> result = ppcProducePackSumService.queryByCond(ppcProducePackSum);
        PageResult pr = new PageResult(page.getTotal(), result);
        return new Result(ResultCode.SUCCESS, pr);
    }

    /**
     * 新增数据
     *
     * @param ppcProducePackSum 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody PpcProducePackSum ppcProducePackSum) throws Exception {
        String result = ppcProducePackSumService.insert(ppcProducePackSum);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param ppcProducePackSum 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody PpcProducePackSum ppcProducePackSum) throws Exception {
        if (ppcProducePackSumService.update(ppcProducePackSum) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    @PostMapping("/addData")
    @ApiOperation(value = "补充数据", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result addData(@RequestBody PpcProducePackSum ppcProducePackSum) throws Exception {
        if (ppcProducePackSumService.addData(ppcProducePackSum) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (ppcProducePackSumService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(PpcProducePackSumSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProducePackSumSearchVo> list = ppcProducePackSumService.queryList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/getDetails")
    @ApiOperation(value = "获取明细")
    public Result getDetails(@RequestParam String reportNo) throws Exception {
        List<PpcProducePackSyncDay> list = ppcProducePackSumService.getDetails(reportNo);
        return Result.SUCCESS(list);
    }

}

