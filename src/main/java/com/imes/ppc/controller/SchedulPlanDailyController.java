/**
 * <AUTHOR> (<EMAIL>)
 */

package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.domain.entities.ppc.po.PpcSchedulPlanDaily;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.SchedulPlanDailyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/ppc/planDaily")
@Api(tags = "计划排产相关接口")
public class SchedulPlanDailyController {

    @Autowired
    private SchedulPlanDailyService schedulPlanDailyService;
    @Autowired
    FeignService feignService;


    @GetMapping("/queryDaily")
    public Result queryDaily(@ApiParam("分页页数") @RequestParam("pageNum") Integer pageNum,
                             @ApiParam("分页条数")  @RequestParam("pageSize") Integer pageSize, @RequestParam Map map) throws Exception {
        Map<String, Object> resultMap = new HashMap<String, Object>();
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> result = schedulPlanDailyService.queryDaily(map);
        resultMap.put("result", result);
        resultMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    @GetMapping("/planDaily")
    @ApiIgnore
    public Result planDaily(@RequestParam Map map) throws Exception {
        List<Map<String, Object>> result = schedulPlanDailyService.planDaily(map);
        return new Result(ResultCode.SUCCESS, result);
    }

    @PostMapping("/saveDaily")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result saveDaily(@RequestBody PpcSchedulPlanDaily daily) throws Exception {
        schedulPlanDailyService.saveDaily(daily);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/batchSaveDaily")
    @ApiIgnore
    @Transactional(rollbackFor = Exception.class)
    public Result batchSaveDaily(@RequestBody List<Map> rows) throws Exception {
        schedulPlanDailyService.batchSaveDaily(rows);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 根据车间编号查询班次班组下拉数据
     *
     * @param workShopCode
     * @return
     */
    @GetMapping("/teamShiftSltToShopCode")
    @ApiOperation(value = "根据车间编号查询班次班组下拉数据", httpMethod = "GET")
    public Result teamShiftSltToShopCode(@ApiParam("车间编码") @RequestParam("workShopCode") String workShopCode) throws Exception {
        Map result = new HashMap<>();
        List<Map<String, Object>> teamSelect = schedulPlanDailyService.TeamSelectToShopCode(workShopCode);
        List<Map<String, Object>> shiftSelect = feignService.shiftSelectToShopCode(workShopCode);
        result.put("teamSelect", teamSelect);
        result.put("shiftSelect", shiftSelect);
        return new Result(ResultCode.SUCCESS, result);
    }

    @DeleteMapping("/deleteDaily/{id}")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    public Result deleteDaily(@PathVariable("id") String id) throws Exception {
        schedulPlanDailyService.deleteDaily(id);
        return new Result(ResultCode.SUCCESS);
    }
}
