package com.imes.ppc.controller;

import com.alibaba.fastjson.JSONObject;
import com.imes.common.utils.CustomUtil;
import com.imes.domain.entities.ppc.po.PpcProduceOrder;
import com.imes.domain.entities.ppc.po.PpcProduceOrderPrepareDetail;
import com.imes.domain.entities.ppc.po.Promaterial;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.query.template.ppc.PpcProducePlanAztAndProcessVo;
import com.imes.domain.entities.query.template.ppc.PpcProducePlanAztTeamOperationVo;
import com.imes.domain.entities.system.vo.UserVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.PpcProduceOrderPrepareDetailService;
import com.imes.ppc.service.PpcProduceOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.PageResult;
import com.imes.domain.entities.ppc.po.PpcProducePlanAzt;
import com.imes.ppc.service.PpcProducePlanAztService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.ppc.PpcProducePlanAztQueryVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 生产计划物料-志特(PpcProducePlanAzt)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-10 14:44:07
 */
@RestController
@Api(tags = "生产计划物料-志特")
@RequestMapping("/api/ppc/ppcProducePlanAzt")
public class PpcProducePlanAztController {

    @Autowired
    private PpcProducePlanAztService ppcProducePlanAztService;

    @Autowired
    private FeignService feignService;


    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    @GetMapping("/info")
    @ApiOperation(value = "查询详情", httpMethod = "GET")
    public Result info(String id) {
        return Result.SUCCESS((ppcProducePlanAztService.getById(id)));
    }

    /**
     * 保存数据
     *
     * @param ppcProducePlanAzt 实体
     * @return 新增结果
     */
    @PostMapping("/save")
    @ApiOperation(value = "保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> save(@RequestBody PpcProducePlanAzt ppcProducePlanAzt) throws Exception {
        return new Result<>(ppcProducePlanAztService.save(ppcProducePlanAzt));
    }

    /**
     * 查询列表
     *
     * @param vo 查询条件
     * @return
     * @throws Exception
     */
    @GetMapping("/queryList")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result queryList(PpcProducePlanAztQueryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProducePlanAztQueryVo> list = ppcProducePlanAztService.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @DeleteMapping("/batchDel")
    @ApiOperation(value = "批量删除", httpMethod = "DELETE")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDel(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        return new Result<>(ppcProducePlanAztService.batchDel(idList) > 0);
    }

    /**
     * 查询列表
     *
     * @param orderId 工单id
     * @return
     * @throws Exception
     */
    @GetMapping("/queryByOrderId")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result queryByOrderId(@RequestParam String orderId) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryByOrderId(orderId);
        return new Result(ResultCode.SUCCESS, list);
    }
    /**
     * 查询列表
     *
     * @param orderId 工单id
     * @return
     * @throws Exception
     */
    @GetMapping("/queryModifyByOrderId")
    @ApiOperation(value = "查询修改单行物料列表1", httpMethod = "GET")
    public Result queryModifyByOrderId(@RequestParam String orderId) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryModifyByOrderId(orderId);
        return new Result(ResultCode.SUCCESS, list);
    }
    /**
     * 取消单查询明细列表
     *
     * @param orderId 工单id
     * @return
     * @throws Exception
     */
    @GetMapping("/queryByOrderIdForCancel")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result queryByOrderIdForCancel(@RequestParam String orderId) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryByOrderIdForCancel(orderId);
        return new Result(ResultCode.SUCCESS, list);
    }


    /**
     * 查询完工入库汇总信息
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/queryGroupDataByProjectNo")
    @ApiOperation(value = "查询完工入库汇总信息", httpMethod = "GET")
    public Result queryGroupDataByProjectNo(@RequestParam Integer pageNum, @RequestParam Integer pageSize,
                                            @RequestParam String productNo, @RequestParam String partType, @RequestParam(required = false) String orderNo) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<Map<String, Object>> list = ppcProducePlanAztService.queryGroupDataByProjectNo(productNo, partType, orderNo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 校验该项目下是否有可入库工单
     *
     * @param productNo
     * @param build
     * @return
     * @throws Exception
     */
    @RequestMapping("/sendProjectStoreCheck")
    @ApiOperation(value = "校验该项目下是否有可入库工单", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> sendProjectStoreCheck(@RequestParam("productNo") String productNo,
                                        @RequestParam("build") String build) throws Exception {
        ProjectStoreVo vo = ppcProducePlanAztService.sendProjectStoreCheck(productNo, build);
        return new Result<>(ResultCode.SUCCESS,vo);
    }

    /**
     * 发送生产入库数据
     *
     * @param vo
     * @return
     * @throws Exception
     */
    @PostMapping("/sendProjectStore")
    @ApiOperation(value = "发送生产入库数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> sendProjectStore(@RequestBody ProjectStoreVo vo) throws Exception {
        ppcProducePlanAztService.sendProjectStore(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 拆分计划(标准件)
     *
     * @param voList
     * @return
     * @throws Exception
     */
    @PostMapping("/splitPlanAzt")
    @ApiOperation(value = "拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> splitPlanAzt(@RequestBody List<SplitPlanAztVo> voList) throws Exception {
        HashMap<String, Object> resultMap = ppcProducePlanAztService.splitPlanAzt(voList);
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    /**
     * 拆分计划(子工单)
     *
     * @param voList
     * @return
     * @throws Exception
     */
    @PostMapping("/splitPlanAztForSubOrder")
    @ApiOperation(value = "拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> splitPlanAztForSubOrder(@RequestBody List<SplitPlanAztVo> voList) throws Exception {
        HashMap<String, Object> resultMap = ppcProducePlanAztService.splitPlanAztForSubOrder(voList);
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    /**
     * 拆分计划(标准件)2，用与普通工单抽取拆分标准件时用
     *
     * @param
     * @return
     * @throws Exception
     */
    @PostMapping("/splitPlanAzt2")
    @ApiOperation(value = "拆分数据2", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> splitPlanAzt2(@RequestBody List<PpcProducePlanAzt> aztList) throws Exception {
        HashMap<String, Object> resultMap = ppcProducePlanAztService.splitPlanAzt2(aztList);
        return new Result(ResultCode.SUCCESS, resultMap);
    }

    /**
     * 保存拆分计划
     *
     * @param list
     * @return
     * @throws Exception
     */
    @PostMapping("/saveSplitPlanAzt")
    @ApiOperation(value = "保存拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result saveSplitPlanAzt(@RequestBody List<PpcProducePlanAzt> list) throws Exception {
        ppcProducePlanAztService.saveSplitPlanAzt(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 保存子工单拆分数据
     *
     * @param list
     * @return
     * @throws Exception
     */
    @PostMapping("/saveSplitPlanAztForSubOrder")
    @ApiOperation(value = "保存子工单拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result saveSplitPlanAztForSubOrder(@RequestBody List<PpcProducePlanAzt> list) throws Exception {
        ppcProducePlanAztService.saveSplitPlanAztForSubOrder(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 保存拆分计划2
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/saveSplitPlanAzt2")
    @ApiOperation(value = "保存拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result saveSplitPlanAzt2(@RequestBody ProduceOrderStdMergePreview preview) throws Exception {
        ppcProducePlanAztService.saveSplitPlanAzt2(preview);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 拆分计划(普通工单)
     *
     * @param voList
     * @return
     * @throws Exception
     */
    @PostMapping("/savePlanAztByOrdinary")
    @ApiOperation(value = "拆分数据", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result<ProjectStoreVo> savePlanAztByOrdinary(@RequestBody List<PpcProducePlanAzt> voList) throws Exception {
         ppcProducePlanAztService.savePlanAztByOrdinary(voList);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 工单，大工单保存行物料数据
     *
     * @param list 实体
     * @return 新增结果
     */
    @PostMapping("/batchSave")
    @ApiOperation(value = "批量保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchSave(@RequestBody List<PpcProducePlanAzt> list) throws Exception {
        //新增时写入物料基本信息自定义字段
        List<String> materialCodeList = list.stream().map(PpcProducePlanAzt::getMaterialCode).collect(Collectors.toList());
        List<Promaterial> materialsByCodeList = feignService.findMaterialsByCodeList(materialCodeList);
        Map<String, Promaterial> materialMap = materialsByCodeList.stream().collect(Collectors.toMap(Promaterial::getMaterialCode, v -> v, (k1, k2) -> k1));
        for (PpcProducePlanAzt azt : list) {
            Promaterial promaterial = materialMap.get(azt.getMaterialCode());
            JSONObject jsonObject = CustomUtil.escapeToJson(promaterial.getCustom());
            azt.setArea(new BigDecimal(jsonObject.getString("单位面积")));
            azt.setUnitArea(new BigDecimal(jsonObject.getString("单位面积")));
            azt.setMaterialMarker(promaterial.getMaterialMarker());
            azt.setSamplePic(jsonObject.getString("模板图"));
            azt.setRemarks(jsonObject.getString("物料备注"));
//            azt.setSamplePic1(jsonObject.getString("面积"));
//            azt.setUnitArea(jsonObject.getString("单位面积"));
            azt.setPartPic(jsonObject.getString("配件图"));
            azt.setPatchInfo(jsonObject.getString("贴片信息"));
            azt.setEcR(jsonObject.getString("附属EC|R角"));
            azt.setScrewHeigh(jsonObject.getString("螺杆高"));
            azt.setScrewWidth(jsonObject.getString("螺杆宽"));
            azt.setMillingGroove(jsonObject.getString("非标铣槽信息"));
            azt.setMaterialMarker(jsonObject.getString("异型"));
            azt.setLength(jsonObject.getString("模板长"));
            azt.setName(jsonObject.getString("模板名称"));
            azt.setWide(jsonObject.getString("模板宽"));
        }
        ppcProducePlanAztService.batchSave(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 保存数据
     *
     * @param
     * @return 新增结果
     */
    @PostMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchDelete(@RequestBody List<String> idList) throws Exception {
        ppcProducePlanAztService.batchDelete(idList);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 转标准件校验
     *
     * @param
     * @return 转标准件校验
     */
    @GetMapping("/checkTransformStd")
    @ApiOperation(value = "转标准件校验", httpMethod = "POST")
    public Result<Boolean> checkTransformStd(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        ppcProducePlanAztService.checkTransformStd(idList);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 保存数据
     *
     * @param list 实体
     * @return 新增结果
     */
    @PostMapping("/batchUpdate")
    @ApiOperation(value = "批量保存", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<Boolean> batchUpdate(@RequestBody List<PpcProducePlanAzt> list) throws Exception {
        ppcProducePlanAztService.batchUpdate(list);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 合并单
     *
     * @param list
     * @return
     * @throws Exception
     */
    @PostMapping("/mergePlanAzt")
    @ApiOperation(value = "合并单", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result mergePlanAzt(@RequestBody List<PpcProduceOrder> list) throws Exception {
        ppcProducePlanAztService.mergePlanAzt(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 标准件合并单2
     *
     * @param list
     * @return
     * @throws Exception
     */
    @PostMapping("/mergePlanAzt2")
    @ApiOperation(value = "合并单2", response = Result.class)
    @Transactional(rollbackFor = Exception.class)
    public Result mergePlanAzt2(@RequestBody List<PpcProduceOrder> list) throws Exception {
        ppcProducePlanAztService.mergePlanAzt2(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 查询列表
     *
     * @param orderId 工单id
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAztAndProcessByOrderId")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result<PageResult<PpcProducePlanAztAndProcessVo>> queryAztAndProcessByOrderId(@RequestParam String orderId, @RequestParam() String bigProcessCode,
                                              @RequestParam int pageSize,
                                              @RequestParam int pageNum) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProducePlanAztAndProcessVo> list = ppcProducePlanAztService.queryAztAndProcessByOrderId(orderId, bigProcessCode);
        return Result.SUCCESS(new PageResult<>(page.getTotal(), list));

    }


    /**
     * 查询列表
     *
     * @param productNo 项目编号
     * @return
     * @throws Exception
     */
    @GetMapping("/queryAztByProductNo")
    @ApiOperation(value = "根据项目编号查询列表", httpMethod = "GET")
    public Result queryAztByProductNo(@RequestParam String productNo) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryAztByProductNo(productNo);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     * 查询变更明细
     *
     * @param id 行物料记录id
     * @return
     * @throws Exception
     */
    @GetMapping("/queryChangeByPlanId")
    @ApiOperation(value = "查询变更明细", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> queryChangeByPlanId(@RequestParam String id) throws Exception {
        PpcProduceOrderAndPlanAztVo erpDetail = ppcProducePlanAztService.queryChangeByPlanId(id);
        return new Result<>(ResultCode.SUCCESS, erpDetail);
    }

    /**
     * 普通工单-修改处理
     *
     * @param id      行物料记录id
     * @param cmdType 处理方式 1合并，2拆分
     * @return
     * @throws Exception
     */
    @GetMapping("/changeHandleFromUsually")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "变更处理", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> changeHandleFromUsually(@RequestParam String id, @RequestParam String cmdType) throws Exception {
        ppcProducePlanAztService.changeHandleFromUsually(id, cmdType);
        return new Result<>(ResultCode.SUCCESS);
    }

    /**
     * 普通工单-修改撤销
     *
     * @param id 行物料记录id
     * @return
     * @throws Exception
     */
    @GetMapping("/changeHandleCancel")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "变更处理", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> changeHandleCancel(@RequestParam String id) throws Exception {
        ppcProducePlanAztService.changeHandleCancel(id);
        return new Result<>(ResultCode.SUCCESS);
    }

//    /**
//     * 取消处理（减少数量）
//     *
//     * @param id 行物料记录id，修改单的行id
//     * @return
//     * @throws Exception
//     */
//    @GetMapping("/cancelNumHandle")
//    @Transactional(rollbackFor = Exception.class)
//    @ApiOperation(value = "取消处理", httpMethod = "GET")
//    public Result<PpcProduceOrderAndPlanAztVo> cancelNumHandle(@RequestParam String id) throws Exception {
//        ppcProducePlanAztService.cancelNumHandle(id);
//        return new Result<>(ResultCode.SUCCESS);
//    }
    /**
     * 取消处理（减少数量）
     *
     * @param id 行物料记录id，修改单的行id
     * @return
     * @throws Exception
     */
    @PostMapping("/cancelNumHandle2")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取消处理2", httpMethod = "GET")
    public Result cancelNumHandle2(@RequestParam String id,@RequestBody List<CancelPreviewVo> voList) throws Exception {
        ppcProducePlanAztService.cancelNumHandle2(id,voList);
        return new Result<>(ResultCode.SUCCESS);
    }

//    /**
//     * 取消处理-撤回（减少数量的加回来）
//     *
//     * @param id 行物料记录id，修改单的行id
//     * @return
//     * @throws Exception
//     */
//    @GetMapping("/cancelNumHandleCancel")
//    @Transactional(rollbackFor = Exception.class)
//    @ApiOperation(value = "取消处理", httpMethod = "GET")
//    @Deprecated
//    public Result<PpcProduceOrderAndPlanAztVo> cancelNumHandleCancel(@RequestParam String id) throws Exception {
//        ppcProducePlanAztService.cancelNumHandleCancel(id);
//        return new Result<>(ResultCode.SUCCESS);
//    }
    /**
     * 取消处理-撤回（减少数量的加回来）,增加子工单处理
     *
     * @param id 行物料记录id，修改单的行id
     * @return
     * @throws Exception
     */
    @GetMapping("/cancelNumHandleCancel2")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取消处理2-增加子工单", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> cancelNumHandleCancel2(@RequestParam String id) throws Exception {
        ppcProducePlanAztService.cancelNumHandleCancel2(id);
        return new Result<>(ResultCode.SUCCESS);
    }

    /**
     * 取消单 - 放行 - 放行原单
     *
     * @param id 行物料记录id，修改单的行id
     * @return
     * @throws Exception
     */
    @GetMapping("/releaseOriginRow")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取消单 - 放行 - 放行原单", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> releaseOriginRow(@RequestParam String id) throws Exception {
        ppcProducePlanAztService.releaseOriginRow(id);
        return new Result<>(ResultCode.SUCCESS);
    }

    /**
     * 取消单 - 取消放行 - 取消放行原单
     *
     * @param id 行物料记录id，修改单的行id
     * @return
     * @throws Exception
     */
    @GetMapping("/cancelReleaseOriginRow")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "取消单 - 取消放行 - 取消放行原单", httpMethod = "GET")
    public Result<PpcProduceOrderAndPlanAztVo> cancelReleaseOriginRow(@RequestParam String id) throws Exception {
        ppcProducePlanAztService.cancelReleaseOriginRow(id);
        return new Result<>(ResultCode.SUCCESS);
    }


    /**
     * 查询工单行物料（班组作业页面）
     *
     * @param orderId 工单id
     * @return
     * @throws Exception
     */
    @GetMapping("/getTeamOperationDetailIcs")
    @ApiOperation(value = "查询列表", httpMethod = "GET")
    public Result getTeamOperationDetailIcs(@RequestParam String orderId, @RequestParam String processCode,
                                            @RequestParam Integer pageNum, @RequestParam Integer pageSize) throws Exception {
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<PpcProducePlanAztTeamOperationVo> list = ppcProducePlanAztService.getTeamOperationDetailIcs(orderId, processCode);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }



    /**
     *报工页面 准备变更报工人数据
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/prepareChangeReportToWorkersForIcs")
    @ApiOperation(value = "报工页面 准备变更报工人数据", httpMethod = "GET")
    public Result prepareChangeReportToWorkersForIcs(@RequestParam String orderId,@RequestParam String bigProcessCode,@RequestParam String processCode) throws Exception {
        List<UserVo> list = ppcProducePlanAztService.prepareChangeReportToWorkersForIcs(orderId,bigProcessCode,processCode);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     *报工页面 变更报工人数据
     *
     * @return
     * @throws Exception
     */
    @GetMapping("/changeReportToWorkersForIcs")
    @ApiOperation(value = "报工页面 变更报工人数据", httpMethod = "GET")
    public Result changeReportToWorkersForIcs(@RequestParam String planIds,@RequestParam String executeUserCode,@RequestParam String executeUserName,@RequestParam String bigProcessCode,@RequestParam String processCode) throws Exception {
         ppcProducePlanAztService.changeReportToWorkersForIcs(planIds,executeUserCode,executeUserName,bigProcessCode,processCode);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 根据工单ID集合查询列表
     *
     * @param orderIdList 项目编号
     * @return
     * @throws Exception
     */
    @PostMapping("/queryByOrderIdList")
    @ApiOperation(value = "根据工单ID集合查询列表", httpMethod = "POST")
    public Result queryByOrderIdList(@RequestBody List<String> orderIdList) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryByOrderIdList(orderIdList);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     * 根据工单ID集合查询列表
     *
     * @param orderIdList 项目编号
     * @return
     * @throws Exception
     */
    @PostMapping("/queryByOrderIdListNotCanCel")
    @ApiOperation(value = "根据工单ID集合查询列表", httpMethod = "POST")
    public Result queryByOrderIdListNotCanCel(@RequestBody List<String> orderIdList) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.queryByOrderIdListNotCanCel(orderIdList);
        return new Result(ResultCode.SUCCESS, list);
    }


    /**
     * ics端入库检验，工单明细查询
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/getDetailByOrderForIcsInspect")
    @ApiOperation(value = "ics端入库检验，工单明细查询", httpMethod = "POST")
    public Result getDetailByOrderForIcsInspect(@RequestParam String orderId) throws Exception {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.getDetailByOrderForIcsInspect(orderId);
        return new Result(ResultCode.SUCCESS, list);
    }

    @Autowired
    PpcProduceOrderService orderService;

    /**
     * 根据localOrderId + 行号查询可生产的行物料信息
     *
     * @return
     * @throws Exception
     */
    @PostMapping("/queryByLocalOrderIdAndLineNo")
    @ApiOperation(value = "行号查询可生产的行物料信息", httpMethod = "POST")
    public Result<PpcProduceOrderAndPlanAztVo> queryByLocalOrderIdAndLineNo(@RequestParam(value = "localOrderId") String localOrderId,
                                                                            @RequestParam(value = "lineNo") String lineNo) {

        PpcProduceOrderAndPlanAztVo vo = new PpcProduceOrderAndPlanAztVo();
        PpcProduceOrder order = orderService.getByErpLocalOrderId(localOrderId);
        vo.setOrder(order);
        PpcProducePlanAzt plan = ppcProducePlanAztService.queryByLocalOrderIdAndLineNo(localOrderId, lineNo);
        vo.setPlan(plan);

        // 查询备料
        PpcProduceOrderPrepareDetail detail =  prepareDetailService.getByOrderAndLineNo(order.getId(),lineNo);
        vo.setPrepareDetail(detail);

        return new Result<>(ResultCode.SUCCESS, vo);
    }
    @Autowired
    PpcProduceOrderPrepareDetailService prepareDetailService;
    @PostMapping("/queryByOrderIdAndLineNo")
    @ApiOperation(value = "行号查询可生产的行物料信息", httpMethod = "POST")
    public Result<PpcProducePlanAzt> queryByOrderIdAndLineNo(@RequestParam(value = "orderId") String orderId,
                                                             @RequestParam(value = "lineNo") String lineNo) {
        PpcProducePlanAzt plan = ppcProducePlanAztService.getEnableProduceDetailByOrderIdAndLineNo(orderId, lineNo);
        return new Result<>(ResultCode.SUCCESS, plan);
    }

    @GetMapping("/inspectByAztIdAndBigProcessCode")
    @ApiOperation(value = "勾选是否全检参数", httpMethod = "GET")
    public Result<PpcProducePlanAzt> queryByOrderIdAndLineNo(@RequestParam String aztId,@RequestParam String bigProcessCode,@RequestParam String isInspect,@RequestParam String orderId) {
        Integer num = ppcProducePlanAztService.inspectByAztIdAndBigProcessCode(aztId, bigProcessCode, isInspect,orderId);
        return new Result(ResultCode.SUCCESS, num);
    }

    /**
     *
     * @param partType 构件类型
     * @param filterType 1贴片信息非空，2两种钻孔非空，3附属EC非空
     * @return
     */
    @GetMapping("/querySubOrderMaterial")
    @ApiOperation(value = "查询可生成子工单的物料", httpMethod = "GET")
    public Result<List<PpcProducePlanAzt>> querySubOrderMaterial(@RequestParam String productNo,
                                                                 @RequestParam String build,
                                                                 @RequestParam String orderNo,
                                                                 @RequestParam(required = false) String partType,
                                                                 @RequestParam(required = false) String factoryLineName,
                                                                 @RequestParam(required = false) String issuingTimeStartDate,
                                                                 @RequestParam(required = false) String issuingTimeEndDate,
                                                             @RequestParam String filterType) {
        List<PpcProducePlanAzt> list = ppcProducePlanAztService.querySubOrderMaterial(productNo, build, partType, filterType, orderNo, factoryLineName, issuingTimeStartDate, issuingTimeEndDate);
        return Result.SUCCESS(list);
    }


    /**
     * 保存数据
     *
     * @param ppcProducePlanAzt 实体
     * @return 新增结果
     */
    @PostMapping("/saveRemarks")
    @ApiOperation(value = "保存备注", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result<PpcProducePlanAzt> saveRemarks(@RequestBody PpcProducePlanAzt ppcProducePlanAzt) throws Exception {
        ppcProducePlanAztService.saveRemarks(ppcProducePlanAzt);
        return new Result(ResultCode.SUCCESS);
    }

    //    @GetMapping("/cancelPreview")
//    @ApiOperation(value = "取消预览")
//    public Result<CancelPreviewVo> cancelPreview(String id) throws Exception {
//        CancelPreviewVo vo = ppcProducePlanAztService.cancelPreview(id);
//        return Result.SUCCESS(vo);
//    }
    @GetMapping("/cancelPreview2")
    @ApiOperation(value = "取消预览")
    public Result<List<CancelPreviewVo>> cancelPreview2(String id) throws Exception {
        List<CancelPreviewVo> vo = ppcProducePlanAztService.cancelPreview2(id);
        return Result.SUCCESS(vo);
    }

    /**
     * 获取行物料工艺路线详情 工单工艺路线
     * @param orderId
     * @return
     * @throws Exception
     */
    @GetMapping("/getAztRouLine")
    @ApiOperation(value = "获取行物料详情 工单工艺路线")
    @Transactional(rollbackFor = Exception.class)
    public Result getAztRouLine(@RequestParam(value = "orderId") String orderId) throws Exception {
        List<PpcProducePlanAztAndProcessVo> result = ppcProducePlanAztService.getAztRouLine(orderId);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 获取工单行物料及备料关联信息
     *
     * @param orderId
     * @return
     * @throws Exception
     */
    @GetMapping("/getPlanListAndPrepare")
    @Transactional(rollbackFor = Exception.class)
    public Result getPlanListAndPrepare(@RequestParam(value = "orderId") String orderId) throws Exception {
        List<PpcProducePlanAztQueryVo> result = ppcProducePlanAztService.getPlanListAndPrepare(orderId);
        return new Result(ResultCode.SUCCESS, result);
    }
}

