package com.imes.ppc.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.support.Resume;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcProducePlanScheduleStore;
import com.imes.domain.entities.ppc.vo.PlanScheduleStoreVo;
import com.imes.domain.entities.ppc.vo.ProduceStoreVo;
import com.imes.domain.entities.query.template.ppc.PpcProductionInboundRequisitionQueryVo;
import com.imes.domain.entities.system.SysUserFieldRe;
import com.imes.domain.entities.system.vo.UserTableFieldVO;
import com.imes.domain.entities.wms.vo.WmsOutboundItemVo;
import com.imes.ppc.feignClient.service.FeignService;
import com.imes.ppc.service.ProducePlanScheduleStoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigDecimal;
import java.util.*;

@RestController
@RequestMapping("api/ppc/planScheduleStore")
@Api(tags = "生产入库控制类", value = "包含入库列表查询、新增、删除等接口")
public class PlanScheduleStoreController {
    @Autowired
    ProducePlanScheduleStoreService storeService;
    @Autowired
    FeignService feignService;

    @GetMapping("/queryProduceStoreList")
    @ApiOperation(value = "根据排产单号，派工单号查询生产入库单", httpMethod = "GET")
    public Result queryProduceStoreList(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo,@ApiParam("派工单号（非必填）") @RequestParam(required = false, value = "woNo") String woNo) throws Exception {
        List<PpcProducePlanScheduleStore> storeList = storeService.queryByProductionNo(productionNo,woNo);
        return new Result(ResultCode.SUCCESS, storeList);
    }
    @Transactional(rollbackFor = Exception.class)
    @PostMapping({"/packPrint"})
    @ApiOperation(value = "生产入库申请单打印", httpMethod = "POST")
    public Result packPrint(@ApiParam("入库申请单Id，带逗号")  String ids) throws Exception {
        //如果ids是null就报错
        if (StringUtils.isNullOrBlank(ids)) {
            AssertUtil.throwException("未勾选有效数据,无法打印!");
        }
        List<String> idList = Arrays.asList(ids.split(","));
        StringBuilder stringBuilder = storeService.packPrint(idList);
        return new Result(ResultCode.SUCCESS, stringBuilder);
    }


    @GetMapping("/queryProduceStoreDetail")
    @ApiOperation(value = "根据生产入库单id查询入库单详情", httpMethod = "GET")
    public Result queryProduceStoreDetailById(@ApiParam("生产入库单id") @RequestParam("id") String id){
        PpcProducePlanScheduleStore store = storeService.queryProduceStoreDetailById(id);
        return new Result(ResultCode.SUCCESS, store);
    }

    /**
     * 生产计划查询全部
     *
     * @param vo
     * @return
     */
    @GetMapping("/getPpcProduceStoreTemplate")
    @ApiOperation(value = "生产入库查询",httpMethod = "GET")
    public Result getPpcProduceStoreTemplate(PpcProductionInboundRequisitionQueryVo vo) {
        Map<String, Object> resultMap = new HashMap<>();
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<PpcProductionInboundRequisitionQueryVo> List = storeService.getPpcProduceStoreTemplate(vo);
        List.forEach(v -> {
            v.setInputQty(new BigDecimal(v.getInputQty()).stripTrailingZeros().toPlainString());
            if (!StringUtils.isNullOrBlank(v.getPackStoreNum())) {
                v.setPackStoreNum(new BigDecimal(v.getPackStoreNum()).stripTrailingZeros().toPlainString());
            }
            if (!StringUtils.isNullOrBlank(v.getRealInputQty())) {
                v.setRealInputQty(new BigDecimal(v.getRealInputQty()).stripTrailingZeros().toPlainString());
            }
        });
        resultMap.put("list", List);
        resultMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, resultMap);
    }
    /**
     * 生产计划查询全部
     *
     * @return
     */
    @GetMapping("/queryProduceStoreByUserFields")
    @ApiOperation(value = "生产入库查询全部", httpMethod = "GET")
    public Result queryProduceStoreByUserFields(@ApiParam("分页页数") @RequestParam("pageNum") Integer pageNum,
                                                @ApiParam("分页条数") @RequestParam("pageSize") Integer pageSize,
                        @RequestParam Map<String, Object> map) throws Exception {

        Map resultMap = new HashMap();
        UserTableFieldVO userTableFields = feignService.queryUserTableField(RedisUtils.getUserCode(), "produce_schedule_store");
        if (StringUtils.isNullOrBlank(userTableFields.getColumns())) {
            AssertUtil.throwException("无权限访问！需管理员配置用户字段权限！");
        }
        StringBuffer  columns = new StringBuffer(userTableFields.getColumns()) ;
        List<String> businessFieldList = userTableFields.getBusinessFields();
        for (String businessField : businessFieldList){
            columns.append(","+businessField);
        }
        map.put("queryFields",columns);// 去除默认补充的子表id，否则sql报错

        if(!StringUtils.isNullOrBlank(map.get("statuIn"))){
            map.put("statuIn", map.get("statuIn").toString().split(","));
        }
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<ProduceStoreVo> storeList = storeService.queryListByFields(map);
        List<SysUserFieldRe> userFields = userTableFields.getUserFields();
        List<Map<String, Object>> showFields = userTableFields.getShowFields();
        resultMap.put("userFields",userFields);
        resultMap.put("showFields",showFields);
        resultMap.put("list", storeList);
        resultMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, resultMap);
    }



    @PostMapping("/insertPpcStore")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "新增生产入库单", httpMethod = "POST")
    public Result insertPpcStore(@RequestBody PpcProducePlanScheduleStore store) throws Exception {
        storeService.insertPpcStore(store);
        return new Result(ResultCode.SUCCESS);
    }


    @PostMapping("/insertPpcStoreCheck")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产入库单新增校验接口（新增生产入库单时调用）", httpMethod = "POST")
    public Result insertPpcStoreCheck(@RequestBody PpcProducePlanScheduleStore store) throws Exception {
        Map map = storeService.insertPpcStoreCheck(store);
        return new Result(ResultCode.SUCCESS,map);
    }

    @PostMapping("/insertPpcStoreTemp")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产入库单暂存", httpMethod = "POST")
    public Result insertPpcStoreTemp(@RequestBody PpcProducePlanScheduleStore store) throws Exception {
        storeService.insertPpcStoreTemp(store);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/updatePpcStore")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产入库单更新", httpMethod = "POST")
    public Result updatePpcStore(@RequestBody PpcProducePlanScheduleStore store) throws Exception {
        storeService.updatePpcStore(store);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/updatePpcStoreTemp")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "生产入库单更新暂存", httpMethod = "POST")
    public Result updatePpcStoreTemp(@RequestBody PpcProducePlanScheduleStore store) throws Exception {
        storeService.updatePpcStoreTemp(store);
        return new Result(ResultCode.SUCCESS);
    }



    @GetMapping("/reversePpcStore")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "根据生产入库单id反审核", httpMethod = "GET")
    public Result reversePpcStore(@ApiParam("生产入库单id") @RequestParam("id")  String id) throws Exception {
        storeService.reversePpcStore(id);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/reversePpcStoreByNo")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "根据生产入库单号反审核", httpMethod = "POST")
    public Result reversePpcStoreByNo(@ApiParam("生产入库单号") @RequestParam("produceStoreNo")  String produceStoreNo) throws Exception {
        storeService.reversePpcStoreByNo(produceStoreNo);
        return new Result(ResultCode.SUCCESS);
    }


    @GetMapping("/deleteById")
    @Transactional(rollbackFor = Exception.class)
    @ApiOperation(value = "根据生产入库单id删除生产入库单", httpMethod = "GET")
    public Result deleteById(@ApiParam("生产入库单id") @RequestParam("id")  String id) throws Exception {
        storeService.deleteByPrimaryKey(id);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/updatePlanStoreStatus")
    @Transactional(rollbackFor = Exception.class)
    @ApiIgnore
    @ApiOperation(value = "废弃", httpMethod = "GET")
    public Result updatePlanStoreStatus(@RequestParam("productionNo") String productionNo) throws Exception {
        storeService.updatePlanStoreStatus(productionNo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 接收仓库入库结果
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "接收仓库入库结果，回写入库单状态", httpMethod = "POST")
    @PostMapping("/updateStatus")
    @Transactional(rollbackFor = Exception.class)
    public Result updateStatus(@RequestBody List<PlanScheduleStoreVo> vo) throws Exception{
        ArrayList<Resume> resumeList = new ArrayList<>();
        List<WmsOutboundItemVo> wmsOutboundItemVos = storeService.updateStatus(vo,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS,wmsOutboundItemVos);
    }

    /**
     * 接收仓库入库结果
     * @return
     * @throws Exception
     */
    @ApiOperation(value = "仓库回滚通知ppc回滚入库单状态", httpMethod = "POST")
    @PostMapping("/updateStatusBack")
    @Transactional(rollbackFor = Exception.class)
    public Result updateStatusBack(@RequestBody List<PlanScheduleStoreVo> vo) throws Exception{
        storeService.updateStatusBack(vo);
        return new Result(ResultCode.SUCCESS);
    }

    @ApiOperation(value = "接收仓库入库取消结果",  httpMethod = "POST")
    @PostMapping("/cancelResult")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelResult(@RequestBody PlanScheduleStoreVo vo) throws Exception {
        storeService.cancelResult(vo);
        return new Result(ResultCode.SUCCESS);
    }
    @ApiOperation(value = "QMS入库质检结束后回调生产接口", httpMethod = "POST")
    @PostMapping("/resultIsQms")
    @Transactional(rollbackFor = Exception.class)
    public Result resultIsQms(@RequestBody PlanScheduleStoreVo vo) throws Exception{
        storeService.resultIsQms(vo);
        return new Result(ResultCode.SUCCESS);
    }
    @GetMapping("/getApplyQty")
    @ApiOperation(value = "查询生产入库申请中数量",httpMethod = "GET")
    public Result getApplyQty(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo,@ApiParam("物料编码")  @RequestParam(required = false, value = "materialCode") String materialCode,@ApiParam("派工单号")  @RequestParam(required = false, value = "woNo") String woNo){
        return new Result(ResultCode.SUCCESS,storeService.getApplyQty(productionNo,materialCode,woNo));
    }
    @GetMapping("/getReallyInputQty")
    @ApiOperation(value = "查询生产入库已入库数量", httpMethod = "GET")
    public Result getReallyInputQty(@ApiParam("排产单号") @RequestParam("productionNo") String productionNo,@ApiParam("物料编码") @RequestParam(required = false, value = "materialCode") String materialCode,@ApiParam("派工单号") @RequestParam(required = false, value = "woNo") String woNo) throws Exception {
        BigDecimal reallyInputQty = storeService.getReallyInputQty(productionNo, materialCode, woNo);
//        WmsStorageInBillItem wmsStorageInBillItem = feignService.findByParentCodeAndMaterialCode(productionNo, materialCode);
//        BigDecimal inventoryQty = new BigDecimal("0");
//        if (wmsStorageInBillItem != null){
//            inventoryQty = wmsStorageInBillItem.getInventoryQty();
//        }
        return new Result(ResultCode.SUCCESS,reallyInputQty);
    }


    @GetMapping("/isNeedBinCode")
    @ApiOperation(value = "生产入库是否需要库位", httpMethod = "GET")
    public Result isNeedBinCode() throws Exception {
        return new Result(ResultCode.SUCCESS,storeService.isNeedBinCode());
    }

    @GetMapping("/isNeedBinCodeByH5")
    @ApiOperation(value = "生产入库是否需要库位（移动端）", httpMethod = "GET")
    public Result isNeedBinCodeByH5(@ApiParam("派工单号") @RequestParam("woNo") String woNo,@ApiParam("工序编码") @RequestParam("processCode") String processCode) throws Exception {
        return new Result(ResultCode.SUCCESS,storeService.isNeedBinCodeByH5(woNo,processCode));
    }


    @PostMapping("/deApproveStore")
    @ApiOperation(value = "批量反审核生产入库单", httpMethod = "POST")
    public Result deApproveStore(@ApiParam("生产入库单号集合")  @RequestBody List<Map<String ,String>> produceStoreNoList) throws Exception {
        ArrayList<Resume> resumeList = new ArrayList<>();
        storeService.deApproveStore(produceStoreNoList,resumeList);
        feignService.saveResume(resumeList);
        return new Result(ResultCode.SUCCESS);
    }

    @GetMapping("/getSumInputQtyByProductionNo")
    @ApiOperation(value = "根据排产单号获取报工入库数量", httpMethod = "GET")
    public Result getSumInputQtyByProductionNo(@ApiParam("排产单号")  @RequestParam("productionNo") String productionNo) throws Exception {
        return new Result(ResultCode.SUCCESS,storeService.getSumInputQtyByProductionNo(productionNo));
    }

    @GetMapping("/findPlusIdByReceiptCode")
    @ApiOperation(value = "根据入库关联单号查询plusId 和 plusDetailId", httpMethod = "GET")
    public Result findPlusIdByReceiptCode(@ApiParam("入库关联单号")  @RequestParam("receiptCode") String receiptCode) throws Exception {
        return new Result(ResultCode.SUCCESS, storeService.findPlusIdByReceiptCode(receiptCode));
    }



}
