package com.imes.qms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.qms.po.QmsWorkInspectDetail;
import com.imes.domain.entities.query.template.qms.QmsWorkInspectDetailSearchVo;
import io.swagger.models.auth.In;

import java.util.List;

/**
 * 异常提报单-主表-志特(QmsWorkInspectDetail)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-13 09:46:16
 */
public interface QmsWorkInspectDetailService extends IService<QmsWorkInspectDetail> {
    List<QmsWorkInspectDetailSearchVo> queryList(QmsWorkInspectDetailSearchVo vo) throws Exception;

    String insert(QmsWorkInspectDetail qmsWorkInspectDetail) throws Exception;

    Integer update(QmsWorkInspectDetail qmsWorkInspectDetail) throws Exception;

    Integer batchDelete(List<String> idList) throws Exception;

    List<QmsWorkInspectDetail> queryByCond(QmsWorkInspectDetail qmsWorkInspectDetail);

    void batchRepalceInto(List<QmsWorkInspectDetail> list);

    List<QmsWorkInspectDetail> queryByInspectList(List<String> list);

    QmsWorkInspectDetail getDetailByNoAndLid(String inspectNo  , String lid);

    List<QmsWorkInspectDetail> getByInspectNoList(List<String> qualityNoList);

    void updateStatus(String status);

    void deleteByNoList(List<String> noList);
}
