package com.imes.qms.service;


import com.imes.domain.entities.qms.po.TourInspection;
import com.imes.domain.entities.qms.po.TourTask;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewVo;
import com.imes.domain.entities.query.template.qms.GetTourListVo;
import org.springframework.stereotype.Service;
import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;

@Service
public interface TourInspectionService {

    void createToutTask(List<TourTask> task) throws Exception;

    ProcessInspectionItemsVo getInspectionItemsStd(String routeCode, String processCode, String bomCode, String id, int batchNum) throws Exception;

    List<GetTourListVo> getList(GetTourListVo vo) throws Exception;

    void saveInspectionDetailDatas(TourInspectionInfoVo data) throws Exception;

    void supportDatas(TourInspectionInfoVo data) throws Exception;

    TourInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    void deleteTask(List<String> ids);

    List<TourInspectionInfoVo> queryDetailList(String id);

    TourInspection getInspectionById(String id);

    List<TourInspectionInfoVo> queryByActivtiId(String activitiId);

    boolean getDelegate() throws Exception;

    Object delegateUserList() throws Exception;

    boolean delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag) throws Exception;

    boolean cancelClaim(String id);

    void unsubmission(String id);

    Object queryInfoByWfNo(String pcNo, String processCode);


    List<TourResultVo> queryTourInfoByVo(TourInforVo vo) throws Exception;
}
