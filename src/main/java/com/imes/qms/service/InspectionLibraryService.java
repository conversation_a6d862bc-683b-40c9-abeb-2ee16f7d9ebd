package com.imes.qms.service;

import com.imes.domain.entities.qms.po.QmsInspectionLibrary;
import com.imes.domain.entities.qms.vo.ItemCodeAndNameVo;
import com.imes.domain.entities.query.template.qms.GetLibraryVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface InspectionLibraryService {

    void createInspection(QmsInspectionLibrary library) throws Exception;

    List<QmsInspectionLibrary> getList(GetLibraryVo vo);

    List<ItemCodeAndNameVo> getItemCodeAndName(String childCode,String childName);

    void deleteInspection(List<String> ids);
}
