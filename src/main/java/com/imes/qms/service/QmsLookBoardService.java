package com.imes.qms.service;

import com.imes.domain.entities.qms.po.ProcessTask;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> <EMAIL>
 * @Description:    看板业务层
 * @date 2023/11/17 13:40
 **/
public interface QmsLookBoardService {

    List<Map<String, Object>> overview(String schema) throws Exception;

    List<Map<String, Object>> processUnusual(Map<String, Object> schema) throws Exception;

}
