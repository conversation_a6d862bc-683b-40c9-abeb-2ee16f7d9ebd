package com.imes.qms.service;

import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.vo.InBillInspectionVo;
import com.imes.domain.entities.qms.vo.StandardInspectionSaveVo;
import com.imes.domain.entities.qms.vo.StandardInspectionVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface StandardInspectionService {

    List<StandardInspectionVo> queryList(Map map);

     void createNewItems(List<InspectionItems> items);

    List<InspectionItems> queryItemList(String type);

    void saveStandardDatas(StandardInspectionSaveVo datas);

    void deleteItems(List<String> ids);

    void deleteStandardList(List<String> ids);

    void updateStandardDatas(StandardInspectionSaveVo datas);

    void updateItems(List<InspectionItems> items);
}
