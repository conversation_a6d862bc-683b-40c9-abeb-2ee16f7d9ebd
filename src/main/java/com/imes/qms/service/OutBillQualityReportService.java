package com.imes.qms.service;

import com.imes.domain.entities.qms.po.OutBillInspection;
import com.imes.domain.entities.qms.po.OutBillQualityReportDetail;
import com.imes.domain.entities.qms.vo.OutBillQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsOutBillInspectionReportVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface OutBillQualityReportService {

    List<OutBillQualityReportVO> getInspectionList(Map map);

    List<OutBillQualityReportDetail> getInspectionDetailList(Map map) throws Exception;

    List<OutBillInspection> getDetailInfo(String id);

    List<OutBillQualityReportVO> getInspectionListNew(QmsOutBillInspectionReportVo vo);
}
