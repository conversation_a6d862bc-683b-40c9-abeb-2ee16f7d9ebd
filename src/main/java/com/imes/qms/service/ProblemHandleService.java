package com.imes.qms.service;

import com.imes.domain.entities.qms.po.ProblemExecution;
import com.imes.domain.entities.qms.po.ProblemHandleDetail;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.ProblemHandleQueryListVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface ProblemHandleService {

    List<ProblemHandleVo> queryMainList(Map map) throws Exception;

    List<ProblemHandleVo> queryList(ProblemHandleQueryListVo vo);

    void saveDatas(ProblemHandleVo data);

    void updateStatus(Map map);

    List<ProblemExecution> queryExecutionList(Map map) throws Exception;

    void saveExecution(ProblemExecution data);

    void updateExecution(ProblemExecution data);

    void deleteExecution(String id);

    Map<String,Object> getTaskList(String parentId, String inspectionType) throws Exception;

    List<Map> queryDepartMentList();

    Map getDetailInfo(Map map);

    void insertSelectiveDetail(ProblemHandleDetail detail);

//    TreeMap<String,List<ProblemHandleDetail>> queryDetailList(Map map);

    void buttonSupport(ProblemHandleVo data) throws Exception;

    void buttonReject(ProblemHandleVo data);

    List<Map> getUsers(Map map) throws Exception;

    Object queryDetailList(String id,String inspectionType) throws Exception;

    void changeFinish(String id);
}
