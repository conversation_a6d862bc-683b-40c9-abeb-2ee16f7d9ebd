package com.imes.qms.service;

import com.imes.common.entity.Result;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewForMobileVo;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewVo;
import com.imes.domain.entities.query.template.qms.QmsProcessResultQueryVo;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public interface ProcessInspectionService {

    List<ProcessInspection> queryList(Map<String,Object> map) throws Exception;

    List<InspectionItemsVo> getInspectionByMaterialCode(String materialCode, String inspectionType);

    List<MaterialBuyOrderVo> getMaterialByOrderNo(String orderNo);

    void saveInspectionDetailDatas(ProcessInspectionInfoVo data) throws Exception;

    List<ProcessInspectionInfoVo> queryDetailList(String id);

    List<ProcessInspection>  getPpNoInfo(Map map) throws Exception;

    List<Map> getStationByProcessCode(String processCode);

    ProcessInspectionItemsVo getInspectionItemsByPpNoInfo(Map<String,String> map) throws Exception;

    void updateStatus(String id) throws Exception;

    void updateInspectionDetailData(ProcessInspectionDetail data);

    void supportDatas(ProcessInspectionInfoVo data) throws Exception;

    List<ProcessInspectionInfoVo> queryByActivtiId(String activitiId);

    ProcessInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    List<ProcessTask> getPpNoInfoStd(Map map) throws Exception;

    ProcessInspectionItemsVo getInspectionItemsStd(String routeCode, String processCode,String bomCode,String id,int batchNum) throws Exception;

    ProcessInspectionInfoVo detailList(String id) throws Exception;

    long getProcessReportTotal();

    long getProcessTotal();

    void deleteByWfNo(String workFinishNo);

    List<QmsProcessResultQueryVo> queryListNew(QmsProcessResultQueryVo vo) throws Exception;

    void toDisuse(List<String> wfNos) throws Exception;

    Result getPpcRouteProcessQmsDepartment(String routeCode, String processCode);

    boolean getDelegate() throws Exception;

    boolean delegateConfirmation(String id,String userCode,String userName,String teamCode,String teamName,String addFlag) throws Exception;

    void obsoleteAntiAuditing(String id);

    void passAntiAuditing(String orderId, String inspectionStatus, String status) throws Exception;

    void rejectedAuditing(String orderId, String inspectionStatus, String status);

    void submittedAuditing(String orderId, String inspectionStatus, String status);

    List<GetPpNoInfoNewVo> getPpNoInfoStdNew(GetPpNoInfoNewVo vo) throws Exception;

    List<ProcessTask> queryTaskByWfNo(String wfNo);

    ProcessInspectionItemsVo getInspectionItemsForMore(String routeCode,String processCode,String bomCode,String id,int batchNum) throws Exception;

    void cancelMerge(List<ProcessTask> processTasks);

    ProcessTask getMergeTasks(List<ProcessTask> processTasks) throws Exception;

    List<ProcessTask> queryMergeTaskByid(String id);

    List<ProcessTask> getPpNoInfoNewForMobile(GetPpNoInfoNewForMobileVo vo) throws Exception;

    List<HashMap<String, String>> getProcessCodeAndNameFromTask();

    Object delegateUserList() throws Exception;

    boolean cancelClaim(String orderId);

    List<ProcessInspectionInfoVo> queryInfoByWfNo(List<String> wfNo);

    List<Map<String, Object>> getQmsLookBoardUnusual(Map<String, Object> param) throws Exception;

    List<ProcessResultVo> queryInfoByVo(ProcessInforVo vo) throws Exception;

    void getCorrectionRemind() throws Exception;

    void getManagerRemind() throws Exception;

    void fakeData(String id) throws Exception;

}
