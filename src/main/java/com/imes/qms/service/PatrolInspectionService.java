package com.imes.qms.service;

import com.imes.domain.entities.qms.po.MaterialInspection;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.PatrolInspection;
import com.imes.domain.entities.qms.po.PatrolInspectionDetail;
import com.imes.domain.entities.qms.vo.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface PatrolInspectionService {

    List<PatrolInspection> queryList(Map map);

    void deleteStandardList(List<String> ids);

    List<InspectionItemsVo> getInspectionByMaterialCode(String materialCode, String inspectionType);

    List<MaterialBuyOrderVo> getMaterialByOrderNo(String orderNo);

    void saveInspectionDetailDatas(PatrolInspectionVo data) throws Exception;

    PatrolInspectionVo queryDetailList(String id);

    List<PatrolInspection>  getPpNoInfo(Map map) throws Exception;

    List<Map> getStationByProcessCode(String processCode);

    PatrolItemsInfoVo getInspectionItemsByPpNoInfo(PatrolInspection patrolInspection);

    void updateStatus(String id);

    void updateInspectionDetailData(PatrolInspectionDetail data);
}
