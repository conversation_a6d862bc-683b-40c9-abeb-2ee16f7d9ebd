package com.imes.qms.service;

import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.system.CoDepartmentUser;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface ProcessTaskService {

    void createProcessTask(ProcessTaskVo vo) throws Exception;

    void createBatchProcessTask(List<ProcessTaskVo> list) throws Exception;

    void delegate(String[] ids, String[] userCodes,String[] userNames);

}
