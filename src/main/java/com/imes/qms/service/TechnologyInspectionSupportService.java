package com.imes.qms.service;

import com.imes.domain.entities.qms.po.FirstAndEndInspectionDetail;
import com.imes.domain.entities.qms.po.PatrolInspectionDetail;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.supportMaterialInfoVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Service
public interface TechnologyInspectionSupportService {

    void saveInspectionDatas(TechnologyInspection data) throws Exception;


    List<supportMaterialInfoVo> getMaterialInfo(Map map);

    Map getUserCode() throws Exception;

    Map<String, Object> getInspectionInfo(String ppNo,String pcNo) throws Exception;

    void temporarySave(TechnologyInspection data) throws Exception;

    List<TechnologyInspection> queryList(Map map) throws Exception;

    TreeMap<String,List<FirstAndEndInspectionDetail>> getInspectionDetails(String patrolId);

    void deleteSupport(String id);
}
