package com.imes.qms.service;

import com.imes.domain.entities.qms.po.InBillQualityReportDetail;
import com.imes.domain.entities.qms.vo.InBillQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsInBillInspectionReportVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface InBillQualityReportService {

    List<InBillQualityReportVO> getInspectionList(Map map);

    List<InBillQualityReportDetail> getInspectionDetail(Map map) throws Exception;

    List<InBillQualityReportVO> getInspectionListNew(QmsInBillInspectionReportVo vo);
}
