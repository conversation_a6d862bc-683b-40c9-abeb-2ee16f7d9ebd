package com.imes.qms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imes.domain.entities.ppc.po.PpcProduceOrder;
import com.imes.domain.entities.ppc.vo.PpcProduceOrderAndPlanAztVo;
import com.imes.domain.entities.qms.po.QmsFillCheckNewData;
import com.imes.domain.entities.qms.po.QmsWorkException;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.qms.vo.QmsWorkInspectBulidVo;
import com.imes.domain.entities.qms.vo.QmsWorkInspectOrderStatusVo;
import com.imes.domain.entities.query.template.qms.QmsWorkInspectSearchVo;

import java.util.List;
import java.util.Map;

/**
 * 异常提报单-主表-志特(QmsWorkInspect)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-13 09:45:58
 */
public interface QmsWorkInspectService extends IService<QmsWorkInspect> {
    List<QmsWorkInspectSearchVo> queryList(QmsWorkInspectSearchVo vo) throws Exception;

    String insert(QmsWorkInspect qmsWorkInspect) throws Exception;

    Integer update(QmsWorkInspect qmsWorkInspect) throws Exception;

    Integer batchDelete(List<String> idList) throws Exception;

    List<QmsWorkInspect> queryByCond(QmsWorkInspect qmsWorkInspect);

    List<QmsWorkInspect> queryByOrderIdList(List<String> list);

    void batchRepalceInto(List<QmsWorkInspect> list);

    List<QmsWorkInspect>  searchWorkOrdersByKeyWord(String keyWord);

    void buildInspectReport( List<QmsWorkInspect> qmsWorkInspectList);

    Map<String , Object> getStatuNumInfor(String keyWord , String factoryLineName);

    Map<String , Object> getScanStatuNumInfor(String keyWord , String factoryLineName);

    List<QmsWorkInspect> searchHandleWorkOrders(String keyWord, String status , String factoryLineName);

    List<QmsWorkInspect> searchWorkOrdersForView(String keyWord, String status , String factoryLineName,String startDate  , String endDate) throws Exception;

    void cancelCheckReport(Map<String , Object> map);

    void saveCheckReport(List<String> idList);

    void submitCheckReport(List<String> idList);

    void buildException(QmsWorkInspectBulidVo vo);

    void cancelSubmit(List<QmsWorkInspect> inspects);


    QmsWorkException getExceptionInfor(String inspectNo , String lineNo);

    QmsWorkInspect getDetialInfor(String id);

    void fillCheck(List<String> idList);

    List<PpcProduceOrder> icsSearchWorkOrders(String keyWord);

    PpcProduceOrderAndPlanAztVo searchBySnCode(String code);

    void snFillCheck(String id);

    QmsWorkInspect getByInspectNo(String inspectNo);

    List<QmsWorkInspectOrderStatusVo> getInspectOrderStatus(List<String> orderIdList);

    List<String> getExceptionIdList(String inspectNo);

    List<QmsWorkInspect> scanHandleWorkOrders(String keyWord, String status, String factoryLineName);

    List<QmsWorkInspect> queryUnCalcEndWorkInspect();

    void updateInspectByCalc(QmsWorkInspect qmsWorkInspect);

    void fillCheckNew(QmsFillCheckNewData qmsFillCheckNewData);

    void updateInspectReCalc(List<String> reCalcWfNoList);

    boolean checkIsCalcByQualityNoList(List<String> reCalcWfNoList);

    String inspectModifySave(QmsWorkInspectSearchVo qmsWorkInspect) throws Exception;

    String inspectModifyCommit(List<String> idList);

    String inspectModifyEndBack(List<String> idList);

    void inspectModifyCancelBack(List<String> idList);
}
