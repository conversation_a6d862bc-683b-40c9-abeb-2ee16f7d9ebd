package com.imes.qms.service;

import com.imes.domain.entities.qms.po.QmsRouteInspectionDetail;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.QmsRouteInspectionDetailVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface TechnologyInspectionPublishService {


    List<TechnologyInspection> getInspectionPlanList(Map map) ;

    void updatePublishStatus(TechnologyInspection inspection) throws Exception;

    List<QmsRouteInspectionDetailVo> getInspectionItemDetail(String bomCode,String bomVer,String processCode);
}
