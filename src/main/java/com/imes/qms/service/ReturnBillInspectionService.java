package com.imes.qms.service;

import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface ReturnBillInspectionService {

    List<ReturnInspectionListVo> queryList(Map map);

    void deleteStandardList(List<String> ids);

    ItemInfoVo getInspectionByMaterialCode(String materialCode, String inspectionType);

    List<MaterialBuyOrderVo> getMaterialByOrderNo(Map map) throws Exception;

    void saveInspectionDetailDatas(ReturnBillInspectionInfoVo data) throws Exception;

    List<ReturnBillInspectionInfoVo> queryDetailList(String id);

    List<WmsArrivalOrder> findAoByFilter(Map map);

    void updateInspectionDetailData(ReturnBillInspectionDetail datas);

    void updateStatus(String id) throws Exception;

    void supportDatas(ReturnBillInspectionInfoVo data) throws Exception;

    List<ReturnBillInspectionInfoVo> queryByActivtiId(String activitiId);

    ReturnBillInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    ReturnBillInspectionInfoVo detailList(String parentId) throws Exception;

    void updatePicStatus(String id, String picStatus);
}
