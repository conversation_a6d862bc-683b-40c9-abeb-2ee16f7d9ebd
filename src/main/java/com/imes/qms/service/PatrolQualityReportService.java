package com.imes.qms.service;

import com.imes.domain.entities.qms.po.PatrolQualityReportDetail;
import com.imes.domain.entities.qms.po.ProcessBadCount;
import com.imes.domain.entities.qms.po.ProcessQualityReportDetail;
import com.imes.domain.entities.qms.vo.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface PatrolQualityReportService {

    List<PatrolQualityReportVO> getInspectionList(Map map) throws Exception;

    List<PatrolQualityReportDetail> getInspectionDetail(Map map);

    List<PatrolQualityBadCountReportVO> getBadCountDetail(Map map) throws Exception;

    Map<String,List<ProcessBadCount>> getBadCountClassify(String inspectionCode,String processCode);

    List<ProcessBadCountsQualityReportVO> getInspectionByBadCounts(Map map) throws Exception;

    List<Map> getBadCountDetails(String badCode,
                              String materialCode,String materialName,
                              String processCode,String processName,
                              String specification) throws Exception;
}
