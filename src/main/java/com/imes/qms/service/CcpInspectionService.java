package com.imes.qms.service;

import com.imes.domain.entities.qms.po.CcpInspection;
import com.imes.domain.entities.qms.po.CcpTask;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetCcpListVo;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;

@Service
public interface CcpInspectionService {

    void saveTask(List<CcpTaskAndInspVo> task) throws Exception;

    CcpInspectionItemsVo getInspectionItemsStd(String routeCode, String processCode, String bomCode, String id, int batchNum) throws Exception;

    List<GetCcpListVo> getList(GetCcpListVo vo) throws Exception;

    void saveInspectionDetailDatas(CcpInspectionInfoVo data) throws Exception;

    CcpInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    CcpInspection getInspectionById(String id);

    List<CcpInspectionInfoVo> queryDetailList(String id) throws Exception;

    boolean getDelegate() throws Exception;

    Object delegateUserList() throws Exception;

    boolean delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag);

    boolean cancelClaim(String id);

    List<CcpInspectionInfoVo> queryByActivtiId(String activitiId) throws Exception;

    List<HashMap<String,String>> selectInsection(String routeCode, String processCode) throws Exception;

    void deleteTask(List<String> ids);

    void supportDatas(CcpInspectionInfoVo data) throws Exception;

    void getTaskRemind() throws Exception;

    void getCorrectionRemind() throws Exception;

    void getManagerRemind() throws Exception;

    void getAuditOutTime() throws Exception;
}
