package com.imes.qms.service;

import com.imes.domain.entities.qms.po.ProcessBadCount;
import com.imes.domain.entities.qms.po.ProcessQualityReportDetail;
import com.imes.domain.entities.qms.vo.ProcessBadCountsQualityReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityBadCountReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsProcessInspectionReportListVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface ProcessQualityReportService {

    List<ProcessQualityReportVO> getInspectionList(Map map) throws Exception;

    List<ProcessQualityReportDetail> getInspectionDetail(Map map);

    List<ProcessQualityBadCountReportVO> getBadCountDetail(Map map) throws Exception;

    Map<String,List<ProcessBadCount>> getBadCountClassify(String inspectionCode,String wfNo,String processCode);

    List<ProcessBadCountsQualityReportVO> getInspectionByBadCounts(Map map) throws Exception;

    List<Map> getBadCountDetails(String badCode,
                              String materialCode,String materialName,
                              String processCode,String processName,
                              String specification) throws Exception;

    List<ProcessQualityReportVO> getInspectionListNew(QmsProcessInspectionReportListVo vo) throws Exception;
}
