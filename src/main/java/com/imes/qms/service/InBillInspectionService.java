package com.imes.qms.service;

import com.imes.domain.entities.qms.po.InBillInspectionDetail;
import com.imes.domain.entities.qms.po.InBillTask;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.vo.InBillInspectionInfoVo;
import com.imes.domain.entities.qms.vo.InBillInspectionListVo;
import com.imes.domain.entities.qms.vo.InspectionDetailForInBillVo;
import com.imes.domain.entities.qms.vo.ItemInfoVo;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetFinishVo;
//import com.imes.domain.entities.query.template.qms.QmsGetInBillListVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface InBillInspectionService {

    List<InBillInspectionListVo> queryList(Map map);

//    Object getItemsByParams(String ppNo, String inspectionType, String productName);
//
//    void saveInspectionDatas(InBillInspectionSaveVo datas);
//
//    void updateInspectionDatas(InBillInspectionSaveVo datas);
//
//    void deleteStandardList(List<String> ids);


    List<InBillInspectionInfoVo> queryDetailList(String id);

    void saveInspectionDetailDatas(InBillInspectionInfoVo data) throws Exception;

    List<GetFinishVo> getWorkFinish(GetFinishVo vo) throws Exception;

    void updateInspectionDetailDatas(InBillInspectionDetail data);

    void updateStatus(String id) throws Exception;

    void supportDatas(InBillInspectionInfoVo data) throws Exception;

    List<InBillInspectionInfoVo> queryByActivtiId(String activitiId);

    InBillInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    InBillInspectionInfoVo detailList(String parentId) throws Exception;

    void updatePicStatus(String id, String picStatus);

    void cancelMergeInspection(List<String> ids);

    long getInBillReportTotal();

    long getInBillTotal();

    boolean deleteInspection(List<String> produceStoreNo);

    void unsubmission(String orderId);

    List<InspectionItems> queryInBillInspectionItems(String materialCode);

    List<InspectionDetailForInBillVo> queryInBillInspectionItemsDetailList(String itemCode);

    Map isInspection(String materialCode);

    InspectionDataProtect getInBillInspectionSequence(String materialCode);

    ItemInfoVo getInspectionByMaterialCode(String materialCode,String customerCode, String inspectionType,int batchNum,String orderId);

    // List<InBillInspectionListVo> queryListNew(QmsGetInBillListVo vo);
    List<InBillResultVo> queryInBillInfoByVo(InBillInforVo vo);

    // List<InBillInspectionListVo> queryListNew(QmsGetInBillListVo vo);
}
