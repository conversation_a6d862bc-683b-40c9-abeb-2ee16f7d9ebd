package com.imes.qms.service;

import com.alibaba.fastjson.JSONObject;
import com.imes.common.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@FeignClient(value = "IMES-FTS")
public interface FtsInterfaceService {
    @PostMapping(value = "/api/fts/file/upload/single", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    Result singleUpload(@RequestPart(value = "file") MultipartFile file, @RequestParam("businessCode") String businessCode, @RequestParam(defaultValue = "") String sourceModule, @RequestParam(defaultValue = "") String bizRelationCode);

    @RequestMapping(value = "/api/fts/file/previewFile", method = RequestMethod.GET)
    Result previewFile(@RequestParam("id") String id);

    @PostMapping("/api/fts/file/upload/base64")
    public Result fileUpload(@RequestBody JSONObject o);

    @GetMapping("api/fts/file/deleteFile")
    public Result deleteFile(@RequestParam("id") String id);

    @RequestMapping(value = "/api/fts/file/queryFile", method = RequestMethod.GET)
    Result getFileListByBusinessCode(@RequestParam("businessCode") String businessCode);

    @GetMapping("/api/fts/file/deleteByBusinessCode")
    Result deleteByBusinessCode(@RequestParam("businessCode") String businessCode);

    @RequestMapping(value = "/api/fts/file/getFileById", method = RequestMethod.GET)
    Result getFileById(@RequestParam("id") String id);

    @PostMapping(value = "/api/fts/file/upload/multiUpload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    Result multiUpload(@RequestPart(value = "file") MultipartFile[] file, @RequestParam("businessCode") String businessCode, @RequestParam(defaultValue = "") String sourceModule, @RequestParam(defaultValue = "") String bizRelationCode);

    @PostMapping("/api/fts/file/batch/base64")
    Result batchUpload(@RequestBody JSONObject o);


    @PostMapping("/api/fts/file/getBase64SysFileByPreUrlList")
    Result getBase64SysFileByPreUrlList(@RequestBody List<String> preUrls);

}

