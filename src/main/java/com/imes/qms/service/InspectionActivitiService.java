package com.imes.qms.service;

import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.ProcessInspection;
import com.imes.domain.entities.qms.vo.ItemInfoVo;
import com.imes.domain.entities.qms.vo.MaterialBuyOrderVo;
import com.imes.domain.entities.qms.vo.MaterialInspectionInfoVo;
import com.imes.domain.entities.qms.vo.MaterialInspectionListVo;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface InspectionActivitiService {

    void qmsCheckMaterial(String activitiId, String result, String activitiProcessInstanceId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType) throws Exception;

    void qmsCheckProcess(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork,
                         String refuseId, String remark, String taskType,String wfNo,String processCode,String orderId) throws Exception;

    void sendDataToPPC(ProcessInspection processInspection) throws Exception;

    void qmsCheckInBill(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType) throws Exception;

    void qmsCheckOutBill(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType) throws Exception;

    void qmsCheckReturn(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType) throws Exception;

    void qmsCheckFirstFinal(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType, String wfNo, String processCode, String orderId) throws Exception;

    void qmsCheckPatrol(String activitiId, String result, String taskId, String reviewOperatorName, String reviewOperatorWork, String refuseId, String remark, String taskType, String wfNo, String processCode, String orderId,String planCode) throws Exception;
}
