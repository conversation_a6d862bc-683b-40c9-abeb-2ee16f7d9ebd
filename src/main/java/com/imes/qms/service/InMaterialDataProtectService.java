package com.imes.qms.service;

import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.AqlPreviewVo;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionDataProtectImportVo;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionItemsImportVo;
import com.imes.domain.entities.system.base.SysFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

@Service
public interface InMaterialDataProtectService {
    List<InspectionDataProtect> queryList(Map map);
    InspectionDataProtect queryByMaterialAndInspectionType(String materialCode ,String inspectionType);

    String saveMaterialDatas(List<InspectionDataProtect> list);

    void saveInspectionRatio(List<InspectionDataProtect> ratios);

    List<InspectionItems> queryItemList(Map map);

    Map<String,String> saveInspectionItem(InspectionItems item) throws Exception;

    void updateItems(InspectionItems item);

    void deleteItem(String itemCode);

    void saveInspectionDetailItems(List<InspectionItemDetail> items);

    List<InspectionItemDetail> queryItemDetailList(Map map);

    void updateItemDetails(List<InspectionItemDetail> items);

    void deleteMaterialProtectData(String id);

    void deleteItemDetails(List<String> ids);

    SysFile uploadProtectFile(MultipartFile uploadFile) throws Exception;

    SysFile uploadMultiFile(MultipartFile[] uploadFile) throws Exception;

    void insertProtectFile(QmsProtectFile qmsProtectFile) throws Exception;

    Map<String, Object> findProtectFile(QmsProtectFile qmsProtectFile) throws Exception;

    void deleteProtectFileById(String id) throws Exception;

    String getInspectionCode() throws Exception;

    String getTempNo(String code) throws Exception;

    Map<String, Object> getRatesAndRatios(Map map) throws Exception;

    void checkMaterialImport(List<List<Object>> importList, int i) throws Exception;

    int importMaterialData(List<List<Object>> importList) throws Exception;

//    void checkVerificationImport(List<List<Object>> importList, int startRow);
//
//    int importVerificationData(List<List<Object>> importList) throws Exception;


    List<QmsInspectionDataProtectImportVo> checkVerificationImport(List<QmsInspectionDataProtectImportVo> importList) throws Exception;

    List<QmsInspectionDataProtectImportVo> importVerificationData(List<QmsInspectionDataProtectImportVo> importList) throws Exception;


//    void checkMaterialInspection(List<List<Object>> importList, int startRow);
//
//    int importMaterialInspection(List<List<Object>> importList) throws Exception;

    List<QmsInspectionItemsImportVo> checkMaterialInspection(List<QmsInspectionItemsImportVo> importList) throws Exception;

    List<QmsInspectionItemsImportVo> importMaterialInspection(List<QmsInspectionItemsImportVo> importList) throws Exception;


    void insertChildFile(QmsProtectFile qmsProtectFile,String inspectionType) throws Exception ;

    List<Map<String, Object>> findChildFile(QmsProtectFile qmsProtectFile) throws Exception ;

    void deleteByFileIds(List<String> fileIds,String inspectionType) throws Exception;

    Map<String, Object> getTempItems() throws Exception;

    Map<String, Object> getTempDetailItemId() throws Exception;

    Map<String, Object> findTechnologyFile(String processCode, String itemCode);

    void deleteByMaterialCode(String materialCode);

    void deleteByMaterialCodeIn(List<String> materialCodeList);

    Boolean needInspection(String materialCode, String inspectionType);

    List<Map<String, Object>> getEmpyesByDepartId(String id) throws Exception;

    Map<String, Object> delegateInfo(String teamCode) throws Exception;

    List<AqlPreviewVo> aqlPreview(String inspectType, String inspectLevel, String aqlCode);

    AqlInfo aqlInfo(AqlInfo infoMap);

    InspectionDataProtect protect(int batchNum,String materialCode, String inspectionType);

    int inspectionNum(int batchNum,String materialCode,String inspectionType);

    int getNum(String inspectType,String inspectLevel,String inspectionRatio,int batchNum);

    int inspectionNumForMaterial(int batchNum,String materialCode, String inspectionType);

    int inspectionNumForInBill(int batchNum,String materialCode, String inspectionType);

}
