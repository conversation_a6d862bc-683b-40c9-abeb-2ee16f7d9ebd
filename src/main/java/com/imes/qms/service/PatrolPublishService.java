package com.imes.qms.service;

import com.imes.common.entity.Result;
import com.imes.domain.entities.ppc.po.PpcRouteProcessQmsDepartment;
import com.imes.domain.entities.qms.po.PatrolPublish;
import com.imes.domain.entities.system.CoDepartment;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface PatrolPublishService {

    List<PatrolPublish> queryList(Map map);

    void publish(PatrolPublish patrolPublish) throws Exception;

    List<Map<String, Object>> inspectedDapart() throws Exception;

    List<PpcRouteProcessQmsDepartment> inspectionDapart(List<String> departCodes) throws Exception;

    void rePublish(PatrolPublish patrolPublish);

    void finish(PatrolPublish patrolPublish) throws Exception;

    void delegate(String[] ids, String[] userCodes, String[] userNames);
}
