package com.imes.qms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imes.domain.entities.ppc.po.PpcProduceOrder;
import com.imes.domain.entities.ppc.po.PpcRouteLineZtProcessDetail;
import com.imes.domain.entities.ppc.po.PpcWorkFinishMain;
import com.imes.domain.entities.ppc.vo.PpcProduceOrderAndPlanAztVo;
import com.imes.domain.entities.qms.po.QmsWorkException;
import com.imes.domain.entities.qms.vo.QmsExceptionInfoVo;
import com.imes.domain.entities.qms.vo.QmsWorkExceptionAddDataVo;
import com.imes.domain.entities.qms.vo.WeeklyProcessQualityAndProduceAreaReportVo;
import com.imes.domain.entities.query.template.qms.QmsWorkExceptionReportSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkExceptionSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkProductionCentreDesignSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkProductionCentreSearchVo;
import com.imes.domain.entities.system.base.SysFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 异常提报单-主表-志特(QmsWorkException)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-13 09:45:10
 */
public interface QmsWorkExceptionService extends IService<QmsWorkException> {
    List<QmsWorkExceptionSearchVo> queryList(QmsWorkExceptionSearchVo vo) throws Exception;

    String insert(QmsWorkException qmsWorkException) throws Exception;

    Integer update(QmsWorkException qmsWorkException) throws Exception;

    Integer batchDelete(List<String> idList) throws Exception;

    List<QmsWorkException> queryByCond(QmsWorkException qmsWorkException);

    void batchRepalceInto(List<QmsWorkException> list);

    List<PpcProduceOrder> searchWorkOrdersByKeyWord(String keyWord,String projectName,String orderNo);

    List<QmsWorkException> buildExceptionReport(List<QmsWorkException> qmsWorkExceptionList);

    List<Map<String , Object>>  queryByReportUser(QmsWorkException qmsWorkException);

    void cancelBulid(Map<String, Object> map);

    QmsWorkException queryHandleInFor(String id);

    void handleExcepetion(QmsWorkException exception);

    List<QmsWorkExceptionAddDataVo> searchAddWorkOrders(QmsWorkException qmsWorkException);

    List<QmsWorkException> getIngInfor(QmsExceptionInfoVo vo);

    void cancelException(QmsWorkException vo) throws Exception;

    void saveResponsibleInfor(QmsWorkException qmsWorkException);

    List<QmsWorkException>  queryByInspectNo(String inspectNo,String lineNo);

    List<QmsWorkException>  queryByInspectNo(String inspectNo);
    List<QmsWorkException>  queryByInspectNoList(List<String> inspectNoList);

    List<QmsWorkExceptionReportSearchVo> queryReportList(QmsWorkExceptionReportSearchVo vo);

    List<QmsWorkProductionCentreSearchVo> queryProductionCentreList(QmsWorkProductionCentreSearchVo vo);

    List<QmsWorkProductionCentreDesignSearchVo> queryProductionCentreListForDesign(QmsWorkProductionCentreDesignSearchVo vo);

    List<QmsWorkException> queryUnHandleException(String orderId);

    void submitResponsibleInfor(String id);

    PpcProduceOrderAndPlanAztVo searchBySnCode(String code);

    QmsWorkException queryOrderIdAndLineNo(String orderId , String lineNo);

    List<PpcRouteLineZtProcessDetail> getFixProcess(String orderId);

    List<PpcWorkFinishMain> getWorkFinishByOrderIdAndLine(String orderId , Integer lineNo);

    List<QmsWorkException> checkHaveException(List<QmsWorkException> list);

    WeeklyProcessQualityAndProduceAreaReportVo queryWeeklyProcessQualityData(WeeklyProcessQualityAndProduceAreaReportVo query);

    SysFile export(HttpServletRequest request, HttpServletResponse response, String dateStr, boolean isPreview);

    void exporQmsWorkProductionCentre(HttpServletRequest request, HttpServletResponse response, List<QmsWorkProductionCentreSearchVo> qmsWorkProductionCentreSearchVos);
}
