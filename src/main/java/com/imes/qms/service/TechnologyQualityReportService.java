package com.imes.qms.service;

import com.imes.domain.entities.qms.po.ProcessBadCount;
import com.imes.domain.entities.qms.po.ProcessQualityReportDetail;
import com.imes.domain.entities.qms.po.TechnologyQualityReportDetail;
import com.imes.domain.entities.qms.vo.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface TechnologyQualityReportService {

    List<FirstFinalQualityReportVO> getFirstEndInspectionList(Map map) throws Exception;

    List<TechnologyQualityReportDetail> getInspectionDetailList(Map map);

    List<TechnologyQualityReportVO> getPatrolInspectionList(Map map);

    List<TechnologyQualityReportDetail> getPatrolDetails(Map map);

    List<TechnologyQualityReportDetail> getInspectionDetail(Map map);

    List<FirstFinalQualityBadCountReportVO> getBadCountDetail(Map map) throws Exception;

    Map<String,List<ProcessBadCount>> getBadCountClassify(String processCode,String inspectionCode);

}
