package com.imes.qms.service;

import com.imes.domain.entities.qms.po.MaterialQualityReport;
import com.imes.domain.entities.qms.po.MaterialQualityReportDetail;
import com.imes.domain.entities.qms.vo.MaterialBuyOrderVo;
import com.imes.domain.entities.query.template.qms.GetInspectionListVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface MaterialQualityReportService {

    List<MaterialQualityReport> getInspectionList(Map map);

    List<MaterialQualityReportDetail> getInspectionDetailList(Map map) throws Exception;

    List<MaterialBuyOrderVo> getDetailInfo(String id);

    List<MaterialQualityReport> getInspectionListNew(GetInspectionListVo vo);
}
