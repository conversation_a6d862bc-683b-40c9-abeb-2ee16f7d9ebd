package com.imes.qms.service;

import com.imes.domain.entities.ppc.po.PpcWorkOrder;
import com.imes.domain.entities.ppc.vo.WorkOderVo;
import com.imes.domain.entities.qms.po.PatrolInspection;
import com.imes.domain.entities.qms.po.PatrolInspectionDetail;
import com.imes.domain.entities.qms.po.PatrolPublish;
import com.imes.domain.entities.qms.vo.*;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface PatrolInspectionNewService {

    List<PatrolInspection> queryList(Map map) throws Exception;

    Map<String,Object> getMaterials(int pageNum,int pageSize,String specification,
                                  String quality,String pcNo,String processName,String materialName,List<String> departCodes) throws Exception;

    List<PatrolPublish> queryTaskList(Map map) throws Exception;

    PatrolItemsInfoVo getInspectionItemsStd(String routeCode, String processCode, String bomCode) throws Exception;

    void saveInspectionDetailDatas(PatrolInspectionNewInfoVo data) throws Exception;

    void supportDatas(PatrolInspectionNewInfoVo data) throws Exception;

    void supportAllDatas(String planCode) throws Exception;

    List<PatrolInspectionNewInfoVo> queryDetailList(String id) throws Exception;

    PatrolInspectionNewInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    List<PatrolInspectionNewInfoVo> queryByActivtiId(String activitiId);

    List<PatrolInspection> queryResultList(Map map);

    void deleteItem(String id);

    PatrolInspectionNewInfoVo queryInspectingDetailList(String id) throws Exception;

    PatrolInspectionNewInfoVo detailList(String id) throws Exception;

    void forcedFinish(PatrolInspection data);

    void saveInspectionTask(PatrolInspectionNewInfoVo data) throws Exception;

    void delegate(String[] ids, String[] userCodes, String[] userNames);

    List<Map<String,Object>> savePlusDetails(PatrolInspectionNewInfoVo data) throws Exception;

    void deleteDetails(List<String> inspectionNos);

    void deleteDetailsNew(List<String> inspectionNos, String id);

    void unsubmission(String id);
}
