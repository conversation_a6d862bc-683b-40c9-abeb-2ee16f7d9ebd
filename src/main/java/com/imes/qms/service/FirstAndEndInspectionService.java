package com.imes.qms.service;

import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetPlanTasksVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface FirstAndEndInspectionService {

    List<FirstAndEndInspectionTaskVo> queryList(Map map);

    void saveInspectionDetailDatas(FirstAndEndInspectionInfoVo datas) throws Exception;

    void supportDatas(FirstAndEndInspectionInfoVo data) throws Exception;

    List<FirstAndEndInspectionInfoVo> queryDetailList(String id);

    void updateStatus(String id) throws Exception;

    void updateInspectionDetailData(FirstAndEndInspectionDetail data);

    List<GetPlanTasksVo> getPlanTasks(GetPlanTasksVo vo) throws Exception;

    FirstAndFinalItemsVo getInspectionItemsStd(String routeCode, String processCode, String bomCode,String inspectType,int batchNum,String id) throws Exception;

    List<FirstAndEndInspectionInfoVo> queryByActivtiId(String activitiId);

    FirstAndEndInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    void rejectDatas(TechnologyInspection data);

    FirstAndEndInspectionInfoVo detailList(String id) throws Exception;

    void unsubmission(String orderId);

    boolean getDelegate() throws Exception;

    Object delegateUserList() throws Exception;

    boolean delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag);

    boolean cancelClaim(String id);

    List<FirstAndEndResultVo> queryFirstAndEndInfoByVo(FirstAndEndInforVo vo) throws Exception;
}
