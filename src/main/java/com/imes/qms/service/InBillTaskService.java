package com.imes.qms.service;

import com.imes.domain.entities.qms.po.InBillTask;
import com.imes.domain.entities.qms.vo.InBillTaskVO;
import com.imes.domain.entities.qms.vo.ProcessTaskVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface InBillTaskService {

    void createInBillTask(InBillTaskVO vo) throws Exception;

    void delegate(String[] ids, String[] userCodes,String[] userNames);

    void updateClaim(String id) throws Exception;

    void createInBillMultiTask(List<InBillTaskVO> vo) throws Exception;
}
