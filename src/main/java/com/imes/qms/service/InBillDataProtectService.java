package com.imes.qms.service;

import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItemDetail;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.vo.RadiosVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface InBillDataProtectService {
    List<InspectionDataProtect> queryList(Map map);

    void saveMaterialDatas(List<InspectionDataProtect> list);

    void saveInspectionRatio(InspectionDataProtect data);

    List<InspectionItems> queryItemList(Map map);

    String saveInspectionItem(InspectionItems item);

    void updateItems(InspectionItems item);

    void deleteItem(String id);

    void saveInspectionDetailItems(List<InspectionItemDetail> items);

    List<InspectionItemDetail> queryItemDetailList(Map map);

    void updateItemDetails(List<InspectionItemDetail> items);

    void deleteMaterialProtectData(List<String> ids);

    String getInspectionCode();

    List<RadiosVo> getInspectionTypeByMaterialCode(Map map);

    List<RadiosVo> findByMaterialCodeAndType(@Param("materialCodes") String[] materialCodes, String inspectionType);

}
