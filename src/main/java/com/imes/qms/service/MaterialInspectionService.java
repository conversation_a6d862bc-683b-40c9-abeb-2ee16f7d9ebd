package com.imes.qms.service;

import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.MaterialByOrderNoNewByMobileVo;
import com.imes.domain.entities.query.template.qms.MaterialByOrderNoNewVo;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public interface MaterialInspectionService {

    List<MaterialInspectionListVo> queryList(Map map);

    void deleteStandardList(List<String> ids);

    ItemInfoVo getInspectionByMaterialCode(String materialCode, String inspectionType,String supplierCode,int batchNum);

    List<MaterialBuyOrderVo> getMaterialByOrderNo(Map map);

    void saveInspectionDetailDatas(MaterialInspectionInfoVo data) throws Exception;

    List<MaterialInspectionInfoVo> queryDetailList(String id);

    List<WmsArrivalOrder> findAoByFilter(Map map);

    void updateInspectionDetailData(MaterialInspectionDetail datas);

    void updateStatus(String id) throws Exception;

    MaterialInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    MaterialInspectionInfoVo detailList(String id) throws Exception;

    void supportDatas(MaterialInspectionInfoVo data) throws Exception;

    List<MaterialInspectionInfoVo> queryByActivtiId(String activitiId);

    void updatePicStatus(String id, String picStatus);

    void cancelMergeInspection(List<String> ids);

    long getMaterialReportTotal();

    long getMaterialTotal();

    List<String> queryMergeOrderIds(String id);

    List<MaterialByOrderNoNewVo> getMaterialByOrderNoNew(MaterialByOrderNoNewVo vo) throws Exception;

    Object getInspectionByMaterialCodeAndSupplier(String materialCode, String inspectionType, String supplierCode, int batchNum,String orderId) throws Exception;

    boolean isMaterialInspection(String arrivalOrder);

    void auditMaterialInspection(String arrivalOrder) throws Exception;

    List<MaterialTask> getTaskBySupperCode(String supperCode);

    MaterialTask getMergeTasks(List<MaterialTask> materialTasks) throws Exception;

    void cancelMerge(List<MaterialTask> materialTasks);

    void deleteAuditMaterialInspection(String arrivalOrder);

    void addMaterialTask(List<MaterialTask> task) throws Exception;

    void deleteMaterialTask(List<String> orders);

    List<MaterialTaskForMobileVo> getMaterialByOrderNoByMobile(MaterialByOrderNoNewByMobileVo vo) throws Exception;

    List<QmsToWmsInspectionListVo> reverseMaterialInspection(String poId) throws Exception;

    InspectionDataProtect getIncomingInspectionSequence(String materialCode);

//    void cancelMergeForAddZero(List<MaterialTask> materialTasks);

    List<HashMap<String, String>> getSupplierCodeAndNameFromTask(String supplierName);

    void updateReverseInspection(List<UpdateReverseInspectionVo> list);

    boolean getDelegate() throws Exception;

    boolean delegateConfirmation(String orderId, String userCode, String userName,String teamCode,String addFlag,String teamName);

    String getInspectionTime();


    void rollBackReverseInspection(List<String> list);

    void unsubmission(String orderId);

    List<MaterialTask> queryTaskByBuyOrder(String buyOrder);

    Object delegateUserList() throws Exception;

    boolean cancelClaim(String orderId);

    List<MaterialResultVo> queryMaterilalInfoByVo(MaterialInforVo vo);

    void deleteAoInspection(List<String> list);

    MaterialInspection getInspectionById(String orderId);


//    List<MaterialInspectionListVo> queryListNew(QmsMaterialInspectionVo vo);
}
