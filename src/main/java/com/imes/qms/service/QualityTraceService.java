package com.imes.qms.service;

import com.imes.common.entity.Result;
import com.imes.domain.entities.qms.po.InBillInspection;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.OutBillInspection;
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;
import com.imes.domain.entities.qms.vo.QualityTraceDetailVo;
import com.imes.domain.entities.qms.vo.QualityTraceVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface QualityTraceService {

    List<QualityTraceVo> queryList(String inspectionType,String inspectionCode,Map map) throws Exception;

//    Map<String,List<QualityTraceDetailVo>> queryItemList(String inspectionType,Map map);

    Result getSupplier();

    Map<String,Object> queryItemList(String inspectionType, String id);
}
