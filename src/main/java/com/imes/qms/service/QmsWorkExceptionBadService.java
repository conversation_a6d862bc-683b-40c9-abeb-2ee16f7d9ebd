package com.imes.qms.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.imes.domain.entities.qms.po.QmsWorkException;
import com.imes.domain.entities.qms.po.QmsWorkExceptionBad;
import com.imes.domain.entities.query.template.qms.QmsWorkExceptionBadSearchVo;

import java.util.List;
import java.util.Map;

/**
 * 异常提报单-主表-志特(QmsWorkExceptionBad)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-13 09:45:33
 */
public interface QmsWorkExceptionBadService extends IService<QmsWorkExceptionBad> {
    List<QmsWorkExceptionBadSearchVo> queryList(QmsWorkExceptionBadSearchVo vo) throws Exception;

    String insert(QmsWorkExceptionBad qmsWorkExceptionBad) throws Exception;

    Integer update(QmsWorkExceptionBad qmsWorkExceptionBad) throws Exception;

    Integer batchDelete(List<String> idList) throws Exception;

    List<QmsWorkExceptionBad> queryByCond(QmsWorkExceptionBad qmsWorkExceptionBad);

    List<QmsWorkExceptionBad> queryByExceptionNoList(List<String> list);

    void batchRepalceInto(List<QmsWorkExceptionBad> list);
    void batchRepalceIntoForApp(QmsWorkException qmsWorkException);

    List<Map<String, Object>>  getProblemPeople(String start, String end, String processName, String badQty);

}
