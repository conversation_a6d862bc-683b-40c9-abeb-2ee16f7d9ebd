package com.imes.qms.service.impl;

import com.imes.common.entity.Result;
import com.imes.common.utils.*;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.AqlPreviewVo;
import com.imes.domain.entities.qms.vo.InspectionItemsVo;
import com.imes.domain.entities.qms.vo.QmsStdInspectionVo;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionDataProtectImportVo;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionItemsImportVo;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.domain.entities.system.base.vo.PreviewFileVo;
import com.imes.domain.entities.system.vo.MaterialVo;
import com.imes.domain.entities.system.vo.SysMaterialSupplierVO;
import com.imes.qms.dao.*;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.InMaterialDataProtectService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class InMaterialDataProtectServiceImpl implements InMaterialDataProtectService {

    @Autowired
    InMaterialDataProtectDao inMaterialDataProtectDao;

    @Autowired
    InspectionItemDetailDao inspectionItemDetailDao;

    @Autowired
    InspectionItemsDao inspectionItemsDao;

    @Autowired
    QmsProtectFileDao qmsProtectFileDao;

    @Autowired
    InBillInspectionDetailDao inBillInspectionDetailDao;

    @Autowired
    OutBillInspectionDetailDao outBillInspectionDetailDao;

    @Autowired
    MaterialInspectionDetailDao materialInspectionDetailDao;

    @Autowired
    ReturnBillInspectionDetailDao returnBillInspectionDetailDao;

    @Autowired
    FirstAndEndInspectionDetailDao firstAndEndInspectionDetailDao;

    @Autowired
    PatrolInspectionDetailDao patrolInspectionDetailDao;

    @Autowired
    ProcessInspectionDetailDao processInspectionDetailDao;

    @Autowired
    AqlSchemeDao aqlSchemeDao;

    @Autowired
    AqlInfoDao aqlInfoDao;


    @Autowired
    IdWorker idWorker;

    @Autowired
    FeignService feignService;

    @Override
    public List<InspectionDataProtect> queryList(Map map) {
        return inMaterialDataProtectDao.queryList(map);
    }

    @Override
    public InspectionDataProtect queryByMaterialAndInspectionType(String materialCode, String inspectionType) {
        Map<String,String> map = new HashMap<>();
        map.put("materialCode",materialCode);
        map.put("inspectionType",inspectionType);
        List<InspectionDataProtect> list = queryList(map);
        if(!list.isEmpty()){
            return list.get(0);
        }else{
            return null;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String saveMaterialDatas(List<InspectionDataProtect> list) {
        String inspectionType = list.get(0).getInspectionType();
        List<String> materialCodes = inMaterialDataProtectDao.getMaterialCodes(inspectionType);
        for (InspectionDataProtect inspectionDataProtect : list) {
            String materialCode = inspectionDataProtect.getMaterialCode();
            if (materialCodes.contains(materialCode)) {
                return "已添加物料不能继续添加,物料编码:" + materialCode;
            }
        }
        list.forEach(item -> {
            item.setId(idWorker.nextId() + "");
            RecordUtils.createData(item);
            inMaterialDataProtectDao.insertSelective(item);
        });
        return "ok";
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInspectionRatio(List<InspectionDataProtect> ratios) {
        ratios.forEach(item -> {
            inMaterialDataProtectDao.updateByPrimaryKey(item);
        });
    }

    @Override
    public List<InspectionItems> queryItemList(Map map) {
        return inspectionItemsDao.queryItemList(map);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, String> saveInspectionItem(InspectionItems item) throws Exception {
        Map<String, String> map = new HashMap<>();
        String itemCode = feignService.getAutoCode("C_NO");
        item.setItemCode(itemCode);
        item.setId(idWorker.nextId() + "");
        RecordUtils.createData(item);
        inspectionItemsDao.insertSelective(item);
        map.put("itemId", item.getId());
        map.put("itemCode", itemCode);
        return map;
    }

    @Override
    public void updateItems(InspectionItems item) {
        RecordUtils.updateData(item);
        inspectionItemsDao.updateSelective(item);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteItem(String itemCode) {
        inspectionItemsDao.deleteItem(itemCode);
        inspectionItemDetailDao.deleteItemDetailByItemCode(itemCode);
        inspectionItemsDao.deletePpc(itemCode);
        inspectionItemDetailDao.deleteByItemCode(itemCode);
        inspectionItemsDao.deleteInBill(itemCode);
        inspectionItemDetailDao.deleteInBillDetail(itemCode);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInspectionDetailItems(List<InspectionItemDetail> items) {
        items.forEach(item -> {
            item.setId(idWorker.nextId() + "");
            item.setToPpcMaterial(idWorker.nextId() + "");
            RecordUtils.createData(item);
            inspectionItemDetailDao.insertSelective(item);
        });
    }

    @Override
    public List<InspectionItemDetail> queryItemDetailList(Map map) {
        return inspectionItemDetailDao.queryItemDetailList(map);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateItemDetails(List<InspectionItemDetail> items) {
        items.forEach(item -> {
            RecordUtils.updateData(item);
            inspectionItemDetailDao.updateSelective(item);
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteMaterialProtectData(String id) {
        InspectionDataProtect data = inMaterialDataProtectDao.queryById(id);
        String materialCode = data.getMaterialCode();
        String inspectionType = data.getInspectionType();
        inspectionItemDetailDao.deteleByProtectDataId(materialCode, inspectionType);
        inspectionItemsDao.deteleByMaterialAndInspection(materialCode, inspectionType);
        inMaterialDataProtectDao.deleteById(id);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteItemDetails(List<String> ids) {
        ids.forEach(id -> {
            String vo = inspectionItemDetailDao.selectById(id);
            if (null != vo&& !"".equals(vo)){
                inspectionItemDetailDao.deleteToPpc(vo);
                inspectionItemDetailDao.deleteInBill(vo);
            }
            inspectionItemDetailDao.deleteItemDetail(id);
        });
    }

    @Override
    public SysFile uploadProtectFile(MultipartFile uploadFile) throws Exception {
        SysFile sysFile = feignService.singleUpload(uploadFile);
        return sysFile;
    }

    @Override
    public SysFile uploadMultiFile(MultipartFile[] uploadFile) throws Exception {
        SysFile sysFile = feignService.multiUpload(uploadFile);
        return sysFile;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertProtectFile(QmsProtectFile qmsProtectFile) throws Exception {
        qmsProtectFile.setId(String.valueOf(idWorker.nextId()));
        qmsProtectFile.setCreateBy(RedisUtils.getUserCode());
        qmsProtectFile.setCreateOn(new Date());
        int count = qmsProtectFileDao.update(qmsProtectFile);
        // 如果没有更新说明没数据
        if (0 == count) {
            qmsProtectFileDao.insert(qmsProtectFile);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void insertChildFile(QmsProtectFile qmsProtectFile, String inspectionType) throws Exception {
        qmsProtectFile.setId(String.valueOf(idWorker.nextId()));
        qmsProtectFile.setCreateBy(RedisUtils.getUserCode());
        qmsProtectFile.setCreateOn(new Date());
        int insert = qmsProtectFileDao.insertChildFile(qmsProtectFile);
        //updatePicStatus(qmsProtectFile.getChildId(),inspectionType,ConstantUtil.pic_has);
    }

    private void updatePicStatus(String id, String inspectionType, String status) {
        // 入库检验
        if (ConstantUtil.inBill.equals(inspectionType)) {
            inBillInspectionDetailDao.updatePicStatus(id, status);
        }
        // 来料检验
        if (ConstantUtil.material.equals(inspectionType)) {
            materialInspectionDetailDao.updatePicStatus(id, status);
        }
        // 出库检验
        if (ConstantUtil.outBill.equals(inspectionType)) {
            outBillInspectionDetailDao.updatePicStatus(id, status);
        }
        // 首末检验
        if (ConstantUtil.firstFinal.equals(inspectionType)) {
            firstAndEndInspectionDetailDao.updatePicStatus(id, status);
        }
        // 巡检
        if (ConstantUtil.patrol.equals(inspectionType)) {
            patrolInspectionDetailDao.updatePicStatus(id, status);
        }
        // 退货检验
        if (ConstantUtil.returns.equals(inspectionType)) {
            returnBillInspectionDetailDao.updatePicStatus(id, status);
        }
        // 过程检验
        if (ConstantUtil.process.equals(inspectionType)) {
            processInspectionDetailDao.updatePicStatus(id, status);
        }
    }

    @Override
    public Map<String, Object> findProtectFile(QmsProtectFile qmsProtectFile) throws Exception {
        List<QmsProtectFile> protectFile = qmsProtectFileDao.findProtectFile(qmsProtectFile);
        Map<String, Object> strMap = null;
        if (!protectFile.isEmpty()) {
            try {
                QmsProtectFile qmsProtectFile1 = protectFile.get(0);
                PreviewFileVo vo = feignService.previewFile(qmsProtectFile1.getFileId());
                SysFile fileById = feignService.getFileById(qmsProtectFile1.getFileId());
                strMap = new HashMap<>();
                strMap.put("id", qmsProtectFile1.getId());
                strMap.put("fileName", fileById.getOriginalName());
                strMap.put("fileSize", fileById.getFileSize());
                strMap.put("uploadTime", fileById.getCreateOn());
                strMap.put("localUrl", fileById.getLocalUrl());
                strMap.put("preUrl", fileById.getPreUrl());
                strMap.put("fileType", fileById.getFileType());
                strMap.put("fileId", qmsProtectFile1.getFileId());
                strMap.put("vo", vo);
            } catch (Exception exception) {
                exception.printStackTrace();
            }
        }
        return strMap;
    }

    @Override
    public List<Map<String, Object>> findChildFile(QmsProtectFile qmsProtectFile) throws Exception {
        List<QmsProtectFile> protectFile = qmsProtectFileDao.findProtectFile(qmsProtectFile);
        List<Map<String, Object>> mapList = null;
        if (null != protectFile && protectFile.size() > 0) {
            mapList = new ArrayList<>();
            for (QmsProtectFile qms : protectFile) {
                Map<String, Object> strMap = null;
                if (!protectFile.isEmpty()) {
                    try {
                        strMap = new HashMap<>();
                        SysFile fileById = feignService.getFileById(qms.getFileId());
                        strMap = new HashMap<>();
                        strMap.put("id", qms.getId());
                        strMap.put("fileName", fileById.getOriginalName());
                        strMap.put("fileSize", fileById.getFileSize());
                        strMap.put("uploadTime", fileById.getCreateOn());
                        strMap.put("localUrl", fileById.getLocalUrl());
                        strMap.put("preUrl", fileById.getPreUrl());
                        strMap.put("fileType", fileById.getFileType());
                        strMap.put("fileId", qms.getFileId());
                        mapList.add(strMap);
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                }
            }

        }
        return mapList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteProtectFileById(String id) throws Exception {
        if (com.imes.common.utils.StringUtils.isNullOrBlank(id)) {
            AssertUtil.throwException("待删除id不能为空");
        }
        QmsProtectFile qmsProtectFile = qmsProtectFileDao.selectByPrimaryKey(id);
        if (null == qmsProtectFile) {
            AssertUtil.throwException("待删除id不存在");
        }
        //先删除工艺文件关系
        qmsProtectFileDao.deleteByPrimaryKey(id);
        //删除文件
        Result result = feignService.fileDelete(qmsProtectFile.getFileId());
        if (!result.isSuccess()) {
            AssertUtil.throwException("删除附件失败！原因：" + result.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByFileIds(List<String> fileIds, String inspectionType) throws Exception {
        if (null != fileIds) {
            for (String fileId : fileIds) {
                List<QmsProtectFile> qmsProtectFiles = qmsProtectFileDao.selectByFileId(fileId);
                if (null != qmsProtectFiles && qmsProtectFiles.size() > 0) {
                    for (QmsProtectFile qmsProtectFile : qmsProtectFiles) {
                        // 先删除工艺文件关系
                        qmsProtectFileDao.deleteByPrimaryKey(qmsProtectFile.getId());
                        // 删除文件
                        Result result = feignService.fileDelete(qmsProtectFile.getFileId());
                        if (!result.isSuccess()) {
                            AssertUtil.throwException("删除附件失败！原因：" + result.getMessage());
                        }
                    }
                    // 设置图片状态为0
                    updatePicStatus(fileId, inspectionType, ConstantUtil.pic_not_has);
                } else {
                    //删除文件
                    Result result = feignService.fileDelete(fileId);
                    if (!result.isSuccess()) {
                        AssertUtil.throwException("删除附件失败！原因：" + result.getMessage());
                    }
                }

            }
        }
    }

    @Override
    public Map<String, Object> getTempItems() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("itemId", idWorker.nextId() + "");
        map.put("itemCode", feignService.getAutoCode("C_NO"));
        return map;
    }

    @Override
    public Map<String, Object> getTempDetailItemId() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("detailId", idWorker.nextId() + "");
        map.put("createOn", new Date());
        map.put("createBy", RedisUtils.getUserCode());
        return map;
    }

    @Override
    public Map<String, Object> findTechnologyFile(String processCode, String itemCode) {
        Map<String, Object> map = new HashMap<>();
        map.put("processCode", processCode);

        Map<String, Object> strMap = null;
        try {
            List<QmsStdInspectionVo> voList = feignService.queryByMap(map);
            List<QmsStdInspectionVo> file = voList.stream()
                    .filter(x -> Objects.equals(x.getInspectCode(), itemCode))
                    .collect(Collectors.toList());
            QmsStdInspectionVo qmsProtectFile1 = file.get(0);
            PreviewFileVo vo = feignService.previewFile(qmsProtectFile1.getFileId());
            strMap = new HashMap<>();
            strMap.put("vo", vo);
        } catch (Exception exception) {
            exception.printStackTrace();
        }
        return strMap;
    }

    @Override
    public String getInspectionCode() throws Exception {
        String inspectionCode = "";
        inspectionCode = feignService.getAutoCode("C_NO");
        return inspectionCode;
    }

    @Override
    public String getTempNo(String code) throws Exception {
        String tempNo = "";
        tempNo = feignService.getAutoCode(code);
        return tempNo;
    }

    @Override
    public void checkMaterialImport(List<List<Object>> importList, int startRow) throws Exception {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("导入数据有误：");
        int i = 1;
        int j = 1;
        List<String> strings = new ArrayList<>();
//        List<String> strings = inspectionItemsDao.getAllItemCodes(id);
        for (List<Object> list : importList) {
            if (i > 20) {
                break;
            }
            if (StringUtils.isNullOrBlank(list.get(0))) {
                stringBuilder.append("第" + j + "行数据有误！检验项编码为空。");
                i++;
            } else {
                if (strings.contains(list.get(0))) {
                    stringBuilder.append("第" + j + "行数据有误！检验项编码重复了。");
                    i++;
                }
                strings.add(list.get(0).toString());
            }
            if (list.get(0).toString().length() > 40) {
                stringBuilder.append("第" + j + "行数据有误！检验项编码太大了。");
                i++;
            }
            if (StringUtils.isNullOrBlank(list.get(1))) {
                stringBuilder.append("第" + j + "行数据有误！检验项名称为空。");
                i++;
            }
            j++;
        }
        if (stringBuilder.length() > 8) {
            AssertUtil.throwException(stringBuilder.toString());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public int importMaterialData(List<List<Object>> importList) throws Exception {
        if (importList.isEmpty()) {
            AssertUtil.throwException("数据保存失败！数据为空。");
        }
//        List<InspectionItems> items = inspectionItemsDao.queryItemList(new HashMap<>());
//        List<String> collect = items.stream().map(InspectionItems::getItemCode).collect(Collectors.toList());
//        for (List<Object> list : importList) {
//            InspectionItems qmsInspection = new InspectionItems();
//            qmsInspection.setItemCode(list.get(0).toString());
//            qmsInspection.setItemName(list.get(1).toString());
//            qmsInspection.setParentId(id);
//            qmsInspection.setId(idWorker.nextId() + "");
//            RecordUtils.createData(qmsInspection);
//            inspectionItemsDao.insertSelective(qmsInspection);
//        }
        return 1;
    }

    @Override
    public Map<String, Object> getRatesAndRatios(Map map) throws Exception {
        Map<String, Object> resultMap = inMaterialDataProtectDao.getRatesAndRatios(map);
        if (null != map && null != map.get("inspectionType")) {
            if (ConstantUtil.material.equals(map.get("inspectionType").toString())) {
                List<SysMaterialSupplierVO> list = feignService.findByMaterialCode(map.get("materialCode").toString());
                if (null != list && list.size() > 0) {
                    for (SysMaterialSupplierVO sys : list) {
                        if (sys.getSupplierCode().equals(map.get("supplierCode").toString())) {
                            BigDecimal inspectionRate = sys.getInspectionRate().multiply(new BigDecimal(100));
                            BigDecimal goodRate = sys.getGoodRate().multiply(new BigDecimal(100));
                            resultMap.put("inspectionRatio", inspectionRate);
                            resultMap.put("qualifiedRate", goodRate);
                            break;
                        }
                    }
                }
            }
        }
        return resultMap;
    }

    @Override
    public List<QmsInspectionDataProtectImportVo> checkVerificationImport(List<QmsInspectionDataProtectImportVo> importList) throws Exception {

        int startRow = 3;
        if (importList.size() > 2000) {
            AssertUtil.throwException("每次最多导入2000条数据");
        }else if (importList.size() == 0){
            AssertUtil.throwException("导入数据为空");
        }
        StringBuilder info = new StringBuilder();
        int k = 0;
        List<String> list1 = new ArrayList<>(4);
        list1.add("1");
        list1.add("2");
        list1.add("3");
        list1.add("4");
        List<String> list2 = new ArrayList<>(4);
        list2.add("1");
        list2.add("2");
        list2.add("3");
        List<String> list3 = new ArrayList<>();
        for (int i = 1; i <= 7; i++) {
            list3.add(String.valueOf(i));
        }
        List<String> list4 = new ArrayList<>();
        for (int i = 1; i <= 25; i++) {
            list4.add(String.valueOf(i));
        }
        List<String> codeTypeList = new ArrayList<>(importList.size());
        for (int i = 0; i < importList.size(); i++) {
            QmsInspectionDataProtectImportVo importVo = importList.get(i);
            if (StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                info.append("第").append(startRow + i + 1).append("行,物料编码为空\r\n");
            }
            if (StringUtils.isNullOrBlank(importVo.getInspectionType())) {
                info.append("第").append(startRow + i + 1).append("行,检验类型为空\r\n");
            }
            if (!list1.contains(importVo.getInspectionType())) {
                info.append("第").append(startRow + i + 1).append("行,检验类型非预设值\r\n");
            }
            String codeType = importVo.getMaterialCode() + importVo.getInspectionType();
            if (codeTypeList.contains(codeType)) {
                info.append("第").append(startRow + i + 1).append("行,物料编码和检验类型重复\r\n");
                k++;
            } else {
                codeTypeList.add(codeType);
            }
            if (!StringUtils.isNullOrBlank(importVo.getQualifiedRate())) {
                String qualifiedRate = importVo.getQualifiedRate();
                if (!NumberUtils.isNumber(qualifiedRate)) {
                    info.append("第").append(startRow + i + 1).append("行,过检合格率请输入数字\r\n");
                    k++;
                } else {
                    if (Double.parseDouble(qualifiedRate) <= 0 || Double.parseDouble(qualifiedRate) > 100) {
                        info.append("第").append(startRow + i + 1).append("行,过检合格率允许范围是(0,100]\r\n");
                        k++;
                    }
                    if (qualifiedRate.contains(".") && qualifiedRate.substring(qualifiedRate.indexOf(".") + 1).length() > 2) {
                        info.append("第").append(startRow + i + 1).append("行,过检合格率最多为两位小数\r\n");
                        k++;
                    }
                }
            }
            //判定检验方式
            if (StringUtils.isNullOrBlank(importVo.getInspectionSamplingMethod())) {
                info.append("第").append(startRow + i + 1).append("行,检验方式不能为空\r\n");
                k++;
            } else {
                String inspectionMethod = importVo.getInspectionSamplingMethod();
                if (!list2.contains(inspectionMethod)) {
                    info.append("第").append(startRow + i + 1).append("行,检验方式非预设值\r\n");
                } else {
                    if ("1".equals(inspectionMethod)) {
                        if (StringUtils.isNullOrBlank(importVo.getInspectLevel())){
                            info.append("第").append(startRow + i + 1).append("行,检验水平必填\r\n");
                        }else {
                            if (!list3.contains(importVo.getInspectLevel())) {
                                info.append("第").append(startRow + i + 1).append("行,检验水平非预设值\r\n");
                            }
                        }
                    }else if ("2".equals(inspectionMethod)) {
                        if (StringUtils.isNullOrBlank(importVo.getInspectionRatio())){
                            info.append("第").append(startRow + i + 1).append("行,检验比率必填\r\n");
                        }else {
                            String inspectionRatio = importVo.getInspectionRatio();
                            if (!NumberUtils.isNumber(inspectionRatio)) {
                                info.append("第").append(startRow + i + 1).append("行,检验比率请输入数字\r\n");
                            } else {
                                if (Double.parseDouble(inspectionRatio) <= 0 || Double.parseDouble(inspectionRatio) > 100) {
                                    info.append("第").append(startRow + i + 1).append("行,检验比率允许范围是(0,100]\r\n");
                                    k++;
                                }
                                if (inspectionRatio.contains(".") && inspectionRatio.substring(inspectionRatio.indexOf(".") + 1).length() > 2) {
                                    info.append("第").append(startRow + i + 1).append("行,检验比率最多为两位小数\r\n");
                                    k++;
                                }
                            }
                        }
                    }else if ("3".equals(inspectionMethod)) {
                        if (StringUtils.isNullOrBlank(importVo.getQuSampling())){
                            info.append("第").append(startRow + i + 1).append("行,定量抽样数量必填\r\n");
                        }else {
                            String quSampling = importVo.getQuSampling();
                            if (!NumberUtils.isNumber(quSampling)) {
                                info.append("第").append(startRow + i + 1).append("行,定量抽样数量请输入数字\r\n");
                            } else {
                                if (quSampling.contains(".")) {
                                    info.append("第").append(startRow + i + 1).append("行,定量抽样数量为整数\r\n");
                                    k++;
                                }
                            }
                        }
                    }
                }
            }
            //判定方式
            String inspectionMethod = importVo.getInspectionSamplingMethod();
            String inspectionJudgmentMethod = importVo.getInspectionJudgmentMethod();
            if (StringUtils.isNullOrBlank(inspectionJudgmentMethod)) {
                info.append("第").append(startRow + i + 1).append("行,判定方式不能为空\r\n");
                k++;
            }else {
                if ("1".equals(inspectionMethod)){
                    if("1".equals(inspectionJudgmentMethod) || "2".equals(inspectionJudgmentMethod)){
                        if ("1".equals(inspectionJudgmentMethod)){
                            if (StringUtils.isNullOrBlank(importVo.getAqlValue())){
                                info.append("第").append(startRow + i + 1).append("行,AQL值为空\r\n");
                                k++;
                            }else {
                                if (!list4.contains(importVo.getAqlValue())) {
                                    info.append("第").append(startRow + i + 1).append("行,AQL值非预设值\r\n");
                                }
                            }
                        }else {
                            if (StringUtils.isNullOrBlank(importVo.getQualifiedRate())) {
                                info.append("第").append(startRow + i + 1).append("行,过检合格率必填\r\n");
                            }else {
                                if (Double.parseDouble(importVo.getQualifiedRate()) <= 0 || Double.parseDouble(importVo.getQualifiedRate()) > 100) {
                                    info.append("第").append(startRow + i + 1).append("行,过检合格率允许范围是(0,100]\r\n");
                                    k++;
                                }
                                if (importVo.getQualifiedRate().contains(".") && importVo.getQualifiedRate().substring(importVo.getQualifiedRate().indexOf(".") + 1).length() > 2) {
                                    info.append("第").append(startRow + i + 1).append("行,过检合格率最多为两位小数\r\n");
                                    k++;
                                }
                            }
                        }
                    }else {
                        info.append("第").append(startRow + i + 1).append("行,检验抽样方式选择1(AQL标准抽样)，则判定方式可选1(AQL判定)或2(合格率判定)\r\n");
                        k++;
                    }
                }else if ("2".equals(inspectionMethod)){
                    if("2".equals(inspectionJudgmentMethod)){
                        if (StringUtils.isNullOrBlank(importVo.getQualifiedRate())) {
                            info.append("第").append(startRow + i + 1).append("行,过检合格率必填\r\n");
                        }else {
                            if (Double.parseDouble(importVo.getQualifiedRate()) <= 0 || Double.parseDouble(importVo.getQualifiedRate()) > 100) {
                                info.append("第").append(startRow + i + 1).append("行,过检合格率允许范围是(0,100]\r\n");
                                k++;
                            }
                            if (importVo.getQualifiedRate().contains(".") && importVo.getQualifiedRate().substring(importVo.getQualifiedRate().indexOf(".") + 1).length() > 2) {
                                info.append("第").append(startRow + i + 1).append("行,过检合格率最多为两位小数\r\n");
                                k++;
                            }
                        }
                    }else {
                        info.append("第").append(startRow + i + 1).append("行,检验抽样方式选择2(比率抽样)，则判定方式只可选2(合格率判定)\r\n");
                        k++;
                    }
                }else if ("3".equals(inspectionMethod)){
                    if("2".equals(inspectionJudgmentMethod) || "3".equals(inspectionJudgmentMethod)){
                        if ("2".equals(inspectionJudgmentMethod)){
                            if (StringUtils.isNullOrBlank(importVo.getQualifiedRate())) {
                                info.append("第").append(startRow + i + 1).append("行,过检合格率必填\r\n");
                            }else {
                                if (Double.parseDouble(importVo.getQualifiedRate()) <= 0 || Double.parseDouble(importVo.getQualifiedRate()) > 100) {
                                    info.append("第").append(startRow + i + 1).append("行,过检合格率允许范围是(0,100]\r\n");
                                    k++;
                                }
                                if (importVo.getQualifiedRate().contains(".") && importVo.getQualifiedRate().substring(importVo.getQualifiedRate().indexOf(".") + 1).length() > 2) {
                                    info.append("第").append(startRow + i + 1).append("行,过检合格率最多为两位小数\r\n");
                                    k++;
                                }
                            }
                        }
                    }else {
                        info.append("第").append(startRow + i + 1).append("行,检验抽样方式选择3(定量抽样)，则判定方式可选2(合格率判定)或3(主观判定)\r\n");
                        k++;
                    }
                }
            }
            //质检样本管理参数
            String qmsSampleManager = importVo.getQmsSampleManager();
            if (StringUtils.isNullOrBlank(qmsSampleManager)) {
                info.append("第").append(startRow + i + 1).append("行,质检样本管理不能为空\r\n");
                k++;
            }else {
                if ("1".equals(qmsSampleManager) || "2".equals(qmsSampleManager)){
                }else {
                    info.append("第").append(startRow + i + 1).append("行,质检样本管理参数有误\r\n");
                    k++;
                }
            }
            //来料检验顺序
            String inspectionType = importVo.getInspectionType();
            if ("2".equals(inspectionType)){
                String incomingInspectionSequence = importVo.getIncomingInspectionSequence();
                if (StringUtils.isNullOrBlank(incomingInspectionSequence)){
                    info.append("第").append(startRow + i + 1).append("行,来料检验顺序不能为空\r\n");
                }else {
                    if ("1".equals(incomingInspectionSequence) || "2".equals(incomingInspectionSequence)){
                    }else {
                        info.append("第").append(startRow + i + 1).append("行,来料检验顺序参数有误\r\n");
                        k++;
                    }
                }
            }
            if (!StringUtils.isNullOrBlank(info)) {
                importVo.getErrorInfo().add(info.toString());
            }
        }
        importList = importVerificationData(importList);
        return importList;
    }

    @Override
    public List<QmsInspectionDataProtectImportVo> importVerificationData(List<QmsInspectionDataProtectImportVo> importList) throws Exception {
        //查询所有物料用于校验物料编码是否存在
        List<MaterialVo> materialList = getMaterialListByDataProject(importList);
        List<InspectionDataProtect> dataProtectList = inMaterialDataProtectDao.queryAll();
        int dataNum = 0;
        List<InspectionDataProtect> insertList = new ArrayList<>(importList.size());
        for (QmsInspectionDataProtectImportVo importVo : importList) {
            if (StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                continue;
            }
            MaterialVo material = materialList.stream().filter(e -> e.getMaterialCode().equals(importVo.getMaterialCode())).findFirst().orElse(null);
            if (material == null) {
                importVo.getErrorInfo().add("物料编码【" + importVo.getMaterialCode() + "】不存在");
            }
            String materialCode = importVo.getMaterialCode();
            String inspectionType = importVo.getInspectionType();
            InspectionDataProtect inspectionDataProtect = dataProtectList.stream().filter(e -> e.getMaterialCode().equals(materialCode)
                    && e.getInspectionType().equals(inspectionType)).findFirst().orElse(null);
            if (inspectionDataProtect != null) {
                importVo.getErrorInfo().add("物料编码【" + materialCode + "】和检验类型【" + inspectionType + "】已存在");
            }
            if (material != null) {
                InspectionDataProtect record = new InspectionDataProtect();
                record.setId(String.valueOf(idWorker.nextId()));
                record.setMaterialCode(material.getMaterialCode());
                record.setMaterialName(material.getMaterialName());
                record.setSpecification(material.getSpecification());
                record.setCategory(material.getCategory());
                record.setInspectionType(importVo.getInspectionType());
                if (!StringUtils.isNullOrBlank(importVo.getInspectionRatio())) {
                    record.setInspectionRatio(importVo.getInspectionRatio());
                }
                if (!StringUtils.isNullOrBlank(importVo.getQualifiedRate())) {
                    record.setQualifiedRate(importVo.getQualifiedRate());
                }
                if (!StringUtils.isNullOrBlank(importVo.getInspectionSamplingMethod())) {
                    record.setInspectionSamplingMethod(importVo.getInspectionSamplingMethod());
                }
                if (!StringUtils.isNullOrBlank(importVo.getInspectionJudgmentMethod())) {
                    record.setInspectionJudgmentMethod(importVo.getInspectionJudgmentMethod());
                }
                if (!StringUtils.isNullOrBlank(importVo.getQuSampling())) {
                    record.setQuSampling(new BigDecimal(importVo.getQuSampling()));
                }
                if (!StringUtils.isNullOrBlank(importVo.getInspectLevel())) {
                    record.setInspectLevel(importVo.getInspectLevel());
                }
                if (!StringUtils.isNullOrBlank(importVo.getAqlValue())) {
                    record.setAqlValue(importVo.getAqlValue());
                }
                if (!StringUtils.isNullOrBlank(importVo.getQmsSampleManager())) {
                    record.setQmsSampleManager(importVo.getQmsSampleManager());
                }
                if (!StringUtils.isNullOrBlank(importVo.getIncomingInspectionSequence()) && "2".equals(record.getInspectionType())) {
                    record.setIncomingInspectionSequence(importVo.getIncomingInspectionSequence());
                }
                //如果没有错误信息才新增
                if (StringUtils.isNullOrBlank(importVo.getErrorInfo()) || importVo.getErrorInfo().isEmpty()) {
                    RecordUtils.createData(record);
                    insertList.add(record);
                }
            }
        }
        //批量插入
        if (!insertList.isEmpty()) {
            int count = insertList.size() / 500;
            if (count > 0) {
                for (int i = 0; i <= count; i++) {
                    int fromIndex = i * 500;
                    int toIndex = (i + 1) * 500;
                    if (toIndex > insertList.size()) {
                        toIndex = insertList.size();
                    }
                    List<InspectionDataProtect> subList = insertList.subList(fromIndex, toIndex);
                    inMaterialDataProtectDao.batchInsert(subList);
                }
            } else {
                inMaterialDataProtectDao.batchInsert(insertList);
            }
        }
        return importList;
    }

    @Override
    public List<QmsInspectionItemsImportVo> checkMaterialInspection(List<QmsInspectionItemsImportVo> importList) throws Exception {
        int startRow = 3;
        if (importList.size() > 2000) {
            AssertUtil.throwException("每次最多导入2000条数据");
        }else if (importList.size() == 0){
            AssertUtil.throwException("检验项数据为空，无法导入");
        }
        List<String> list1 = new ArrayList<>(4);
        list1.add("1");
        list1.add("2");
        list1.add("3");
        List<String> list5 = new ArrayList<>(2);
        list5.add("1");
        list5.add("2");
        List<String> list6 = new ArrayList<>(2);
        list6.add("0");
        list6.add("1");
        List<InspectionItems> itemsList = new ArrayList<>();
        //数据库所有的检验子项
        List<InspectionItemsVo> itemDetailRecordList = inspectionItemsDao.findAllItemAndDetails();
        List<String> dbItemDetailsStr = new ArrayList<>();
        for (InspectionItemsVo inspectionItemsVo : itemDetailRecordList) {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getMaterialCode()) ? inspectionItemsVo.getMaterialCode() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getItemCode()) ? inspectionItemsVo.getItemCode() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getChildItem()) ? inspectionItemsVo.getChildItem() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getInspectionTool()) ? inspectionItemsVo.getInspectionTool() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getDetermineType()) ? inspectionItemsVo.getDetermineType() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getUpLimit()) ? inspectionItemsVo.getUpLimit() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getDownLimit()) ? inspectionItemsVo.getDownLimit() : "") + "-");
            stringBuilder.append((!StringUtils.isNullOrBlank(inspectionItemsVo.getUnStandard()) ? inspectionItemsVo.getUnStandard() : ""));
            dbItemDetailsStr.add(stringBuilder.toString());
        }
        //查询所有物料用于校验物料编码是否存在
        List<MaterialVo> materialList = getMaterialList(importList);
        List<String> materialCodeList = materialList.stream().map(item -> item.getMaterialCode()).collect(Collectors.toList());
        List<String> subItemList = new ArrayList<>(importList.size());
        for (int i = 0; i < importList.size(); i++) {
            QmsInspectionItemsImportVo importVo = importList.get(i);
            //判断相同的检验项编码，检验项名称是否重复
            InspectionItems qmsInspection = new InspectionItems();
            qmsInspection.setItemCode(importVo.getItemCode());
            qmsInspection.setItemName(importVo.getItemName());
            itemsList.add(qmsInspection);
        }
        for (int i = 0; i < importList.size(); i++) {
            StringBuilder info = new StringBuilder();
            QmsInspectionItemsImportVo importVo = importList.get(i);
            if (StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                info.append("第").append(startRow + i + 1).append("行,物料编码为空\r\n");
            } else {
                if (!materialCodeList.contains(importVo.getMaterialCode())) {
                    info.append("第").append(startRow + i + 1).append("行,物料编码不存在\r\n");
                }
            }
            if (StringUtils.isNullOrBlank(importVo.getInspectionType())) {
                info.append("第").append(startRow + i + 1).append("行,检验类型为空\r\n");
            }
            if (!list1.contains(importVo.getInspectionType())) {
                info.append("第").append(startRow + i + 1).append("行,检验类型非预设值\r\n");
            }
            if (StringUtils.isNullOrBlank(importVo.getItemCode())) {
                info.append("第").append(startRow + i + 1).append("行,检验项编码为空\r\n");
            }
            if (!StringUtils.isNullOrBlank(importVo.getItemCode()) && importVo.getItemCode().length() > 40) {
                info.append("第").append(startRow + i + 1).append("行,检验项编码长度不能超过40\r\n");
            }
            if (StringUtils.isNullOrBlank(importVo.getItemName())) {
                info.append("第").append(startRow + i + 1).append("行,检验项名称为空\r\n");
            }
            if (StringUtils.isNullOrBlank(importVo.getChildItem())) {
                info.append("第").append(startRow + i + 1).append("行,子项名称为空\r\n");
            }
            if (StringUtils.isNullOrBlank(importVo.getDetermineType())) {
                info.append("第").append(startRow + i + 1).append("行,判定方式为空\r\n");
            }
            if (!list6.contains(importVo.getDetermineType())) {
                info.append("第").append(startRow + i + 1).append("行,判定方式非预设值\r\n");
            }
            if ("0".equals(importVo.getDetermineType())) {
                if (StringUtils.isNullOrBlank(importVo.getUpLimit()) && StringUtils.isNullOrBlank(importVo.getDownLimit())) {
                    info.append("第").append(startRow + i + 1).append("行,参数判定时参数上限和下限不能全为空\r\n");
                }
            }
            if (!StringUtils.isNullOrBlank(importVo.getDownLimit()) && !StringUtils.isNullOrBlank(importVo.getUpLimit())) {
                if (Double.parseDouble(importVo.getDownLimit()) > Double.parseDouble(importVo.getUpLimit())) {
                    info.append("第").append(startRow + i + 1).append("行,参数下限不能大于上限\r\n");
                }
            }
            if (!StringUtils.isNullOrBlank(importVo.getDownLimit())) {
                String downLimit = importVo.getDownLimit();
                if (!NumberUtils.isNumber(downLimit)) {
                    info.append("第").append(startRow + i + 1).append("行,参数下限请输入数字\r\n");
                } else {
                    if (downLimit.contains(".") && downLimit.substring(downLimit.indexOf(".") + 1).length() > 6) {
                        info.append("第").append(startRow + i + 1).append("行,参数下限最多为六位小数\r\n");
                    }
                }
            }
            if (!StringUtils.isNullOrBlank(importVo.getUpLimit())) {
                String upLimit = importVo.getUpLimit();
                if (!NumberUtils.isNumber(upLimit)) {
                    info.append("第").append(startRow + i + 1).append("行,参数上限请输入数字\r\n");
                } else {
                    if (upLimit.contains(".") && upLimit.substring(upLimit.indexOf(".") + 1).length() > 6) {
                        info.append("第").append(startRow + i + 1).append("行,参数上限最多为六位小数\r\n");
                    }
                }
            }
            if ("1".equals(importVo.getDetermineType())){
                if (StringUtils.isNullOrBlank(importVo.getUnStandard())) {
                    info.append("第").append(startRow + i + 1).append("行,判定方式为主观判断则非理化标准必填\r\n");
                }
            }

            if (!StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                StringBuilder stringBuilder = new StringBuilder();
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getMaterialCode()) ? importVo.getMaterialCode() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getItemCode()) ? importVo.getItemCode() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getChildItem()) ? importVo.getChildItem() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getInspectionTool()) ? importVo.getInspectionTool() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getDetermineType()) ? importVo.getDetermineType() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getUpLimit()) ? importVo.getUpLimit() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getDownLimit()) ? importVo.getDownLimit() : "") + "-");
                stringBuilder.append((!StringUtils.isNullOrBlank(importVo.getUnStandard()) ? importVo.getUnStandard() : ""));
                if (subItemList.contains(stringBuilder.toString())) {
                    info.append("第").append(startRow + i + 1).append("行,检验项子项在excel中重复\r\n");
                } else {
                    subItemList.add(stringBuilder.toString());
                }
                if (dbItemDetailsStr.contains(stringBuilder.toString())) {
                    info.append("第").append(startRow + i + 1).append("行,检验项子项在数据库中已存在");
                }
            }
            List<InspectionItems> collect = itemsList.stream().filter(e -> e.getItemCode().equals(importVo.getItemCode())).distinct().collect(Collectors.toList());
            if (collect.size()>1){
                info.append("第").append(startRow + i + 1).append("行,相同的检验项编码检验项名称与其他行不同");
            }
            if (!StringUtils.isNullOrBlank(info)) {
                importVo.getErrorInfo().add(info.toString());
            }
        }
        importList = importMaterialInspection(importList);
        return importList;
    }

    @Override
    public List<QmsInspectionItemsImportVo> importMaterialInspection(List<QmsInspectionItemsImportVo> importList) throws Exception {
        int dataNum = 0;
        List<InspectionItems> itemsList = new ArrayList<>(importList.size());
        List<InspectionItemDetail> itemDetailList = new ArrayList<>(importList.size());
        List<InspectionDataProtect> dataProtectList = inMaterialDataProtectDao.queryAll();
        for (int i = 0; i < importList.size(); i++) {
            QmsInspectionItemsImportVo importVo = importList.get(i);
            if (StringUtils.isNullOrBlank(importVo.getMaterialCode()) || !importVo.isSuccess()) {
                continue;
            }
            String materialCode = importVo.getMaterialCode();
            String inspectionType = importVo.getInspectionType();
            InspectionDataProtect inspection = dataProtectList.stream().filter(e -> e.getMaterialCode().equals(materialCode) && e.getInspectionType().equals(inspectionType)).findFirst().orElse(null);
            if (inspection == null) {
                importVo.getErrorInfo().add(String.format("物料编码【%s】的检验类型【%s】未配置", materialCode, inspectionType));
            }
            String itemCode = importVo.getItemCode();
            String itemName = importVo.getItemName();
            String childItem = importVo.getChildItem();
            String inspectionTool = importVo.getInspectionTool();
            String determineType = importVo.getDetermineType();
            String unStandard = importVo.getUnStandard();
            if (StringUtils.isNullOrBlank(importVo.getErrorInfo()) || importVo.getErrorInfo().isEmpty()) {
                InspectionItems qmsInspection = new InspectionItems();
                //qmsInspection.setId(String.valueOf(idWorker.nextId()));
                qmsInspection.setMaterialCode(materialCode);
                qmsInspection.setInspectionType(inspectionType);
                qmsInspection.setItemCode(itemCode);
                qmsInspection.setItemName(itemName);
                //RecordUtils.createData(qmsInspection);
                itemsList.add(qmsInspection);
            }
            if (StringUtils.isNullOrBlank(importVo.getErrorInfo()) || importVo.getErrorInfo().isEmpty()) {
                //子项
                InspectionItemDetail itemDetail = new InspectionItemDetail();
                itemDetail.setId(String.valueOf(idWorker.nextId()));
                itemDetail.setItemCode(itemCode);
                itemDetail.setChildItem(childItem);
                itemDetail.setInspectionTool(inspectionTool);
                itemDetail.setDetermineType(determineType);
                itemDetail.setToPpcMaterial(String.valueOf(idWorker.nextId()));
                if (!StringUtils.isNullOrBlank(importVo.getDownLimit())) {
                    itemDetail.setDownLimit(new BigDecimal(importVo.getDownLimit()));
                }
                if (!StringUtils.isNullOrBlank(importVo.getUpLimit())) {
                    itemDetail.setUpLimit(new BigDecimal(importVo.getUpLimit()));
                }
                itemDetail.setUnStandard(unStandard);
                RecordUtils.createData(itemDetail);
                itemDetailList.add(itemDetail);
                dataNum++;
            }
        }
        //去重检验父项集合
        List<InspectionItems> collect = itemsList.stream().distinct().collect(Collectors.toList());
        for (InspectionItems inspectionItems : collect) {
            inspectionItems.setId(String.valueOf(idWorker.nextId()));
            RecordUtils.createData(inspectionItems);
        }
        if (!itemsList.isEmpty()) {
            inspectionItemsDao.batchInsert(collect);
        }
        if (!itemDetailList.isEmpty()) {
            inspectionItemDetailDao.batchInsert(itemDetailList);
        }
        return importList;
    }

//    public List<MaterialVo> getMaterialList(List<List<Object>> importList) throws Exception {
//        List<String> materialCodeList = new ArrayList<>(100);
//        List<MaterialVo> materialList = new ArrayList<>(importList.size());
//        int count = importList.size() / 500;
//        if (count > 0) {
//            for (int i = 0; i <= count; i++) {
//                int fromIndex = i * 500;
//                int toIndex = (i + 1) * 500;
//                if (toIndex > importList.size()) {
//                    toIndex = importList.size();
//                }
//                materialCodeList.clear();
//                List<List<Object>> subList = importList.subList(fromIndex, toIndex);
//                for (List<Object> list : subList) {
//                    materialCodeList.add(list.get(0).toString());
//                }
//                materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
//                materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
//            }
//        } else {
//            for (List<Object> list : importList) {
//                materialCodeList.add(list.get(0).toString());
//            }
//            materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
//            materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
//        }
//        return materialList;
//    }


    public List<MaterialVo> getMaterialList(List<QmsInspectionItemsImportVo> importList) throws Exception {
        List<String> materialCodeList = new ArrayList<>(100);
        List<MaterialVo> materialList = new ArrayList<>(importList.size());
        int count = importList.size() / 500;
        if (count > 0) {
            for (int i = 0; i <= count; i++) {
                int fromIndex = i * 500;
                int toIndex = (i + 1) * 500;
                if (toIndex > importList.size()) {
                    toIndex = importList.size();
                }
                materialCodeList.clear();
                List<QmsInspectionItemsImportVo> subList = importList.subList(fromIndex, toIndex);
                for (QmsInspectionItemsImportVo importVo : subList) {
                    if (!StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                        materialCodeList.add(importVo.getMaterialCode());
                    }
                }
                materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
                materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
            }
        } else {
            for (QmsInspectionItemsImportVo itemsImportVo : importList) {
                if (!StringUtils.isNullOrBlank(itemsImportVo.getMaterialCode())) {
                    materialCodeList.add(itemsImportVo.getMaterialCode());
                }
            }
            materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
            materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
        }
        return materialList;
    }


    public List<MaterialVo> getMaterialListByDataProject(List<QmsInspectionDataProtectImportVo> importList) throws Exception {
        List<String> materialCodeList = new ArrayList<>(100);
        List<MaterialVo> materialList = new ArrayList<>(importList.size());
        int count = importList.size() / 500;
        if (count > 0) {
            for (int i = 0; i <= count; i++) {
                int fromIndex = i * 500;
                int toIndex = (i + 1) * 500;
                if (toIndex > importList.size()) {
                    toIndex = importList.size();
                }
                materialCodeList.clear();
                List<QmsInspectionDataProtectImportVo> subList = importList.subList(fromIndex, toIndex);
                for (QmsInspectionDataProtectImportVo importVo : subList) {
                    if (!StringUtils.isNullOrBlank(importVo.getMaterialCode())) {
                        materialCodeList.add(importVo.getMaterialCode());
                    }
                }
                materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
                materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
            }
        } else {
            for (QmsInspectionDataProtectImportVo itemsImportVo : importList) {
                if (!StringUtils.isNullOrBlank(itemsImportVo.getMaterialCode())) {
                    materialCodeList.add(itemsImportVo.getMaterialCode());
                }
            }
            materialCodeList = materialCodeList.stream().distinct().collect(Collectors.toList());
            materialList.addAll(feignService.queryMaterialByCodeIn(materialCodeList));
        }
        return materialList;
    }


    @Override
    public void deleteByMaterialCode(String materialCode) {
        //删除质量项信息
        inMaterialDataProtectDao.deleteByMaterialCode(materialCode);
        //删除检验项a
        List<String> itemCodeList = inspectionItemsDao.getAllItemCodes(materialCode);
        inspectionItemsDao.deleteByMaterialCode(materialCode);
        //删除检验项子项
        if (!itemCodeList.isEmpty()) {
            inspectionItemDetailDao.deleteItemDetailByItemCodeIn(itemCodeList);
        }
    }

    @Override
    public void deleteByMaterialCodeIn(List<String> materialCodeList) {
        //删除质量项信息
        inMaterialDataProtectDao.deleteByMaterialCodeIn(materialCodeList);
        //删除检验项
        List<String> itemCodeList = inspectionItemsDao.getAllItemCodesIn(materialCodeList);
        inspectionItemsDao.deleteByMaterialCodeIn(materialCodeList);
        //删除检验项子项
        if (!itemCodeList.isEmpty()) {
            inspectionItemDetailDao.deleteItemDetailByItemCodeIn(itemCodeList);
        }
    }

    @Override
    public Boolean needInspection(String materialCode, String inspectionType) {
        int count = inMaterialDataProtectDao.needInspection(materialCode, inspectionType);
        if (count > 0) {
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<Map<String, Object>> getEmpyesByDepartId(String id) throws Exception {
        return feignService.getEmpyesByDepartId(id);
    }

    @Override
    public Map<String, Object> delegateInfo(String teamCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        List<Map<String, Object>> list = feignService.findUserByDepartCode(teamCode);
        CoDepartment dept = feignService.findByDepartmentCode(teamCode);
        map.put("list", list);
        map.put("depart", dept);
        return map;
    }

    @Override
    public List<AqlPreviewVo> aqlPreview(String inspectType, String inspectLevel, String aqlCode) {
        List<AqlPreviewVo> list = null;
        // AQL抽样+AQL检验判定
        if (ConstantUtil.AQL_AQL.equals(inspectType)) {
            Map schemeMap = new HashMap();
            schemeMap.put("inspectLevel", inspectLevel);
            List<AqlScheme> schemeList = aqlSchemeDao.queryList(schemeMap);
            if (null != schemeList) {
                list = new ArrayList<>();
                for (AqlScheme aqlScheme : schemeList) {
                    AqlPreviewVo aqlPreviewVo = new AqlPreviewVo();
                    AqlInfo param = new AqlInfo();
                    param.setSampleCode(aqlScheme.getSampleCode());
                    param.setAqlCode(aqlCode);
                    AqlInfo aqlInfo = aqlInfo(param);
                    aqlPreviewVo.setAqlScheme(aqlScheme);
                    aqlPreviewVo.setAqlInfo(aqlInfo);
                    list.add(aqlPreviewVo);
                }
            }
        }

        // AQL抽样+合格率检验判定
        if (ConstantUtil.AQL_rate.equals(inspectType)) {
            Map schemeMap = new HashMap();
            schemeMap.put("inspectLevel", inspectLevel);
            List<AqlScheme> schemeList = aqlSchemeDao.queryList(schemeMap);
            if (null != schemeList) {
                list = new ArrayList<>();
                for (AqlScheme aqlScheme : schemeList) {
                    AqlPreviewVo aqlPreviewVo = new AqlPreviewVo();
                    aqlPreviewVo.setAqlScheme(aqlScheme);
                    list.add(aqlPreviewVo);
                }
            }
        }
        return list;
    }

    @Override
    public AqlInfo aqlInfo(AqlInfo infoMap) {
        return aqlInfoDao.queryByMap(infoMap);
    }

    @Override
    public InspectionDataProtect protect(int batchNum, String materialCode, String inspectionType) {
        Map materialMap = new HashMap();
        materialMap.put("materialCode", materialCode);
        materialMap.put("inspectionType", inspectionType);
        return inMaterialDataProtectDao.queryListByMaterial(materialMap);
    }

    @Override
    public int inspectionNum(int batchNum, String materialCode, String inspectionType) {
        Map materialMap = new HashMap();
        int num = 0;
        materialMap.put("materialCode", materialCode);
        materialMap.put("inspectionType", inspectionType);
        InspectionDataProtect protect = inMaterialDataProtectDao.queryListByMaterial(materialMap);
        if (null != protect) {
            num = getNum(protect.getInspectType(), protect.getInspectLevel(), protect.getInspectionRatio(), batchNum);
        }
        return num;
    }

    @Override
    public int getNum(String inspectType, String inspectLevel, String inspectionRatio, int batchNum) {
        int num = 0;
        if (batchNum < 2) {
            return 1;
        }
        if (ConstantUtil.AQL_AQL.equals(inspectType) || ConstantUtil.AQL_rate.equals(inspectType)) {
            if (500000 < batchNum){
                num = aqlSchemeDao.getNumNew(inspectLevel, batchNum);
            }else {
                num = aqlSchemeDao.getNum(inspectLevel, batchNum);
            }
        }

        if (null == inspectType || ConstantUtil.ratio_rate.equals(inspectType)) {
            if (null != inspectionRatio && !"".equals(inspectionRatio)){
                // 小数点转换成int类型
//                Double a = Double.valueOf(inspectionRatio);
//                Double b = Double.valueOf(batchNum);
//                Double c = a * b / 100;
//                Double d = Math.ceil(c);
//                num = d.intValue();
                BigDecimal a = new BigDecimal(inspectionRatio);
                BigDecimal b = new BigDecimal(batchNum);
                BigDecimal bigDecimal = new BigDecimal("100");
                BigDecimal c = a.multiply(b).divide(bigDecimal);
                BigDecimal d = c.setScale(0, BigDecimal.ROUND_UP);
                num = d.intValue();
            }
        }
        return num == 0 ? 1 : num;
    }

    @Override
    public int inspectionNumForMaterial(int batchNum,String materialCode, String inspectionType) {
        Map materialMap = new HashMap();
        int num = 0;
        materialMap.put("materialCode", materialCode);
        materialMap.put("inspectionType", inspectionType);
        InspectionDataProtect protect = inMaterialDataProtectDao.queryListByMaterial(materialMap);
        if (null != protect) {
            num = getNumForMaterial(protect,batchNum);
        }
        return num;
    }
    @Override
    public int inspectionNumForInBill(int batchNum,String materialCode, String inspectionType) {
        Map materialMap = new HashMap();
        int num = 0;
        materialMap.put("materialCode", materialCode);
        materialMap.put("inspectionType", inspectionType);
        InspectionDataProtect protect = inMaterialDataProtectDao.queryListByMaterial(materialMap);
        if (null != protect) {
            num = getNumForInBill(protect,batchNum);
        }
        return num;
    }

    public int getNumForInBill(InspectionDataProtect protect,int batchNum) {
        String inspectionSamplingMethod = protect.getInspectionSamplingMethod();
        String inspectLevel = protect.getInspectLevel();
        String inspectionRatio = protect.getInspectionRatio();
        BigDecimal quSampling = protect.getQuSampling();
        int num = 0;
        if (batchNum < 2) {
            return 1;
        }
        if (ConstantUtil.AQL.equals(inspectionSamplingMethod)) {
            if (500000 < batchNum){
                num = aqlSchemeDao.getNumNew(inspectLevel, batchNum);
            }else {
                num = aqlSchemeDao.getNum(inspectLevel, batchNum);
            }
        }else if (ConstantUtil.ratio.equals(inspectionSamplingMethod)){
            if (null != inspectionRatio && !"".equals(inspectionRatio)){
                BigDecimal a = new BigDecimal(inspectionRatio);
                BigDecimal b = new BigDecimal(batchNum);
                BigDecimal bigDecimal = new BigDecimal("100");
                BigDecimal c = a.multiply(b).divide(bigDecimal);
                BigDecimal d = c.setScale(0, BigDecimal.ROUND_UP);
                num = d.intValue();
            }
        }else if (ConstantUtil.qu.equals(inspectionSamplingMethod)){
            int i = quSampling.intValue();//填写的定量数量
            if (i >= batchNum){
                num = batchNum;
            }else {
                num = i;
            }
        }else {
            AssertUtil.throwException("入库检验抽样方式不正确，请重新配置");
        }
        return num == 0 ? 1 : num;
    }

    public int getNumForMaterial(InspectionDataProtect protect,int batchNum) {
        String inspectionSamplingMethod = protect.getInspectionSamplingMethod();
        String inspectLevel = protect.getInspectLevel();
        String inspectionRatio = protect.getInspectionRatio();
        BigDecimal quSampling = protect.getQuSampling();
        int num = 0;
        if (batchNum < 2) {
            return 1;
        }
        if (ConstantUtil.AQL.equals(inspectionSamplingMethod)) {
            if (500000 < batchNum){
                num = aqlSchemeDao.getNumNew(inspectLevel, batchNum);
            }else {
                num = aqlSchemeDao.getNum(inspectLevel, batchNum);
            }
        }else if (ConstantUtil.ratio.equals(inspectionSamplingMethod)){
            if (null != inspectionRatio && !"".equals(inspectionRatio)){
                BigDecimal a = new BigDecimal(inspectionRatio);
                BigDecimal b = new BigDecimal(batchNum);
                BigDecimal bigDecimal = new BigDecimal("100");
                BigDecimal c = a.multiply(b).divide(bigDecimal);
                BigDecimal d = c.setScale(0, BigDecimal.ROUND_UP);
                num = d.intValue();
            }
        }else if (ConstantUtil.qu.equals(inspectionSamplingMethod)){
            int i = quSampling.intValue();//填写的定量数量
            if (i >= batchNum){
                num = batchNum;
            }else {
                num = i;
            }
        }else {
            AssertUtil.throwException("来料检验抽样方式不正确，请重新配置");
        }
        return num == 0 ? 1 : num;
    }
}
