package com.imes.qms.service.impl;

import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RecordUtils;
import com.imes.domain.entities.ppc.po.PpcRouteLine;
import com.imes.domain.entities.ppc.po.Route;
import com.imes.domain.entities.qms.po.QmsRouteInspection;
import com.imes.domain.entities.qms.po.QmsRouteInspectionDetail;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.QmsRouteInspectionDetailVo;
import com.imes.qms.dao.TechnologyInspectionPublishDao;
import com.imes.qms.dao.TechnologyInspectionSupportDao;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.TechnologyInspectionPublishService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class TechnologyInspectionPublishServiceImpl implements TechnologyInspectionPublishService {

    @Autowired
    TechnologyInspectionSupportDao technologyInspectionSupportDao;

    @Autowired
    TechnologyInspectionPublishDao technologyInspectionPublishDao;

    @Autowired
    FeignService feignService;

    @Autowired
    IdWorker idWorker;

    @Override
    public List<TechnologyInspection> getInspectionPlanList(Map map) {
        List<TechnologyInspection> technologyInspectionList = technologyInspectionSupportDao.getInspectionPlanList(map);
        return technologyInspectionList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updatePublishStatus(TechnologyInspection inspection) throws Exception {

        String publishStatus = inspection.getPublishStatus();
        // 重新发布
        if (ConstantUtil.has_stopped.equals(publishStatus)) {
            inspection.setStatus(ConstantUtil.has_stopped);
            RecordUtils.updateData(inspection);
            technologyInspectionPublishDao.updatePublishStatus(inspection);
            inspection.setPlanCode(feignService.getAutoCode("INSPECTION_PUBLISH"));
            inspection.setPublishStatus(ConstantUtil.un_publish);
            inspection.setId(idWorker.nextId() + "");
            inspection.setUpdateBy(null);
            inspection.setUpdateOn(null);
            RecordUtils.createData(inspection);
            technologyInspectionSupportDao.insertSelective(inspection);
        } else if (ConstantUtil.has_rejected.equals(publishStatus)) {
            // 点击驳回按钮（3），状态变成已驳回
            inspection.setStatus(ConstantUtil.has_reject);
            RecordUtils.updateData(inspection);
            technologyInspectionPublishDao.updatePublishStatus(inspection);
        } else {
            // 点击发布按钮（1），状态变成已发布
            RecordUtils.updateData(inspection);
            technologyInspectionPublishDao.updatePublishStatus(inspection);
        }
    }

    @Override
    public List<QmsRouteInspectionDetailVo> getInspectionItemDetail(String bomCode,String bomVer,String processCode) {
        // 获取生产路线
        PpcRouteLine route = technologyInspectionSupportDao.findByBomCodeAndBomVer(bomCode, bomVer);
        if (null==route) {
            return null;
        }
        // 获取质检项
        Map paramMap = new HashMap();
        paramMap.put("routeCode",route.getRouteCode());
        paramMap.put("processCode",processCode);
        List<QmsRouteInspection>  qmsList = technologyInspectionSupportDao.getQmsRoute(paramMap);
//        // 获取工艺文件id
//        List<String> fileIdList = technologyInspectionSupportDao.getFileLists(paramMap);
//        // 获取工位
//        List<Map> station = technologyInspectionSupportDao.getStationByProcessCode(processCode);
//        map.put("qms",qmsList);
//        map.put("station",station);
//        map.put("fileLists",fileIdList);
        List<QmsRouteInspectionDetailVo> resultList = new ArrayList<>();
        for (QmsRouteInspection qmsRouteInspection : qmsList) {
            String mainId = qmsRouteInspection.getId();
            String mainItemName = qmsRouteInspection.getInspectName();
            String mainItemCode = qmsRouteInspection.getInspectCode();
            Map map = new HashMap();
            map.put("mainId",mainId);
            List<QmsRouteInspectionDetailVo> detailList = technologyInspectionPublishDao.getInspectionItemDetail(map);
            detailList.forEach(x->{
                x.setMainItemName(mainItemName);
                x.setMainItemCode(mainItemCode);
            });
            resultList.addAll(detailList);
        }
        return resultList;
    }
}
