package com.imes.qms.service.impl;

import com.imes.common.support.Query;
import com.imes.common.utils.Constants;
import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcProducePlanSchedul;
import com.imes.domain.entities.ppc.vo.PpcWorkFinishVo;
import com.imes.domain.entities.qms.po.ProcessBadCount;
import com.imes.domain.entities.qms.po.ProcessInspection;
import com.imes.domain.entities.qms.po.ProcessQualityReportDetail;
import com.imes.domain.entities.qms.po.ProcessTask;
import com.imes.domain.entities.qms.vo.ProcessBadCountsQualityReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityBadCountReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityReportVO;
import com.imes.domain.entities.system.empyes;
import com.imes.domain.entities.query.template.qms.QmsProcessInspectionReportListVo;
import com.imes.qms.dao.ProcessBadCountDao;
import com.imes.qms.dao.ProcessInspectionDao;
import com.imes.qms.dao.ProcessQualityReportDao;
import com.imes.qms.dao.ProcessTaskDao;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.ProcessQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProcessQualityReportServiceImpl implements ProcessQualityReportService {

    @Autowired
    ProcessQualityReportDao processQualityReportDao;

    @Autowired
    ProcessInspectionDao processInspectionDao;

    @Autowired
    ProcessTaskDao processTaskDao;

    @Autowired
    ProcessBadCountDao processBadCountDao;

    @Autowired
    FeignService feignService;

    @Autowired
    IdWorker idWorker;

////    @Override
////    public List<ProcessQualityReportVO> getInspectionList(Map paramMap) {
////        List<ProcessQualityReportVO> voList = null;
////        // 获取所有排产单号
////        List<String> ppNos = processQualityReportDao.getPpNos(paramMap);
////        if (null != ppNos&&ppNos.size() > 0) {
////            voList = new ArrayList<>();
////            String newPpNos = StringUtils.join(ppNos.toArray(),",");
////            paramMap.put("ppNos", com.imes.common.utils.StringUtils.str2In(newPpNos));
////            List<ProcessInspection> inspections = processInspectionDao.queryList(paramMap);
////            for (String ppNo : ppNos) {
////                ProcessQualityReportVO reportVO = new ProcessQualityReportVO();
////                List<ProcessInspection> ppNoList = inspections.stream()
////                                                .filter(x->Objects.equals(x.getPpNo(),ppNo))
////                                                .collect(Collectors.toList());
////                Map<String,Long> qualifiedCounts = ppNoList.stream()
////                                                    .collect(Collectors.groupingBy(x->x.getIsQualified(),Collectors.counting()));
////                String materialName = ppNoList.get(0).getMaterialName();
////                String materialCode = ppNoList.get(0).getMaterialCode();
////                String materialMarker = ppNoList.get(0).getMaterialMarker();
////                String specification = ppNoList.get(0).getSpecification();
////                String primaryUnit = ppNoList.get(0).getPrimaryUnit();
////                reportVO.setMaterialCode(materialCode);
////                reportVO.setMaterialName(materialName);
////                reportVO.setMaterialMarker(materialMarker);
////                reportVO.setSpecification(specification);
////                reportVO.setPrimaryUnit(primaryUnit);
////                reportVO.setPpNo(ppNo);
////                reportVO.setInspectionNum(ppNoList.size());
////                reportVO.setIsQualified(qualifiedCounts.get("合格"));
////                reportVO.setUnQualified(qualifiedCounts.get("不合格"));
////                voList.add(reportVO);
////
////            }
////        }
////        // 获取所有临时单号
////        paramMap.remove("ppNos");
////        List<String> tempNos = processQualityReportDao.getTempNos(paramMap);
////        if (null != tempNos&&tempNos.size() > 0) {
////            if (null == voList) {
////                voList = new ArrayList<>();
////            }
////            String newTempNos = StringUtils.join(tempNos.toArray(),",");
////            paramMap.put("tempNos", com.imes.common.utils.StringUtils.str2In(newTempNos));
////            List<ProcessInspection> inspections = processInspectionDao.queryList(paramMap);
////            for (String tempNo : tempNos) {
////                InBillQualityReportVO reportVO = new InBillQualityReportVO();
////                List<ProcessInspection> tempNoList = inspections.stream()
////                        .filter(x->Objects.equals(x.getTemporaryNo(),tempNo))
////                        .collect(Collectors.toList());
////                Map<String,Long> qualifiedCounts = tempNoList.stream()
////                        .collect(Collectors.groupingBy(x->x.getIsQualified(),Collectors.counting()));
////                String materialName = tempNoList.get(0).getMaterialName();
////                String materialCode = tempNoList.get(0).getMaterialCode();
////                String materialMarker = tempNoList.get(0).getMaterialMarker();
////                String specification = tempNoList.get(0).getSpecification();
////                String primaryUnit = tempNoList.get(0).getPrimaryUnit();
////                reportVO.setMaterialCode(materialCode);
////                reportVO.setMaterialName(materialName);
////                reportVO.setMaterialMarker(materialMarker);
////                reportVO.setSpecification(specification);
////                reportVO.setPrimaryUnit(primaryUnit);
////                reportVO.setPpNo(tempNo);
////                reportVO.setInspectionNum(tempNoList.size());
////                reportVO.setIsQualified(qualifiedCounts.get("合格"));
////                reportVO.setUnQualified(qualifiedCounts.get("不合格"));
////                voList.add(reportVO);
////
////            }
////        }
//        return voList;
//    }

    @Override
    public List<ProcessQualityReportVO> getInspectionList(Map map) throws Exception {
        List<ProcessQualityReportVO> voList = null;
        // 获取所有排产单号
        List<String> pcNos = processQualityReportDao.getPcNos(map);
        if (null != pcNos && pcNos.size() > 0) {
            voList = new ArrayList<>();
            for (String pcNo : pcNos) {
                Map<String,String> pcNoMap = processQualityReportDao.getMaterialByPcNo(pcNo);
                String materialCode = pcNoMap.get("materialCode");
                String materialName = pcNoMap.get("materialName");
                String materialMarker = pcNoMap.get("materialMarker");
                String specification = pcNoMap.get("specification");
                String primaryUnit = pcNoMap.get("primaryUnit");
                String ppNo = pcNoMap.get("ppNo");
                map.put("pcNo",pcNo);
                // 获取不良品合计
//                List<ProcessQualityBadCountReportVO> reportVOS = getBadCountDetail(map);
//                int realNum = reportVOS.stream().collect(Collectors.summingInt(ProcessQualityBadCountReportVO::getFinishNum));
//                int badNum = reportVOS.stream().collect(Collectors.summingInt(ProcessQualityBadCountReportVO::getAllBadQty));
//                int gooQty = realNum - badNum;

                ProcessQualityReportVO reportVO = new ProcessQualityReportVO();
                List<PpcProducePlanSchedul> pcList = feignService.queryAll(pcNo);
                if (null != pcList && pcList.size() > 0) {
                    BigDecimal goodQty = pcList.get(0).getGoodQty();
                    BigDecimal badQty = pcList.get(0).getBadQty();
                    reportVO.setPcNum(null == pcList.get(0).getProduceQty() ? 0: pcList.get(0).getProduceQty().intValue());
                    reportVO.setStatus(null == pcList.get(0).getStatus() ? "":pcList.get(0).getStatus());
                    BigDecimal completePercent = null;
                    if (reportVO.getPcNum() == 0) {
                        completePercent = new BigDecimal(0);
                    } else {
                        //completePercent = new BigDecimal(gooQty*100).divide(new BigDecimal(reportVO.getPcNum()),BigDecimal.ROUND_HALF_UP);
                        completePercent = goodQty.multiply(new BigDecimal(100)).divide(new BigDecimal(reportVO.getPcNum()),BigDecimal.ROUND_HALF_UP);
                    }
                    reportVO.setCompletePercent(completePercent);
                    reportVO.setRealFinishNum(goodQty);
                    reportVO.setBadNum(badQty);
                    reportVO.setGoodQty(goodQty);
                }
//                reportVO.setBadRate(badRate);
                reportVO.setMaterialCode(materialCode);
                reportVO.setMaterialName(materialName);
                reportVO.setMaterialMarker(materialMarker);
                reportVO.setSpecification(specification);
                reportVO.setPrimaryUnit(primaryUnit);
                reportVO.setPpNo(ppNo);
                reportVO.setPcNo(pcNo);
                reportVO.setId(idWorker.nextId()+"");
                voList.add(reportVO);

            }
        }
        return voList;
    }

    @Override
    public List<ProcessQualityReportDetail> getInspectionDetail(Map map) {
        List<ProcessQualityReportDetail> details = processQualityReportDao.queryDetails(map);
        if (null != details && details.size() > 0) {
            details.forEach(x->{
                empyes empyes = feignService.findByUserCode(x.getInspector());
                if (null != empyes) {
                    x.setInspector(empyes.getUserName());
                }
            });
        }
        return details;
    }

    @Override
    public List<ProcessQualityBadCountReportVO> getBadCountDetail(Map map) throws Exception {
        List<String> inspectionCodes = processQualityReportDao.getInspectionCodesByPcNo(map);
        List<ProcessQualityBadCountReportVO> resultList = new ArrayList<>(inspectionCodes.size());
        //查看生产计划增加数据权限
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            List<String> userCodes = feignService.dataPermission(RedisUtils.getUserCode());
            StringBuffer planerCode = new StringBuffer();
            planerCode.append(",admin");
            for (String userCode : userCodes){
                planerCode.append(","+userCode);
            }
            String userCodesIn = StringUtils.str2In(planerCode.toString().substring(1));
            map.put("userCodesIn",userCodesIn);
        }
        for (String inspectionCode : inspectionCodes) {
            map.put("inspectionCode",inspectionCode);
            ProcessInspection processInspection = processInspectionDao.getByInspectionCode(map);
            if (null == processInspection) {
                continue;
            }
            ProcessQualityBadCountReportVO result = new ProcessQualityBadCountReportVO();
            result.setWfNo(processInspection.getWfNo());
            List<PpcWorkFinishVo>  list = feignService.getWorkFinishByPcNo(processInspection.getWfNo());
            // 获取报工信息
            ProcessTask processTask = processTaskDao.queryByWfNo(processInspection.getWfNo());
            result.setId(idWorker.nextId()+"");
            result.setProcessCode(processTask.getProcessCode());
            result.setProcessName(processTask.getProcessName());
            result.setFinishGoodQty(processTask.getInspectionNum());
            result.setFinishTime(processTask.getCreateOn());
            if (null != list && list.size() > 0) {
                result.setFinisherName(list.get(0).getWorkerName());
                result.setTeamName(list.get(0).getTeamName());
                result.setWorkshopName(list.get(0).getWorkshopName());
            }
            // 获取报工不良项信息
            int badNum = processBadCountDao.querySumBadNum(processInspection.getWfNo(),processTask.getProcessCode(),ConstantUtil.bad_finish,ConstantUtil.process);
            result.setFinishBadQty(badNum);
            result.setFinishNum(result.getFinishGoodQty()+badNum);
            // 获取检验信息
            result.setInspectionCode(inspectionCode);
            result.setInspectionNum(processInspection.getInspectionNum());
            result.setIsQualified(processInspection.getIsQualified());
            result.setInspectionBadQty(processInspection.getUnqualifiedNum());
            result.setInspectionGoodQty(processInspection.getInspectionNum()-processInspection.getUnqualifiedNum());
            empyes empyes = feignService.findByUserCode(processInspection.getInspector());
            if (null != empyes) {
                result.setInspector(empyes.getUserName());
            }
            result.setInspectionTime(processInspection.getCreateOn());
            // 暂时检验比率为100%
            result.setAllGoodQty(result.getInspectionGoodQty());
            result.setAllBadQty(badNum+ result.getInspectionBadQty());
            // 获取不良项数量信息
            int badItemNum = processBadCountDao.querySumBadNum(inspectionCode, processInspection.getProcessCode(),ConstantUtil.bad_inspection,ConstantUtil.process);
            result.setBadItemQty(badItemNum+badNum);
            BigDecimal badRate = new BigDecimal(result.getBadItemQty()*100).divide(new BigDecimal(result.getFinishNum()),BigDecimal.ROUND_HALF_UP);
            result.setBadPercent(badRate);
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public Map<String,List<ProcessBadCount>> getBadCountClassify(String inspectionCode,String wfNo,String processCode) {
        Map<String,List<ProcessBadCount>> resultMap = new HashMap<>();
        Map map = new HashMap();
        map.put("inspectionCode",inspectionCode);
        map.put("wfNo",wfNo);
        map.put("processCode",processCode);
        map.put("inspectionType",ConstantUtil.process);
        List<ProcessBadCount> resultList = processBadCountDao.queryList(map);
        List<ProcessBadCount> finishList = resultList.stream()
                                                .filter(x->Objects.equals(x.getStatus(),ConstantUtil.bad_finish))
                                                .collect(Collectors.toList());
        List<ProcessBadCount> inspectionList = resultList.stream()
                                                .filter(x->Objects.equals(x.getStatus(),ConstantUtil.bad_inspection))
                                                .collect(Collectors.toList());
        resultMap.put("finish",finishList);
        resultMap.put("inspection",inspectionList);
        return resultMap;
    }

    @Override
    public List<ProcessBadCountsQualityReportVO> getInspectionByBadCounts(Map map) throws Exception {
        List<ProcessBadCountsQualityReportVO> protectList = null;
        if (null != map.get("startTime")) {
            protectList = feignService.queryBadCount(map.get("startTime").toString(),map.get("endTime").toString(),null);
        } else {
            protectList = feignService.queryBadCount2();
        }
        if (feignService.getAuthorityByModuleCode(Constants.MODULE_CODE_QMS)) {
            List<ProcessBadCountsQualityReportVO> qmsList = processQualityReportDao.getInspectionByBadCounts(map);
            if (null != qmsList && qmsList.size() > 0) {
                protectList.addAll(qmsList);
            }
            return new ArrayList<>(protectList.stream().collect(Collectors.toMap(ProcessBadCountsQualityReportVO::getBadCode, a -> a, (o1, o2) -> {
                o1.setSumQty(o1.getSumQty() + o2.getSumQty());
                return o1;
            })).values()).stream().sorted(Comparator.comparing(ProcessBadCountsQualityReportVO::getSumQty).reversed()).collect(Collectors.toList());
        } else {
            return protectList;
        }
    }

    @Override
    public List<Map> getBadCountDetails(String badCode,
                                     String materialCode,String materialName,
                                     String processCode,String processName,
                                     String specification) throws Exception {
        List<Map> mapList = feignService.getBadCountDetails(badCode,materialCode,materialName,processCode,
                processName,specification);
        if (feignService.getAuthorityByModuleCode(Constants.MODULE_CODE_QMS)) {
            List<Map> qmsBadList = processQualityReportDao.getQmsBadList(badCode,materialCode,materialName,processCode,
                    processName,specification,ConstantUtil.process);
            if (null != qmsBadList && qmsBadList.size() > 0) {
                mapList.addAll(qmsBadList);
            }
        }
        return mapList;
    }

    @Override
    public List<ProcessQualityReportVO> getInspectionListNew(QmsProcessInspectionReportListVo vo) throws Exception {
        Query q = Query.build();
        q.apply("inspection_status in ({0})",vo.getProcess());
        List<ProcessQualityReportVO> voList = null;
        // 获取所有排产单号
        List<String> pcNos = processQualityReportDao.getPcNosNew(q.initParams(vo));
        if (null != pcNos && pcNos.size() > 0) {
            voList = new ArrayList<>();
            for (String pcNo : pcNos) {
                Map<String,String> pcNoMap = processQualityReportDao.getMaterialByPcNo(pcNo);
                String materialCode = pcNoMap.get("materialCode");
                String materialName = pcNoMap.get("materialName");
                String materialMarker = pcNoMap.get("materialMarker");
                String specification = pcNoMap.get("specification");
                String primaryUnit = pcNoMap.get("primaryUnit");
                String ppNo = pcNoMap.get("ppNo");
                vo.setPcNo(pcNo);
                // 获取不良品合计
                List<ProcessQualityBadCountReportVO> reportVOS = getBadCountDetailNew(vo);
                ProcessQualityReportVO reportVO = new ProcessQualityReportVO();
                List<PpcProducePlanSchedul> pcList = feignService.queryAll(pcNo);
                if (null != pcList && pcList.size() > 0) {
                    BigDecimal goodQty = pcList.get(0).getGoodQty();
                    BigDecimal badQty = pcList.get(0).getBadQty();
                    reportVO.setPcNum(null == pcList.get(0).getProduceQty() ? 0: pcList.get(0).getProduceQty().intValue());
                    reportVO.setStatus(null == pcList.get(0).getStatus() ? "":pcList.get(0).getStatus());
                    BigDecimal completePercent = null;
                    if (reportVO.getPcNum() == 0) {
                        completePercent = new BigDecimal(0);
                    } else {
                        //completePercent = new BigDecimal(gooQty*100).divide(new BigDecimal(reportVO.getPcNum()),BigDecimal.ROUND_HALF_UP);
                        completePercent = goodQty.multiply(new BigDecimal(100)).divide(new BigDecimal(reportVO.getPcNum()),BigDecimal.ROUND_HALF_UP);
                    }
                    reportVO.setCompletePercent(completePercent);
                    reportVO.setRealFinishNum(goodQty);
                    reportVO.setBadNum(badQty);
                    reportVO.setGoodQty(goodQty);
                }
//                reportVO.setBadRate(badRate);
                reportVO.setMaterialCode(materialCode);
                reportVO.setMaterialName(materialName);
                reportVO.setMaterialMarker(materialMarker);
                reportVO.setSpecification(specification);
                reportVO.setPrimaryUnit(primaryUnit);
                reportVO.setPpNo(ppNo);
                reportVO.setPcNo(pcNo);
                reportVO.setId(idWorker.nextId()+"");
                voList.add(reportVO);

            }
        }
        return voList;
    }

    private List<ProcessQualityBadCountReportVO> getBadCountDetailNew(QmsProcessInspectionReportListVo vo) throws Exception {
        List<String> inspectionCodes = processQualityReportDao.getInspectionCodesByPcNoNew(vo);
        List<ProcessQualityBadCountReportVO> resultList = new ArrayList<>(inspectionCodes.size());
        Map map = new HashMap();
        map.put("process",ConstantUtil.adopt);
        //查看生产计划增加数据权限
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            List<String> userCodes = feignService.dataPermission(RedisUtils.getUserCode());
            StringBuffer planerCode = new StringBuffer();
            planerCode.append(",admin");
            for (String userCode : userCodes){
                planerCode.append(","+userCode);
            }
            String userCodesIn = StringUtils.str2In(planerCode.toString().substring(1));
            map.put("userCodesIn",userCodesIn);
        }
        for (String inspectionCode : inspectionCodes) {
            map.put("inspectionCode",inspectionCode);
            ProcessInspection processInspection = processInspectionDao.getByInspectionCode(map);
            if (null == processInspection) {
                continue;
            }
            ProcessQualityBadCountReportVO result = new ProcessQualityBadCountReportVO();
            result.setWfNo(processInspection.getWfNo());
            List<PpcWorkFinishVo>  list = feignService.getWorkFinishByPcNo(processInspection.getWfNo());
            // 获取报工信息
            ProcessTask processTask = processTaskDao.queryByWfNo(processInspection.getWfNo());
            result.setId(idWorker.nextId()+"");
            result.setProcessCode(processTask.getProcessCode());
            result.setProcessName(processTask.getProcessName());
            result.setFinishGoodQty(processTask.getInspectionNum());
            result.setFinishTime(processTask.getCreateOn());
            if (null != list && list.size() > 0) {
                result.setFinisherName(list.get(0).getWorkerName());
                result.setTeamName(list.get(0).getTeamName());
                result.setWorkshopName(list.get(0).getWorkshopName());
            }
            // 获取报工不良项信息
            int badNum = processBadCountDao.querySumBadNum(processInspection.getWfNo(),processTask.getProcessCode(),ConstantUtil.bad_finish,ConstantUtil.process);
            result.setFinishBadQty(badNum);
            result.setFinishNum(result.getFinishGoodQty()+badNum);
            // 获取检验信息
            result.setInspectionCode(inspectionCode);
            result.setInspectionNum(processInspection.getInspectionNum());
            result.setIsQualified(processInspection.getIsQualified());
            result.setInspectionBadQty(processInspection.getUnqualifiedNum());
            result.setInspectionGoodQty(processInspection.getInspectionNum()-processInspection.getUnqualifiedNum());
            empyes empyes = feignService.findByUserCode(processInspection.getInspector());
            if (null != empyes) {
                result.setInspector(empyes.getUserName());
            }
            result.setInspectionTime(processInspection.getCreateOn());
            // 暂时检验比率为100%
            result.setAllGoodQty(result.getInspectionGoodQty());
            result.setAllBadQty(badNum+ result.getInspectionBadQty());
            // 获取不良项数量信息
            int badItemNum = processBadCountDao.querySumBadNum(inspectionCode, processInspection.getProcessCode(),ConstantUtil.bad_inspection,ConstantUtil.process);
            result.setBadItemQty(badItemNum+badNum);
            BigDecimal badRate = new BigDecimal(result.getBadItemQty()*100).divide(new BigDecimal(result.getFinishNum()),BigDecimal.ROUND_HALF_UP);
            result.setBadPercent(badRate);
            resultList.add(result);
        }
        return resultList;
    }
}