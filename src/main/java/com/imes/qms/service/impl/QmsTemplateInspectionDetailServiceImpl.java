package com.imes.qms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imes.common.support.Query;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RecordUtils;
import com.imes.domain.entities.qms.po.QmsTemplateInspectionDetail;
import com.imes.domain.entities.query.model.po.QueryModelTemplate;
import com.imes.domain.entities.query.template.qms.QmsTemplateInspectionDetailQueryVo;
import com.imes.qms.dao.QmsTemplateInspectionDetailMapper;
import com.imes.qms.service.QmsTemplateInspectionDetailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class QmsTemplateInspectionDetailServiceImpl extends ServiceImpl<QmsTemplateInspectionDetailMapper, QmsTemplateInspectionDetail> implements QmsTemplateInspectionDetailService {

    @Autowired
    private IdWorker idWorker;

    /**
     * 保存搜索列权限列表
     *
     * @param button 查询按钮
     * @return
     * @throws Exception
     */
    public void apply(QmsTemplateInspectionDetail button) {
        // 名字重复检查
        Query nameQuery = Query.build(QueryModelTemplate.class)
                .idNe(button.getId())
                .sceneEq("0");
        AssertUtil.isTrue(nameQuery.getCount(baseMapper) <= 0, "按钮名称已存在，请检查！");
        if (StrUtil.isBlank(button.getId())) {
            button.setId(idWorker.nextIdStr());
            RecordUtils.createData(button);
            baseMapper.insert(button);
        } else {
            AssertUtil.notNull(baseMapper.selectById(button.getId()), "方案内容不存在！");
            RecordUtils.updateData(button);
            baseMapper.updateById(button);
        }
    }

    /**
     * 获取ID获取详情
     *
     * @param id id
     * @return
     * @throws Exception
     */
    public QmsTemplateInspectionDetail info(String id) {
        return baseMapper.selectById(id);
    }

    /**
     * 获取列权限列表
     *
     * @param vo 权限搜索参数
     * @return
     * @throws Exception
     */
    public List<QmsTemplateInspectionDetailQueryVo> queryList(QmsTemplateInspectionDetailQueryVo vo) {
        return baseMapper.selectList(Query.build().initParams(vo));
    }

    /**
     * 根据id删除质检项
     *
     * @param id
     */
    public void del(String id) {
        baseMapper.deleteById(id);
    }

    @Override
    public List<QmsTemplateInspectionDetail> getByMainId(String id) {
        return baseMapper.selectList(Query.build().mainIdEq(id));
    }
}
