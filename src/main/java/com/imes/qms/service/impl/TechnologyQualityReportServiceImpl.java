package com.imes.qms.service.impl;

import com.imes.common.utils.Constants;
import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcProcess;
import com.imes.domain.entities.ppc.po.PpcProducePlanSchedul;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.qms.vo.TechnologyQualityReportVO;
import com.imes.domain.entities.system.empyes;
import com.imes.qms.dao.FirstAndEndInspectionDao;
import com.imes.qms.dao.ProcessBadCountDao;
import com.imes.qms.dao.TechnologyQualityReportDao;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.TechnologyQualityReportService;
import com.imes.qms.service.TechnologyQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TechnologyQualityReportServiceImpl implements TechnologyQualityReportService {

    @Autowired
    TechnologyQualityReportDao technologyQualityReportDao;

    @Autowired
    IdWorker idWorker;

    @Autowired
    ProcessBadCountDao processBadCountDao;

    @Autowired
    FirstAndEndInspectionDao firstAndEndInspectionDao;

    @Autowired
    FeignService feignService;

    @Override
    public List<FirstFinalQualityReportVO> getFirstEndInspectionList(Map map) throws Exception {

        List<FirstFinalQualityReportVO> voList = null;
        //查看生产计划增加数据权限
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            List<String> userCodes = feignService.dataPermission(RedisUtils.getUserCode());
            StringBuffer planerCode = new StringBuffer();
            planerCode.append(",admin");
            for (String userCode : userCodes){
                planerCode.append(","+userCode);
            }
            String userCodesIn = StringUtils.str2In(planerCode.toString().substring(1));
            map.put("userCodesIn",userCodesIn);
        }
        // 获取所有工序
        List<String> processCodes = technologyQualityReportDao.getProcessCodes(map);
        if (null != processCodes && processCodes.size() > 0) {
            voList = new ArrayList<>();
            for (String processCode : processCodes) {
                Map<String,String> pcNoMap = technologyQualityReportDao.getMaterialByProcessCode(processCode);
                String materialCode = pcNoMap.get("materialCode");
                String materialName = pcNoMap.get("materialName");
                String materialMarker = pcNoMap.get("materialMarker");
                String specification = pcNoMap.get("specification");
                String primaryUnit = pcNoMap.get("primaryUnit");
                String taskInspectionCode = pcNoMap.get("taskInspectionCode");
                map.put("processCode",processCode);
                // 获取不良品合计
                List<FirstFinalQualityBadCountReportVO> reportVOS = getBadCountDetail(map);
                if (reportVOS.size() >0 ){
                    int realNum = reportVOS.stream().collect(Collectors.summingInt(FirstFinalQualityBadCountReportVO::getInspectionNum));
                    int badNum = reportVOS.stream().collect(Collectors.summingInt(FirstFinalQualityBadCountReportVO::getInspectionBadQty));
                    BigDecimal badRate = new BigDecimal(badNum*100).divide(new BigDecimal(realNum),BigDecimal.ROUND_HALF_UP);
                    FirstFinalQualityReportVO reportVO = new FirstFinalQualityReportVO();
//                List<PpcProducePlanSchedul> pcList = feignService.queryAll(pcNo);
//                if (null != pcList&&pcList.size() > 0) {
//                    reportVO.setPcNum(null == pcList.get(0).getProduceQty()?0:pcList.get(0).getProduceQty());
//                }
                    PpcProcess p = feignService.queryByProcessCode(processCode);
                    reportVO.setInspectionNum(realNum);
                    reportVO.setBadNum(badNum);
                    reportVO.setBadRate(badRate);
                    reportVO.setMaterialCode(materialCode);
                    reportVO.setMaterialName(materialName);
                    reportVO.setMaterialMarker(materialMarker);
                    reportVO.setSpecification(specification);
                    reportVO.setPrimaryUnit(primaryUnit);
                    reportVO.setProcessCode(processCode);
                    reportVO.setProcessName(p.getProcessName());
                    reportVO.setTaskInspectionCode(taskInspectionCode);
                    reportVO.setId(idWorker.nextId()+"");
                    voList.add(reportVO);
                }
            }
        }
        return voList;
    }
    @Override
    public List<FirstFinalQualityBadCountReportVO> getBadCountDetail(Map map) throws Exception {
        //查看生产计划增加数据权限
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            List<String> userCodes = feignService.dataPermission(RedisUtils.getUserCode());
            StringBuffer planerCode = new StringBuffer();
            planerCode.append(",admin");
            for (String userCode : userCodes){
                planerCode.append(","+userCode);
            }
            String userCodesIn = StringUtils.str2In(planerCode.toString().substring(1));
            map.put("userCodesIn",userCodesIn);
        }
        List<String> inspectionCodes = technologyQualityReportDao.getInspectionCodesByProcessCode(map);
        List<FirstFinalQualityBadCountReportVO> resultList = new ArrayList<>(inspectionCodes.size());
        for (String inspectionCode : inspectionCodes) {
            map.put("inspectionCode",inspectionCode);
            FirstAndEndInspection firstAndEndInspection = firstAndEndInspectionDao.getByInspectionCode(map);
            FirstFinalQualityBadCountReportVO result = new FirstFinalQualityBadCountReportVO();
            result.setId(idWorker.nextId()+"");
//            result.setProcessCode(processTask.getProcessCode());
//            result.setProcessName(processTask.getProcessName());
//            result.setFinishGoodQty(processTask.getInspectionNum());
//            result.setFinishTime(processTask.getCreateOn());
            // 获取检验信息
            result.setInspectionCode(inspectionCode);
            result.setInspectionNum(firstAndEndInspection.getInspectionNum());
            result.setIsQualified(firstAndEndInspection.getIsQualified());
            result.setInspectionBadQty(firstAndEndInspection.getUnqualifiedNum());
            result.setInspectionGoodQty(firstAndEndInspection.getInspectionNum()-firstAndEndInspection.getUnqualifiedNum());
            result.setPcNo(firstAndEndInspection.getPcNo());
            result.setPpNo(firstAndEndInspection.getPpNo());
            empyes empyes = feignService.findByUserCode(firstAndEndInspection.getInspector());
            if (null != empyes) {
                result.setInspector(empyes.getUserName());
            }
            result.setInspectionTime(firstAndEndInspection.getCreateOn());
            resultList.add(result);
        }
        return resultList;
    }

    @Override
    public List<TechnologyQualityReportDetail> getInspectionDetail(Map map) {
        List<TechnologyQualityReportDetail> details = technologyQualityReportDao.queryDetails(map);
        if (null != details && !details.isEmpty()) {
            details.forEach(x->{
                empyes empyes = feignService.findByUserCode(x.getInspector());
                if (null != empyes) {
                    x.setInspector(empyes.getUserName());
                }
            });
        }
        return details;
    }

    @Override
    public Map<String,List<ProcessBadCount>> getBadCountClassify(String processCode,String inspectionCode) {
        Map<String,List<ProcessBadCount>> resultMap = new HashMap<>();
        Map map = new HashMap();
        map.put("inspectionCode",inspectionCode);
        map.put("processCode",processCode);
        map.put("inspectionType",ConstantUtil.firstFinal);
        List<ProcessBadCount> resultList = processBadCountDao.queryList(map);
        resultMap.put("inspection",resultList);
        return resultMap;
    }

    @Override
    public List<TechnologyQualityReportDetail> getInspectionDetailList(Map map) {
        map.put("status", ConstantUtil.has_support);
        map.put("inspectionStatus",ConstantUtil.has_inspection);
        return technologyQualityReportDao.getInspectionDetailList(map);
    }

    @Override
    public List<TechnologyQualityReportVO> getPatrolInspectionList(Map paramMap) {
        List<TechnologyQualityReportVO> voList = null;
        paramMap.put("status", ConstantUtil.has_support);
        paramMap.put("inspectionStatus",ConstantUtil.has_inspection);
        // 获取 物料编码、物料规格、物料型号、工序
        List<Map> mapList = technologyQualityReportDao.getPatrolCounts(paramMap);
        if (null != mapList && mapList.size() > 0) {
            voList = new ArrayList<>();
            for (Map map : mapList) {
                // 获取所有首末检数据
                List<PatrolInspection> technologyList = technologyQualityReportDao.getPatrolInspectionList(map);
                if (null != technologyList && technologyList.size() > 0) {
                    TechnologyQualityReportVO reportVO = new TechnologyQualityReportVO();
                    // 获取不合格的数据
                    List<PatrolInspection> unQualified = technologyList.stream().filter(x -> Objects.equals(x.getIsQualified(), "不合格")).collect(Collectors.toList());
                    String materialName = technologyList.get(0).getMaterialName();
                    String materialCode = technologyList.get(0).getMaterialCode();
                    String specification = technologyList.get(0).getSpecification();
                    String materialMarker = technologyList.get(0).getMaterialMarker();
                    String processCode = technologyList.get(0).getProcessCode();
                    String processName = technologyList.get(0).getProcessName();
                    reportVO.setMaterialCode(materialCode);
                    reportVO.setMaterialName(materialName);
                    reportVO.setSpecification(specification);
                    reportVO.setMaterialMarker(materialMarker);
                    reportVO.setProcessCode(processCode);
                    reportVO.setProcessName(processName);
                    reportVO.setInspectionNum(technologyList.size());
                    reportVO.setIsQualified(technologyList.size()-unQualified.size());
                    reportVO.setUnQualified(unQualified.size());
                    voList.add(reportVO);
                }
            }
        }

        return voList;
    }

    @Override
    public List<TechnologyQualityReportDetail> getPatrolDetails(Map map) {
        map.put("status", ConstantUtil.has_support);
        map.put("inspectionStatus",ConstantUtil.has_inspection);
        return technologyQualityReportDao.getPatrolDetails(map);
    }
}