package com.imes.qms.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imes.common.support.Query;
import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RecordUtils;
import com.imes.domain.entities.qms.po.QmsWorkInspectDetail;
import com.imes.domain.entities.query.template.qms.QmsWorkInspectDetailSearchVo;
import com.imes.qms.dao.QmsWorkInspectDetailDao;
import com.imes.qms.service.QmsWorkInspectDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 异常提报单-主表-志特(QmsWorkInspectDetail)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-13 09:46:16
 */
@Service("qmsWorkInspectDetailService")
public class QmsWorkInspectDetailServiceImpl extends ServiceImpl<QmsWorkInspectDetailDao, QmsWorkInspectDetail> implements QmsWorkInspectDetailService {

    @Resource
    private QmsWorkInspectDetailDao baseMapper;

    @Resource
    private IdWorker idWorker;

    /*
     * 默认生成的高级查询
     */
    @Override
    public List<QmsWorkInspectDetailSearchVo> queryList(QmsWorkInspectDetailSearchVo vo) throws Exception {
        Query query = Query.build().initParams(vo);
        query.orderByAsc("line_no + 0");
        return baseMapper.queryList(query);
    }

    @Override
    public String insert(QmsWorkInspectDetail vo) throws Exception {
        String id = String.valueOf(idWorker.nextId());
        vo.setId(id);
        RecordUtils.createData(vo);
        baseMapper.insert(vo);
        return id;
    }

    @Override
    public Integer update(QmsWorkInspectDetail qmsWorkInspectDetail) throws Exception {
        return baseMapper.updateById(qmsWorkInspectDetail);
    }

    @Override
    public Integer batchDelete(List<String> idList) throws Exception {
        return baseMapper.deleteBatchIds(idList);
    }

    @Override
    public List<QmsWorkInspectDetail> queryByCond(QmsWorkInspectDetail qmsWorkInspectDetail) {
        return baseMapper.queryByCond(qmsWorkInspectDetail);
    }

    @Override
    public void batchRepalceInto(List<QmsWorkInspectDetail> list) {
        baseMapper.batchRepalceInto(list);
    }

    @Override
    public List<QmsWorkInspectDetail> queryByInspectList(List<String> list) {
        return baseMapper.queryByInspectList(list);
    }

    @Override
    public QmsWorkInspectDetail getDetailByNoAndLid(String inspectNo, String lid) {
        LambdaQueryWrapper<QmsWorkInspectDetail> query = new LambdaQueryWrapper<>();
        query.eq(QmsWorkInspectDetail::getInspectNo, inspectNo);
        query.eq(QmsWorkInspectDetail::getLineNo, lid);
        List<QmsWorkInspectDetail> details = baseMapper.selectList(query);
        if(ObjectUtil.isNotNull(details) && !details.isEmpty()){
            return  details.get(0);
        }else {
            return null;
        }
    }

    @Override
    public List<QmsWorkInspectDetail> getByInspectNoList(List<String> qualityNoList) {
        LambdaQueryWrapper<QmsWorkInspectDetail> query = new LambdaQueryWrapper<>();
        query.in(QmsWorkInspectDetail::getInspectNo, qualityNoList);
        return baseMapper.selectList(query);
    }

    @Override
    public void updateStatus(String status) {
        LambdaUpdateWrapper<QmsWorkInspectDetail> update = new LambdaUpdateWrapper<>();
        update.set(QmsWorkInspectDetail::getStatus, status);
        RecordUtils.updateData(update);
        baseMapper.update(null, update);

    }

    @Override
    public void deleteByNoList(List<String> noList) {
        LambdaUpdateWrapper<QmsWorkInspectDetail> update = new LambdaUpdateWrapper<>();
        update.in(QmsWorkInspectDetail::getInspectNo, noList);
        baseMapper.delete(update);

    }
}
