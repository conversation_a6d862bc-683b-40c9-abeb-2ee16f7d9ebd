package com.imes.qms.service.impl;

import com.imes.common.support.Message;
import com.imes.common.utils.*;
import com.imes.domain.entities.ppc.po.RouteAndProcess;
import com.imes.domain.entities.qms.po.FirstAndEndInspectionDetail;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.QmsRouteInspectionVo;
import com.imes.domain.entities.qms.vo.supportMaterialInfoVo;
import com.imes.domain.entities.query.model.base.Model;
import com.imes.domain.entities.query.model.base.MsgAction;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.qms.dao.FirstAndEndInspectionDetailDao;
import com.imes.qms.dao.TechnologyInspectionSupportDao;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.TechnologyInspectionSupportService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TechnologyInspectionSupportServiceImpl implements TechnologyInspectionSupportService {

    @Autowired
    TechnologyInspectionSupportDao technologyInspectionSupportDao;

    @Autowired
    FirstAndEndInspectionDetailDao firstAndEndInspectionDetailDao;

    @Autowired
    FeignService feignService;

    @Autowired
    IdWorker idWorker;


    public Map<String, Object> getInspectionInfo(String ppNo, String pcNo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        Map<String, String> route = technologyInspectionSupportDao.getRouteByPcNo(pcNo);
        // 根据生产单号获取物料信息
        Map<String, String> materialMap = technologyInspectionSupportDao.getMaterialByPpNo(ppNo);
        if (null == materialMap) {
            AssertUtil.throwException("计划单号【{}】所对应的物料信息为空", ppNo);
        } else {
            String bomCode = materialMap.get("bomCode");
            String bomVer = materialMap.get("bomVer");
            if (null == route) {
                return null;
            }
            materialMap.put("routeCode", route.get("routeCode"));
            map.put("material", materialMap);
            map.put("routeName", route.get("routeName"));
            // 获取工序
            List<RouteAndProcess> rpList = technologyInspectionSupportDao.getProcessByRouteCode(route.get("routeCode"), pcNo);
            if (rpList.isEmpty()) {
                return null;
            }
            Map<String, Object> processMap = new HashMap<>();
            for (RouteAndProcess process : rpList) {
                Map<String, Object> routeMap = getQmsRoute(route.get("routeCode"), process.getProcessCode(), bomCode, bomVer, route.get("lineCode"));
                routeMap.put("processName", process.getRemarks());
                processMap.put(process.getProcessCode(), routeMap);
            }
            map.put("process", processMap);
            return map;
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void temporarySave(TechnologyInspection data) throws Exception {
        if (StringUtils.isNullOrBlank(data.getProcessCode())) {
            AssertUtil.throwException("此单据工序编码为空");
        }
        if (StringUtils.isNullOrBlank(data.getRouteCode())) {
            AssertUtil.throwException("此单据工艺路线编码为空");
        }
        if (null != data.getId() && !"".equals(data.getId())) {
            // 多次暂存
            RecordUtils.updateData(data);
            data.setInspectDepartName(getDep(data.getRouteCode(), data.getProcessCode()).getDepartName());
            data.setInspectDepartCode(getDep(data.getRouteCode(), data.getProcessCode()).getDepartCode());
            technologyInspectionSupportDao.updateSelective(data);
        } else {
            // 一次暂存
            data.setPublishStatus(ConstantUtil.firstFinal_unsupport);
            data.setStatus(ConstantUtil.firstFinal_unsupport);
            data.setInspectionStatus(ConstantUtil.un_support);
            data.setId(idWorker.nextId() + "");
            RecordUtils.createData(data);
            data.setUpdateOn(data.getCreateOn());
            data.setUpdateBy(data.getCreateBy());
            data.setInspectDepartName(getDep(data.getRouteCode(), data.getProcessCode()).getDepartName());
            data.setInspectDepartCode(getDep(data.getRouteCode(), data.getProcessCode()).getDepartCode());
            technologyInspectionSupportDao.insertSelective(data);
        }

    }

    @Override
    public List<TechnologyInspection> queryList(Map map) throws Exception {
        String userCode = RedisUtils.getUserCode();
        if (!Constants.ADMIN.equals(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_TYPE_QMS);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到质检班组");
            } else {
                List<String> teamCodes = new ArrayList<>();
                for (CoDepartment department : departmentList) {
                    teamCodes.add(department.getDepartCode());
                }
                map.put("teamCodes", teamCodes);
            }
        }
        return technologyInspectionSupportDao.queryList(map);
    }

    @Override
    public TreeMap<String, List<FirstAndEndInspectionDetail>> getInspectionDetails(String patrolId) {
        List<FirstAndEndInspectionDetail> detailList = firstAndEndInspectionDetailDao.queryDetailList(patrolId);
        TreeMap<String, List<FirstAndEndInspectionDetail>> treeMap = detailList.stream().collect(Collectors.groupingBy(FirstAndEndInspectionDetail::getInspectionNo, TreeMap::new, Collectors.toList()));
        return treeMap;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteSupport(String id) {
        technologyInspectionSupportDao.deleteSelective(id);
    }

    public Map<String, Object> getQmsRoute(String routeCode, String processCode, String bomCode, String bomVer, String lineCode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        // 获取质检项
        Map paramMap = new HashMap();
        paramMap.put("routeCode", routeCode);
        paramMap.put("processCode", processCode);
        // 判断是否有质检项
        List<QmsRouteInspectionVo> list = feignService.getRouteInspectMainAndDetail(routeCode, processCode);
        if (null != list && list.size() > 0) {
            map.put("qms", list);
        }
        try {
            CoDepartment qmsDepart = feignService.getInspectTeamByRouteCode(routeCode, processCode);
            if (null != qmsDepart) {
                map.put("qmsDepart", qmsDepart);
            }
        } catch (Exception e) {
            map.put("qmsDepart", null);
        }
        // 获取工艺文件id
        List<String> fileIdList = technologyInspectionSupportDao.getFileLists(paramMap);
        // 获取工位
        List<Map> station = technologyInspectionSupportDao.getStationByProcessCode(processCode);
        map.put("station", station);
        map.put("fileLists", fileIdList);
        return map;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInspectionDatas(TechnologyInspection data) throws Exception {
        if (StringUtils.isNullOrBlank(data.getProcessCode())) {
            AssertUtil.throwException("此单据工序编码为空");
        }
        if (StringUtils.isNullOrBlank(data.getRouteCode())) {
            AssertUtil.throwException("此单据工艺路线编码为空");
        }
        String planCode = feignService.getAutoCode("INSPECTION_PUBLISH");
        data.setPlanCode(planCode);
        data.setStartPlan(new Date());
        data.setPublishStatus(ConstantUtil.firstFinal_unInspection);
        data.setStatus(ConstantUtil.firstFinal_unInspection);
        data.setInspectionStatus(ConstantUtil.un_inspection);
        // 如果有暂存之后提交（根据id名插数据）
        if (null != data.getId() && !"".equals(data.getId())) {
            RecordUtils.updateData(data);
            data.setInspectDepartName(getDep(data.getRouteCode(), data.getProcessCode()).getDepartName());
            data.setInspectDepartCode(getDep(data.getRouteCode(), data.getProcessCode()).getDepartCode());
            data.setTaskInspectionCode(feignService.getAutoCode("FIRST_END_TASK_CODE"));
            technologyInspectionSupportDao.updateSelective(data);
            technologyInspectionSendMessage(data);
        } else {
            data.setId(idWorker.nextId() + "");
            RecordUtils.createData(data);
            data.setUpdateOn(data.getCreateOn());
            data.setUpdateBy(data.getCreateBy());
            data.setInspectDepartName(getDep(data.getRouteCode(), data.getProcessCode()).getDepartName());
            data.setInspectDepartCode(getDep(data.getRouteCode(), data.getProcessCode()).getDepartCode());
            data.setTaskInspectionCode(feignService.getAutoCode("FIRST_END_TASK_CODE"));
            technologyInspectionSupportDao.insertSelective(data);
            technologyInspectionSendMessage(data);
        }
    }

    public void technologyInspectionSendMessage(TechnologyInspection task) throws Exception {
        if (null != task) {
            // 需要通知的用户
            List<String> userCodes = new ArrayList<>();
            CoDepartment qmsDepart = feignService.getInspectTeamByRouteCode(task.getRouteCode(), task.getProcessCode());
            if (null != qmsDepart) {
                List<Map<String, Object>> userId = feignService.getEmpyesByDepartId(qmsDepart.getId());
                for (Map<String, Object> stringObjectMap : userId) {
                    userCodes.add(stringObjectMap.get("userCode").toString());
                }
            }
            if (!userCodes.isEmpty()) {
                // 首末检验任务生成 M1704
                feignService.sendMsg(Message.build(Model.$02781, MsgAction.CREATE_TASK).id(task.getId()).userCode(userCodes));
            }
        } else {
            AssertUtil.throwException("无对应报工质检任务");
        }
    }

    private CoDepartment getDep(String routeCode, String processCode) throws Exception {
        // 获取质检班组
        CoDepartment qmsDepart = feignService.getInspectTeamByRouteCode(routeCode, processCode);
        if (null != qmsDepart) {
            List<Map<String, Object>> userId = feignService.getEmpyesByDepartId(qmsDepart.getId());
            if (null != userId && userId.size() > 0) {
                return qmsDepart;
            } else {
                AssertUtil.throwException("该角色未配置质检班组用户！");
            }
        } else {
            AssertUtil.throwException("该Bom没有配置质检班组！");
        }
        return null;
    }

    @Override
    public List<supportMaterialInfoVo> getMaterialInfo(Map map) {
        return technologyInspectionSupportDao.getMaterialInfo(map);
    }

    @Override
    public Map getUserCode() throws Exception {
        Map map = new HashMap();
        map.put("userCode", RedisUtils.getUserCode());
        map.put("userName", RedisUtils.getUserName());
        return map;
    }
}
