package com.imes.qms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.imes.common.support.Query;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.IdWorker;
import com.imes.common.utils.RecordUtils;
import com.imes.domain.entities.qms.po.QmsTemplateInspection;
import com.imes.domain.entities.query.model.po.QueryModelTemplate;
import com.imes.qms.dao.QmsTemplateInspectionMapper;
import com.imes.qms.service.QmsTemplateInspectionDetailService;
import com.imes.qms.service.QmsTemplateInspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class QmsTemplateInspectionServiceImpl extends ServiceImpl<QmsTemplateInspectionMapper, QmsTemplateInspection> implements QmsTemplateInspectionService {

    @Autowired
    private IdWorker idWorker;
    @Autowired
    private QmsTemplateInspectionDetailService detailService;

    /**
     * 保存搜索列权限列表
     *
     * @param button 查询按钮
     * @return
     * @throws Exception
     */
    public void apply(QmsTemplateInspection button) {
        //名字重复检查
        Query nameQuery = Query.build(QueryModelTemplate.class)
                .idNe(button.getId())
                .sceneEq("0");
        AssertUtil.isTrue(nameQuery.getCount(baseMapper) <= 0, "按钮名称已存在，请检查！");
        if (StrUtil.isBlank(button.getId())) {
            button.setId(idWorker.nextIdStr());
            RecordUtils.createData(button);
            baseMapper.insert(button);
        } else {
            AssertUtil.notNull(baseMapper.selectById(button.getId()), "方案内容不存在！");
            RecordUtils.updateData(button);
            baseMapper.updateById(button);
        }
    }

    /**
     * 获取条件获取详情
     *
     * @param templateId     方案id
     * @param inspectionType 检验方式
     * @return
     * @throws Exception
     */
    @Override
    public QmsTemplateInspection info(String templateId, String inspectionType) {
        return baseMapper.selectOne(Query.build().templateIdEq(templateId).inspectionTypeEq(inspectionType));
    }

    /**
     * 获取列权限列表
     *
     * @param param 权限搜索参数
     * @return
     * @throws Exception
     */
    public List<QmsTemplateInspection> queryList(QmsTemplateInspection param) {
        return baseMapper.selectList(Query.build().initParams(param));
    }

    /**
     * 根据id删除检验配置
     *
     * @param id
     */
    public void del(String id) {
        //删除对应检验项
        detailService.remove(Query.build().mainIdEq(id));
        //删除检验配置
        baseMapper.deleteById(id);
    }
}
