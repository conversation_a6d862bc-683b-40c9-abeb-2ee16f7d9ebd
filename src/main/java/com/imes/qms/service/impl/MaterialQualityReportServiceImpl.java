package com.imes.qms.service.impl;

import com.imes.common.support.Query;
import com.imes.common.utils.Constants;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.MaterialBuyOrderVo;
import com.imes.domain.entities.qms.vo.MaterialInspectionVo;
import com.imes.domain.entities.query.template.qms.GetInspectionListVo;
import com.imes.qms.dao.MaterialQualityReportDao;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.MaterialQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MaterialQualityReportServiceImpl implements MaterialQualityReportService {

    @Autowired
    MaterialQualityReportDao materialQualityReportDao;

    @Autowired
    FeignService feignService;

    @Override
    public List<MaterialQualityReport> getInspectionList(Map paramMap) {
        List<MaterialQualityReport> voList = null;
        // 获取 物料编码、物料规格、物料型号、供应商
        List<Map> mapList = materialQualityReportDao.getCounts(paramMap);
        if (null != mapList && mapList.size() > 0) {
            voList = new ArrayList<>();
            for (Map map : mapList) {
                // 获取所有来料数据
                List<MaterialInspection> materialList = materialQualityReportDao.getMaterialInspection(map);
                if (null != materialList && materialList.size() > 0) {
                    MaterialQualityReport reportVO = new MaterialQualityReport();
                    // 获取不合格的数据
                    List<MaterialInspection> unQualified = materialList.stream().filter(x -> Objects.equals(x.getIsQualified(), ConstantUtil.unQualified)).collect(Collectors.toList());
                    String materialName = materialList.get(0).getMaterialName();
                    String materialCode = materialList.get(0).getMaterialCode();
                    String specification = materialList.get(0).getSpecification();
                    String materialMarker = materialList.get(0).getMaterialMarker();
                    String supplierCode = materialList.get(0).getSupplierCode();
                    String supplierName = materialList.get(0).getSupplierName();
                    reportVO.setMaterialCode(materialCode);
                    reportVO.setMaterialName(materialName);
                    reportVO.setSpecification(specification);
                    reportVO.setMaterialMarker(materialMarker);
                    reportVO.setSupplierCode(supplierCode);
                    reportVO.setSupplierName(supplierName);
                    reportVO.setInspectionNum(materialList.size());
                    reportVO.setIsQualified(materialList.size()-unQualified.size());
                    reportVO.setUnQualified(unQualified.size());
                    //reportVO.setDetail(materialList);
                    voList.add(reportVO);
                }
            }
        }
        return voList;
    }

    @Override
    public List<MaterialQualityReportDetail> getInspectionDetailList(Map map) throws Exception {
        //查看生产计划增加数据权限
        if (!Constants.ADMIN.equals(RedisUtils.getUserCode()) && !feignService.isTenantAdmin(RedisUtils.getUserCode())) {
            List<String> userCodes = feignService.dataPermission(RedisUtils.getUserCode());
            StringBuffer planerCode = new StringBuffer();
            planerCode.append(",admin");
            for (String userCode : userCodes){
                planerCode.append(","+userCode);
            }
            String userCodesIn = StringUtils.str2In(planerCode.toString().substring(1));
            map.put("userCodesIn",userCodesIn);
        }
        return materialQualityReportDao.getInspectionDetailList(map);
    }

    @Override
    public List<MaterialBuyOrderVo> getDetailInfo(String id) {
        return materialQualityReportDao.getDetailInfo(id);
    }

    @Override
    public List<MaterialQualityReport> getInspectionListNew(GetInspectionListVo vo) {
        Query q = Query.build();
        q.apply("qms_in_material_inspection.inspection_status in ({0})",vo.getProcess());
        List<MaterialQualityReport> voList = null;
        // 获取 物料编码、物料规格、物料型号、供应商
        List<MaterialInspectionVo> mapList = materialQualityReportDao.getCountsNew(q.initParams(vo));
        if (null != mapList && mapList.size() > 0) {
            voList = new ArrayList<>();
            for (MaterialInspectionVo map : mapList) {
                vo.setStatus(map.getStatus());
                vo.setSupplierCode(map.getSupplierCode());
                vo.setMaterialCode(map.getMaterialCode());
                vo.setInspectionStatus(map.getInspectionStatus());
                vo.setTaskInspectionCode(map.getTaskInspectionCode());
                vo.setOrderBy(null);
                // 获取所有来料数据
                List<MaterialInspectionVo> materialList = materialQualityReportDao.getMaterialInspectionNew(vo);
                if (null != materialList && materialList.size() > 0) {
                    MaterialQualityReport reportVO = new MaterialQualityReport();
                    // 获取不合格的数据
                    List<MaterialInspection> unQualified = materialList.stream().filter(x -> Objects.equals(x.getIsQualified(), ConstantUtil.unQualified)).collect(Collectors.toList());
                    String materialName = materialList.get(0).getMaterialName();
                    String materialCode = materialList.get(0).getMaterialCode();
                    String specification = materialList.get(0).getSpecification();
                    String materialMarker = materialList.get(0).getMaterialMarker();
                    String supplierCode = materialList.get(0).getSupplierCode();
                    String supplierName = materialList.get(0).getSupplierName();
                    reportVO.setMaterialCode(materialCode);
                    reportVO.setMaterialName(materialName);
                    reportVO.setSpecification(specification);
                    reportVO.setMaterialMarker(materialMarker);
                    reportVO.setSupplierCode(supplierCode);
                    reportVO.setSupplierName(supplierName);
                    reportVO.setInspectionNum(materialList.size());
                    reportVO.setIsQualified(materialList.size()-unQualified.size());
                    reportVO.setUnQualified(unQualified.size());
                    reportVO.setDetail(materialList);
                    reportVO.setTaskInspectionCode(materialList.get(0).getTaskInspectionCode());
                    voList.add(reportVO);
                }
            }
        }
        return voList;
    }
}