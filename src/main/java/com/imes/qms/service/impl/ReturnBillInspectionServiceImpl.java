package com.imes.qms.service.impl;

import com.imes.common.exception.CommonException;
import com.imes.common.utils.*;
import com.imes.domain.entities.flowable.FlowableProcessDTO;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.domain.entities.system.empyes;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import com.imes.domain.entities.wms.WmsArrivalOrderItem;
import com.imes.qms.dao.*;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.InMaterialDataProtectService;
import com.imes.qms.service.OutBillInspectionService;
import com.imes.qms.service.ProblemHandleService;
import com.imes.qms.service.ReturnBillInspectionService;
import com.imes.qms.utils.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReturnBillInspectionServiceImpl implements ReturnBillInspectionService {

    @Autowired
    ReturnBillInspectionDao returnBillInspectionDao;

    @Autowired
    InMaterialDataProtectDao inMaterialDataProtectDao;

    @Autowired
    ReturnBillInspectionDetailDao returnBillInspectionDetailDao;

    @Autowired
    ReturnInspectionRelationDao returnInspectionRelationDao;

    @Autowired
    InMaterialDataProtectService inMaterialDataProtectService;

    @Autowired
    InspectionItemsDao inspectionItemsDao;

    @Autowired
    ProblemHandleService problemHandleService;

    @Autowired
    FeignService feignService;

    @Autowired
    QmsProtectFileDao qmsProtectFileDao;

    @Autowired
    AqlInfoDao aqlInfoDao;
    @Autowired
    IdWorker idWorker;

    @Override
    public List<ReturnInspectionListVo> queryList(Map map) {
        List<ReturnInspectionListVo> voList = null;
        List<ReturnBillInspection> returnBillInspections = returnBillInspectionDao.queryList(map);
        if (null != returnBillInspections && returnBillInspections.size() > 0) {
            voList = new ArrayList<>();
            for (ReturnBillInspection returnBillInspection : returnBillInspections ) {
                ReturnInspectionListVo vo = new ReturnInspectionListVo();
                String wfNo = returnBillInspection.getArrivalOrder();
                String[] wfNos = wfNo.split(",");
                int length = wfNos.length;
                log.info("QMS开始调用/api/sys/empyes/findByUserCode，参数为："+returnBillInspection.getInspector());
                empyes byUserCode = feignService.findByUserCode(returnBillInspection.getInspector());
                log.info("QMS结束调用/api/sys/empyes/findByUserCode，调用结果为："+byUserCode);
                if (null != byUserCode) {
                    returnBillInspection.setInspector(byUserCode.getUserName());
                }
                vo.setMain(returnBillInspection);
                vo.setInfos(wfNos);
                vo.setLength(length);
                voList.add(vo);
            }
        }
        return voList;
    }

//    @Override
//    public Object getItemsByParams(String ppNo, String inspectionType, String productName) {
//        return null;
//    }
//
//    @Override
//    public void saveInspectionDatas(InBillInspectionSaveVo datas) {
//        InBillInspection inBillInspection = getInBillInspection(datas,"create");
//        List<InBillInspectionDetail> details = datas.getDetails();
//        details.forEach(item->{
//            item.setId(idWorker.nextId() + "");
//            item.setInspectionCode(inBillInspection.getInspectionCode());
//            item.setParentId(inBillInspection.getId());
//            RecordUtils.createData(item);
//        });
//        materialInspectionDao.insertSelective(inBillInspection);
//        materialInspectionDao.insertSelectiveDetails(details);
//    }
//
//    @Override
//    public void updateInspectionDatas(InBillInspectionSaveVo datas) {
//        InBillInspection inBillInspection = getInBillInspection(datas,"update");
//        materialInspectionDao.updateByPrimaryKeySelective(inBillInspection);
//        List<InBillInspectionDetail> details = datas.getDetails();
//        for (InBillInspectionDetail detail :details) {
//            RecordUtils.updateData(detail);
//            materialInspectionDao.updateByPrimaryKeySelectivedetail(detail);
//        }
//    }

    @Override
    public void deleteStandardList(List<String> ids) {
        for (String id :ids) {
            returnBillInspectionDao.deleteMaterialListDetail(id);
            returnBillInspectionDao.deleteMaterialList(id);
        }
    }

    @Override
    public ItemInfoVo getInspectionByMaterialCode(String materialCode, String inspectionType) {
        ItemInfoVo result = null;
        List<InspectionItemsVo> inspectionItemsVos = inspectionItemsDao.getInspectionByMaterialCode(materialCode,inspectionType);
        if (null != inspectionItemsVos && inspectionItemsVos.size() > 0) {
            result = new ItemInfoVo();
            // 主信息
            inspectionItemsVos = inspectionItemsVos.stream().filter(x->x.getChildItem()!=null).collect(Collectors.toList());
            if (inspectionItemsVos.size() > 0) {
                result.setMain(inspectionItemsVos);
                // 关联文件
                List<String> itemIds = inspectionItemsVos.stream().map(x->x.getItemId()).collect(Collectors.toList());
                String itemId = org.apache.commons.lang3.StringUtils.join(itemIds.toArray(),",");
                Map map = new HashMap();
                map.put("itemId", com.imes.common.utils.StringUtils.str2In(itemId));
                List<String> fileIds = inMaterialDataProtectDao.getFileIdByItemId(map);
                List<Map<String, Object>> maps = null;
                if (!fileIds.isEmpty()) {
                    maps = new ArrayList<>();
                    for (String fileId: fileIds) {
                        try {
                            SysFile fileById = feignService.getFileById(fileId);
                            Map<String, Object> strMap = new HashMap<>();
                            strMap.put("fileName", fileById.getOriginalName());
                            strMap.put("fileSize", fileById.getFileSize());
                            strMap.put("uploadTime", fileById.getCreateOn());
                            strMap.put("localUrl", fileById.getLocalUrl());
                            strMap.put("preUrl", fileById.getPreUrl());
                            strMap.put("fileType", fileById.getFileType());
                            strMap.put("fileId", fileId);
                            maps.add(strMap);
                        } catch (Exception exception) {
                            exception.printStackTrace();
                        }
                    }
                }
                result.setProcessFile(maps);
            }
        }

        return result;
    }

    @Override
    public List<MaterialBuyOrderVo> getMaterialByOrderNo(Map map) throws Exception {
        List<MaterialBuyOrderVo> materialBuyOrderVos = new ArrayList<>();
        //查询登录员工所在的班组
        String userCode = RedisUtils.getUserCode();
        if (!Constants.ADMIN.equals(userCode) && !feignService.isTenantAdmin(userCode)) {
            List<CoDepartment> departmentList = feignService.findByUserCodeAndType(userCode, Constants.DEPARTMENT_RETURN_QMS);
            if (departmentList.size() == 0) {
                AssertUtil.throwException("当前登录员工,没有分配到出库检验班组");
            } else {
                materialBuyOrderVos = returnBillInspectionDao.getMaterialByOrderNo(map);
            }
        } else {
            materialBuyOrderVos = returnBillInspectionDao.getMaterialByOrderNo(map);
        }
        materialBuyOrderVos.forEach(x->x.setInspectionStatus(null==x.getInspectionStatus()?ConstantUtil.un_inspection:x.getInspectionStatus()));
        return materialBuyOrderVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveInspectionDetailDatas(ReturnBillInspectionInfoVo data) throws Exception {
        ReturnBillInspection returnBillInspection = data.getMain();
        // 如果没有id则为新增
        if (null == returnBillInspection.getId()) {
            save(data);
            // 如果有id且状态为驳回
        } else if (ConstantUtil.has_reject.equals(returnBillInspection.getInspectionStatus())) {
            saveReject(data);
        } else {
            update(data);
        }
    }

    @Override
    public List<ReturnBillInspectionInfoVo> queryDetailList(String id) {
        List<ReturnBillInspectionInfoVo> list = new ArrayList<>();
        list = queryListByRejectId(list,id);
        ReturnBillInspectionInfoVo mainVo = detailLists(id);
        list.add(mainVo);
        return list;
    }

    private List<ReturnBillInspectionInfoVo> queryListByRejectId(List<ReturnBillInspectionInfoVo> list,String id) {
        Map<String,String> idMap= returnBillInspectionDao.getRejectIdById(id);
        if (null != idMap && null != idMap.get("rejectId")) {
            ReturnBillInspectionInfoVo rejectVo = detailLists(idMap.get("rejectId"));
            list.add(rejectVo);
            list = queryListByRejectId(list,idMap.get("rejectId"));
            Collections.reverse(list);
        }
        Collections.reverse (list);
        return list;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void supportDatas(ReturnBillInspectionInfoVo data) throws Exception {
        ReturnBillInspection returnBillInspection = data.getMain();
        // 如果没有id则为新增
        if (null == returnBillInspection.getId()) {
            save(data);
            // 如果有id且状态为驳回
        } else if (ConstantUtil.has_reject.equals(returnBillInspection.getInspectionStatus())) {
            saveReject(data);
        } else {
            update(data);
        }
        updateStatus(returnBillInspection.getId());
    }

    @Transactional(rollbackFor = Exception.class)
    void update(ReturnBillInspectionInfoVo data) throws Exception {
        ReturnBillInspection returnBillInspection = data.getMain();
        Map<String,List<ReturnBillInspectionDetail>> detailMap = data.getDetail();
        String isQualified = ConstantUtil.qualified;
        RecordUtils.updateData(returnBillInspection);
        int unQualified = 0;
        // 判定是否有不合格数据
        boolean flag = false;
        String inspectionCode = returnBillInspection.getInspectionCode();

        // 保存明细表
        for (String count : detailMap.keySet()) {
            String inspectionNo = inspectionCode+"_"+count;
            List<ReturnBillInspectionDetail> detailList = detailMap.get(count);
            for(ReturnBillInspectionDetail x : detailList) {
                int picCount = qmsProtectFileDao.hasCount(x.getId());
                if (0 == picCount) {
                    x.setPicStatus(ConstantUtil.pic_not_has);
                } else {
                    x.setPicStatus(ConstantUtil.pic_has);
                }
                if (null == x.getCreateOn()) {
                    RecordUtils.createData(x);
                    x.setParentId(returnBillInspection.getId());
                    x.setInspectionCode(inspectionCode);
                    x.setInspectionNo(inspectionNo);
                    if (ConstantUtil.unQualified_num.equals(x.getIsQualified())||"".equals(x.getIsQualified())) {
                        isQualified = ConstantUtil.unQualified;
                        flag = true;
                    }
                    returnBillInspectionDetailDao.insertSelective(x);
                } else {
                    RecordUtils.updateData(x);
                    x.setInspectionNo(inspectionNo);
                    if (ConstantUtil.unQualified_num.equals(x.getIsQualified())||"".equals(x.getIsQualified())) {
                        isQualified = ConstantUtil.unQualified;
                        flag = true;
                    }
                    returnBillInspectionDetailDao.updateByPrimaryKeySelective(x);
                }

            }
            if (flag) {
                unQualified++;
                flag = false;
            }
        }
        // 保存主表
        returnBillInspection.setInspectionNum(detailMap.size());
        returnBillInspection.setUnqualifiedNum(unQualified);
        returnBillInspection.setInspectionCode(inspectionCode);
        returnBillInspection.setInspectionStatus(ConstantUtil.temporary_save);
        returnBillInspection.setStatus(ConstantUtil.un_support);
        // 过检合格率
        BigDecimal qualifiedRate = null;
        BigDecimal supplierRate = null;
        Map map = new HashMap();
        map.put("materialCode",returnBillInspection.getMaterialCode());
        map.put("inspectionType",ConstantUtil.returns);
        Map<String, Object> protectFile = inMaterialDataProtectService.getRatesAndRatios(map);
        if (null != protectFile && null != protectFile.get("qualifiedRate")) {
            qualifiedRate = new BigDecimal(protectFile.get("qualifiedRate").toString());
        }
        // 判断是哪种检验方式
        String inspectType = returnBillInspection.getInspectType();
        if (ConstantUtil.AQL_AQL.equals(inspectType)) {
            AqlInfo param = new AqlInfo();
            param.setAqlCode(returnBillInspection.getAqlCode());
            param.setSampleNum(returnBillInspection.getInspectionNum() == 1 ? "2" : returnBillInspection.getInspectionNum()+"");
            AqlInfo aqlInfo = aqlInfoDao.queryByMap(param);
            if (null == aqlInfo) {
                AssertUtil.throwException("AQL设置错误！");
            } else {
                if (unQualified <= aqlInfo.getReceptNum()) {
                    isQualified = ConstantUtil.qualified;
                } else {
                    isQualified = ConstantUtil.unQualified;
                }
            }
        } else {
            if (null != qualifiedRate) {
                // 获取实际合格率
                BigDecimal allNum = new BigDecimal(returnBillInspection.getInspectionNum());
                BigDecimal qualifiedNum = new BigDecimal(returnBillInspection.getInspectionNum()-returnBillInspection.getUnqualifiedNum());
                if (qualifiedNum.compareTo(BigDecimal.ZERO) == 0) {
                    isQualified = ConstantUtil.unQualified;
                } else {
                    BigDecimal realRate = qualifiedNum.multiply(new BigDecimal(100)).divide(allNum,BigDecimal.ROUND_HALF_UP);
                    if(realRate.compareTo(qualifiedRate) == -1){
                        isQualified = ConstantUtil.unQualified;
                    } else {
                        isQualified = ConstantUtil.qualified;
                    }
                }
            }
        }
        returnBillInspection.setIsQualified(isQualified);
        returnBillInspection.setInspector(RedisUtils.getUserCode());
        returnBillInspection.setCreateOn(new Date());
        returnBillInspectionDao.updateByPrimaryKeySelective(returnBillInspection);
    }

    @Transactional(rollbackFor = Exception.class)
    public ReturnBillInspection save(ReturnBillInspectionInfoVo data) throws Exception {
        ReturnBillInspection returnBillInspection = data.getMain();
        Map<String,List<ReturnBillInspectionDetail>> detailMap = data.getDetail();
        returnBillInspection.setInspector(RedisUtils.getUserCode());
        String isQualified = ConstantUtil.qualified;
        RecordUtils.createData(returnBillInspection);
        returnBillInspection.setId(idWorker.nextId() + "");
        int unQualified = 0;
        // 判定是否有不合格数据
        boolean flag = false;
        String inspectionCode = feignService.getAutoCode("MATERIAL_NO");

        // 保存明细表
        for (String count : detailMap.keySet()) {
            String inspectionNo = inspectionCode+"_"+count;
            List<ReturnBillInspectionDetail> detailList = detailMap.get(count);
            for(ReturnBillInspectionDetail x : detailList) {
                int picCount = qmsProtectFileDao.hasCount(x.getId());
                if (0 == picCount) {
                    x.setPicStatus(ConstantUtil.pic_not_has);
                } else {
                    x.setPicStatus(ConstantUtil.pic_has);
                }
                RecordUtils.createData(x);
                x.setParentId(returnBillInspection.getId());
                x.setInspectionCode(inspectionCode);
                x.setInspectionNo(inspectionNo);
                if (ConstantUtil.unQualified_num.equals(x.getIsQualified())||"".equals(x.getIsQualified())) {
                    isQualified = ConstantUtil.unQualified;
                    flag = true;
                }
                returnBillInspectionDetailDao.insertSelective(x);
            }
            if (flag) {
                unQualified++;
                flag = false;
            }
        }
        // 保存主表
        returnBillInspection.setInspectionNum(detailMap.size());
        returnBillInspection.setUnqualifiedNum(unQualified);
        returnBillInspection.setInspectionCode(inspectionCode);
        returnBillInspection.setInspectionStatus(ConstantUtil.temporary_save);
        returnBillInspection.setStatus(ConstantUtil.un_support);
        // 过检合格率
        BigDecimal qualifiedRate = null;
        BigDecimal supplierRate = null;
        Map map = new HashMap();
        map.put("materialCode",returnBillInspection.getMaterialCode());
        map.put("inspectionType",ConstantUtil.returns);
        Map<String, Object> protectFile = inMaterialDataProtectService.getRatesAndRatios(map);
        if (null != protectFile && null != protectFile.get("qualifiedRate")) {
            qualifiedRate = new BigDecimal(protectFile.get("qualifiedRate").toString());
        }
        // 判断是哪种检验方式
        String inspectType = returnBillInspection.getInspectType();
        if (ConstantUtil.AQL_AQL.equals(inspectType)) {
            AqlInfo param = new AqlInfo();
            param.setAqlCode(returnBillInspection.getAqlCode());
            param.setSampleNum(returnBillInspection.getInspectionNum() == 1 ? "2" : returnBillInspection.getInspectionNum()+"");
            AqlInfo aqlInfo = aqlInfoDao.queryByMap(param);
            if (null == aqlInfo) {
                AssertUtil.throwException("AQL设置错误！");
            } else {
                if (unQualified <= aqlInfo.getReceptNum()) {
                    isQualified = ConstantUtil.qualified;
                } else {
                    isQualified = ConstantUtil.unQualified;
                }
            }
        } else {
            if (null != qualifiedRate) {
                // 获取实际合格率
                BigDecimal allNum = new BigDecimal(returnBillInspection.getInspectionNum());
                BigDecimal qualifiedNum = new BigDecimal(returnBillInspection.getInspectionNum()-returnBillInspection.getUnqualifiedNum());
                if (qualifiedNum.compareTo(BigDecimal.ZERO) == 0) {
                    isQualified = ConstantUtil.unQualified;
                } else {
                    BigDecimal realRate = qualifiedNum.multiply(new BigDecimal(100)).divide(allNum,BigDecimal.ROUND_HALF_UP);
                    if(realRate.compareTo(qualifiedRate) == -1){
                        isQualified = ConstantUtil.unQualified;
                    } else {
                        isQualified = ConstantUtil.qualified;
                    }
                }
            }
        }
        returnBillInspection.setIsQualified(isQualified);
        returnBillInspectionDao.insertSelective(returnBillInspection);
        // 保存入库与报工单关联表
        List<String> orders = data.getOrderIds();
        for (String order : orders) {
            ReturnInspectionRelation returnInspectionRelation = new ReturnInspectionRelation();
            returnInspectionRelation.setId(idWorker.nextId() + "");
            returnInspectionRelation.setParentId(returnBillInspection.getId());
            returnInspectionRelation.setOrderId(order);
            returnInspectionRelation.setInspectionStatus(returnBillInspection.getInspectionStatus());
            RecordUtils.createData(returnInspectionRelation);
            returnInspectionRelationDao.insertSelective(returnInspectionRelation);
        }
        return returnBillInspection;
    }

    @Transactional(rollbackFor = Exception.class)
    public ReturnBillInspection saveReject(ReturnBillInspectionInfoVo data) throws Exception {
        ReturnBillInspection returnBillInspection = data.getMain();
        Map<String,List<ReturnBillInspectionDetail>> detailMap = data.getDetail();
        returnBillInspection.setInspector(RedisUtils.getUserCode());
        String isQualified = ConstantUtil.qualified;
        String oldId = returnBillInspection.getId();
        // 修改原id关联表状态为结束，不显示在任务界面上
        ReturnInspectionRelation relation = new ReturnInspectionRelation();
        relation.setParentId(oldId);
        relation.setInspectionStatus(ConstantUtil.ending);
        returnInspectionRelationDao.updateByParentId(relation);
        returnBillInspection.setId(idWorker.nextId() + "");
        RecordUtils.createData(returnBillInspection);
        returnBillInspection.setRejectId(oldId);
        int unQualified = 0;
        // 判定是否有不合格数据
        boolean flag = false;
        String inspectionCode = feignService.getAutoCode("MATERIAL_NO");

        // 保存明细表
        for (String count : detailMap.keySet()) {
            String inspectionNo = inspectionCode+"_"+count;
            List<ReturnBillInspectionDetail> detailList = detailMap.get(count);
            for(ReturnBillInspectionDetail x : detailList) {
                int picCount = qmsProtectFileDao.hasCount(x.getId());
                if (0 == picCount) {
                    x.setPicStatus(ConstantUtil.pic_not_has);
                } else {
                    x.setPicStatus(ConstantUtil.pic_has);
                }
                RecordUtils.createData(x);
                x.setParentId(returnBillInspection.getId());
                x.setInspectionCode(inspectionCode);
                x.setInspectionNo(inspectionNo);
                if (ConstantUtil.unQualified_num.equals(x.getIsQualified())||"".equals(x.getIsQualified())) {
                    isQualified = ConstantUtil.unQualified;
                    flag = true;
                }
                returnBillInspectionDetailDao.insertSelective(x);
            }
            if (flag) {
                unQualified++;
                flag = false;
            }
        }
        // 保存主表
        returnBillInspection.setInspectionNum(detailMap.size());
        returnBillInspection.setUnqualifiedNum(unQualified);
        returnBillInspection.setInspectionCode(inspectionCode);
        returnBillInspection.setInspectionStatus(ConstantUtil.temporary_save);
        returnBillInspection.setStatus(ConstantUtil.un_support);
        // 过检合格率
        BigDecimal qualifiedRate = null;
        BigDecimal supplierRate = null;
        Map map = new HashMap();
        map.put("materialCode",returnBillInspection.getMaterialCode());
        map.put("inspectionType",ConstantUtil.returns);
        Map<String, Object> protectFile = inMaterialDataProtectService.getRatesAndRatios(map);
        if (null != protectFile && null != protectFile.get("qualifiedRate")) {
            qualifiedRate = new BigDecimal(protectFile.get("qualifiedRate").toString());
        }
        // 判断是哪种检验方式
        String inspectType = returnBillInspection.getInspectType();
        if (ConstantUtil.AQL_AQL.equals(inspectType)) {
            AqlInfo param = new AqlInfo();
            param.setAqlCode(returnBillInspection.getAqlCode());
            param.setSampleNum(returnBillInspection.getInspectionNum() == 1 ? "2" : returnBillInspection.getInspectionNum()+"");
            AqlInfo aqlInfo = aqlInfoDao.queryByMap(param);
            if (null == aqlInfo) {
                AssertUtil.throwException("AQL设置错误！");
            } else {
                if (unQualified <= aqlInfo.getReceptNum()) {
                    isQualified = ConstantUtil.qualified;
                } else {
                    isQualified = ConstantUtil.unQualified;
                }
            }
        } else {
            if (null != qualifiedRate) {
                // 获取实际合格率
                BigDecimal allNum = new BigDecimal(returnBillInspection.getInspectionNum());
                BigDecimal qualifiedNum = new BigDecimal(returnBillInspection.getInspectionNum()-returnBillInspection.getUnqualifiedNum());
                if (qualifiedNum.compareTo(BigDecimal.ZERO) == 0) {
                    isQualified = ConstantUtil.unQualified;
                } else {
                    BigDecimal realRate = qualifiedNum.multiply(new BigDecimal(100)).divide(allNum,BigDecimal.ROUND_HALF_UP);
                    if(realRate.compareTo(qualifiedRate) == -1){
                        isQualified = ConstantUtil.unQualified;
                    } else {
                        isQualified = ConstantUtil.qualified;
                    }
                }
            }
        }
        returnBillInspection.setIsQualified(isQualified);
        returnBillInspectionDao.insertSelective(returnBillInspection);
        // 保存入库与报工单关联表
        List<String> orders = data.getOrderIds();
        for (String order : orders) {
            ReturnInspectionRelation returnInspectionRelation = new ReturnInspectionRelation();
            returnInspectionRelation.setId(idWorker.nextId() + "");
            returnInspectionRelation.setParentId(returnBillInspection.getId());
            returnInspectionRelation.setOrderId(order);
            returnInspectionRelation.setInspectionStatus(returnBillInspection.getInspectionStatus());
            RecordUtils.createData(returnInspectionRelation);
            returnInspectionRelationDao.insertSelective(returnInspectionRelation);
        }
        return returnBillInspection;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateInspectionDetailData(ReturnBillInspectionDetail data) {
        // 获取原数据是否合格
        ReturnBillInspectionDetail oldDetail = returnBillInspectionDetailDao.queryDetailById(data.getId());
        // 如果原数据合格
        if (ConstantUtil.qualified_num.equals(oldDetail.getIsQualified())) {
            // 如果修改后为不合格
            if (ConstantUtil.unQualified_num.equals(data.getIsQualified())) {
                int unQualifiedNum = returnBillInspectionDetailDao.getIsQualifiedNum(data.getInspectionNo());
                // 如果原检验品都为合格
                if (0 == unQualifiedNum) {
                    ReturnBillInspection main = returnBillInspectionDao.getById(data.getParentId());
                    main.setUnqualifiedNum(main.getUnqualifiedNum()+1);
                    main.setIsQualified(ConstantUtil.unQualified);
                    RecordUtils.updateData(main);
                    returnBillInspectionDao.updateByPrimaryKeySelective(main);
                }
            }
            // 如果原数据不合格
        } else if (ConstantUtil.unQualified_num.equals(oldDetail.getIsQualified())) {
            // 如果修改后为合格
            if (ConstantUtil.qualified_num.equals(data.getIsQualified())) {
                int unQualifiedNum = returnBillInspectionDetailDao.getIsQualifiedNum(data.getInspectionNo());
                // 如果原检验品只有一个不合格，即修改的这一个
                if (1 == unQualifiedNum) {
                    ReturnBillInspection main = returnBillInspectionDao.getById(data.getParentId());
                    // 如果原检验单只有一个检验品不合格，即修改的这一个
                    if (1 == main.getUnqualifiedNum()) {
                        main.setIsQualified(ConstantUtil.qualified);
                    }
                    main.setUnqualifiedNum(main.getUnqualifiedNum()-1);
                    RecordUtils.updateData(main);
                    returnBillInspectionDao.updateByPrimaryKeySelective(main);
                }
            }
            // 如果原数据为空（未编辑）
        } else {
            String isQualified = "";
            // 获取该检验品信息
            List<ReturnBillInspectionDetail> maList = returnBillInspectionDetailDao.queryByInspectionNo(data.getInspectionNo());
            // 获取总列表信息是否合格
            ReturnBillInspection mation = returnBillInspectionDao.getById(data.getParentId());
            String hasQualified = mation.getIsQualified();
            // 总列表暂时合格
            if (hasQualified.equals(ConstantUtil.qualified)) {
                // 填入数据为不合格
                if (ConstantUtil.unQualified_num.equals(data.getIsQualified())) {
                    mation.setUnqualifiedNum(1);
                    mation.setIsQualified(ConstantUtil.unQualified);
                    RecordUtils.updateData(mation);
                    returnBillInspectionDao.updateByPrimaryKeySelective(mation);
                }
                // 总列表暂时不合格
            } else {
                // 填入数据为不合格
                if (ConstantUtil.unQualified_num.equals(data.getIsQualified())) {
                    // 判断当前检验品是否合格，如果合格则改为不合格，不合格数量加1
                    List<String> isQualifieds = maList.stream().map(x->x.getIsQualified()).collect(Collectors.toList());
                    // 全是合格
                    if (!isQualifieds.contains(ConstantUtil.unQualified_num)) {
                        mation.setUnqualifiedNum(mation.getUnqualifiedNum()+1);
                        mation.setIsQualified(ConstantUtil.unQualified);
                        RecordUtils.updateData(mation);
                        returnBillInspectionDao.updateByPrimaryKeySelective(mation);
                    }
                }
            }
        }
        RecordUtils.updateData(data);
        returnBillInspectionDetailDao.updateByPrimaryKeySelective(data);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStatus(String id) throws Exception {
        int count = returnBillInspectionDao.countHasQualified(id);
        if (count > 0) {
            AssertUtil.throwException(String.format("当前条目还未全部检验！"));
        }
        ReturnBillInspection returnBillInspection = returnBillInspectionDao.getById(id);
        if (null != returnBillInspection.getRejectId() || !"".equals(returnBillInspection.getRejectId())) {
            // 修改原记录检验状态为结束
            ReturnBillInspection oldMain = new ReturnBillInspection();
            oldMain.setId(returnBillInspection.getRejectId());
            oldMain.setInspectionStatus(ConstantUtil.ending);
            returnBillInspectionDao.updateStatusById(oldMain);
        }
        returnBillInspection.setStatus(ConstantUtil.has_support);
        returnBillInspection.setInspectionStatus(ConstantUtil.has_inspection);
        // 提交检验流程
        returnBillInspection = submit(returnBillInspection);
        returnBillInspectionDao.updateByPrimaryKeySelective(returnBillInspection);
        returnBillInspectionDao.updateRalationStatus(returnBillInspection);
    }

    private ReturnBillInspection submit(ReturnBillInspection returnBillInspection) throws Exception {
        /** activiti流程启动 */
        String processInstanceId = "";
//        Map<String, Object> map = new HashMap<>();
//        map.put("titleName", "到货单号:" + returnBillInspection.getArrivalOrder() + "检验单号:" + returnBillInspection.getInspectionCode());
//        processInstanceId = feignService.startActivitiProcess(ConstantUtil.QMS_RETURN_ACTIVITI, map);
//
//        if (StringUtils.isNullOrBlank(processInstanceId)) {
//            throw new Exception("流程不存在,请先配置流程");
//        }
        FlowableProcessDTO flowableProcess = new FlowableProcessDTO();
        flowableProcess.setProcessKey(ConstantUtil.QMS_RETURN_ACTIVITI);
        flowableProcess.setProcessInstanceName("到货单号:" + returnBillInspection.getArrivalOrder() + "检验单号:" + returnBillInspection.getInspectionCode());
        processInstanceId = feignService.startProcessFlow(flowableProcess);
        if (StringUtils.isNullOrBlank(processInstanceId)) {
            AssertUtil.throwException("流程不存在,请先配置流程");
        }
        returnBillInspection.setActivitiId(processInstanceId);
        log.info("工作流执行完成processInstanceId："+processInstanceId);
        return returnBillInspection;
    }

    @Override
    public ReturnBillInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception {
        Map map = new HashMap();
        map.put("ending",ConstantUtil.ending);
        map.put("orderId",orderId);
        String mainId = returnBillInspectionDao.getIdByOrderId(map);
        return detailList(mainId);
    }

    @Override
    public List<ReturnBillInspectionInfoVo> queryByActivtiId(String activitiId) {
        Map<String,String> idMap= returnBillInspectionDao.getIdByActivtiId(activitiId);
        List<ReturnBillInspectionInfoVo> list = new ArrayList<>();
//        if (null != idMap && null != idMap.get("rejectId")) {
//            ReturnBillInspectionInfoVo rejectVo = detailLists(idMap.get("rejectId"));
//            list.add(rejectVo);
//        }
        if (null != idMap && null != idMap.get("mainId")) {
            ReturnBillInspectionInfoVo mainVo = detailLists(idMap.get("mainId"));
            list.add(mainVo);
        }
        return list;
    }

    @Override
    public ReturnBillInspectionInfoVo detailList(String id) throws Exception {
        ReturnBillInspectionInfoVo result = new ReturnBillInspectionInfoVo();
        // 主信息
        ReturnBillInspection returnBillInspection = returnBillInspectionDao.getById(id);
        empyes byUserCode = feignService.findByUserCode(returnBillInspection.getInspector());
        if (null != byUserCode) {
            returnBillInspection.setInspector(byUserCode.getUserName());
        }
        byUserCode = feignService.findByUserCode(returnBillInspection.getReviewer());
        if (null != byUserCode) {
            returnBillInspection.setReviewer(byUserCode.getUserName());
        }
        result.setMain(returnBillInspection);
        // 明细信息
        List<ReturnBillInspectionDetail> detailList = returnBillInspectionDetailDao.queryDetailList(id);
        if (!detailList.isEmpty()) {
            String[] rejectIds = new String[detailList.size()];
            String newInspectionCode = "";
            // 如果是驳回数据则id重新生成
            if (ConstantUtil.has_reject.equals(returnBillInspection.getInspectionStatus())) {
                newInspectionCode = feignService.getAutoCode("MATERIAL_NO");
                returnBillInspection.setInspectionCode(newInspectionCode);
                for (int i=0;i<rejectIds.length;i++) {
                    rejectIds[i] = idWorker.nextId() +"";
                }
            }
            Map<String,List<ReturnBillInspectionDetail>> map = detailList.stream().collect(Collectors.groupingBy(x->(x.getInspectionNo())));
            Map<String,List<ReturnBillInspectionDetail>> newMap = new HashMap<>();
            if (ConstantUtil.has_reject.equals(returnBillInspection.getInspectionStatus())) {
                for (String inspectionNo : map.keySet()) {
                    String newInspectionNo = inspectionNo;

                    returnBillInspection.setInspectionCode(newInspectionCode);
                    List<ReturnBillInspectionDetail> detail = map.get(inspectionNo);
                    for (int i = 0; i < detail.size(); i++) {
                        String newDetailId = rejectIds[i] + "" + detail.get(i).getInspectionNo().substring(detail.get(i).getInspectionNo().indexOf("_") + 1);
                        QmsProtectFile qmsProtectFile = new QmsProtectFile();
                        qmsProtectFile.setChildId(detail.get(i).getId());
                        if (ConstantUtil.pic_has.equals(detail.get(i).getPicStatus())) {
                            List<QmsProtectFile> protectFile = qmsProtectFileDao.findProtectFile(qmsProtectFile);
                            if (null != protectFile && protectFile.size() > 0) {
                                for (QmsProtectFile qmsProtectFile1 : protectFile) {
                                    qmsProtectFile1.setId(idWorker.nextId() +"");
                                    qmsProtectFile1.setChildId(newDetailId);
                                    qmsProtectFileDao.insert(qmsProtectFile1);
                                }
                            }
                        }
                        newInspectionNo = returnBillInspection.getInspectionCode() + "_" + detail.get(i).getInspectionNo().substring(detail.get(i).getInspectionNo().indexOf("_") + 1);
                        detail.get(i).setId(newDetailId);
                        detail.get(i).setInspectionNo(newInspectionNo);
                    }
                    newMap.put(newInspectionNo, detail);
                }
                result.setDetail(newMap);
            } else {
                result.setDetail(map);
            }
            // 关联文件
            List<String> itemIds = detailList.stream().map(x->x.getItemId()).collect(Collectors.toList());
            String itemId = org.apache.commons.lang3.StringUtils.join(itemIds.toArray(),",");
            Map paramMap = new HashMap();
            paramMap.put("itemId", com.imes.common.utils.StringUtils.str2In(itemId));
            List<String> fileIds = inMaterialDataProtectDao.getFileIdByItemId(paramMap);
            List<Map<String, Object>> maps = null;
            if (!fileIds.isEmpty()) {
                maps = new ArrayList<>();
                for (String fileId: fileIds) {
                    try {
                        SysFile fileById = feignService.getFileById(fileId);
                        Map<String, Object> strMap = new HashMap<>();
                        strMap.put("fileName", fileById.getOriginalName());
                        strMap.put("fileSize", fileById.getFileSize());
                        strMap.put("uploadTime", fileById.getCreateOn());
                        strMap.put("localUrl", fileById.getLocalUrl());
                        strMap.put("preUrl", fileById.getPreUrl());
                        strMap.put("fileType", fileById.getFileType());
                        strMap.put("fileId", fileId);
                        maps.add(strMap);
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                }
            }
            result.setProcessFile(maps);
        }
        if (null != returnBillInspection.getOrderId() && !"".equals(returnBillInspection.getOrderId())) {
            Map paramMap = new HashMap();
            paramMap.put("parentId", returnBillInspection.getId());
            paramMap.put("inspectionType",ConstantUtil.outBill);
            // 采购信息
            List<MaterialBuyOrderVo> list = returnBillInspectionDao.getMaterialByOrderNos(paramMap);
            result.setInfo(list);
        }
        return result;
    }

    public ReturnBillInspectionInfoVo detailLists(String id) {
        ReturnBillInspectionInfoVo result = new ReturnBillInspectionInfoVo();
        // 主信息
        ReturnBillInspection returnBillInspection = returnBillInspectionDao.getById(id);
        empyes byUserCode = feignService.findByUserCode(returnBillInspection.getInspector());
        if (null != byUserCode) {
            returnBillInspection.setInspector(byUserCode.getUserName());
        }
        byUserCode = feignService.findByUserCode(returnBillInspection.getReviewer());
        if (null != byUserCode) {
            returnBillInspection.setReviewer(byUserCode.getUserName());
        }
        result.setMain(returnBillInspection);
        // 明细信息
        List<ReturnBillInspectionDetail> detailList = returnBillInspectionDetailDao.queryDetailList(id);
        if (!detailList.isEmpty()) {
            Map<String,List<ReturnBillInspectionDetail>> map = detailList.stream().collect(Collectors.groupingBy(x->(x.getInspectionNo())));
            result.setDetail(map);
            // 关联文件
            List<String> itemIds = detailList.stream().map(x->x.getItemId()).collect(Collectors.toList());
            String itemId = org.apache.commons.lang3.StringUtils.join(itemIds.toArray(),",");
            Map paramMap = new HashMap();
            paramMap.put("itemId", com.imes.common.utils.StringUtils.str2In(itemId));
            List<String> fileIds = inMaterialDataProtectDao.getFileIdByItemId(paramMap);
            List<Map<String, Object>> maps = null;
            if (!fileIds.isEmpty()) {
                maps = new ArrayList<>();
                for (String fileId: fileIds) {
                    try {
                        SysFile fileById = feignService.getFileById(fileId);
                        Map<String, Object> strMap = new HashMap<>();
                        strMap.put("fileName", fileById.getOriginalName());
                        strMap.put("fileSize", fileById.getFileSize());
                        strMap.put("uploadTime", fileById.getCreateOn());
                        strMap.put("localUrl", fileById.getLocalUrl());
                        strMap.put("preUrl", fileById.getPreUrl());
                        strMap.put("fileType", fileById.getFileType());
                        strMap.put("fileId", fileId);
                        maps.add(strMap);
                    } catch (Exception exception) {
                        exception.printStackTrace();
                    }
                }
            }
            result.setProcessFile(maps);
        }
        if (null != returnBillInspection.getOrderId() && !"".equals(returnBillInspection.getOrderId())) {
            Map paramMap = new HashMap();
            paramMap.put("parentId", returnBillInspection.getId());
            paramMap.put("inspectionType",ConstantUtil.outBill);
            // 采购信息
            List<MaterialBuyOrderVo> list = returnBillInspectionDao.getMaterialByOrderNos(paramMap);
            result.setInfo(list);
        }
        return result;
    }

    @Override
    public List<WmsArrivalOrder> findAoByFilter(Map map) {
        List<WmsArrivalOrder> orders = returnBillInspectionDao.findAoByFilter(map);
        if (orders == null || orders.size() == 0)
            return new ArrayList<WmsArrivalOrder>();

        String[] ids = new String[orders.size()];
        for (int i = 0; i < orders.size(); i++) {
            ids[i] = orders.get(i).getId();
        }

        //查找明细单
        Map<String, List<WmsArrivalOrderItem>> itemMap = new HashMap<String, List<WmsArrivalOrderItem>>();
        List<WmsArrivalOrderItem> items = this.findByAoIds(ids);
        if (items != null){
            for (WmsArrivalOrderItem item : items) {
                List<WmsArrivalOrderItem> itemList = itemMap.get(item.getAoId());
                if (itemList == null){
                    itemList = new ArrayList<WmsArrivalOrderItem>();
                    itemMap.put(item.getAoId(), itemList);
                }

                itemList.add(item);
            }
        }

        for (WmsArrivalOrder order : orders) {
            order.setItems(itemMap.get(order.getId()));
        }

        return orders;
    }

    public List<WmsArrivalOrderItem> findByAoIds(String[] aoIds) {
        return returnBillInspectionDao.findByAoIds(StringUtils.array2SrtIn(aoIds));
    }

    @Override
    public void updatePicStatus(String id, String picStatus) {
        returnBillInspectionDetailDao.updatePicStatus(id,picStatus);
    }
}
