package com.imes.qms.service;

import com.imes.domain.entities.qms.po.MaterialTask;
import com.imes.domain.entities.qms.vo.InBillTaskVO;
import com.imes.domain.entities.qms.vo.MaterialTaskVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface MaterialTaskService {

    void createMaterialTask(MaterialTaskVo vo) throws Exception;

    void claim(MaterialTask task) throws Exception;

    void updateClaim(String id) throws Exception;

    void createOutInterfaceTask(List<Map> list) throws Exception;
}
