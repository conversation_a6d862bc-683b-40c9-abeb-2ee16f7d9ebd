package com.imes.qms.service;

import com.imes.domain.entities.qms.vo.InBillInspectionSaveVo;
import com.imes.domain.entities.qms.vo.InBillInspectionVo;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public interface SendOutInspectionService {

    List<InBillInspectionVo> queryList();

    Object getItemsByParams(String ppNo, String inspectionType, String productName);

    void saveInspectionDatas(InBillInspectionSaveVo datas);

    void updateInspectionDatas(InBillInspectionSaveVo datas);

    void deleteStandardList(List<String> ids);
}
