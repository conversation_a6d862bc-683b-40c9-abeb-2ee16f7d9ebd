package com.imes.qms.service;

import com.imes.domain.entities.qms.po.OutBillInspection;
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.QueryMaterialListVo;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public interface OutBillInspectionService {

    List<OutBillInspectionListVo> queryList(Map map);

    List<OutBillInspection> queryMaterialList(QueryMaterialListVo vo) throws Exception;

    List<OutBillInspectionInfoVo> queryDetailList(String id);


    void saveInspectionDetailDatas(OutBillInspectionInfoVo datas) throws Exception;

    void updateInspectionDetailData(OutBillInspectionDetail datas);

    void updateStatus(String id) throws Exception;

    void supportDatas(OutBillInspectionInfoVo data) throws Exception;

    OutBillInspectionInfoVo queryDetailListByOrderId(String orderId) throws Exception;

    List<OutBillInspectionInfoVo> queryByActivtiId(String activitiId);

    OutBillInspectionInfoVo detailList(String parentId) throws Exception;

    void updatePicStatus(String id, String picStatus);

    void cancelMergeInspection(List<String> ids);

    long getOutBillReportTotal();

    long getOutBillTotal();
}
