package com.imes.qms.constants;

public enum InspectEnum {
    STATUS_10("文员调整", "10"),
    STATUS_20("已生效待检验", "20"),
    STATUS_25("待处置", "25"),
    STATUS_30("已结束", "30"),
    STATUS_40("已取消", "40"),


    ;


    private String name;
    private String code;

    InspectEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    /**
     * 根据code获取对应的name
     *
     * @param code 状态编码
     * @return 匹配到的状态名称，未找到返回null
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (InspectEnum status : values()) {
            if (code.equals(status.code)) {
                return status.name;
            }
        }
        return null;
    }
}
