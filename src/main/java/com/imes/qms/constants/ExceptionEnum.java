package com.imes.qms.constants;

public enum ExceptionEnum {
    STATUS_10("已录入", "10"),
    STATUS_20("已生效", "20"),
    STATUS_30("处置结束", "30"),
    STATUS_35("补录结束", "35"),
    STATUS_40("已取消", "40"),







    ;


    private String name;
    private String code;

    ExceptionEnum(String name, String code) {
        this.name = name;
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }


    /**
     * 根据code获取对应的name
     * @param code 状态编码
     * @return 匹配到的状态名称，未找到返回null
     */
    public static String getNameByCode(String code) {
        if (code == null) {
            return null;
        }
        for (ExceptionEnum status : values()) {
            if (code.equals(status.code)) {
                return status.name;
            }
        }
        return null;
    }
}
