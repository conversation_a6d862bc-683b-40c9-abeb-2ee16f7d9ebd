package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import com.imes.domain.entities.ppc.vo.InspectResultVo;
import com.imes.domain.entities.ppc.vo.PlanScheduleStoreVo;
import com.imes.domain.entities.ppc.vo.PpcProduceOrderPrepareAndDetailVo;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.system.warning.SysWarningInfo;
import com.imes.domain.entities.system.wechart.CompanyWechartMsg;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient("IMES-PPC")
public interface PpcClient {

    /**
     * 推送质检结果给PPC模块
     *
     * @param vo
     * @return
     */
    @RequestMapping("/api/ppc/inspection/inspectResultCallBack")
    Result inspectResultCallBack(List<InspectResultVo> vo);

    /**
     * 获取排产信息
     *
     * @param productionNo
     * @return
     */
    @RequestMapping("/api/ppc/PpcProducePlanSchedul/queryAll")
    Result queryAll(@RequestParam("productionNo") String productionNo);


    /**
     * 获取排产报工信息
     *
     * @param wfNo
     * @return
     */
    @RequestMapping("/api/ppc/WorkFinish/queryAll")
    Result getWorkFinishByPcNo(@RequestParam("wfNo") String wfNo);

    /**
     * 根据车间的code获取当前车间正在开工的数据
     *
     * @param status
     * @param deptCodes
     * @return
     */
    @RequestMapping("/api/ppc/workOrder/getWorkOrderByStatusAndDeptCodes")
    Result getWorkFinishByStatusAndDeptCodes(@RequestParam("current") int current, @RequestParam("pageSize") int pageSize,
                                             @RequestParam("status") String status, @RequestParam("specification") String specification,
                                             @RequestParam("materialMarker") String materialMarker, @RequestParam("productionNo") String productionNo,
                                             @RequestParam("processName") String processName, @RequestParam("materialName") String materialName,
                                             @RequestBody List<String> deptCodes);

    /**
     * 根据车间的code获取当前车间正在开工的数据
     *
     * @param startDate
     * @param endDate
     * @return
     */
    @RequestMapping("/api/ppc/WorkFinish/getWorkFinishRejects")
    Result queryBadCount(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate, @RequestParam("limit") String limit);

    /**
     * 根据车间的code获取当前车间正在开工的数据
     *
     * @return
     */
    @RequestMapping("/api/ppc/WorkFinish/getWorkFinishRejects")
    Result queryBadCount();

    /**
     * 查询报工不良项详情
     */
    @RequestMapping("/api/ppc/WorkFinish/getWorkFinishRejectsDetails")
    Result getBadCountDetails(@RequestParam("badCode") String badCode, @RequestParam("materialCode") String materialCode,
                              @RequestParam("materialName") String materialName, @RequestParam("processCode") String processCode,
                              @RequestParam("processName") String processName, @RequestParam("specification") String specification);

    /**
     * 推送入库检验质检结果给PPC模块
     *
     * @param vo
     * @return
     */
    @RequestMapping("/api/ppc/planScheduleStore/resultIsQms")
    Result resultIsQms(PlanScheduleStoreVo vo);

    /**
     * 推送入库检验质检结果给PPC模块
     *
     * @param wfNoList
     * @return
     */
    @PostMapping("/api/ppc/pick/workReportBackByQMS")
    Result workReportBackByQMS(@RequestBody List<String> wfNoList);

    @GetMapping("/api/ppc/ppcProduceOrder/searchWorkOrders")
    Result searchWorkOrders(@RequestParam String keyWord,@RequestParam(required = false) String projectName,@RequestParam(required = false) String orderNo);

    @PostMapping("/api/ppc/ppcProducePlanAzt/queryByLocalOrderIdAndLineNo")
    Result queryByLocalOrderIdAndLineNo(@RequestParam(value = "localOrderId") String localOrderId,
                                        @RequestParam(value = "lineNo") String lineNo);


    @GetMapping("/api/ppc/ppcProduceOrder/ics/searchWorkOrders")
    Result icsSearchWorkOrders(@RequestParam(defaultValue = "") String keyWord,
                               @RequestParam Integer pageSize, @RequestParam Integer pageNum,
                                @RequestParam(required = false) String productNo,@RequestParam(required = false) String productShortName,
                               @RequestParam(required = false) String build,@RequestParam(required = false) String orderNo

    );


    @PostMapping("/api/ppc/ppcProducePlanAzt/queryByOrderIdListNotCanCel")
    Result queryByAztOrderIdList(@RequestBody List<String> list);

    @PostMapping("/api/ppc/ppcWorkFinishMain/queryByOrderIdList")
    Result queryByOrderIdList(@RequestBody List<String> OrderIdList);

    @PostMapping("/api/ppc/ppcProduceProcessAzt/queryByProducePlanIdList")
    Result queryByProducePlanIdList(@RequestBody List<String> list);

    @PostMapping("/api/ppc/ppcProduceOrder/getByIdList")
    Result getByIdList(@RequestBody List<String> list);

    @PostMapping("/api/ppc/ppcProducePlanAzt/queryByOrderIdAndLineNo")
    Result getEffectPlanByOrderAndLine(@RequestParam("orderId") String orderId, @RequestParam("lineNo") String lineNo);

    @GetMapping("/api/ppc/ppcWorkFinishMain/queryByOrderIdAndLine")
    Result getWorkFinishByOrderIdAndLine(@RequestParam("orderId") String orderId, @RequestParam("lineNo") Integer lineNo);

    @PostMapping("/api/ppc/ppcWorkFinishMain/checkProduceOrderIsFinish")
    Result checkProduceOrderIsFinish(@RequestBody List<String> orderIdList);

    @GetMapping("/api/ppc/ppcWorkFinishMain/checkForQms")
    Result checkForQms(@RequestParam String keyWord);

    @PostMapping("/api/ppc/ppcProduceOrderPrepareDetail/getPrepareOoderAndDetailByOrderIdList")
    Result<PpcProduceOrderPrepareAndDetailVo> getPrepareOoderAndDetailByOrderIdList(@RequestBody List<String> orderIdList);

    @PostMapping("/api/ppc/ppcWorkFinishMain/generateInspectPieceData")
    Result generateInspectPieceData(@RequestBody QmsWorkInspect list);

    @GetMapping("/api/ppc/ppcWorkFinishMain/deleteInspectPieceData")
    Result deleteInspectPieceData(@RequestParam("inspectNo") String inspectNo);

    @GetMapping("/api/ppc/ppcWorkFinishMaterialPiece/isExistsCalc")
    Result isExistsCalc(@RequestParam String orderId);
}
