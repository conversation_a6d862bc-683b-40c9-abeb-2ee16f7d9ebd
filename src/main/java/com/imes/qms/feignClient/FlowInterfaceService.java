package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import com.imes.domain.entities.flowable.FlowableProcessDTO;
import com.imes.domain.entities.flowable.FlowableTaskDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient("IMES-FLOWABLE")
public interface FlowInterfaceService {

    /**
     * 启动流程
     */
    @PostMapping("/api/flowable/process/start")
    Result startProcess(@RequestBody FlowableProcessDTO flowableProcessDTO);

    /**
     * 提交任务
     */
    @PostMapping("/api/flowable/task/complete")
    Result completeTask(@RequestBody FlowableTaskDTO taskDTO);

    /**
     * 彻底删除运行中流程
     */
    @DeleteMapping("/api/flowable/process/deleteProcessInstanceComplete")
    Result deleteProcessInstanceComplete(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping("/api/flowable/process/autoCheck")
    Result autoCheck(FlowableProcessDTO flowableProcessDTO);

}
