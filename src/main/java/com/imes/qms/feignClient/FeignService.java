package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import com.imes.common.exception.CommonException;
import com.imes.common.support.Message;
import com.imes.common.support.Resume;
import com.imes.domain.entities.activiti.po.TaskDTO;
import com.imes.domain.entities.activiti.vo.TasksResultVo;
import com.imes.domain.entities.flowable.FlowableProcessDTO;
import com.imes.domain.entities.flowable.FlowableTaskDTO;
import com.imes.domain.entities.ppc.po.*;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.qms.vo.MaterialTaskItemVo;
import com.imes.domain.entities.qms.vo.ProcessBadCountsQualityReportVO;
import com.imes.domain.entities.qms.vo.QmsRouteInspectionVo;
import com.imes.domain.entities.qms.vo.QmsStdInspectionVo;
import com.imes.domain.entities.query.template.sys.PpcProcessQmsDepartmentSearchVo;
import com.imes.domain.entities.system.*;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.domain.entities.system.base.vo.PreviewFileVo;
import com.imes.domain.entities.system.base.vo.SysExportTemplateWrap;
import com.imes.domain.entities.system.qz.QzEventNow;
import com.imes.domain.entities.system.vo.MaterialVo;
import com.imes.domain.entities.system.vo.SendUserMessageVO;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import com.imes.domain.entities.system.vo.SysMaterialSupplierVO;
import com.imes.domain.entities.system.warning.SysWarningInfo;
import com.imes.domain.entities.system.wechart.CompanyWechartMsg;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import com.imes.domain.entities.wms.WmsArrivalOrderItem;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.List;
import java.util.Map;

public interface FeignService {

    QzEventNow getEventNow(String id) throws Exception;

    void updateFail(Map map) throws Exception;

    String startActivitiProcess(String activitiProcessKey, Map<String, Object> map) throws Exception;

    TasksResultVo queryTasksByUserCode(int pageNum, int pageSize, String userCode) throws InvocationTargetException, IllegalAccessException, Exception;

    String getKeyByTaskId(String taskId) throws Exception;

    Map<String, Object> deleteProcessInstance(String processInstanceId) throws Exception;

    Integer submitTask(String taskId, String comment, String flag) throws Exception;

    List<TaskDTO> getTaskByProcessKey(List<String> ProcessKey, String userCode, List<String> groups) throws Exception;

    Integer submitTaskAll(Map<String, String> map) throws Exception;

    Boolean workOrNot(String calendarCode, String date) throws Exception;

    String getAutoCode(String autocode) throws Exception;

    List<Map<String, Object>> findAreaTree(String areaCategory) throws Exception;

    /*根据员工编码 组织类型  查询所在的组织*/
    List<CoDepartment> findByUserCodeAndType(String userCode, String type) throws Exception;

    TaskDTO getTaskByProcessInstanceId(String processInstanceId) throws Exception;

    //根据用户编码查询用户信息
    empyes findByUserCode(String userCode);

    SysFile singleUpload(MultipartFile file) throws Exception;

    SysFile multiUpload(MultipartFile[] file) throws Exception;

    PreviewFileVo previewFile(String id) throws Exception;

    SysFile getFileById(String id) throws Exception;

    Result fileDelete(String fileId);

    List<empyes> departLeader(String workshopCode) throws Exception;

    Result findPullSupplier();

    List<MaterialVo> queryMaterialByCodeIn(List<String> materialCodeList) throws Exception;

    boolean startWarning(SysWarningInfo sysWarningInfo) throws Exception;

    /**
     * 根据角色id获取用户列表
     *
     * @param id
     * @return
     * @throws Exception
     */
    List<Map<String, Object>> getEmpyesByDepartId(String id) throws Exception;

    List<Map<String, Object>> findUserByDepartCode(String code) throws Exception;

    void sendWechartMsg(CompanyWechartMsg companyWechartMsg) throws Exception;

    Result sendMobileMsg(String title, String content, String userCodes, String messageCode) throws Exception;

    CoDepartment getInspectTeam(String bomCode, String bomVer, String lineCode, String processCode) throws Exception;

    List<Map<String, Object>> queryDepartmentByType(String type) throws Exception;

    List<Map<String, Object>> sltUserByDepartId(String departId) throws Exception;

    List<String> findChildDeptByUserCode(String userCode);

    public Boolean sendPcMessage(String messageCode, String messageContent, List<String> userCodes, String sendUserCode) throws Exception;

    CoDepartment getInspectTeamByRouteCode(String routeCode, String processCode) throws Exception;

    void inspectResultCallBack(List<InspectResultVo> vo) throws Exception;

    List<QmsRouteInspectionVo> getRouteInspectMainAndDetail(String routeCode, String processCode) throws Exception;

    List<PpcProcessQmsDepartmentSearchVo> queryInspectTeamByProcessCode(String processCode) throws Exception;

    List<PpcProcessBadCode> queryBadCode(PpcProcessBadCode ppcProcessBadCode) throws Exception;

    List<Map<String, Object>> getEmpyesByDepartCode(String id);

    List<QmsStdInspectionVo> queryByMap(Map<String, Object> map) throws Exception;

    /*根据部门编码查询部门信息*/
    CoDepartment findByDepartmentCode(String departmentCode) throws Exception;

    List<PpcProducePlanSchedul> queryAll(String pcNo) throws Exception;

    List<Map<String, Object>> getUserByIncludeDepType(String departmentType);

    /*获取工序 */
    PpcProcess queryByProcessCode(String processCode) throws Exception;

    List<PpcWorkFinishVo> getWorkFinishByPcNo(String pcNo) throws Exception;

    List<Map<String, Object>> inspectedDapart(String type) throws Exception;

    List<PpcRouteProcessQmsDepartment> inspectionDapart(List<String> departCodes) throws Exception;

    Map<String, Object> getMaterials(int pageNum, int pageSize, String specification,
                                     String quality, String pcNo, String processName, String materialName, String status, List<String> departCodes) throws Exception;

    List<ProcessBadCountsQualityReportVO> queryBadCount(String startTime, String end, String limit) throws Exception;

    List<ProcessBadCountsQualityReportVO> queryBadCount2() throws Exception;

    List<Map> getBadCountDetails(String badCode, String materialCode, String materialName, String processCode, String processName, String specification) throws Exception;

    /**
     * 查看当前租户是否拥有该模块权限
     *
     * @param moduleCode 模块编码
     * @return
     */
    boolean getAuthorityByModuleCode(String moduleCode);

    List<CoDepartmentUser> findByDepartCode(String departmentCode) throws IntrospectionException, IllegalAccessException, ParseException, InvocationTargetException, Exception;

    List<SysMaterialSupplierVO> findByMaterialCode(String materialCode) throws Exception;

    // 是否租户管理员
    boolean isTenantAdmin(String userCode) throws Exception;

    void resultIsQms(PlanScheduleStoreVo vo) throws Exception;

    void receiveQmsData(List<MaterialTaskItemVo> vo) throws Exception;

    //查询用户所负责部门
    List<CoDepartmentUser> findCoDepartmentByUserCode(String userCode) throws Exception;

    Map<String, Object> queryInfo(String processCode) throws Exception;

    /*获取物料 */
    Promaterial findMaterialByCode(String materialCode) throws Exception;

    /**
     * flow 启动流程
     */
    String startProcessFlow(FlowableProcessDTO flowableProcessDTO);

    /**
     * 提交任务
     */
    void submitTaskFlow(FlowableTaskDTO taskDTO);

    void autoCheck(FlowableProcessDTO flowableProcessDTO);

    List<CoDepartment> getLeaderTeamCodeByUserCode(String userCode, String departmentTypeQms) throws Exception;

    List<CoDepartment> findChildrenByParentCodeAndType(String departCode, String departmentTypeQms);

    /*根据用户编码获取 数据权限*/
    List<String> dataPermission(String userCode) throws Exception;

    SysExportTemplateWrap exportTemplateFetch(String bizCode);

    Result getPpcRouteProcessQmsDepartment(String routeCode, String processCode);

    Result deleteProcessInstanceComplete(String processInstanceId);

    Result workReportBackByQMS(List<String> wfNo);


    Result queryQmsSampleManager(String processCode);

    List<SysDictDetailVo> getDictCode(String mainCode) throws Exception;

    Result addCheckoutSheet(Map<String, Object> param);

    SysUnit findSysUnitByName(String name) throws Exception;

    SysIoManage getIoByCode(@RequestParam("ioCode") String ioCode) throws Exception;

    /**
     * 获取物料详情
     *
     * @param materialCode
     * @return
     * @throws Exception
     */
    Promaterial getMaterialByCode(String materialCode) throws CommonException, InvocationTargetException, IllegalAccessException, IntrospectionException, ParseException;

    Boolean batchSendMessage(List<SendUserMessageVO> messageList);

    Result batchSendWechartMsg(List<CompanyWechartMsg> companyWechartMsgList) throws Exception;

    Result unAuditAndDel(Map<String, Object> param);

    WmsArrivalOrder getByAoCode(String aoCode) throws Exception;

    WmsArrivalOrderItem getByAoItemCode(String aoitemCode) throws Exception;

    List<String> findMainLeaderByDeptType(@RequestParam("departType") String departType);

    /*获取所有工序 */
    List<PpcProcess> queryAllProcessList() throws Exception;

    void saveResume(Resume resume);

    void sendMsg(Message userMsg);

    void sendMsg(List<Message> msgList);

    List<PpcProduceOrder> searchWorkOrders(@RequestParam String keyWord,String projectName,String orderNo);

    PpcProduceOrderAndPlanAztVo queryByLocalOrderIdAndLineNo(@RequestParam(value = "localOrderId") String localOrderId,
                                                             @RequestParam(value = "lineNo") String lineNo);

    List<PpcWorkFinishMain> queryByOrderIdList(List<String> OrderIdList);

    List<PpcProduceProcessAzt> queryByProducePlanIdList(List<String> list);

    PpcProduceOrderPrepareAndDetailVo getPrepareOoderAndDetailByOrderIdList(List<String> orderIdList);

    List<PpcProducePlanAzt> queryByAztOrderIdList(List<String> list);

    List<PpcProduceOrder> getByIdList(List<String> list);

    PpcProducePlanAzt getEffectPlanByOrderAndLine(@RequestParam("orderId") String orderId, @RequestParam("lineNo") String lineNo);

    List<PpcRouteLineZtProcessBigAndSmallVo> getAllDetailByRouteCode(@RequestParam(value ="routeCode") String routeCode,
                                                                     @RequestParam(value ="firstBigProcessCode",required = false) String firstBigProcessCode);

    List<PpcWorkFinishMain> getWorkFinishByOrderIdAndLine(@RequestParam("orderId") String orderId, @RequestParam("lineNo") Integer lineNo);

    void checkProduceOrderIsFinish(List<String> orderIdList);

    void checkOrderProcess(String keyWord);

    List<SysFile> getBase64SysFileByPreUrlList(@RequestBody List<String> preUrls);

    void generateInspectPieceData(QmsWorkInspect list);

    void deleteInspectPieceData(String inspectNo);

    boolean isExistsCalc(String orderId);
}
