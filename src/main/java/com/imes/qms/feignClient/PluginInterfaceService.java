package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import retrofit2.http.GET;

import java.util.Map;


@FeignClient("IMES-PLUGINS")
public interface PluginInterfaceService {

    @PostMapping(value = "/api/plugins/kd/addCheckoutSheet")
    Result addCheckoutSheet(@RequestBody Map<String, Object> param);

    @PostMapping(value = "/api/plugins/kd/unAuditAndDel")
    Result unAuditAndDel(@RequestBody Map<String, Object> param);

}
