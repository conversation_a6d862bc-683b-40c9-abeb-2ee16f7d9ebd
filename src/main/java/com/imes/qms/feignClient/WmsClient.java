package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import com.imes.domain.entities.qms.vo.MaterialTaskItemVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(value = "IMES-WMS")
public interface WmsClient {

    @RequestMapping(value = "/api/wms/arrival/receiveQmsData",method = RequestMethod.POST)
    Result receiveQmsData(@RequestBody List<MaterialTaskItemVo> taskVo);

    @GetMapping("/api/wms/arrival/getByAoCode")
    Result getByAoCode(@RequestParam("aoCode") String aoCode);

}
