package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@FeignClient("IMES-activiti")
public interface ActivitiClient {

    @RequestMapping(value = "/api/activiti/process/startActiviti", method = RequestMethod.POST)
    Result startActivitiProcess(@RequestParam("activitiProcessKey") String activitiProcessKey,@RequestBody Map<String,Object> map);

    @RequestMapping(value = "/api/activiti/task/queryTasksByUserCode", method = RequestMethod.GET)
    Result queryTasksByUserCode(@RequestParam("userCode") String userCode,
                                @RequestParam(defaultValue = "1", value = "pageNum") Integer pageNum,
                                @RequestParam(defaultValue = "10", value = "pageSize") Integer pageSize);

    @GetMapping(value = "/api/activiti/task/getTaskByProcessKey")
    Result getTaskByProcessKey(@RequestParam("ProcessKey") String ProcessKey, @RequestParam("userCode") String userCode);

    @PostMapping(value = "/api/activiti/task/getGroupsTaskByProcessKey")
    Result getGroupsTaskByProcessKey(@RequestParam("ProcessKey") String ProcessKey, @RequestBody List<String> groupIds);

    @GetMapping(value = "/api/activiti/task/getKeyByTaskId")
    Result getKeyByTaskId(@RequestParam("taskId") String taskId);

    @GetMapping(value = "/api/activiti/task/submitTask")
    public Result submitTask(@RequestParam("taskId") String taskId, @RequestParam("comment") String comment,
                             @RequestParam("flag") String flag);

    @GetMapping(value = "/api/activiti/process/deleteProcessInstance")
    public Result deleteProcessInstance(@RequestParam("processInstanceId") String processInstanceId) throws Exception;

    @GetMapping(value = "/api/activiti/task/getTaskByProcessKeyAll")
    public Result getTaskByProcessKey(@RequestParam("ProcessKey") List<String> ProcessKey, @RequestParam("userCode") String userCode, @RequestParam("groups") List<String> groups);

    @PostMapping("/api/activiti/task/submitTaskAll")
    public Result submitTask(@RequestBody Map<String, String> map);

    @GetMapping("/api/activiti/task/getTaskByProcessInstanceId/{processInstanceId}")
    public Result getTaskByProcessInstanceId(@PathVariable("processInstanceId") String processInstanceId);

}
