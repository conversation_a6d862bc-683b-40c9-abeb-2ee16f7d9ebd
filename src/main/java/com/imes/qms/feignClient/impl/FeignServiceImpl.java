package com.imes.qms.feignClient.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.imes.common.annotation.RequireModule;
import com.imes.common.entity.Result;
import com.imes.common.exception.CommonException;
import com.imes.common.support.Message;
import com.imes.common.support.Resume;
import com.imes.common.utils.*;
import com.imes.domain.entities.activiti.po.TaskDTO;
import com.imes.domain.entities.activiti.vo.TasksResultVo;
import com.imes.domain.entities.flowable.FlowableProcessDTO;
import com.imes.domain.entities.flowable.FlowableTaskDTO;
import com.imes.domain.entities.ppc.po.*;
import com.imes.domain.entities.ppc.vo.*;
import com.imes.domain.entities.qms.po.QmsRouteInspectionDetail;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.sys.PpcProcessQmsDepartmentSearchVo;
import com.imes.domain.entities.system.*;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.domain.entities.system.base.vo.PreviewFileVo;
import com.imes.domain.entities.system.base.vo.SysExportTemplateWrap;
import com.imes.domain.entities.system.qz.QzEventNow;
import com.imes.domain.entities.system.vo.MaterialVo;
import com.imes.domain.entities.system.vo.SendUserMessageVO;
import com.imes.domain.entities.system.vo.SysDictDetailVo;
import com.imes.domain.entities.system.vo.SysMaterialSupplierVO;
import com.imes.domain.entities.system.warning.SysWarningInfo;
import com.imes.domain.entities.system.wechart.CompanyWechartMsg;
import com.imes.domain.entities.wms.WmsArrivalOrder;
import com.imes.domain.entities.wms.WmsArrivalOrderItem;
import com.imes.qms.feignClient.*;
import com.imes.qms.service.FtsInterfaceService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.multipart.MultipartFile;

import java.beans.IntrospectionException;
import java.lang.reflect.InvocationTargetException;
import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FeignServiceImpl implements FeignService {

    @Autowired
    SysClient sysClient;
    @Autowired
    ActivitiClient activitiClient;

    @Autowired
    FtsInterfaceService ftsInterfaceService;

    @Autowired
    FlowInterfaceService flowInterfaceService;

    @Autowired
    PpcClient ppcClient;

    @Autowired
    WmsClient wmsClient;

    @Autowired
    PluginInterfaceService pluginInterfaceService;

    @Autowired
    private ObjectMapper objectMapper;


    @Override
    public QzEventNow getEventNow(String id) throws Exception {
        Result result = sysClient.getEventNow(id);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            QzEventNow po = new QzEventNow();
            CustomerDateConverter dateConverter = new CustomerDateConverter();
            ConvertUtils.register(dateConverter, Date.class);
            BeanUtils.copyProperties(po, map);
            return po;
        } else {
            AssertUtil.throwException("调用系统模块的事件日志接口失败【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public void updateFail(Map map) throws Exception {
        Result result = sysClient.updateFail(map);
        if (result.isSuccess()) {
        } else {
            AssertUtil.throwException("调用系统模块的事件写失败日志接口失败【{}】", result.getMessage());
        }
    }

    @Override
    public String startActivitiProcess(String activitiProcessKey, Map<String, Object> map) throws Exception {
        Result result = activitiClient.startActivitiProcess(activitiProcessKey, map);
        if (result.isSuccess()) {
            Object data = result.getData();
            if (StringUtils.isNullOrBlank(data)) {
                return "";
            } else {
                return data.toString();
            }
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public TasksResultVo queryTasksByUserCode(int pageNum, int pageSize, String userCode) throws Exception {
        Result result = activitiClient.queryTasksByUserCode(userCode, pageNum, pageSize);
        Map<String, Object> resultMap = new HashMap<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
            TasksResultVo vo = new TasksResultVo();
            vo.setTotal(Integer.parseInt(resultMap.get("total").toString()));
            List<TaskDTO> taskList = new ArrayList<>();
            List<Map<String, Object>> poList = (List<Map<String, Object>>) resultMap.get("list");
            for (int i = 0; i < poList.size(); i++) {
                Map map = poList.get(i);
                TaskDTO bean = new TaskDTO();
                CustomerDateConverter dateConverter = new CustomerDateConverter();
                ConvertUtils.register(dateConverter, Date.class);
                BeanUtils.copyProperties(bean, map);
                taskList.add(bean);
            }
            vo.setList(taskList);
            return vo;
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public String getKeyByTaskId(String taskId) throws Exception {
        Result result = activitiClient.getKeyByTaskId(taskId);
        Map<String, Object> resultMap = new HashMap<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
            if (resultMap.get("taskAttrKey") == null) {
                return "";
            } else {
                return resultMap.get("taskAttrKey").toString();
            }
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public Map<String, Object> deleteProcessInstance(String processInstanceId) throws Exception {
        Result result = activitiClient.deleteProcessInstance(processInstanceId);
        Map<String, Object> resultMap = new HashMap<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
            if (resultMap.get("status") == null) {
                AssertUtil.throwException("调用工作流接口异常");
            }
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return resultMap;
    }

    @Override
    public Integer submitTask(String taskId, String comment, String flag) throws Exception {
        Result result = activitiClient.submitTask(taskId, comment, flag);
        Map<String, Object> resultMap = new HashMap<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return Integer.parseInt(resultMap.get("status").toString());
    }

    @Override
    public List<TaskDTO> getTaskByProcessKey(List<String> ProcessKey, String userCode, List<String> groups) throws Exception {
        Result result = activitiClient.getTaskByProcessKey(ProcessKey, userCode, groups);
        Map<String, Object> resultMap = new HashMap<>();
        List<TaskDTO> taskDTOS = new ArrayList<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
            if (Integer.parseInt(resultMap.get("total").toString()) != 0) {
                List<Map<String, Object>> mapList = (List<Map<String, Object>>) resultMap.get("list");
                for (int i = 0; i < mapList.size(); i++) {
                    TaskDTO bean = new TaskDTO();
                    CustomerDateConverter dateConverter = new CustomerDateConverter();
                    ConvertUtils.register(dateConverter, Date.class);
                    BeanUtils.copyProperties(bean, mapList.get(i));
                    taskDTOS.add(bean);
                }
            }
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return taskDTOS;
    }

    @Override
    public Integer submitTaskAll(Map<String, String> map) throws Exception {
        Result result = activitiClient.submitTask(map);
        Map<String, Object> resultMap = new HashMap<>();
        if (result.isSuccess()) {
            resultMap = (Map<String, Object>) result.getData();
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return Integer.parseInt(resultMap.get("status").toString());
    }

    @Override
    public Boolean workOrNot(String calendarCode, String date) throws Exception {
        Result result = sysClient.workOrNot(calendarCode, date);
        Boolean bool = false;
        if (result.isSuccess()) {
            bool = Boolean.parseBoolean(result.getData().toString());
        } else {
            AssertUtil.throwException("调用获取工作流接口失败【{}】", result.getMessage());
        }
        return bool;
    }

    @Override
    public String getAutoCode(String autocode) throws Exception {
        Result result = sysClient.getAutoCode(autocode);
        String code = "";
        if (result.isSuccess()) {
            code = result.getData().toString();
        } else {
            AssertUtil.throwException("调用生成字段编码接口失败【{}】", result.getMessage());
        }
        return code;
    }

    @Override
    public List<Map<String, Object>> findAreaTree(String areaCategory) throws Exception {
        Result result = sysClient.findAreaTree(areaCategory);
        if (result.isSuccess()) {
            return (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException("获取区域树结构异常【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public List<CoDepartment> findByUserCodeAndType(String userCode, String type) throws Exception {
        Result result = sysClient.findByUserCodeAndType(userCode, type);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        List<CoDepartment> reList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map map = list.get(i);
            CoDepartment bean = new CoDepartment();
            BeanMapUtils.map2Bean(bean, map);
            reList.add(bean);
        }
        return reList;
    }

    @Override
    public TaskDTO getTaskByProcessInstanceId(String processInstanceId) throws Exception {
        Result result = activitiClient.getTaskByProcessInstanceId(processInstanceId);
        if (result.isSuccess()) {

            Map<String, Object> mapList = ((Map<String, Object>) result.getData());
            TaskDTO taskDTO = new TaskDTO();
            CustomerDateConverter dateConverter = new CustomerDateConverter();
            ConvertUtils.register(dateConverter, Date.class);
            BeanUtils.copyProperties(taskDTO, mapList);
            return taskDTO;
        } else {
            AssertUtil.throwException("根据工作流实例ID获取任务信息接口异常【{}】", result.getMessage());
        }
        return null;
    }

    @Override
    public empyes findByUserCode(String userCode) {
        if (StrUtil.isBlank(userCode)) {
            return null;
        }
        Result result = sysClient.findByUserCode(userCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            empyes empyes = new empyes();
//            CustomerDateConverter dateConverter = new CustomerDateConverter();
//            ConvertUtils.register(dateConverter, Date.class);
//            BeanUtils.copyProperties(empyes, map);
            // 移除没有用到的字段
            map.remove("agentStartTime");
            map.remove("agentEndTime");
            try {
                BeanMapUtils.map2Bean(empyes, map);
            } catch (Exception e) {
                log.error("BeanMapUtils.map2Bean转换失败！错误信息：{}", e);
            }
            return empyes;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public SysFile singleUpload(MultipartFile file) throws Exception {
        Result result = ftsInterfaceService.singleUpload(file, "", Constants.BELONG_MODULE_QMS, "");
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            SysFile sysFile = new SysFile();
            CustomerDateConverter dateConverter = new CustomerDateConverter();
            ConvertUtils.register(dateConverter, Date.class);
            BeanUtils.copyProperties(sysFile, map);
            return sysFile;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public PreviewFileVo previewFile(String id) throws Exception {
        Result result = ftsInterfaceService.previewFile(id);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            PreviewFileVo vo = new PreviewFileVo();
            CustomerDateConverter dateConverter = new CustomerDateConverter();
            ConvertUtils.register(dateConverter, Date.class);
            BeanUtils.copyProperties(vo, map);
            return vo;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public SysFile getFileById(String id) throws Exception {
        Result result = ftsInterfaceService.getFileById(id);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) {
                return null;
            }
            SysFile sysFile = new SysFile();
            BeanMapUtils.map2Bean(sysFile, map);
            return sysFile;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public Result fileDelete(String id) {
        return ftsInterfaceService.deleteFile(id);
    }


    @Override
    public List<empyes> departLeader(String workshopCode) throws Exception {
        Result result = sysClient.departLeader(workshopCode);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        List<empyes> reList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map map = list.get(i);
            empyes bean = new empyes();
            BeanMapUtils.map2Bean(bean, map);
            reList.add(bean);
        }
        return reList;
    }

    @Override
    public Result findPullSupplier() {
        return sysClient.findPullSupplier();
    }

    @Override
    public List<MaterialVo> queryMaterialByCodeIn(List<String> materialCodeList) throws Exception {
        Result result = sysClient.queryMaterialByCodeIn(materialCodeList);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<MaterialVo> materialList = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                MaterialVo bean = new MaterialVo();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                materialList.add(bean);
            }
            return materialList;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public boolean startWarning(SysWarningInfo sysWarningInfo) throws Exception {
        Result result = sysClient.startWarning(sysWarningInfo);
        if (result.isSuccess()) {
            boolean b = (boolean) result.getData();
            return b;
        } else {
            AssertUtil.throwException(result.getMessage());
        }
        return false;
    }

    @Override
    public List<Map<String, Object>> getEmpyesByDepartId(String id) {
        Result userByDepartId = sysClient.getEmpyesByDepartId(id);
        if (!userByDepartId.isSuccess()) {
            AssertUtil.throwException(userByDepartId.getMessage(), userByDepartId.getCode());
        }
        if (null != userByDepartId.getData()) {
            return (List<Map<String, Object>>) userByDepartId.getData();
        }
        return null;
    }

    @Override
    public Result sendMobileMsg(String title, String content, String userCodes, String messageCode) throws Exception {
        CompanyWechartMsg msg = new CompanyWechartMsg();
        msg.setContent(content);
        msg.setTitle(title);
        msg.setTouser(userCodes);
        msg.setMessageCode(messageCode);
        List<CompanyWechartMsg> res = new ArrayList<>();
        res.add(msg);
        Result result = sysClient.batchSendWechartMsg(res);
        if (result.isSuccess()) {
        } else {
            log.error("消息发送失败, {}", result.getMessage());
        }
        return result;
    }

    @Override
    public List<Map<String, Object>> queryDepartmentByType(String type) throws Exception {
        Result result = sysClient.queryDepartmentByType(type);
        if (result.isSuccess()) {
            List<Map<String, Object>> data = (List<Map<String, Object>>) result.getData();
            return data;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> sltUserByDepartId(String departId) throws Exception {
        Result result = sysClient.sltUserByDepartId(departId);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return list;
    }

    @Override
    public List<String> findChildDeptByUserCode(String userCode) {
        List<String> resultList = null;
        Result result = sysClient.findChildDeptByUserCode(userCode);
        if (result.isSuccess()) {
            resultList = (List<String>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return resultList;
    }

    @Override
    public Boolean sendPcMessage(String messageCode, String messageContent, List<String> userCodes, String sendUserCode) throws Exception {
        SendUserMessageVO message = SendUserMessageVO.builder()
                .messageCode(messageCode)
                .messageContent(messageContent)
                .userCodes(userCodes)
                .sendUserCode(sendUserCode).build();
        Result result = sysClient.batchSave(message);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return true;
    }

    @Override
    public List<Map<String, Object>> findUserByDepartCode(String code) throws Exception {
        Result userByDepartCode = sysClient.findUserByDepartCode(code);
        if (!userByDepartCode.isSuccess()) {
            AssertUtil.throwException(userByDepartCode.getMessage(), userByDepartCode.getCode());
        }
        if (null != userByDepartCode.getData()) {
            return (List<Map<String, Object>>) userByDepartCode.getData();
        }
        return null;
    }

    @Override
    public void sendWechartMsg(CompanyWechartMsg companyWechartMsg) throws Exception {
        Result result = sysClient.sendWechartMsg(companyWechartMsg);
        if (result.isSuccess()) {
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public CoDepartment getInspectTeam(String bomCode, String bomVer, String lineCode, String processCode) throws Exception {
        Result result = sysClient.getInspectTeam(bomCode, bomVer, lineCode, processCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) {
                return null;
            }
            CoDepartment coDepartment = new CoDepartment();
            BeanMapUtils.map2Bean(coDepartment, map);
            return coDepartment;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public CoDepartment getInspectTeamByRouteCode(String routeCode, String processCode) throws Exception {
        Result result = sysClient.getInspectTeamByRouteCode(routeCode, processCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) {
                return null;
            }
            CoDepartment coDepartment = new CoDepartment();
            BeanMapUtils.map2Bean(coDepartment, map);
            return coDepartment;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public void inspectResultCallBack(List<InspectResultVo> vo) throws Exception {
        Result result = ppcClient.inspectResultCallBack(vo);
        if (result.isSuccess()) {
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public List<QmsRouteInspectionVo> getRouteInspectMainAndDetail(String routeCode, String processCode) throws Exception {
        if (!StringUtils.isNullOrBlank(routeCode)) {
            log.info("工艺路线模式：工艺路线【{}】", routeCode);
            Result result = sysClient.getRouteInspectMainAndDetail(routeCode, processCode);
            if (result.isSuccess()) {
                List<Map> resultList = (List<Map>) result.getData();
                List<QmsRouteInspectionVo> qmsList = new ArrayList<>(resultList.size());
                for (int i = 0; i < resultList.size(); i++) {
                    QmsRouteInspectionVo bean = new QmsRouteInspectionVo();
                    BeanMapUtils.map2Bean(bean, resultList.get(i));
                    qmsList.add(bean);
                }
                for (int i = 0; i < qmsList.size(); i++) {
                    QmsRouteInspectionVo qmsVo = qmsList.get(i);
                    List<Map> detailList = (List<Map>) resultList.get(i).get("detailList");
                    List<QmsRouteInspectionDetail> qmsDetailList = new ArrayList<>(detailList.size());
                    for (int j = 0; j < detailList.size(); j++) {
                        QmsRouteInspectionDetail detailBean = new QmsRouteInspectionDetail();
                        BeanMapUtils.map2Bean(detailBean, detailList.get(j));
                        qmsDetailList.add(detailBean);
                    }
                    qmsVo.setDetailList(qmsDetailList);
                    qmsList.set(i, qmsVo);
                }
                return qmsList;
            } else {
                AssertUtil.throwException(result.getCode(), result.getMessage());
            }
        } else {
            log.info("简单计划模式：工序【{}】", processCode);
            Result result = sysClient.getProcessStdInspectionAndDetail(processCode);
            if (result.isSuccess()) {
                if (result.getData() != null) {
                    List<QmsStdInspectionDetailSeniorVo> list = JSON.parseArray(JSON.toJSONString(((Map) result.getData()).get("rows")), QmsStdInspectionDetailSeniorVo.class);
                    List<QmsRouteInspectionVo> reList = new ArrayList<>();
                    for (QmsStdInspectionDetailSeniorVo qvo : list) {
                        boolean b = reList.stream().anyMatch(x -> x.getInspectCode().equals(qvo.getInspectCode()));
                        if (b) {
                            continue;
                        }
                        QmsRouteInspectionVo svo = new QmsRouteInspectionVo();
                        reList.add(svo);
                        svo.setInspectCode(qvo.getInspectCode());
                        svo.setInspectType("3");// 巡检/临时巡检
                        svo.setInspectName(qvo.getInspectName());
                        svo.setProcessCode(processCode);
                        List<QmsRouteInspectionDetail> detailList = new ArrayList<>();
                        svo.setDetailList(detailList);
                        List<QmsStdInspectionDetailSeniorVo> matchList = list.stream().filter(x -> x.getInspectCode().equals(qvo.getInspectCode())).collect(Collectors.toList());
                        for (QmsStdInspectionDetailSeniorVo matchVo : matchList) {
                            QmsRouteInspectionDetail detail = getQmsRouteInspectionDetail(processCode, matchVo);
                            detailList.add(detail);
                        }

                    }
                    return reList;
                }
            } else {
                AssertUtil.throwException(result.getCode(), result.getMessage());
            }

        }
        return null;
    }

    @Override
    public List<PpcProcessQmsDepartmentSearchVo> queryInspectTeamByProcessCode(String processCode) throws Exception {
        Result result = sysClient.queryInspectTeamByProcessCode(processCode);
        if (result.isSuccess()) {
            if (result.getData() != null) {
                List<PpcProcessQmsDepartmentSearchVo> list = JSON.parseArray(JSON.toJSONString(result.getData()), PpcProcessQmsDepartmentSearchVo.class);
                return list;
            }
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @NotNull
    private static QmsRouteInspectionDetail getQmsRouteInspectionDetail(String processCode, QmsStdInspectionDetailSeniorVo matchVo) {
        QmsRouteInspectionDetail detail = new QmsRouteInspectionDetail();
        detail.setId(matchVo.getId());
        detail.setInspectCode(matchVo.getInspectCode());
        detail.setIsExempt("0");
        detail.setProcessCode(processCode);
        detail.setItemIndex(matchVo.getItemIndex());
        detail.setItemName(matchVo.getItemName());
        detail.setJudgeMethod(matchVo.getJudgeMethod());
        detail.setLowerLimit(matchVo.getLowerLimit());
        detail.setUpperLimit(matchVo.getUpperLimit());
        detail.setStandardDesc(matchVo.getStandardDesc());
        detail.setToolType(matchVo.getToolType());
        return detail;
    }

    @Override
    public List<PpcProcessBadCode> queryBadCode(PpcProcessBadCode ppcProcessBadCode) throws Exception {
        Result result = sysClient.queryByCond(ppcProcessBadCode);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<PpcProcessBadCode> processBadCodeList = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                PpcProcessBadCode bean = new PpcProcessBadCode();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                processBadCodeList.add(bean);
            }
            return processBadCodeList;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getEmpyesByDepartCode(String id) {
        Result result = sysClient.getEmpyesByDepartCode(id);
        if (result.isSuccess()) {
            return (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<QmsStdInspectionVo> queryByMap(Map<String, Object> map) throws Exception {
        Result result = sysClient.queryByMap(map);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<QmsStdInspectionVo> voList = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                QmsStdInspectionVo bean = new QmsStdInspectionVo();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                voList.add(bean);
            }
            return voList;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public CoDepartment findByDepartmentCode(String departmentCode) throws Exception {
        Result result = sysClient.getByDepartmentCode(departmentCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            CoDepartment po = new CoDepartment();
            BeanMapUtils.map2Bean(po, map);
            return po;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public List<PpcProducePlanSchedul> queryAll(String pcNo) throws Exception {
        Result result = ppcClient.queryAll(pcNo);
        try {
            if (result.isSuccess()) {
                if (null != result.getData()) {
                    Map<String, Object> resultMap = (Map<String, Object>) result.getData();
                    List<Map> mapList = (List<Map>) resultMap.get("ppcProducePlanScheduls");
                    List<PpcProducePlanSchedul> results = new ArrayList<>(mapList.size());
                    for (int i = 0; i < mapList.size(); i++) {
                        PpcProducePlanSchedul bean = new PpcProducePlanSchedul();
                        Map<String, Object> map = (Map<String, Object>) mapList.get(i);
                        BeanMapUtils.map2Bean(bean, map);
                        results.add(bean);
                    }
                    return results;
                } else {
                    return null;
                }

            } else {
                AssertUtil.throwException(result.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> getUserByIncludeDepType(String departmentType) {
        Result mainLeaderByDeptType = sysClient.getUserByIncludeDepType(departmentType);
        List<Map<String, Object>> resultMap = (List<Map<String, Object>>) mainLeaderByDeptType.getData();
        return resultMap;
    }

    @Override
    public PpcProcess queryByProcessCode(String processCode) throws Exception {
        Result result = sysClient.selectDevListByProcessCode(processCode);
        if (result.isSuccess()) {
            Map map = (Map) ((Map) result.getData()).get("processInfo");
            PpcProcess process = new PpcProcess();
            BeanMapUtils.map2Bean(process, map);
            return process;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public List<PpcWorkFinishVo> getWorkFinishByPcNo(String wfNo) throws Exception {
        Result result = ppcClient.getWorkFinishByPcNo(wfNo);
        try {
            if (result.isSuccess()) {
                if (null != result.getData()) {
                    Map<String, Object> resultMap = (Map<String, Object>) result.getData();
                    List<Map> mapList = (List<Map>) resultMap.get("list");
                    List<PpcWorkFinishVo> results = new ArrayList<>(mapList.size());
                    for (int i = 0; i < mapList.size(); i++) {
                        PpcWorkFinishVo bean = new PpcWorkFinishVo();
                        Map<String, Object> map = (Map<String, Object>) mapList.get(i);
                        BeanMapUtils.map2Bean(bean, map);
                        results.add(bean);
                    }
                    return results;
                } else {
                    return null;
                }

            } else {
                AssertUtil.throwException(result.getCode(), result.getMessage());
            }
        } catch (Exception e) {
            return null;
        }
        return null;
    }

    @Override
    public List<Map<String, Object>> inspectedDapart(String type) throws Exception {
        Result result = sysClient.inspectedDapart(type);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<Map<String, Object>> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                Map<String, Object> bean = new HashMap<>();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<PpcRouteProcessQmsDepartment> inspectionDapart(List<String> departCodes) throws Exception {
        Result result = sysClient.inspectionDapart(departCodes);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<PpcRouteProcessQmsDepartment> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                PpcRouteProcessQmsDepartment bean = new PpcRouteProcessQmsDepartment();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public Map<String, Object> getMaterials(int pageNum, int pageSize, String specification,
                                            String quality, String pcNo, String processName, String materialName, String status, List<String> departCodes) throws Exception {
        Result result = ppcClient.getWorkFinishByStatusAndDeptCodes(pageNum, pageSize, status, specification,
                quality, pcNo, processName, materialName, departCodes);
        if (result.isSuccess()) {
            Map map = new HashMap();
            if (null != result.getData()) {
                Map<String, Object> resultList = (Map<String, Object>) result.getData();
//                List<Map> mapList = (List<Map>)resultMap.get("list");
//                List<WorkOderVo> results = new ArrayList<>(mapList.size());
//                for (int i = 0; i < mapList.size(); i++) {
//                    WorkOderVo bean = new WorkOderVo();
//                    Map<String,Object> map = (Map<String, Object>) mapList.get(i);
//                    BeanMapUtils.map2Bean(bean,map);
//                    results.add(bean);
//                }
                return resultList;
            } else {
                return null;
            }
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public List<ProcessBadCountsQualityReportVO> queryBadCount(String start, String end, String limit) throws Exception {
        Result result = ppcClient.queryBadCount(start, end, limit);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<ProcessBadCountsQualityReportVO> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                ProcessBadCountsQualityReportVO bean = new ProcessBadCountsQualityReportVO();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public List<ProcessBadCountsQualityReportVO> queryBadCount2() throws Exception {
        Result result = ppcClient.queryBadCount();
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<ProcessBadCountsQualityReportVO> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                ProcessBadCountsQualityReportVO bean = new ProcessBadCountsQualityReportVO();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("ppc")
    public List<Map> getBadCountDetails(String badCode, String materialCode, String materialName, String processCode, String processName, String specification) throws Exception {
        Result result = ppcClient.getBadCountDetails(badCode, materialCode, materialName, processCode,
                processName, specification);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<Map> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                Map bean = new HashMap();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public boolean getAuthorityByModuleCode(String moduleCode) {
        Result result = sysClient.getAuthorityByModuleCode(moduleCode);
        if (result.isSuccess()) {
            return (boolean) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return false;
    }

    @Override
    public List<CoDepartmentUser> findByDepartCode(String departmentCode) throws Exception {
        Result result = sysClient.findByDepartCode(departmentCode);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<CoDepartmentUser> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                CoDepartmentUser bean = new CoDepartmentUser();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<SysMaterialSupplierVO> findByMaterialCode(String materialCode) throws Exception {
        Result result = sysClient.findByMaterialCode(materialCode);
        if (result.isSuccess()) {
            List<Map> resultList = (List<Map>) result.getData();
            List<SysMaterialSupplierVO> results = new ArrayList<>(resultList.size());
            for (int i = 0; i < resultList.size(); i++) {
                SysMaterialSupplierVO bean = new SysMaterialSupplierVO();
                BeanMapUtils.map2Bean(bean, resultList.get(i));
                results.add(bean);
            }
            return results;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public SysFile multiUpload(MultipartFile[] file) throws Exception {
        Result result = ftsInterfaceService.multiUpload(file, "", Constants.BELONG_MODULE_FILE, Constants.BELONG_MODULE_FILE);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            SysFile sysFile = new SysFile();
//            CustomerDateConverter dateConverter = new CustomerDateConverter();
//            ConvertUtils.register(dateConverter, Date.class);
//            BeanUtils.copyProperties(sysFile, map);
            BeanMapUtils.map2Bean(sysFile, map);
            return sysFile;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public boolean isTenantAdmin(String userCode) throws Exception {
        Result result = sysClient.isTenantAdmin(userCode);
        if (result.isSuccess()) {
            boolean b = Boolean.parseBoolean(result.getData().toString());
            return b;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return false;
    }

    @Override
    @RequireModule("ppc")
    public void resultIsQms(PlanScheduleStoreVo vo) throws Exception {
        Result result = ppcClient.resultIsQms(vo);
        if (result.isSuccess()) {
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    @RequireModule("wms")
    public void receiveQmsData(List<MaterialTaskItemVo> vo) throws Exception {
        Result result = wmsClient.receiveQmsData(vo);
        if (result.isSuccess()) {
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    @RequireModule("wms")
    public WmsArrivalOrder getByAoCode(String aoCode) throws Exception {
        if (StringUtils.isNullOrBlank(aoCode)) {
            AssertUtil.throwException("aoCode不能为空");
        }
        Result result = wmsClient.getByAoCode(aoCode);
        if (result.isSuccess()) {
            Map<String, Object> map = (HashMap) result.getData();
            WmsArrivalOrder wmsArrivalOrder = new WmsArrivalOrder();
            BeanMapUtils.map2Bean(wmsArrivalOrder, map);
            return wmsArrivalOrder;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    @RequireModule("wms")
    public WmsArrivalOrderItem getByAoItemCode(String aoItemCode) throws Exception {
        if (StringUtils.isNullOrBlank(aoItemCode)) {
            AssertUtil.throwException("aoItemCode不能为空");
        }
        Result result = wmsClient.getByAoCode(aoItemCode);
        if (result.isSuccess()) {
            Map<String, Object> map = (HashMap) result.getData();
            WmsArrivalOrderItem arrivalOrderItem = new WmsArrivalOrderItem();
            BeanMapUtils.map2Bean(arrivalOrderItem, map);
            return arrivalOrderItem;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<String> findMainLeaderByDeptType(String departType) {
        Result result = sysClient.findMainLeaderByDeptType(departType);
        if (result.isSuccess()) {
            return (List<String>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }


    @Override
    public List<CoDepartmentUser> findCoDepartmentByUserCode(String userCode) throws Exception {
        Result result = sysClient.findCoDepartmentByUserCode(userCode);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        List<CoDepartmentUser> departmentUserList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map map = list.get(i);
            CoDepartmentUser bean = new CoDepartmentUser();
            BeanMapUtils.map2Bean(bean, map);
            departmentUserList.add(bean);
        }
        return departmentUserList;
    }

    @Override
    public Map<String, Object> queryInfo(String processCode) throws Exception {
        Result result = sysClient.queryInfo(processCode);
        if (result.isSuccess()) {
            Map map = new HashMap();
            if (null != result.getData()) {
                Map<String, Object> resultList = (Map<String, Object>) result.getData();
//                List<Map> mapList = (List<Map>)resultMap.get("list");
//                List<WorkOderVo> results = new ArrayList<>(mapList.size());
//                for (int i = 0; i < mapList.size(); i++) {
//                    WorkOderVo bean = new WorkOderVo();
//                    Map<String,Object> map = (Map<String, Object>) mapList.get(i);
//                    BeanMapUtils.map2Bean(bean,map);
//                    results.add(bean);
//                }
                return resultList;
            } else {
                return null;
            }
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public Promaterial findMaterialByCode(String materialCode) throws Exception {
        if (StringUtils.isNullOrBlank(materialCode)) {
            AssertUtil.throwException("物料编码不能为空");
        }
        Result result = sysClient.findMaterialByCode(materialCode);
        if (result.isSuccess()) {
            Map<String, Object> map = (HashMap) result.getData();
            Promaterial po = new Promaterial();
            BeanMapUtils.map2Bean(po, map);
            return po;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public String startProcessFlow(FlowableProcessDTO flowableProcessDTO) {
        Result result = flowInterfaceService.startProcess(flowableProcessDTO);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        } else {
            String actId = result.getData().toString();
            return actId;
        }
        return null;
    }

    @Override
    public void autoCheck(FlowableProcessDTO flowableProcessDTO) {
        Result result = flowInterfaceService.autoCheck(flowableProcessDTO);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public void submitTaskFlow(FlowableTaskDTO taskDTO) {
        Result result = flowInterfaceService.completeTask(taskDTO);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public List<CoDepartment> getLeaderTeamCodeByUserCode(String userCode, String type) throws Exception {
        Result result = sysClient.getLeaderTeamCodeByUserCode(userCode, type);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = (List<Map<String, Object>>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        List<CoDepartment> reList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map map = list.get(i);
            CoDepartment bean = new CoDepartment();
            BeanMapUtils.map2Bean(bean, map);
            reList.add(bean);
        }
        return reList;
    }

    @Override
    public List<CoDepartment> findChildrenByParentCodeAndType(String parentCode, String type) {
        Result result = sysClient.findChildrenByParentCodeAndType(parentCode, type);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<CoDepartment> departments = changeList(data, CoDepartment.class);
            return departments;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<String> dataPermission(String userCode) throws Exception {
        Result result = sysClient.dataPermission(userCode);
        if (result.isSuccess()) {
            return (List<String>) result.getData();
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public SysExportTemplateWrap exportTemplateFetch(String bizCode) {
        Result result = sysClient.exportTemplateFetch(bizCode);
        SysExportTemplateWrap item = new SysExportTemplateWrap();
        if (result.getData() != null) {
            item = JSON.parseObject(JSON.toJSONString(result.getData()), SysExportTemplateWrap.class);
        }
        return item;
    }

    @Override
    public Result getPpcRouteProcessQmsDepartment(String routeCode, String processCode) {
        Result ppcRouteProcessQmsDepartment = sysClient.getPpcRouteProcessQmsDepartment(routeCode, processCode);
        return ppcRouteProcessQmsDepartment;
    }

    @Override
    public Result deleteProcessInstanceComplete(String processInstanceId) {
        return flowInterfaceService.deleteProcessInstanceComplete(processInstanceId);
    }

    @Override
    @RequireModule("ppc")
    public Result workReportBackByQMS(List<String> wfNo) {
        Result result = ppcClient.workReportBackByQMS(wfNo);
        if (result.isSuccess()) {
            return result;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<SysDictDetailVo> getDictCode(String mainCode) throws Exception {
        Result result = sysClient.getDictDetailList(mainCode);
        List<Map<String, Object>> list = new ArrayList<>();
        if (result.isSuccess()) {
            list = ((List) result.getData());
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        List<SysDictDetailVo> reList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            Map map = (Map) list.get(i);
            SysDictDetailVo bean = new SysDictDetailVo();
            BeanMapUtils.map2Bean(bean, map);
            reList.add(bean);
        }
        return reList;
    }

    @Override
    public Result addCheckoutSheet(Map<String, Object> param) {
        Result result = pluginInterfaceService.addCheckoutSheet(param);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        } else {
            return result;
        }
        return null;
    }

    @Override
    public SysUnit findSysUnitByName(String name) throws Exception {
        Result result = sysClient.findSysUnitByName(name);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            SysUnit bean = new SysUnit();
            BeanMapUtils.map2Bean(bean, map);
            return bean;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public SysIoManage getIoByCode(String ioCode) throws Exception {
        Result result = sysClient.getIoByCode(ioCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) {
                return null;
            }
            SysIoManage po = new SysIoManage();
            BeanMapUtils.map2Bean(po, map);
            return po;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public Result queryQmsSampleManager(String processCode) {
        Result result = sysClient.queryQmsSampleManager(processCode);
        if (result.isSuccess()) {
            return result;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }


    /**
     * Object对象转 List集合
     *
     * @param object Object对象
     * @param clazz  需要转换的集合
     * @param <T>    泛型类
     * @return
     */
    public static <T> List<T> changeList(Object object, Class<T> clazz) {
        List<T> result = new ArrayList<>();
        if (object instanceof List<?>) {
            for (Object o : (List<?>) object) {
                String string = JSONObject.toJSONString(o);
                T t = JSONObject.parseObject(string, clazz);
                result.add(t);
            }
            return result;
        }
        return result;
    }


    /**
     * 获取物料详情
     *
     * @param materialCode
     * @return
     * @throws Exception
     */
    @Override
    public Promaterial getMaterialByCode(String materialCode) throws CommonException, InvocationTargetException, IllegalAccessException, IntrospectionException, ParseException {
        Result result = sysClient.findMaterialByCode(materialCode);
        if (result.isSuccess()) {
            Map map = (Map) result.getData();
            if (map == null) return null;
            Promaterial po = new Promaterial();
            BeanMapUtils.map2Bean(po, map);
            return po;
        } else {
            AssertUtil.throwException("调用系统模块的获取物料接口失败" + result.getMessage());
        }
        return null;
    }

    @Override
    public Boolean batchSendMessage(List<SendUserMessageVO> messageList) {
        Result result = sysClient.batchSendMessage(messageList);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return true;
    }

    @Override
    public Result batchSendWechartMsg(List<CompanyWechartMsg> companyWechartMsgList) throws Exception {
        Result result = sysClient.batchSendWechartMsg(companyWechartMsgList);
        if (result.isSuccess()) {
        } else {
            log.error("消息发送失败, {}", result.getMessage());
        }
        return result;
    }

    @Override
    public Result unAuditAndDel(Map<String, Object> param) {
        Result result = pluginInterfaceService.unAuditAndDel(param);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        } else {
            return result;
        }
        return null;
    }

    @Override
    public List<PpcProcess> queryAllProcessList() throws Exception {
        Result result = sysClient.queryProcessList(1, Integer.MAX_VALUE, new HashMap<>());
        List<PpcProcess> list = new ArrayList<>();
        if (result.isSuccess()) {
            Map<String, Object> resultMap = (Map) result.getData();
            list = JSON.parseArray(JSON.toJSONString(resultMap.get("list")), PpcProcess.class);
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return list;
    }

    @Override
    public void saveResume(Resume resume) {
        Result result = sysClient.saveResume(ListUtil.toList(resume));
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public void sendMsg(Message userMsg) {
        Result result = sysClient.sendMsg(ListUtil.toList(userMsg));
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public void sendMsg(List<Message> msgList) {
        Result result = sysClient.sendMsg(msgList);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public List<PpcProduceOrder> searchWorkOrders(String keyWord,String projectName,String orderNo) {
        Result result = ppcClient.searchWorkOrders(keyWord,projectName,orderNo);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<PpcProduceOrder> ppcProduceOrders = changeList(data, PpcProduceOrder.class);
            return ppcProduceOrders;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @SneakyThrows
    @Override
    public PpcProduceOrderAndPlanAztVo queryByLocalOrderIdAndLineNo(String localOrderId, String lineNo) {
        Result result = ppcClient.queryByLocalOrderIdAndLineNo(localOrderId , lineNo);
        if (result.isSuccess()) {
            PpcProduceOrderAndPlanAztVo ppcProduceOrderAndPlanAztVo = JSON.parseObject(JSON.toJSONString(result.getData()), PpcProduceOrderAndPlanAztVo.class);
            return ppcProduceOrderAndPlanAztVo;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<PpcProduceOrder> getByIdList(List<String> list) {
        Result result = ppcClient.getByIdList(list);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<PpcProduceOrder> ppcProduceOrders = changeList(data, PpcProduceOrder.class);
            return ppcProduceOrders;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public PpcProducePlanAzt getEffectPlanByOrderAndLine(String orderId, String lineNo) {
        Result result = ppcClient.getEffectPlanByOrderAndLine(orderId, lineNo);
        if (result.isSuccess()) {
            if(result.getData()==null){
                return null;
            }
            return JSONObject.parseObject(JSONObject.toJSONString(result.getData()), PpcProducePlanAzt.class);
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }


    @Override
    public List<PpcWorkFinishMain> queryByOrderIdList(List<String> OrderIdList) {
        Result result = ppcClient.queryByOrderIdList(OrderIdList);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<PpcWorkFinishMain> ppcWorkFinishMains = changeList(data, PpcWorkFinishMain.class);
            return ppcWorkFinishMains;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<PpcProduceProcessAzt> queryByProducePlanIdList(List<String> list) {
        Result result = ppcClient.queryByProducePlanIdList(list);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<PpcProduceProcessAzt> ppcProduceProcessAzts = changeList(data, PpcProduceProcessAzt.class);
            return ppcProduceProcessAzts;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public PpcProduceOrderPrepareAndDetailVo getPrepareOoderAndDetailByOrderIdList(List<String> orderIdList) {
        Result result = ppcClient.getPrepareOoderAndDetailByOrderIdList(orderIdList);
        if (result.isSuccess()) {
            if(result.getData()==null){
                return null;
            }
            return JSONObject.parseObject(JSONObject.toJSONString(result.getData()), PpcProduceOrderPrepareAndDetailVo.class);
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<PpcProducePlanAzt> queryByAztOrderIdList(List<String> list) {
        Result result = ppcClient.queryByAztOrderIdList(list);
        if (result.isSuccess()) {
            Object data = result.getData();
            List<PpcProducePlanAzt> aztList = changeList(data, PpcProducePlanAzt.class);
            return aztList;
        } else {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
        return null;
    }

    @Override
    public List<PpcRouteLineZtProcessBigAndSmallVo> getAllDetailByRouteCode(String routeCode, String firstBigProcessCode) {
        Result result = sysClient.getAllDetailByRouteCode(routeCode , firstBigProcessCode);
        List<PpcRouteLineZtProcessBigAndSmallVo> list = new ArrayList<>();
        if (result.getData() != null) {
            list = JSON.parseArray(JSON.toJSONString(result.getData()), PpcRouteLineZtProcessBigAndSmallVo.class);
        } else {
            throw new CommonException(result.getMessage(), result.getCode());
        }
        return list;
    }


    @Override
    public List<PpcWorkFinishMain> getWorkFinishByOrderIdAndLine(String orderId, Integer lineNo) {
        Result result = ppcClient.getWorkFinishByOrderIdAndLine(orderId , lineNo);
        List<PpcWorkFinishMain> list = new ArrayList<>();
        if (result.getData() != null) {
            list = JSON.parseArray(JSON.toJSONString(result.getData()), PpcWorkFinishMain.class);
        } else {
            throw new CommonException(result.getMessage(), result.getCode());
        }
        return list;
    }

    @Override
    public void checkProduceOrderIsFinish(List<String> orderIdList) {
        Result result = ppcClient.checkProduceOrderIsFinish(orderIdList);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public void checkOrderProcess(String keyWord) {
        Result result = ppcClient.checkForQms(keyWord);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public List<SysFile> getBase64SysFileByPreUrlList(List<String> preUrls) {
        Result result = ftsInterfaceService.getBase64SysFileByPreUrlList(preUrls);
        Assert.isTrue(result.isSuccess(), result.getMessage());
        Object data = result.getData();
        return objectMapper.convertValue(data, new TypeReference<List<SysFile>>() {
        });
    }

    @Override
    public void generateInspectPieceData(QmsWorkInspect inspect) {
        Result result = ppcClient.generateInspectPieceData(inspect);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public void deleteInspectPieceData(String inspectNo) {
        Result result = ppcClient.deleteInspectPieceData(inspectNo);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
        }
    }

    @Override
    public boolean isExistsCalc(String orderId) {
        Result result = ppcClient.isExistsCalc(orderId);
        if (!result.isSuccess()) {
            AssertUtil.throwException(result.getCode(), result.getMessage());
            return false;
        } else {
            return (boolean) result.getData();
        }
    }
}
