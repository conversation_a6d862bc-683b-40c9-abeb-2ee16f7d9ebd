package com.imes.qms.feignClient;

import com.imes.common.entity.Result;
import com.imes.domain.entities.ppc.po.PpcProcessBadCode;
import com.imes.common.support.Resume;
import com.imes.common.support.Message;
import com.imes.domain.entities.system.vo.SendUserMessageVO;
import com.imes.domain.entities.system.warning.SysWarningInfo;
import com.imes.domain.entities.system.wechart.CompanyWechartMsg;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@FeignClient("IMES-SYSTEM")
public interface SysClient {

    /**
     * 自动编码
     *
     * @param autocode
     * @return
     */
    @RequestMapping(value = "/api/sys/autocode/getAutocode", method = RequestMethod.GET)
    Result getAutoCode(@RequestParam("autocode") String autocode);

    /**
     * 查询单位
     */
    @GetMapping(value = "/api/sys/unit/findAllUnitByMap")
    Result findUnitList();

    /*    */

    /**
     * 查询车间,取样地点
     *//*
    @GetMapping(value = "/api/sys/dev/deviceByCondi")
    Result getDevices(@RequestParam("workshopItem") Map<String, Object> map);*/

    //根据工号查询员工所在部门的所有下级部门
    @GetMapping(value = "/api/sys/department/findChildDeptByUserCode")
    Result findChildDeptByUserCode(@RequestParam("userCode") String userCode);

    /**
     * 化验部门
     */
    @GetMapping("/api/sys/department/findByType")
    Result findByType(@RequestParam("type") String type);

    @PostMapping("/api/sys/user/message/notice/batchSendMessage")
    Result batchSendMessage(@RequestBody List<SendUserMessageVO> messages);

    /**
     * 质检班组
     */
    @GetMapping("/api/sys/department/getInspectTeam")
    Result getInspectTeam(@RequestParam("bomCode") String bomCode,
                          @RequestParam("bomVer") String bomVer,
                          @RequestParam("lineCode") String lineCode,
                          @RequestParam("processCode") String processCode);


    /**
     * 质检班组
     */
    @GetMapping("/api/sys/department/getInspectTeamByRouteCode")
    Result getInspectTeamByRouteCode(@RequestParam("routeCode") String routeCode, @RequestParam("processCode") String processCode);

    /**
     * 部门人员
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/api/sys/department/getEmpyesByDepartCode/{id}")
    Result getEmpyesByDepartId(@PathVariable("id") String id);

    /**
     * 部门人员
     *
     * @param departCode
     * @return
     */
    @GetMapping(value = "/api/sys/department/findUserByDepartCode")
    Result findUserByDepartCode(@RequestParam("departCode") String departCode);

    /**
     * 移动端消息推送
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/api/sys/wechart/batchSendWechartMsg", method = RequestMethod.POST)
    Result batchSendWechartMsg(List<CompanyWechartMsg> companyWechartMsgList);

    /**
     * 根据部门分类获取人
     *
     * @param departmentType
     * @return
     */
    @GetMapping("/api/sys/department/getUserByIncludeDepType")
    Result getUserByIncludeDepType(@RequestParam("departmentType") String departmentType);

    /**
     * 根据字典code查询列表
     *
     * @param mainCode
     * @return
     */
    @GetMapping("/api/sys/dict/getDictDetailList")
    Result getDictDetailList(@RequestParam(value = "mainCode") String mainCode);

    /**
     * 批量新增消息
     *
     * @return
     */
    @PostMapping("/api/sys/user/message/notice/batchSave")
    public Result batchSave(@RequestBody SendUserMessageVO message);

    @GetMapping(value = "/api/sys/department/queryDepartmentByType")
    public Result queryDepartmentByType(@RequestParam("type") String type);

    @GetMapping(value = "/api/sys/department/sltUserByDepartId/{id}")
    Result sltUserByDepartId(@PathVariable("id") String id);

    /**
     * 根据员工编码 组织类型查询所在的组织
     *
     * @param userCode
     * @param type
     * @return
     */
    @GetMapping("/api/sys/department/findByUserCodeAndType")
    Result findByUserCodeAndType(@RequestParam("userCode") String userCode, @RequestParam("type") String type);

//    @GetMapping("/api/sys/department/findByUserCodeAndType")
//    Result findEmployeeAndDepartTypeByUserCode(String userCode);

    /**
     * 根据部门id获取所有员工
     *
     * @param id
     * @return
     */
    @GetMapping("/api/sys/department/getEmpyesByDepartCode/{id}")
    Result getEmpyesByDepartCode(@PathVariable("id") String id);

    /**
     * 根据父部门编码和子部门分类查询组织列表
     *
     * @param parentCode 父编码
     * @param type       子部门所属分类
     * @return
     */
    @GetMapping("/api/sys/department/findChildrenByParentAndType")
    Result findChildrenByParentCodeAndType(@RequestParam("parentCode") String parentCode,
                                           @RequestParam("type") String type);

    /**
     * 定时任务之更新用时
     */
    @GetMapping(value = "/api/sys/quartz/eventNow/updateEventNowExcuteDate")
    Result updateEventNowExcuteDate(@RequestParam Map<String, Object> map);

    /**
     * 定时任务之获取日志
     */
    @GetMapping(value = "/api/sys/quartz/eventNow/getEventNow")
    Result getEventNow(@RequestParam("id") String id);

    /**
     * 定时任务之更新失败
     */
    @GetMapping(value = "/api/sys/quartz/eventNow/updateFail")
    Result updateFail(@RequestParam Map<String, Object> map);


    /**          ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑     activiti接口     ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑          */


    /**
     * 添加化验流程变量
     *
     * @param processInstanceId 项目部署ID
     * @param VariableName      流程变量名称
     * @return
     */
    @PostMapping(value = "/api/sys/activiti/addVariableMap")
    public String addVariableMap(@RequestParam("processInstanceId") String processInstanceId,
                                 @RequestParam("VariableName") String VariableName,
                                 @RequestBody Map<String, Object> map);

    /**
     * 添加化验流程变量
     *
     * @param processInstanceId 项目部署ID
     * @param VariableName      流程变量名称
     * @return
     */
    @PostMapping(value = "/api/sys/activiti/addVariableList")
    public String addVariableList(@RequestParam("processInstanceId") String processInstanceId,
                                  @RequestParam("VariableName") String VariableName,
                                  @RequestBody Object list);

    /**
     * 查询流程变量
     *
     * @param processInstanceId 项目部署ID
     * @param VariableName      流程变量名称
     * @return 查询对象
     */
    @GetMapping(value = "/api/sys/activiti/findVariable")
    public Map<String, Object> findVariable(@RequestParam("processInstanceId") String processInstanceId,
                                            @RequestParam("VariableName") String VariableName);

    /**
     * 启动流程
     *
     * @param processDefinitionKey activiti部署的key
     * @return
     */
    @GetMapping(value = "/api/sys/activiti/useKeyStartProcess")
    public Map<String, String> useKeyStartProcess(@RequestParam("processDefinitionKey") String processDefinitionKey);

    /**
     * 将组任务分配给个人任务   分配的个人任务（可以是组任务中的成员，也可以是非组任务的成员）
     *
     * @param activitiId         分配id
     * @param reviewOperatorName 分配任务者名字
     * @return
     */
    @GetMapping(value = "/api/sys/activiti/allotTask")
    public void allotTask(@RequestParam("activitiId") String activitiId,
                          @RequestParam("reviewOperatorName") String reviewOperatorName);

    /**
     * 分配map至流程变量
     *
     * @param activitiId 分配id
     * @param variables  流程变量map
     */
    @GetMapping(value = "/api/sys/activiti/allotMapGetTask")
    public void allotMapGetTask(@RequestParam("activitiId") String activitiId,
                                @RequestParam("variables") Map<String, Object> variables);

    /**
     * 查询流程实例是否结束  1结束 0否
     *
     * @param activitiProcessInstanceId 流程实例ID
     */
    @GetMapping(value = "/api/sys/activiti/findProcessInstanceResult")
    public int findProcessInstanceResult(@RequestParam("activitiProcessInstanceId") String activitiProcessInstanceId);

    /**
     * 查询当前任务的属性值（TaskDefinitionKey，ExecutionId）
     *
     * @param taskId 任务ID
     * @return
     */
    @GetMapping(value = "/api/sys/activiti/findTaskResult")
    public Map<String, Object> findTaskResult(@RequestParam("taskId") String taskId);

    /**
     * 查询下一个任务是否还存在，如果存在返回审核人或审核组名
     */
    @GetMapping(value = "/api/sys/activiti/findNextTaskResult")
    public String findNextTaskResult(@RequestParam("cutKey") String cutKey, @RequestParam("executionId") String executionId);


    /**          ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑     activiti接口     ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑          */

    /**
     * 查询(产品,部件,型号)类型的物料
     *
     * @return
     */
    @GetMapping("/api/sys/ppc/material/findMaterialByType")
    Result findMaterialByType(@RequestParam("current") Integer current,
                              @RequestParam("size") Integer size,
                              @RequestParam("materialCode") String materialCode,
                              @RequestParam("materialName") String materialName);

    /**
     * 根据id查询物料
     *
     * @param materialId materialId
     * @return
     */
    @GetMapping("/api/sys/ppc/material/findMaterialById/{materialId}")
    Result findMaterialById(@PathVariable("materialId") String materialId);

    /**
     * 下拉供应商
     *
     * @return
     */
    @GetMapping("/api/sys/supplier/findPullSupplier")
    Result findPullSupplier();

    /**
     * 根据日历编码 日期 查询是否为工作日
     *
     * @param calendarCode
     * @param date
     * @return
     */
    @RequestMapping(value = "/api/sys/calendar/workOrNot", method = RequestMethod.GET)
    Result workOrNot(@RequestParam("calendarCode") String calendarCode, @RequestParam("date") String date);

    /**
     * 获取日历下拉
     *
     * @return
     */
    @RequestMapping(value = "/api/sys/calendar/sltCalendar", method = RequestMethod.GET)
    Result sltCalendar();

    @RequestMapping(value = "/api/sys/dev/area/findAreaTree", method = RequestMethod.GET)
    Result findAreaTree(@RequestParam("areaCategory") String areaCategory);

    @RequestMapping(value = "/api/sys/empyes/findByUserCode", method = RequestMethod.GET)
    Result findByUserCode(@RequestParam("userCode") String userCode);

    @RequestMapping(value = "/api/sys/ppc/material/queryMaterialByCodeIn", method = RequestMethod.POST)
    Result queryMaterialByCodeIn(@RequestBody List<String> materialCodeList);

    @RequestMapping(value = "/api/sys/warning/info/startWarning", method = RequestMethod.POST)
    Result startWarning(SysWarningInfo sysWarningInfo);

    /**
     * 根据角色id获取用户列表
     *
     * @return
     */
    @GetMapping("/api/sys/role/userList/{id}")
    public Result findUserByRoleId(@PathVariable("id") String id);

    @RequestMapping(value = "/api/sys/wechart/sendWechartMsg", method = RequestMethod.POST)
    Result sendWechartMsg(CompanyWechartMsg companyWechartMsg);

    /**
     * 根据工艺路线和工序获取质检项
     *
     * @param routeCode
     * @param processCode
     * @return
     */
    @RequestMapping("/api/sys/QmsRouteInspection/getRouteInspectMainAndDetail")
    Result getRouteInspectMainAndDetail(@RequestParam("routeCode") String routeCode,
                                        @RequestParam("processCode") String processCode);
    @RequestMapping("/api/sys/QmsStdInspection/queryByMapBySenior")
    Result getProcessStdInspectionAndDetail( @RequestParam("processCode") String processCode);
    @RequestMapping("/api/sys/ppcProcessQmsDepartment/queryByProcessCode")
    Result queryInspectTeamByProcessCode( @RequestParam("processCode") String processCode);

    /**
     * 通过工序编码获取不良代码
     *
     * @param ppcProcessBadCode
     * @return Result
     */
    @RequestMapping(value = "/api/sys/ppc/ppcProcessBadCode/getBadCodeByProcessCode", method = RequestMethod.GET)
    Result queryByCond(@RequestParam("ppcProcessBadCode") PpcProcessBadCode ppcProcessBadCode);

    /**
     * 通过工序编码获取检验项信息
     *
     * @param map
     * @return Result
     */
    @RequestMapping(value = "/api/sys/QmsStdInspection/queryByMap", method = RequestMethod.GET)
    Result queryByMap(@RequestParam Map<String, Object> map);

    @GetMapping(value = "/api/sys/department/getByDepartmentCode")
    Result getByDepartmentCode(@RequestParam("departmentCode") String departmentCode);

    @RequestMapping(value = "/api/sys/ppc/process/queryInfo", method = RequestMethod.GET)
    Result selectDevListByProcessCode(@RequestParam("processCode") String processCode);

    @RequestMapping(value = "/api/sys/department/queryDepartmentByType", method = RequestMethod.GET)
    Result inspectedDapart(@RequestParam("type") String type);

    @RequestMapping(value = "/api/sys/ppc/ppcRouteProcessQmsDepartment/getPpcRouteProcessQmsDepartmentByDepartmentCode", method = RequestMethod.POST)
    Result inspectionDapart(@RequestBody List<String> departCodes);

    @GetMapping(value = "/api/sys/company/isEnableModule")
    Result getAuthorityByModuleCode(@RequestParam("moduleCode") String moduleCode);

    @GetMapping(value = "/api/sys/materialSupplier/findByMaterialCode")
    Result findByMaterialCode(@RequestParam("materialCode") String materialCode);

    @RequestMapping(value = "/api/sys/role/isRbac", method = RequestMethod.GET)
    Result isTenantAdmin(@RequestParam("userCode") String userCode);

    @GetMapping(value = "/api/sys/department/findCoDepartmentByUserCode")
    Result findCoDepartmentByUserCode(@RequestParam("userCode") String userCode);

    @GetMapping(value = "/api/sys/department/findByDepartmantCode")
    Result findByDepartCode(@RequestParam("departmentCode") String departmentCode);

    /**
     * //查工序AQL信息
     *
     * @param processCode
     * @return
     */
    @RequestMapping(value = "/api/sys/ppc/process/queryInfo", method = RequestMethod.GET)
    Result queryInfo(@RequestParam("processCode") String processCode);

    @RequestMapping(value = "/api/sys/ppc/material/findMaterialByCode", method = RequestMethod.GET)
    Result findMaterialByCode(@RequestParam("materialCode") String materialCode);

    @RequestMapping(value = "/api/sys/department/getLeaderTeamCodeByUserCode", method = RequestMethod.GET)
    Result getLeaderTeamCodeByUserCode(@RequestParam("userCode") String userCode, @RequestParam("type") String type);

    @GetMapping(value = "/api/sys/department/dataPermission")
    Result dataPermission(@RequestParam("userCode") String userCode);

    /*
     * 系统通用导出模板内容获取
     */
    @GetMapping(value = "/api/sys/exportTemplate/fetch")
    Result exportTemplateFetch(@RequestParam("bizCode") String bizCode);

    /**
     * 获得工艺路线下的检验组
     *
     * @param
     * @return
     */
    @RequestMapping(value = "/api/sys/ppc/ppcRouteProcessQmsDepartment/getPpcRouteProcessQmsDepartment", method = RequestMethod.GET)
    Result getPpcRouteProcessQmsDepartment(@RequestParam("routeCode") String routeCode, @RequestParam("processCode") String processCode);

    @RequestMapping(value = "/api/sys/ppc/process/queryQmsSampleManager", method = RequestMethod.GET)
    Result queryQmsSampleManager(@RequestParam("processCode") String processCode);

    @GetMapping("/api/sys/unit/findSysUnitByName")
    Result findSysUnitByName(@RequestParam("name") String name);

    @GetMapping(value = "/api/sys/ioManage/getIoByCode")
    Result getIoByCode(@RequestParam("ioCode") String ioCode);

    @GetMapping("/api/sys/department/findMainLeaderByDeptType")
    Result findMainLeaderByDeptType(@RequestParam("departType") String departType);

    @RequestMapping(value = "/api/sys/ppc/process/queryProcessList", method = RequestMethod.GET)
    Result queryProcessList(
            @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
            @RequestParam Map<String, Object> map);

    @PostMapping(value = "/api/sys/queryModel/resume/save")
    Result saveResume(@RequestBody List<Resume> list);

    @PostMapping(value = "/api/sys/user/message/config/trigger")
    Result sendMsg(@RequestBody List<Message> msgList);

    @GetMapping(value = "/api/sys/department/departLeader")
    Result departLeader(@RequestParam("workshopCode") String workshopCode);

    @GetMapping("/api/sys/ppcRouteLineZtProcessMain/getAllDetailByRouteCode")
    Result getAllDetailByRouteCode(@RequestParam(value ="routeCode") String routeCode,
                                   @RequestParam(value ="firstBigProcessCode",required = false) String firstBigProcessCode);

}
