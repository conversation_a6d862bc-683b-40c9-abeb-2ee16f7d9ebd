package com.imes.qms;

import com.imes.common.config.ZipkinConfig;

import com.imes.common.utils.IdWorker;
import com.imes.common.utils.JwtUtils;
import com.imes.common.utils.MathUtils;
import com.imes.common.utils.QRCodeUtils;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.netflix.eureka.EnableEurekaClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter;
import org.springframework.scheduling.annotation.EnableAsync;

@SpringBootApplication(scanBasePackages = "com.imes")
@EnableEurekaClient //本服务启动后会自动注册进eureka服务中
@EntityScan(basePackages = "com.imes.domain.entities")
@MapperScan("com.imes.qms.dao")
@EnableDiscoveryClient
@EnableFeignClients
@EnableAspectJAutoProxy(exposeProxy = true)//开启aop代理，解决嵌套事务且事务隔离
@EnableAsync
public class QmsApplication {

    public static void main(String[] args) {

        SpringApplication.run(QmsApplication.class, args);
    }

    @Bean
    public IdWorker idWorker() {
        return new IdWorker();
    }

    @Bean
    public JwtUtils jwtUtils() {
        return new JwtUtils();
    }

    @Bean
    public MathUtils mathUtils() {
        return new MathUtils();
    }

    @Bean
    public QRCodeUtils qrCodeUtils() {
        return new QRCodeUtils();
    }

    @Bean
    public OpenEntityManagerInViewFilter openEntityManagerInViewFilter() {
        return new OpenEntityManagerInViewFilter();
    }

	@Bean
	public ZipkinConfig zipkinConfig() {
		return new ZipkinConfig();
	}
}
