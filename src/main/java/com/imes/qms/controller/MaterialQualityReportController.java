package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.MaterialQualityReport;
import com.imes.domain.entities.query.template.qms.GetInspectionListVo;
import com.imes.qms.service.MaterialQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/MaterialQualityReport")
@Api(tags = "来料检验质量报表相关接口")
public class MaterialQualityReportController {

    @Autowired
    MaterialQualityReportService materialQualityReportService;

    /**
     * 获取来料检验列表
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验列表",httpMethod = "GET")
    @GetMapping("/getInspectionList1")
    public Result getInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String materialCode,String materialName,String supplierName) {
        try {
            Map reMap=new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("supplierName",supplierName);
            // 通过
            map.put("process", ConstantUtil.process);
            List<MaterialQualityReport> materialQualityReports = materialQualityReportService.getInspectionList(map);
            reMap.put("list",materialQualityReports);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "根据采购单号获取物料信息",httpMethod = "GET")
    @GetMapping("/getInspectionList")
    public Result getInspectionListNew(GetInspectionListVo vo) throws Exception {
        vo.setProcess(ConstantUtil.adopt);
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<MaterialQualityReport> materialQualityReports = materialQualityReportService.getInspectionListNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), materialQualityReports));
    }

    /**
     * 获取来料检验详情列表
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验详情列表",httpMethod = "GET")
    @GetMapping("/getInspectionDetailList")
    public Result getInspectionDetailList(
                                    @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String materialCode,String materialMarker,
                                    String specification,String supplierCode,
                                    String inspectionStartTime,String inspectionEndTime,
                                    String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialMarker",materialMarker);
            map.put("specification",specification);
            map.put("supplierCode",supplierCode);
            map.put("inspector",inspector);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            // 通过
            map.put("process", ConstantUtil.process);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",materialQualityReportService.getInspectionDetailList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取来料检验详情明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验详情明细列表",httpMethod = "GET")
    @GetMapping("/detailInfos")
    public Result getDetailInfo(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",materialQualityReportService.getDetailInfo(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
