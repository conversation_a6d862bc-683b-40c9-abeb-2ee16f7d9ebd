package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.InBillInspectionDetail;
import com.imes.domain.entities.qms.po.InBillTask;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.vo.InBillInspectionInfoVo;
import com.imes.domain.entities.qms.vo.*;
//import com.imes.domain.entities.qms.vo.InBillInspectionListVo;
import com.imes.domain.entities.qms.vo.InspectionDetailForInBillVo;
import com.imes.domain.entities.query.template.qms.GetFinishVo;
//import com.imes.domain.entities.query.template.qms.QmsGetInBillListVo;
import com.imes.qms.service.InBillInspectionService;
import com.imes.qms.service.InBillTaskService;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inBillInspection")
@Api(tags = "入库检验相关接口")
public class InBillInspectionController {

    @Autowired
    InBillInspectionService inBillInspectionService;

    @Autowired
    MaterialInspectionService materialInspectionService;

    @Autowired
    InBillTaskService inBillTaskService;

    /**
     * 获取入库检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验列表信息",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String reportOrder,String materialCode,String productName,
                            String inspectionCode,String inspectionStartTime,
                            String inspectionEndTime,String inspectionStatus,String status,
                            String materialName,String pcNo) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("reportOrder",reportOrder);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("pcNo",pcNo);
            map.put("productName",productName);
            map.put("inspectionCode",inspectionCode);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("inspectionStatus",inspectionStatus);
            map.put("ending",ConstantUtil.ending);
            map.put("unSupport",ConstantUtil.un_support);
            map.put("status",status);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",inBillInspectionService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
//    /**
//     * 获取入库检验列表信息(高级搜索版本)
//     *
//     * @return
//     */
//    @ApiOperation(value = "获取入库检验列表信息",httpMethod = "GET")
//    @GetMapping("/queryListNew")
//    public Result queryList(QmsGetInBillListVo vo) {
//        vo.setEnding(ConstantUtil.ending);
//        vo.setUnSupport(ConstantUtil.un_support);
//        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
//        List<InBillInspectionListVo> inBillInspectionListVos = inBillInspectionService.queryListNew(vo);
//        return Result.SUCCESS(new PageResult(page.getTotal(), inBillInspectionListVos));
//    }
    /**
     * 根据物料编码获取检验项
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码获取检验项",httpMethod = "GET")
    @GetMapping("/getInspectionByMaterialCode")
    public Result getInspectionByMaterialCode(String materialCode, String inspectionType, String customerCode, BigDecimal batchNum, String[] ids,String orderId) throws Exception {
        Map reMap=new HashMap();
        int num = batchNum.intValue();
        reMap.put("list",inBillInspectionService.getInspectionByMaterialCode(materialCode,customerCode,inspectionType,num,orderId));
        // 修改认领状态
        for (String id : ids) {
            inBillTaskService.updateClaim(id);
        }
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 反提交按钮
     *
     * @return Result
     */
    @ApiOperation(value = "反提交按钮",httpMethod = "GET")
    @GetMapping("/unsubmission")
    public Result unsubmission(String orderId) throws Exception {
        inBillInspectionService.unsubmission(orderId);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 获取入库检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",inBillInspectionService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 获取入库检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码查询入库检验检验项信息",httpMethod = "GET")
    @GetMapping("/queryInBillInspectionItems")
    public List<InspectionItems> queryInBillInspectionItems(@RequestParam("materialCode") String materialCode) {
        List<InspectionItems> reMap = inBillInspectionService.queryInBillInspectionItems(materialCode);
        return reMap;
    }
    /**
     * 获取入库检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码查询入库检验检验子项信息",httpMethod = "GET")
    @GetMapping("/queryInBillInspectionItemsDetailList")
    public List<InspectionDetailForInBillVo> queryInBillInspectionItemsDetailList(@RequestParam("itemCode") String itemCode) {
        List<InspectionDetailForInBillVo> reMap = inBillInspectionService.queryInBillInspectionItemsDetailList(itemCode);
        return reMap;
    }
    /**
     * 根据检验项编码查询检验子项信息
     *
     * @return
     */
    @ApiOperation(value = "是否有检验项标识",httpMethod = "GET")
    @GetMapping("/isInspection")
    public Result isInspection(@RequestParam("materialCode") String materialCode) throws Exception {
        Map materialByOrderNoNew = inBillInspectionService.isInspection(materialCode);
        return new Result(ResultCode.SUCCESS, materialByOrderNoNew);
    }

    /**
     * 根据ID查询主信息
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码查询质检配置信息",httpMethod = "GET")
    @GetMapping ("/getInBillInspectionSequence")
    public Result getInBillInspectionSequence(@RequestParam("materialCode") String materialCode) throws Exception {
        InspectionDataProtect inBillInspectionSequence = inBillInspectionService.getInBillInspectionSequence(materialCode);
        return new Result(ResultCode.SUCCESS, inBillInspectionSequence);
    }
    /**
     * mom-open-api接口
     *
     * @return
     */
    @ApiOperation(value = "mom-open-api接口",httpMethod = "POST")
    @PostMapping("/queryInBillInfoByVo")
    public Result queryInBillInfoByVo(@RequestBody InBillInforVo vo) throws Exception {
        List<InBillResultVo> list = inBillInspectionService.queryInBillInfoByVo(vo);
        return new Result(ResultCode.SUCCESS,list);
    }

    /**
     * 根据activitiId获取入库检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取入库检验详情",httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",inBillInspectionService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",inBillInspectionService.queryDetailListByOrderId(orderId));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     //     * 编辑修改检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "编辑修改检验详细数据",httpMethod = "PUT")
    @PutMapping ("/updateInspectionDetailDatas")
    public Result updateInspectionDetailDatas(InBillInspectionDetail data) {
        Map reMap=new HashMap();
        inBillInspectionService.updateInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 删除任务，结果数据，流程任务数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "删除任务，结果数据，流程任务数据",httpMethod = "POST")
    @PostMapping("/deleteInspection")
    public Result deleteInspection(@RequestBody List<String> produceStoreNo) {
        boolean b = inBillInspectionService.deleteInspection(produceStoreNo);
        if (b){
            return new Result(ResultCode.SUCCESS,b);
        }else {
            return new Result(ResultCode.FAIL,b);
        }
    }

    /**
     //     * 提交检验条目
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "提交检验条目",httpMethod = "PUT")
    @PutMapping ("/updateStatus")
    public Result updateStatus(String id) throws Exception {
        Map reMap=new HashMap();
        inBillInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 取消合并检验
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "取消合并检验",httpMethod = "DELETE")
    @DeleteMapping ("/cancelMergeInspection")
    public Result cancelMergeInspection(@RequestParam("ids") List<String> ids) throws Exception {
        Map reMap=new HashMap();
        inBillInspectionService.cancelMergeInspection(ids);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 更改图片状态
     *
     * @return
     */
    @ApiOperation(value = "更改图片状态",httpMethod = "PUT")
    @PutMapping ("/updatePicStatus")
    public Result updatePicStatus(String id,String picStatus) throws Exception {
        Map reMap=new HashMap();
        inBillInspectionService.updatePicStatus(id,picStatus);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 暂存检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "暂存检验详细数据",httpMethod = "POST")
    @PostMapping ("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody InBillInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        inBillInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 提交检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "提交检验详细数据",httpMethod = "POST")
    @PostMapping ("/supportDatas")
    public Result supportDatas(@RequestBody InBillInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        inBillInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 获取质检物料信息
     *
     * @return
     */
    @ApiOperation(value = "获取质检物料信息",httpMethod = "GET")
    @GetMapping("/getFinish")
    public Result getFinish(GetFinishVo vo) throws Exception {
        vo.setInspectionType(ConstantUtil.inBill);
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setUnInspection(ConstantUtil.un_inspection);
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<GetFinishVo> result = inBillInspectionService.getWorkFinish(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), result));
    }
}
