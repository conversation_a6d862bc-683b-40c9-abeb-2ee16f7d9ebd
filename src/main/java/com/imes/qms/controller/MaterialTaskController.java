package com.imes.qms.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.MaterialTask;
import com.imes.domain.entities.qms.vo.InBillTaskVO;
import com.imes.domain.entities.qms.vo.MaterialTaskVo;
import com.imes.qms.service.InBillTaskService;
import com.imes.qms.service.MaterialTaskService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/materialTask")
@Api(tags = "来料检验任务相关接口")
public class MaterialTaskController {

    @Autowired
    MaterialTaskService materialTaskService;


    /**
     * 到货单生成来料/出库检验任务
     *
     * @return Result
     */
    @ApiOperation(value = "到货单生成来料/出库检验任务",httpMethod = "POST")
    @PostMapping ("/materialTasks")
    public Result createMaterialTask(@RequestBody MaterialTaskVo vo) throws Exception {
        materialTaskService.createMaterialTask(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 多个到货单生成来料/出库检验任务
     *
     * @return Result
     */
    @ApiOperation(value = "多个到货单生成来料/出库检验任务",httpMethod = "POST")
    @PostMapping ("/materialTaskAll")
    public Result createMaterialTaskAll(@RequestBody List<Map> list) throws Exception {
        if (null != list && list.size() > 0) {
            materialTaskService.createOutInterfaceTask(list);
        }
        return new Result(ResultCode.SUCCESS);
    }

//    /**
//     * 认领按钮
//     *
//     * @return Result
//     */
//    @ApiOperation(value = "认领按钮",httpMethod = "POST")
//    @PostMapping ("/claim")
//    public Result claim(@RequestBody MaterialTask task) throws Exception {
//        materialTaskService.claim(task);
//        return new Result(ResultCode.SUCCESS);
//    }

//    /**
//     * 报工结束生成批量质检任务
//     *
//     * @return Result
//     */
//    @ApiOperation(value = "报工结束生成批量质检任务",httpMethod = "POST")
//    @PostMapping ("/batchProcessTasks")
//    public Result createBatchProcessTask(@RequestBody List<ProcessTaskVo> list) throws Exception {
//        processTaskService.createBatchProcessTask(list);
//        return new Result(ResultCode.SUCCESS);
//    }
}
