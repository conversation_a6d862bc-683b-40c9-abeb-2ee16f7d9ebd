package com.imes.qms.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.domain.entities.qms.po.QmsTemplateMaterial;
import com.imes.domain.entities.query.template.qms.QmsTemplateMaterialQueryVo;
import com.imes.qms.service.impl.QmsTemplateMaterialServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * QMS模板控制器
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Api("QMS模板控制器")
@RestController
@RequestMapping("/api/qms/template/material")
public class QmsTemplateMaterialController {

    @Autowired
    private QmsTemplateMaterialServiceImpl service;


    @ApiOperation("新增方案")
    @PostMapping("/apply")
    public Result apply(@RequestBody QmsTemplateMaterial material) throws Exception {
        service.apply(material);
        return Result.SUCCESS();
    }

    @ApiOperation("获取列表")
    @GetMapping("/queryList")
    public Result queryList(QmsTemplateMaterialQueryVo vo) {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsTemplateMaterialQueryVo> list = service.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @ApiOperation("删除方案")
    @DeleteMapping("/del")
    public Result deL(String ids) {
        service.del(ids);
        return Result.SUCCESS();
    }
}
