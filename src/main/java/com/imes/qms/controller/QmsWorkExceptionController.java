package com.imes.qms.controller;

import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.ppc.po.PpcRouteLineZtProcessDetail;
import com.imes.domain.entities.ppc.po.PpcWorkFinishMain;
import com.imes.domain.entities.qms.po.QmsWorkException;
import com.imes.domain.entities.qms.po.QmsWorkExceptionBad;
import com.imes.domain.entities.qms.vo.QmsExceptionInfoVo;
import com.imes.domain.entities.qms.vo.QmsWorkExceptionAddDataVo;
import com.imes.domain.entities.qms.vo.WeeklyProcessQualityAndProduceAreaReportVo;
import com.imes.domain.entities.query.template.qms.QmsWorkExceptionReportSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkExceptionSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkProductionCentreDesignSearchVo;
import com.imes.domain.entities.query.template.qms.QmsWorkProductionCentreSearchVo;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.qms.service.QmsWorkExceptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 异常提报单-主表-志特(QmsWorkException)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-13 09:45:09
 */
@RestController
@RequestMapping("/api/qms/qmsWorkException")
@Api(tags = "异常提报单-主表-志特")
public class QmsWorkExceptionController {
    /**
     * 服务对象
     */
    @Autowired
    private QmsWorkExceptionService qmsWorkExceptionService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (qmsWorkExceptionService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param qmsWorkException 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody QmsWorkException qmsWorkException) throws Exception {
        String result = qmsWorkExceptionService.insert(qmsWorkException);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param qmsWorkException 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody QmsWorkException qmsWorkException) throws Exception {
        if (qmsWorkExceptionService.update(qmsWorkException) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (qmsWorkExceptionService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(QmsWorkExceptionSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkExceptionSearchVo> list = qmsWorkExceptionService.queryList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/queryReportList")
    @ApiOperation(value = "品质异常管理-高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryReportList(QmsWorkExceptionReportSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkExceptionReportSearchVo> list = qmsWorkExceptionService.queryReportList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/queryProductionCentreList")
    @ApiOperation(value = "品质异常管理-高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryProductionCentreList(QmsWorkProductionCentreSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkProductionCentreSearchVo> list = qmsWorkExceptionService.queryProductionCentreList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/queryProductionCentreListForDesign")
    @ApiOperation(value = "品质异常管理-高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryProductionCentreListForDesign(QmsWorkProductionCentreDesignSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkProductionCentreDesignSearchVo> list = qmsWorkExceptionService.queryProductionCentreListForDesign(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }



    @GetMapping("/searchWorkOrders")
    @ApiOperation(value = "关键字查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchWorkOrders(@RequestParam(defaultValue = "") String keyWord,@RequestParam(required = false) String projectName,@RequestParam(required = false) String orderNo) {
        return Result.SUCCESS(qmsWorkExceptionService.searchWorkOrdersByKeyWord(keyWord,projectName,orderNo));
    }

    @PostMapping("/checkHaveException")
    @ApiOperation(value = "检查是否有提报单")
    @Transactional(rollbackFor = Exception.class)
    public Result checkHaveException(@RequestBody List<QmsWorkException> list) {
        return Result.SUCCESS(qmsWorkExceptionService.checkHaveException(list));
    }

    @GetMapping("/searchBySnCode")
    @ApiOperation(value = "通过SN码查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchBySnCode(@RequestParam(defaultValue = "") String code) {
        return Result.SUCCESS(qmsWorkExceptionService.searchBySnCode(code));
    }


    @PostMapping("/buildExceptionReport")
    @ApiOperation(value = "生成填报单")
    @Transactional(rollbackFor = Exception.class)
    public Result buildExceptionReport(@RequestBody List<QmsWorkException> qmsWorkExceptionList ) {
        for (QmsWorkException qmsWorkException : qmsWorkExceptionList) {
            List<QmsWorkExceptionBad> badList = qmsWorkException.getQmsWorkExceptionBadList();
            Optional<QmsWorkExceptionBad> first = badList.stream().filter(x -> StringUtils.isNullOrBlank(x.getBadQty())).findFirst();
            AssertUtil.isFalse(first.isPresent(),"行号：【"+qmsWorkException.getLineNo()+"】，物料：【"+qmsWorkException.getMaterialCode()+"】" +"不良数量不能为空");
            first = badList.stream().filter(x -> StringUtils.isNullOrBlank(x.getBadCode())).findFirst();
            AssertUtil.isFalse(first.isPresent(),"行号：【"+qmsWorkException.getLineNo()+"】，物料：【"+qmsWorkException.getMaterialCode()+"】" +"不良类型不能为空");
//            first = badList.stream().filter(x -> StringUtils.isNullOrBlank(x.getBadReason())).findFirst();
//            AssertUtil.isFalse(first.isPresent(),"行号：【"+qmsWorkException.getLineNo()+"】，物料：【"+qmsWorkException.getMaterialCode()+"】" +"造成不良原因不能为空");
        }
        return Result.SUCCESS( qmsWorkExceptionService.buildExceptionReport(qmsWorkExceptionList));

    }
    @PostMapping("/buildExceptionReportIcs")
    @ApiOperation(value = "生成填报单-ics")
    @Transactional(rollbackFor = Exception.class)
    public Result buildExceptionReportIcs(@RequestBody List<QmsWorkException> qmsWorkExceptionList ) {
        for (QmsWorkException qmsWorkException : qmsWorkExceptionList) {
            List<QmsWorkExceptionBad> badList = qmsWorkException.getQmsWorkExceptionBadList();
            Optional<QmsWorkExceptionBad> first = badList.stream().filter(x -> StringUtils.isNullOrBlank(x.getBadQty())).findFirst();
            AssertUtil.isFalse(first.isPresent(),"行号：【"+qmsWorkException.getLineNo()+"】，物料：【"+qmsWorkException.getMaterialCode()+"】" +"不良数量不能为空");
        }
        qmsWorkExceptionService.buildExceptionReport(qmsWorkExceptionList);
        return Result.SUCCESS();

    }

    @GetMapping("/queryByReportUser")
    @ApiOperation(value = "品质异常-恢复-查询")
    @Transactional(rollbackFor = Exception.class)
    public Result queryByReportUser(QmsWorkException qmsWorkException) {
        if(ObjectUtil.isNotNull(qmsWorkException.getIdList())&& !qmsWorkException.getIdList().isEmpty()){
            return Result.SUCCESS(qmsWorkExceptionService.queryByReportUser(qmsWorkException));
        }else {
            Map<String, Object> map = new HashMap<>();
            //如有需要计算合计对map进行赋值
            Page page = PageHelper.startPage(qmsWorkException.getPageNum(), qmsWorkException.getPageSize());
            List<Map<String , Object>>  list = qmsWorkExceptionService.queryByReportUser(qmsWorkException);
            return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
        }
    }



    @PostMapping("/cancelOrConfirmBulid")
    @ApiOperation(value = "品质异常-恢复-取消提报/处置确认")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelBulid(@RequestBody Map<String , Object> map) {
        qmsWorkExceptionService.cancelBulid(map);
        return Result.SUCCESS();
    }
    @GetMapping("/queryHandleInFor")
    @ApiOperation(value = "品质异常-恢复-处置信息")
    @Transactional(rollbackFor = Exception.class)
    public Result queryHandleInFor(@RequestParam String id) {
        return Result.SUCCESS(qmsWorkExceptionService.queryHandleInFor(id));
    }

    @PostMapping("/handleExcepetion")
    @ApiOperation(value = "品质异常-恢复-处置确认")
    @Transactional(rollbackFor = Exception.class)
    public Result handleExcepetion(@RequestBody QmsWorkException exception) {
        qmsWorkExceptionService.handleExcepetion(exception);
        return Result.SUCCESS();
    }

    @GetMapping("/searchAddWorkOrders")
    @ApiOperation(value = "品质异常-补充-关键字查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchAddWorkOrders(QmsWorkException qmsWorkException) {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(qmsWorkException.getPageNum(), qmsWorkException.getPageSize());
        List<QmsWorkExceptionAddDataVo> list = qmsWorkExceptionService.searchAddWorkOrders(qmsWorkException);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @PostMapping("/getIngInfor")
    @ApiOperation(value = "品质异常-补充-待执行信息")
    @Transactional(rollbackFor = Exception.class)
    public Result getIngInfor(@RequestBody QmsExceptionInfoVo vo) {
        return Result.SUCCESS(qmsWorkExceptionService.getIngInfor(vo));
    }

    @GetMapping("/cancelException")
    @ApiOperation(value = "品质异常-补充-待执行信息")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelException(QmsWorkException vo) throws Exception {
        qmsWorkExceptionService.cancelException(vo);
        return Result.SUCCESS();
    }

    @GetMapping("/getResponsibleInfor")
    @ApiOperation(value = "品质异常-补充-定责信息")
    @Transactional(rollbackFor = Exception.class)
    public Result getResponsibleInfor(@RequestParam String id) {
        return Result.SUCCESS(qmsWorkExceptionService.queryHandleInFor(id));
    }

    @PostMapping("/saveResponsibleInfor")
    @ApiOperation(value = "品质异常-补充-保存")
    @Transactional(rollbackFor = Exception.class)
    public Result saveResponsibleInfor(@RequestBody QmsWorkException qmsWorkException) {
        qmsWorkExceptionService.saveResponsibleInfor(qmsWorkException);
        return Result.SUCCESS();
    }

    @GetMapping("/submitResponsibleInfor")
    @ApiOperation(value = "品质异常-补充-保存并关闭补录")
    @Transactional(rollbackFor = Exception.class)
    public Result submitResponsibleInfor(@RequestParam String id) {
        qmsWorkExceptionService.submitResponsibleInfor(id);
        return Result.SUCCESS();
    }

    @GetMapping("/queryUnHandleException")
    @ApiOperation(value = "查询未处置的提报单")
    public Result<List<QmsWorkException>> queryUnHandleException(@RequestParam String orderId) {
        List<QmsWorkException> list = qmsWorkExceptionService.queryUnHandleException(orderId);
        return Result.SUCCESS(list);
    }

    @GetMapping("/getFixProcess")
    @ApiOperation(value = "获取返修工序")
    public Result<List<PpcRouteLineZtProcessDetail>> getFixProcess(@RequestParam String orderId) {
        List<PpcRouteLineZtProcessDetail> list = qmsWorkExceptionService.getFixProcess(orderId);
        return Result.SUCCESS(list);
    }

    @GetMapping("/getWorkFinishByOrderIdAndLine")
    @ApiOperation(value = "获取生产工序")
    public Result<List<PpcWorkFinishMain>> getWorkFinishByOrderIdAndLine(@RequestParam String orderId , @RequestParam Integer lineNo) {
        List<PpcWorkFinishMain> list = qmsWorkExceptionService.getWorkFinishByOrderIdAndLine(orderId , lineNo);
        return Result.SUCCESS(list);
    }

    @PostMapping("/weeklyProcessQualityReport")
    @ApiOperation(value = "产线各工序每周质量问题报表查询")
    public Result queryWeeklyProcessQualityData(@RequestBody WeeklyProcessQualityAndProduceAreaReportVo query) {
        return Result.SUCCESS(qmsWorkExceptionService.queryWeeklyProcessQualityData(query));
    }


    @GetMapping("/weeklyProcessQualityReport/export")
    @ApiOperation(value = "产线各工序每周质量问题报表查询")
    public SysFile export(HttpServletRequest request, HttpServletResponse response, @RequestParam(defaultValue = "") String dateStr , boolean isPreview) throws Exception {
        return qmsWorkExceptionService.export(request , response , dateStr , isPreview);
    }

    /**
     * 自定义导出治生产中心问题统计
     * */
    @SneakyThrows
    @GetMapping(value = "/export/qmsWorkProductionCentre")
    public void exporQmsWorkProductionCentre (HttpServletRequest request,
                                   HttpServletResponse response,
                                   QmsWorkProductionCentreSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkProductionCentreSearchVo> list = qmsWorkExceptionService.queryProductionCentreList(vo);
        if (StringUtils.isNotNullOrBlank(vo.getExportIndex())) {
            List<QmsWorkProductionCentreSearchVo> finalList = list;
            list = Arrays.stream(vo.getExportIndex().split(","))
                    .map(index -> finalList.get(Integer.parseInt(index)))
                    .collect(Collectors.toList());
        }
        qmsWorkExceptionService.exporQmsWorkProductionCentre(request, response, list);
    }



}

