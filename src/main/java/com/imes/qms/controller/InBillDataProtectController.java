package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItemDetail;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.qms.service.InBillDataProtectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inBillDataProtect")
@Api(tags = "检验基础数据相关接口")
public class InBillDataProtectController {

    @Autowired
    InBillDataProtectService inBillDataProtectService;

    /**
     * 获取入库检验数据维护列表
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验数据维护列表",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            @RequestParam(required = false, value = "materialCode") String materialCode,
                            @RequestParam(required = false, value = "materialName") String materialName) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            reMap.put("list",inBillDataProtectService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存物料
     *
     * @return
     */
    @ApiOperation(value = "保存物料",httpMethod = "POST")
    @PostMapping ("/saveMaterialDatas")
    public Result saveMaterialDatas(@RequestBody List<InspectionDataProtect> list) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.saveMaterialDatas(list);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存检验比率
     *
     * @return
     */
    @ApiOperation(value = "保存检验比率",httpMethod = "POST")
    @PostMapping ("/saveInspectionRatio")
    public Result saveInspectionRatio(@RequestBody InspectionDataProtect ratios) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.saveInspectionRatio(ratios);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除物料维护数据
     *
     * @return
     */
    @ApiOperation(value = "删除物料维护数据",httpMethod = "POST")
    @PostMapping ("/deleteMaterialProtectData")
    public Result deleteMaterialProtectData(@RequestParam("ids") List<String> ids) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.deleteMaterialProtectData(ids);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.deleteItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验项",httpMethod = "GET")
    @GetMapping("/queryItemList")
    public Result queryItemList(@RequestParam(value = "id") String id,@RequestParam(required = false, value = "itemName") String itemName) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("parentId",id);
            map.put("itemName",itemName);
            reMap.put("list",inBillDataProtectService.queryItemList(map));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取检验项编码
     *
     * @return
     */
    @ApiOperation(value = "获取检验项编码",httpMethod = "GET")
    @GetMapping("/getInspectionCode")
    public Result getInspectionCode() {
        try {
            Map reMap=new HashMap();
            reMap.put("inspectionCode",inBillDataProtectService.getInspectionCode());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存检验项
     *
     * @return
     */
    @ApiOperation(value = "保存检验项",httpMethod = "POST")
    @PostMapping ("/saveInspectionItem")
    public Result saveInspectionItem(@RequestBody InspectionItems item) {
        try {
            Map reMap=new HashMap();

            reMap.put("itemId",inBillDataProtectService.saveInspectionItem(item));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改检验项
     *
     * @return
     */
    @ApiOperation(value = "修改检验项",httpMethod = "POST")
    @PostMapping ("/updateItems")
    public Result updateItems (@RequestBody InspectionItems item) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.updateItems(item);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.updateItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除检验项
     *
     * @return
     */
    @ApiOperation(value = "删除检验项",httpMethod = "POST")
    @PostMapping ("/deleteItem")
    public Result deleteItem(@RequestParam("id") String id) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.deleteItem(id);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.deleteItem出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验子项",httpMethod = "GET")
    @GetMapping("/queryItemDetailList")
    public Result queryItemDetailList(@RequestParam(value = "id") String id) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("parentId",id);
            reMap.put("list",inBillDataProtectService.queryItemDetailList(map));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存检验子项
     *
     * @return
     */
    @ApiOperation(value = "保存检验子项",httpMethod = "POST")
    @PostMapping ("/saveInspectionDetailItems")
    public Result saveInspectionDetailItems(@RequestBody List<InspectionItemDetail> items) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.saveInspectionDetailItems(items);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改检验子项
     *
     * @return
     */
    @ApiOperation(value = "修改检验子项",httpMethod = "POST")
    @PostMapping ("/updateItemDetails")
    public Result updateItemDetails (@RequestBody List<InspectionItemDetail> items) {
        try {
            Map reMap=new HashMap();
            inBillDataProtectService.updateItemDetails(items);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.updateItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据物料编码关联检验项
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码关联检验项",httpMethod = "GET")
    @GetMapping ("/getInspectionTypeByMaterialCode")
    public Result getInspectionTypeByMaterialCode (String materialCode) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            reMap.put("list",inBillDataProtectService.getInspectionTypeByMaterialCode(map));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillDataProtectController.updateItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据物料编码关联检验项
     * @return
     */
    @ApiOperation(value = "根据物料编码关联检验项",httpMethod = "GET")
    @GetMapping ("/findByMaterialCodeAndType")
    public Result findByMaterialCodeAndType (@RequestParam("materialCode") String materialCode, @RequestParam("inspectionType") String inspectionType) {
        try {
            String[] materialCodes = materialCode.split(",");
            return new Result(ResultCode.SUCCESS,inBillDataProtectService.findByMaterialCodeAndType(materialCodes, inspectionType));
        } catch (Exception e) {
            log.error("InBillDataProtectController.updateItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

}
