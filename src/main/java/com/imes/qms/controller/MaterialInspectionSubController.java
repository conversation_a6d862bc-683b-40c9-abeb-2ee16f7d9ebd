package com.imes.qms.controller;
import com.imes.qms.service.MaterialInspectionService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/materialSub")
@Api(tags = "来料检验流程提交相关接口")
public class MaterialInspectionSubController {

    @Autowired
    MaterialInspectionService materialInspectionService;

//    /**
//     * 化验录入指标全部完成后开启签核
//     */
//    @Transactional
//    @RequestMapping(value = "/entering", method = RequestMethod.POST)
//    public Result entering(@RequestBody enteringVO enteringVO) throws Exception {
//        try {
//            if (enteringVO == null) {//计算参数
//                return new Result(ResultCode.FAIL);
//            }
////            if (!StringUtils.isNullOrBlank(enteringVO.getIsTemporary())) {
////                if (enteringVO.getIsTemporary() == 0) {
////                    if (StringUtils.isNullOrBlank(enteringVO.getLabSubId()) || enteringVO.getLaboratoryTime() == null || StringUtils.isNullOrBlank(enteringVO.getLaboratoryTime())
////                            || StringUtils.isNullOrBlank(enteringVO.getLaboratoryPersonnelName()) || StringUtils.isNullOrBlank(enteringVO.getLaboratoryPersonnelWork())
////                            || StringUtils.isNullOrBlank(enteringVO.getCalculateResult()) || StringUtils.isNullOrBlank(enteringVO.getCalculateParam())
////                            || StringUtils.isNullOrBlank(enteringVO.getTemporaryDataStorageUserName())) {
////                        return new Result(ResultCode.FAIL);
////                    }
////                }
////            } else {
////                return new Result(ResultCode.FAIL);
////            }
//            String result = labSubService.entering(enteringVO);
//            if (!StringUtils.isNullOrBlank(result)) {
//                if (result.equals("成功")) {
//                    return new Result(ResultCode.SUCCESS);
//                } else {
//                    return new Result(ResultCode.FAIL,result);
//                }
//            } else {
//                return new Result(ResultCode.FAIL);
//            }
//        } catch (Exception e) {
//            log.error("LabSubController.entering", e);
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            return new Result(ResultCode.FAIL.code(), e.getMessage(),false);
//        }
//    }

}
