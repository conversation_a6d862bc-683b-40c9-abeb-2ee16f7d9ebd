package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.ProcessInspection;
import com.imes.domain.entities.qms.vo.MaterialInspectionInfoVo;
import com.imes.qms.service.InspectionActivitiService;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/activiti")
@Api(tags = "检验流程相关接口")
public class InspectionActivitiController {

    @Autowired
    InspectionActivitiService inspectionActivitiService;

    /**
     * QMS数据审核
     */
    @ApiOperation(value = "QMS数据审核",httpMethod = "POST")
    @PostMapping("/qmsCheck")
    @Transactional
    public Result qmsCheck(@RequestBody Map<String, Object> loginMap) throws Exception {
            String activitiId = loginMap.get("activitiId") == null ? "" : loginMap.get("activitiId").toString();
            String result = loginMap.get("result") == null ? "" : loginMap.get("result").toString();
            String wfNo = loginMap.get("wfNo") == null ? "" : loginMap.get("wfNo").toString();
            String processCode = loginMap.get("processCode") == null ? "" : loginMap.get("processCode").toString();
            String orderId = loginMap.get("orderId") == null ? "" : loginMap.get("orderId").toString();
            String taskId = loginMap.get("taskId") == null ? "" : loginMap.get("taskId").toString();
            String reviewOperatorName = loginMap.get("reviewOperatorName") == null ? "" : loginMap.get("reviewOperatorName").toString();
            String reviewOperatorWork = loginMap.get("reviewOperatorWork") == null ? "" : loginMap.get("reviewOperatorWork").toString();
            String refuseId = loginMap.get("refuseId") == null ? "" : loginMap.get("refuseId").toString();
            String remark = loginMap.get("remark") == null || loginMap.get("remark").equals("") ? "同意!" : loginMap.get("remark").toString();
            String taskType = loginMap.get("taskType") == null ? "group" : loginMap.get("taskType").toString();
            String inspectionType = loginMap.get("inspectionType") == null ? "" : loginMap.get("inspectionType").toString();
            String planCode = loginMap.get("planCode") == null ? "" : loginMap.get("planCode").toString();
            if (StringUtils.isNullOrBlank(activitiId) || StringUtils.isNullOrBlank(result) || StringUtils.isNullOrBlank(taskId) || StringUtils.isNullOrBlank(inspectionType)) {
                return new Result(ResultCode.FAIL);
            }
            // 入库检验
            if (ConstantUtil.inBill.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckInBill(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType);
            }

            // 出库检验
            if (ConstantUtil.outBill.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckOutBill(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType);
            }

            // 来料检验
            if (ConstantUtil.material.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckMaterial(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType);
            }

            // 退货检验
            if (ConstantUtil.returns.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckReturn(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType);
            }

            // 首末检验
            if (ConstantUtil.firstFinal.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckFirstFinal(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType,wfNo,processCode,orderId);
            }

            // 巡检检验
            if (ConstantUtil.patrol.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckPatrol(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType,wfNo,processCode,orderId,planCode);
            }

            // 过程检验
            if (ConstantUtil.process.equals(inspectionType)) {
                inspectionActivitiService.qmsCheckProcess(activitiId, result, taskId, reviewOperatorName, reviewOperatorWork, refuseId, remark, taskType,wfNo,processCode,orderId);
            }

            return new Result(ResultCode.SUCCESS);
    }
    /**
     * test
     */
    @ApiOperation(value = "test",httpMethod = "POST")
    @PostMapping("/test1")
    @Transactional
    public Result test() {
        try {
            ProcessInspection processInspection = new ProcessInspection();
            processInspection.setWfNo("WF220119025");
            processInspection.setProcessCode("S2");
            processInspection.setInspectionNum(1);
            processInspection.setUnqualifiedNum(0);
            inspectionActivitiService.sendDataToPPC(processInspection);
            return new Result(ResultCode.SUCCESS);
        } catch (Exception e) {
            log.error("InspectionActivitiController.qmsCheck", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return new Result(ResultCode.FAIL.code(), e.getMessage(),false);
        }
    }
}
