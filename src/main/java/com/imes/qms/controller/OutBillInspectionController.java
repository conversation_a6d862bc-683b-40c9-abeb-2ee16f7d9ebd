package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;
import com.imes.domain.entities.qms.vo.OutBillInspectionInfoVo;
import com.imes.domain.entities.query.template.qms.QueryMaterialListVo;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.OutBillInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/OutBillInspection")
@Api(tags = "出库检验相关接口")
public class OutBillInspectionController {

    @Autowired
    OutBillInspectionService outBillInspectionService;

    @Autowired
    MaterialInspectionService materialInspectionService;

    /**
     * 获取出库检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取出库检验列表信息", httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String materialCode, String outOrder, String materialName,
                            String inspectionCode, String inspectionStartTime, String inspectionEndTime,
                            String inspectionStatus, String status) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("materialCode", materialCode);
            map.put("materialName", materialName);
            map.put("outOrder", outOrder);
            map.put("inspectionCode", inspectionCode);
            map.put("startTime", inspectionStartTime);
            map.put("endTime", inspectionEndTime);
            map.put("inspectionStatus", inspectionStatus);
            map.put("ending", ConstantUtil.ending);
            map.put("unSupport", ConstantUtil.un_support);
            map.put("status", status);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", outBillInspectionService.queryList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("OutBillInspectionController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取出库检验物料明细
     *
     * @return
     */
    @SneakyThrows
    @ApiOperation(value = "获取出库检验物料明细", httpMethod = "GET")
    @GetMapping("/queryMaterialList")
    public Result queryMaterialList(QueryMaterialListVo vo) {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        vo.setTemporarySave(ConstantUtil.temporary_save);;
        vo.setUnInspect(ConstantUtil.un_inspection);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setInspectionType(ConstantUtil.outBill);
        List list = outBillInspectionService.queryMaterialList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 暂存检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "暂存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody OutBillInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        outBillInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 提交检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "提交检验详细数据", httpMethod = "POST")
    @PostMapping("/supportDatas")
    public Result supportDatas(@RequestBody OutBillInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        outBillInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取来料检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", outBillInspectionService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("OutBillInspectionController.queryDetailList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", outBillInspectionService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("OutBillInspectionController.queryByActivtiId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", outBillInspectionService.queryDetailListByOrderId(orderId));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("OutBillInspectionController.queryDetailListByOrderId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据物料编码获取检验项
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码获取检验项", httpMethod = "GET")
    @GetMapping("/getInspectionByMaterialCode")
    public Result getInspectionByMaterialCode(String materialCode, String inspectionType, BigDecimal batchNum) {
        try {
            Map reMap = new HashMap();
            int num = batchNum.intValue();
            reMap.put("list", materialInspectionService.getInspectionByMaterialCode(materialCode, inspectionType, null, num));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("OutBillInspectionController.getInspectionByMaterialCode出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }
//    /**
//     * 根据产品名称，生产计划编号和检验方式获取检验项
//     *
//     * @return
//     */
//    @ApiOperation(value = "根据产品名称，生产计划编号和检验方式获取检验项",httpMethod = "GET")
//    @GetMapping("/getItemsByParams")
//    public Result getItemsByParams(@RequestParam(value = "ppNo") String ppNo,
//                            @RequestParam(value = "inspectionType") String inspectionType,
//                                   @RequestParam(value = "productName") String productName) {
//        try {
//            Map reMap=new HashMap();
//            reMap.put("list",inBillInspectionService.getItemsByParams(ppNo,inspectionType,productName));
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("InBillInspectionController.getItemsByParams出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }
//

    /**
     * 编辑修改检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "编辑修改检验详细数据", httpMethod = "PUT")
    @PutMapping("/updateInspectionDetailData")
    public Result updateInspectionDetailData(OutBillInspectionDetail data) {
        Map reMap = new HashMap();
        outBillInspectionService.updateInspectionDetailData(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 提交检验条目
     *
     * @return
     */
    @ApiOperation(value = "提交检验条目", httpMethod = "PUT")
    @PutMapping("/updateStatus")
    public Result updateStatus(String id) throws Exception {
        Map reMap = new HashMap();
        outBillInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 取消合并检验
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "取消合并检验", httpMethod = "DELETE")
    @DeleteMapping("/cancelMergeInspection")
    public Result cancelMergeInspection(@RequestParam("ids") List<String> ids) throws Exception {
        Map reMap = new HashMap();
        outBillInspectionService.cancelMergeInspection(ids);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 更改图片状态
     *
     * @return
     */
    @ApiOperation(value = "更改图片状态", httpMethod = "PUT")
    @PutMapping("/updatePicStatus")
    public Result updatePicStatus(String id, String picStatus) throws Exception {
        Map reMap = new HashMap();
        outBillInspectionService.updatePicStatus(id, picStatus);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     //     * 保存检验详细数据
     //     *
     //     * @return
     //     */
//    @ApiOperation(value = "保存检验详细数据",httpMethod = "POST")
//    @PostMapping ("/saveInspectionDetailDatas")
//    public Result saveInspectionDetailDatas(@RequestBody InBillInspectionDetail datas) {
//        try {
//            Map reMap=new HashMap();
//            inBillInspectionService.saveInspectionDetailDatas(datas);
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("InBillInspectionController.saveStandardDatas出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

}
