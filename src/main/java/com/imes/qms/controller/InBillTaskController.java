package com.imes.qms.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.InBillTask;
import com.imes.domain.entities.qms.vo.InBillTaskVO;
import com.imes.domain.entities.qms.vo.ProcessTaskVo;
import com.imes.domain.entities.system.warning.SysWarningInfo;
import com.imes.qms.service.InBillTaskService;
import com.imes.qms.service.InMaterialDataProtectService;
import com.imes.qms.service.ProcessTaskService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inBillTask")
@Api(tags = "入库检验任务相关接口")
public class InBillTaskController {

    @Autowired
    InBillTaskService inBillTaskService;

    @Autowired
    InMaterialDataProtectService inMaterialDataProtectService;


    /**
     * 报工结束生成入库检验任务
     *
     * @return Result
     */
    @ApiOperation(value = "报工结束生成入库检验任务",httpMethod = "POST")
    @PostMapping ("/inBillTasks")
    public Result createInBillTask(@RequestBody InBillTaskVO vo) throws Exception {
        inBillTaskService.createInBillTask(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 多个报工结束生成入库检验任务
     *
     * @return Result
     */
    @ApiOperation(value = "多个报工结束生成多个入库检验任务",httpMethod = "POST")
    @PostMapping ("/inBillMultiTasks")
    public Result createInBillMultiTask(@RequestBody List<InBillTaskVO> vo) throws Exception {
        inBillTaskService.createInBillMultiTask(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 是否需要进行入库检验
     *
     * @return Result
     */
    @GetMapping("/needInspection")
    @Transactional(rollbackFor = Exception.class)
    public Result needInspection(@RequestParam("materialCode") String materialCode) throws Exception {
        Boolean result = inMaterialDataProtectService.needInspection(materialCode, ConstantUtil.inBill);
        return new Result(ResultCode.SUCCESS, result);
    }

//    /**
//     * 报工结束生成批量质检任务
//     *
//     * @return Result
//     */
//    @ApiOperation(value = "报工结束生成批量质检任务",httpMethod = "POST")
//    @PostMapping ("/batchProcessTasks")
//    public Result createBatchProcessTask(@RequestBody List<ProcessTaskVo> list) throws Exception {
//        processTaskService.createBatchProcessTask(list);
//        return new Result(ResultCode.SUCCESS);
//    }
    /**
     * 委派
     *
     * @return Result
     */
    @ApiOperation(value = "委派",httpMethod = "POST")
    @PostMapping ("/delegate")
    public Result delegate(@RequestBody Map<String,String[]> map) throws Exception {
        String[] ids = map.get("ids");
        String[] userCodes = map.get("userCodes");
        String[] userNames = map.get("userNames");
        inBillTaskService.delegate(ids,userCodes,userNames);
        return new Result(ResultCode.SUCCESS);
    }
}
