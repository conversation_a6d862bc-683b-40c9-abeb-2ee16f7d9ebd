package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;
import com.imes.domain.entities.qms.po.PatrolInspection;
import com.imes.domain.entities.qms.po.PatrolInspectionDetail;
import com.imes.domain.entities.qms.vo.PatrolInspectionNewInfoVo;
import com.imes.domain.entities.qms.vo.PatrolInspectionVo;
import com.imes.domain.entities.qms.vo.ProcessInspectionInfoVo;
import com.imes.qms.service.PatrolInspectionNewService;
import com.imes.qms.service.PatrolInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/patrolInspectionNew")
@Api(tags = "巡检相关接口(新)")
public class PatrolInspectionNewController {

    @Autowired
    PatrolInspectionNewService patrolInspectionNewService;

    /**
     * 获取巡检检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取巡检检验列表信息", httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String planCode) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("planCode", planCode);
//            // 显示未提交数据(暂不需要)
//            map.put("supportStatus",ConstantUtil.un_support);
            // 停止
            map.put("stopped", ConstantUtil.pat_stopped);
            map.put("ending", ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", patrolInspectionNewService.queryList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionNewController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取巡检检验结果列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取巡检检验结果列表信息", httpMethod = "GET")
    @GetMapping("/queryResultList")
    public Result queryResultList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                  @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                  String planCode, String ppNo, String pcNo, String processName,
                                  String materialName, String status) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("planCode", planCode);
            map.put("ppNo", ppNo);
            map.put("pcNo", pcNo);
            map.put("status", status);
            map.put("processName", processName);
            map.put("materialName", materialName);
            // 显示提交数据
            map.put("unSupport", ConstantUtil.un_support);
            map.put("ending", ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", patrolInspectionNewService.queryResultList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionNewController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 反提交按钮
     *
     * @return Result
     */
    @ApiOperation(value = "反提交按钮", httpMethod = "GET")
    @GetMapping("/unsubmission")
    public Result unsubmission(String id) throws Exception {
        patrolInspectionNewService.unsubmission(id);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 获取巡检任务信息
     *
     * @return
     */
    @ApiOperation(value = "获取巡检任务信息", httpMethod = "GET")
    @GetMapping("/queryTaskList")
    public Result queryTaskList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                String planCode, String inspectedDepartName,
                                String inspectionDepartName, String status,
                                String createStartTime, String createEndTime) throws Exception {
        Map reMap = new HashMap();
        Map map = new HashMap();
        map.put("status", ConstantUtil.pat_publish);
        map.put("publish", ConstantUtil.pat_publish);
        map.put("audit", ConstantUtil.pat_audit);
        map.put("inspectionStatus", ConstantUtil.has_inspection);
        map.put("planCode", planCode);
        map.put("inspectedDepartName", inspectedDepartName);
        map.put("inspectionDepartName", inspectionDepartName);
        map.put("createStartTime", createStartTime);
        map.put("createEndTime", createEndTime);
        map.put("checkStatus", status);
        Page page = PageHelper.startPage(pageNum, pageSize);
        reMap.put("list", patrolInspectionNewService.queryTaskList(map));
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 选择物料
     *
     * @return
     */
    @ApiOperation(value = "选择物料", httpMethod = "GET")
    @GetMapping("/materials")
    public Result getMaterials(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                               @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                               @RequestParam("departCodes") List<String> departCodes, String specification,
                               String materialMarker, String pcNo, String processName, String materialName) {
        try {

            return new Result(ResultCode.SUCCESS, patrolInspectionNewService.getMaterials(pageNum, pageSize, specification,
                    materialMarker, pcNo, processName, materialName, departCodes));
        } catch (Exception e) {
            log.error("PatrolInspectionController.getMaterials出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取检验项信息（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "获取检验项信息（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItemsByPpNoInfo")
    public Result getInspectionItemsStd(String routeCode, String processCode, String bomCode) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", patrolInspectionNewService.getInspectionItemsStd(routeCode, processCode, bomCode));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 暂存检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "暂存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody PatrolInspectionNewInfoVo data) throws Exception {
        Map reMap = new HashMap();
        patrolInspectionNewService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 加号新增检验数量
     *
     * @return
     */
    @ApiOperation(value = "加号新增检验数量", httpMethod = "POST")
    @PostMapping("/plusDetails")
    public Result savePlusDetails(@RequestBody PatrolInspectionNewInfoVo data) throws Exception {
        return new Result(ResultCode.SUCCESS, patrolInspectionNewService.savePlusDetails(data));
    }

    /**
     * 减号减检验数量(批量)
     *
     * @return
     */
    @ApiOperation(value = "减号减检验数量(批量)", httpMethod = "DELETE")
    @DeleteMapping("/deleteDetails")
    public Result deleteDetails(@RequestParam("inspectionNos") List<String> inspectionNos, @RequestParam("id") String id) throws Exception {
        patrolInspectionNewService.deleteDetailsNew(inspectionNos, id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 暂存检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "保存检验任务", httpMethod = "POST")
    @PostMapping("/saveInspectionTask")
    public Result saveInspectionTask(@RequestBody PatrolInspectionNewInfoVo data) throws Exception {
        Map reMap = new HashMap();
        patrolInspectionNewService.saveInspectionTask(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 删除检验数据
     *
     * @return
     */
    @ApiOperation(value = "删除检验数据", httpMethod = "DELETE")
    @DeleteMapping("/deleteItem")
    public Result deleteItem(@RequestParam("id") String id) {
        Map reMap = new HashMap();
        patrolInspectionNewService.deleteItem(id);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 提交检验详细数据(小提交)
     *
     * @return
     */
    @ApiOperation(value = "提交检验详细数据(小提交)", httpMethod = "POST")
    @PostMapping("/supportDatas")
    public Result supportDatas(@RequestBody PatrolInspectionNewInfoVo data) throws Exception {
        Map reMap = new HashMap();
        patrolInspectionNewService.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 提交检验详细数据(大提交)
     *
     * @return
     */
    @ApiOperation(value = "提交检验详细数据(大提交)", httpMethod = "POST")
    @PostMapping("/supportAllDatas")
    public Result supportAllDatas(@RequestBody PatrolInspection patrolInspection) throws Exception {
        String planCode = patrolInspection.getPlanCode();
        patrolInspectionNewService.supportAllDatas(planCode);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取检验条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", patrolInspectionNewService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryDetailList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取待检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取待检验条目详情", httpMethod = "GET")
    @GetMapping("/queryInspectingDetailList")
    public Result queryInspectingDetailList(String id) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", patrolInspectionNewService.queryInspectingDetailList(id));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryDetailList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", patrolInspectionNewService.queryDetailListByOrderId(orderId));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryDetailListByOrderId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", patrolInspectionNewService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryByActivtiId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 强制完成
     *
     * @return
     */
    @ApiOperation(value = "强制完成", httpMethod = "PUT")
    @PutMapping("/forcedFinish")
    public Result forcedFinish(PatrolInspection data) {
        patrolInspectionNewService.forcedFinish(data);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 委派
     *
     * @return Result
     */
    @ApiOperation(value = "委派", httpMethod = "POST")
    @PostMapping("/delegate")
    public Result delegate(@RequestBody Map<String, String[]> map) throws Exception {
        String[] ids = map.get("ids");
        String[] userCodes = map.get("userCodes");
        String[] userNames = map.get("userNames");
        patrolInspectionNewService.delegate(ids, userCodes, userNames);
        return new Result(ResultCode.SUCCESS);
    }
}
