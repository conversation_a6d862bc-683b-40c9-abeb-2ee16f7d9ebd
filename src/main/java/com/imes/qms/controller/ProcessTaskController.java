package com.imes.qms.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.ProcessTaskVo;
import com.imes.qms.service.ProcessTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/processTask")
@Api(tags = "质检任务相关接口")
public class ProcessTaskController {

    @Autowired
    ProcessTaskService processTaskService;

//    /**
//     * 获取过程检验列表信息
//     *
//     * @return
//     */
//    @ApiOperation(value = "获取过程检验列表信息",httpMethod = "GET")
//    @GetMapping("/queryList")
//    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
//                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
//                            String ppNo,String materialName,String processName,String materialCode,
//                            String inspectionCode,String inspectionStartTime,String inspectionEndTime,
//                            String inspectionStatus,String status,String isQualified) {
//        try {
//            Map reMap=new HashMap();
//            Map<String,Object> map = new HashMap();
//            map.put("ppNo",ppNo);
//            map.put("materialName",materialName);
//            map.put("materialCode",materialCode);
//            map.put("processName",processName);
//            map.put("inspectionCode",inspectionCode);
//            map.put("startTime",inspectionStartTime);
//            map.put("endTime",inspectionEndTime);
//            map.put("inspectionStatus",inspectionStatus);
//            map.put("status",status);
//            map.put("isQualified",isQualified);
//            Page page = PageHelper.startPage(pageNum, pageSize);
//            reMap.put("list",processInspectionService.queryList(map));
//            reMap.put("total",page.getTotal());
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("PatrolInspectionController.queryList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 报工结束生成质检任务
     *
     * @return Result
     */
    @ApiOperation(value = "报工结束生成质检任务",httpMethod = "POST")
    @PostMapping ("/processTasks")
    public Result createProcessTask(@RequestBody ProcessTaskVo vo) throws Exception {
        processTaskService.createProcessTask(vo);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 报工结束生成批量质检任务
     *
     * @return Result
     */
    @ApiOperation(value = "报工结束生成批量质检任务",httpMethod = "POST")
    @PostMapping ("/batchProcessTasks")
    public Result createBatchProcessTask(@RequestBody List<ProcessTaskVo> list) throws Exception {
        processTaskService.createBatchProcessTask(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 委派
     *
     * @return Result
     */
    @ApiOperation(value = "委派",httpMethod = "POST")
    @PostMapping ("/delegate")
    public Result delegate(@RequestBody Map<String,String[]> map) throws Exception {
        String[] ids = map.get("ids");
        String[] userCodes = map.get("userCodes");
        String[] userNames = map.get("userNames");
        processTaskService.delegate(ids,userCodes,userNames);
        return new Result(ResultCode.SUCCESS);
    }
}
