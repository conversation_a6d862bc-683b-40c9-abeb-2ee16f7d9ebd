package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.vo.StandardInspectionSaveVo;
import com.imes.qms.service.InBillInspectionService;
import com.imes.qms.service.StandardInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/standardInspection")
@Api(tags = "检验基础数据相关接口")
public class StandardInspectionController {

    @Autowired
    StandardInspectionService standardInspectionService;

    /**
     * 获取标准检验数据维护列表
     *
     * @return
     */
    @ApiOperation(value = "获取标准检验数据维护列表",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                @RequestParam(required = false, value = "productName") String productName) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            map.put("productName",productName);
            reMap.put("list",standardInspectionService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存检验基础数据
     *
     * @return
     */
    @ApiOperation(value = "保存检验基础数据",httpMethod = "POST")
    @PostMapping ("/saveStandardDatas")
    public Result saveStandardDatas(@RequestBody StandardInspectionSaveVo datas) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.saveStandardDatas(datas);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改检验基础数据
     *
     * @return
     */
    @ApiOperation(value = "修改检验基础数据",httpMethod = "POST")
    @PostMapping ("/updateStandardDatas")
    public Result updateStandardDatas(@RequestBody StandardInspectionSaveVo datas) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.updateStandardDatas(datas);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.updateStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除质检数据
     *
     * @return
     */
    @ApiOperation(value = "删除质检数据",httpMethod = "POST")
    @PostMapping ("/deleteStandardList")
    public Result deleteStandardList(@RequestParam("ids") List<String> ids) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.deleteStandardList(ids);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.deleteStandardList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 新增检验项
     *
     * @return
     */
    @ApiOperation(value = "新增检验项",httpMethod = "POST")
    @PostMapping ("/createNewItems")
    public Result createNewItems(@RequestBody List<InspectionItems> items) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.createNewItems(items);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.createNewItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改检验项
     *
     * @return
     */
    @ApiOperation(value = "修改检验项",httpMethod = "POST")
    @PostMapping ("/updateItems")
    public Result updateItems (@RequestBody List<InspectionItems> items) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.updateItems(items);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.updateItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除检验项
     *
     * @return
     */
    @ApiOperation(value = "删除检验项",httpMethod = "POST")
    @PostMapping ("/deleteItems")
    public Result deleteItems(@RequestParam("ids") List<String> ids) {
        try {
            Map reMap=new HashMap();
            standardInspectionService.deleteItems(ids);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.deleteItems出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验项",httpMethod = "GET")
    @GetMapping("/queryItemList")
    public Result queryItemList(@RequestParam(value = "type") String type) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",standardInspectionService.queryItemList(type));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("StandardInspectionController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

}
