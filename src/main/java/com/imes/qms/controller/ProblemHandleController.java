package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.ProblemExecution;
import com.imes.domain.entities.qms.vo.ProblemHandleVo;
import com.imes.domain.entities.query.template.qms.ProblemHandleQueryListVo;
import com.imes.qms.service.ProblemHandleService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/problemHandle")
@Api(tags = "问题处理相关接口")
public class ProblemHandleController {

    @Autowired
    ProblemHandleService problemHandleService;

    /**
     * 获取问题已处理数据列表
     *
     * @return
     */
    @ApiOperation(value = "获取问题已处理数据列表",httpMethod = "GET")
    @GetMapping("/queryMainList")
    public Result queryMainList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                String taskCode,String materialCode,String materialName,String progress,
                                String inspectionCode,String inspectionStartTime,String inspectionEndTime,
                                String inspectionType,String inspector,String executionMeasure) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("taskCode",taskCode);
            map.put("progress",progress);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("inspectionCode",inspectionCode);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("inspectionType",inspectionType);
            map.put("executionMeasure",executionMeasure);
            map.put("inspector",inspector);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",problemHandleService.queryMainList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryMainList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取问题待处理数据列表
     *
     * @return
     */
    @ApiOperation(value = "获取问题待处理数据列表",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(ProblemHandleQueryListVo vo) {
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<ProblemHandleVo> problemHandleVos = problemHandleService.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), problemHandleVos));

    }

//    /**
//     * 获取问题列表明细
//     *
//     * @return
//     */
//    @ApiOperation(value = "获取问题列表明细",httpMethod = "GET")
//    @GetMapping("/questionDetails")
//    public Result queryDetailList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
//                                  @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
//                                  String id) {
//        try {
//            Map reMap = new HashMap();
//            Map map = new HashMap();
//            map.put("parentId",id);
//            Page page = PageHelper.startPage(pageNum, pageSize);
//            reMap.put("list",problemHandleService.queryDetailList(map));
//            reMap.put("total",page.getTotal());
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("ProblemHandleController.queryDetailList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 获取问题列表明细
     *
     * @return
     */
    @ApiOperation(value = "获取问题列表明细",httpMethod = "GET")
    @GetMapping("/questionDetails")
    public Result queryDetailList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                  @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                  String id,String inspectionType) {
        try {
            Map reMap = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",problemHandleService.queryDetailList(id,inspectionType));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

     /**
      * 保存检验详细数据
      *
      * @return
      */
    @ApiOperation(value = "保存问题数据",httpMethod = "POST")
    @PostMapping ("/saveDatas")
    public Result saveDatas(@RequestBody ProblemHandleVo data) {
        try {
            Map reMap=new HashMap();
            problemHandleService.saveDatas(data);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.saveDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 条目提交按钮
     *
     * @return
     */
    @ApiOperation(value = "条目提交按钮",httpMethod = "PUT")
    @PutMapping("/buttonSupport")
    public Result buttonSupport(ProblemHandleVo data) {
        try {
            Map reMap=new HashMap();
            data.setStatus(ConstantUtil.has_support);
            problemHandleService.buttonSupport(data);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.updateStatus出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 条目驳回按钮
     *
     * @return
     */
    @ApiOperation(value = "条目驳回按钮",httpMethod = "PUT")
    @PutMapping("/buttonReject")
    public Result buttonReject(ProblemHandleVo data) {
        try {
            Map reMap=new HashMap();
            data.setStatus(ConstantUtil.has_reject);
            problemHandleService.buttonReject(data);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.updateStatus出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取执行措施维护列表
     *
     * @return
     */
    @ApiOperation(value = "获取执行措施维护列表",httpMethod = "GET")
    @GetMapping("/queryExecutionList")
    public Result queryExecutionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                     String executionPart,String partName,String inspectionType) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("executionPart",executionPart);
            map.put("partName",partName);
            map.put("inspectionType",inspectionType);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",problemHandleService.queryExecutionList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存执行措施维护数据
     *
     * @return
     */
    @ApiOperation(value = "保存执行措施维护数据",httpMethod = "POST")
    @PostMapping ("/saveExecution")
    public Result saveExecution(@RequestBody ProblemExecution data) {
        try {
            Map reMap=new HashMap();
            problemHandleService.saveExecution(data);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.saveExecution出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改执行措施维护数据
     *
     * @return
     */
    @ApiOperation(value = "修改执行措施维护数据",httpMethod = "POST")
    @PostMapping ("/updateExecution")
    public Result updateExecution(@RequestBody ProblemExecution data) {
        try {
            Map reMap=new HashMap();
            problemHandleService.updateExecution(data);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.updateExecution出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除执行措施维护数据
     *
     * @return
     */
    @ApiOperation(value = "删除执行措施维护数据",httpMethod = "POST")
    @PostMapping ("/deleteExecution")
    public Result deleteExecution(@RequestParam("id") String id) {
        try {
            Map reMap=new HashMap();
            problemHandleService.deleteExecution(id);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.deleteExecution出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取不合格检验任务
     *
     * @return
     */
    @ApiOperation(value = "获取不合格检验任务",httpMethod = "GET")
    @GetMapping("/getTaskList")
    public Result getTaskList(String parentId,String inspectionType) {
        try {
            Map reMap = new HashMap();
            reMap.put("list",problemHandleService.getTaskList(parentId,inspectionType));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取不合格检验任务
     *
     * @return
     */
    @ApiOperation(value = "获取不合格检验任务",httpMethod = "POST")
    @PostMapping("/inspections")
    public Result saveTasks(String materialCode,String inspectionType) {
        try {
            Map reMap = new HashMap();
            reMap.put("list",problemHandleService.getTaskList(materialCode,inspectionType));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改未完成状态为已完成
     *
     * @return
     */
    @ApiOperation(value = "修改未完成状态为已完成",httpMethod = "POST")
    @PostMapping("/changeFinish")
    public Result changeFinish(String id) {
        try {
            Map reMap = new HashMap();
            problemHandleService.changeFinish(id);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取部门列表
     *
     * @return
     */
    @ApiOperation(value = "获取部门列表",httpMethod = "GET")
    @GetMapping("/queryDepartMentList")
    public Result queryDepartMentList() {
        try {
            Map reMap = new HashMap();
            reMap.put("list",problemHandleService.queryDepartMentList());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据部门code获取人员
     *
     * @return
     */
    @ApiOperation(value = "根据部门code获取人员",httpMethod = "GET")
    @GetMapping("/executors")
    public Result getUsers(String executionMeasure,String inspectionType) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("executionMeasure",executionMeasure);
            map.put("inspectionType",inspectionType);
            reMap.put("list",problemHandleService.getUsers(map));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 重新检验提交后修改提交状态为已检验
     *
     * @return
     */
    @ApiOperation(value = "重新检验提交后修改提交状态为已检验",httpMethod = "PUT")
    @PutMapping("/statuses")
    public Result updateStatus(String id) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("id",id);
            map.put("status",ConstantUtil.has_inspection);
            map.put("progress","已完成");
            problemHandleService.updateStatus(map);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取子项具体信息
     *
     * @return
     */
    @ApiOperation(value = "获取子项具体信息",httpMethod = "GET")
    @GetMapping("/getDetailInfo")
    public Result getDetailInfo(String detailId) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("detailId",detailId);
            reMap.put("list",problemHandleService.getDetailInfo(map));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProblemHandleController.queryExecutionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
