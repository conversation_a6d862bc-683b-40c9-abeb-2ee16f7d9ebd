package com.imes.qms.controller;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.domain.entities.qms.po.QmsWorkInspectDetail;
import com.imes.qms.service.QmsWorkInspectDetailService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.qms.QmsWorkInspectDetailSearchVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 异常提报单-主表-志特(QmsWorkInspectDetail)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-13 09:46:15
 */
@RestController
@RequestMapping("/api/qms/qmsWorkInspectDetail")
@Api(tags = "异常提报单-主表-志特")
public class QmsWorkInspectDetailController {
    /**
     * 服务对象
     */
    @Autowired
    private QmsWorkInspectDetailService qmsWorkInspectDetailService;

    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (qmsWorkInspectDetailService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param qmsWorkInspectDetail 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody QmsWorkInspectDetail qmsWorkInspectDetail) throws Exception {
        String result = qmsWorkInspectDetailService.insert(qmsWorkInspectDetail);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 编辑数据
     *
     * @param qmsWorkInspectDetail 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody QmsWorkInspectDetail qmsWorkInspectDetail) throws Exception {
        if (qmsWorkInspectDetailService.update(qmsWorkInspectDetail) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (qmsWorkInspectDetailService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(QmsWorkInspectDetailSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkInspectDetailSearchVo> list = qmsWorkInspectDetailService.queryList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

}

