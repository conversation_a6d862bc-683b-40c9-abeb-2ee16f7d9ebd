package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RecordUtils;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.flowable.mqtt.FlowableEventMqttDTO;
import com.imes.domain.entities.qms.po.CcpTask;
import com.imes.domain.entities.qms.vo.CcpInspectionInfoVo;
import com.imes.domain.entities.qms.vo.CcpTaskAndInspVo;
import com.imes.domain.entities.qms.vo.CcpTaskVo;
import com.imes.domain.entities.query.template.qms.GetCcpListVo;
import com.imes.qms.dao.CcpTaskDao;
import com.imes.qms.service.CcpInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/ccpInspection")
@Api(tags = "CCP检验相关接口")
public class CcpInspectionController {

    @Autowired
    CcpTaskDao dao;
    @Autowired
    CcpInspectionService service;

    @ApiOperation(value = "生成质检任务", httpMethod = "POST")
    @PostMapping("/saveTask")
    public Result saveTask(@RequestBody List<CcpTaskAndInspVo> task) throws Exception {
        service.saveTask(task);
        return Result.SUCCESS(new PageResult());
    }

    @ApiOperation(value = "删除质检任务", httpMethod = "POST")
    @PostMapping("/deleteTask")
    public Result deleteTask(@RequestBody List<String> ids) throws Exception {
        service.deleteTask(ids);
        return Result.SUCCESS(new PageResult());
    }

    @ApiOperation(value = "获取生产单号信息（高级查询版本）", httpMethod = "GET")
    @GetMapping("/getList")
    public Result getList(GetCcpListVo vo) throws Exception {
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setUnInspection(ConstantUtil.un_inspection);
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<GetCcpListVo> list = service.getList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @ApiOperation(value = "根据任务id查找到显示结果对象（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionById")
    public Result getInspectionById(String id) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.getInspectionById(id));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "获取巡检检验条目详情(移动端)", httpMethod = "GET")
    @GetMapping("/queryDetailListByMobile")
    public Result queryDetailList(String id) throws Exception {
        Map reMap = new HashMap();
        List<CcpInspectionInfoVo> ccpInspectionInfoVos = service.queryDetailList(id);
//        List<CcpInspectionInfoVo> collect = ccpInspectionInfoVos.stream().sorted((u1, u2) -> new Long(u2.getMain().getLatestInspectionTime().getTime()).intValue() - new Long(u1.getMain().getLatestInspectionTime().getTime()).intValue()).collect(Collectors.toList());
        CcpInspectionInfoVo ccpInspectionInfoVo = ccpInspectionInfoVos.get(ccpInspectionInfoVos.size() - 1);
        reMap.put("vo", ccpInspectionInfoVo);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "根据生产单号获取检验项信息（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItemsByPpNoInfo")
    public Result getInspectionItemsStd(String routeCode, String processCode, String bomCode, String id, int batchNum) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.getInspectionItemsStd(routeCode, processCode, bomCode, id, batchNum));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "提交检验详细数据", httpMethod = "POST")
    @PostMapping("/supportDatas")
    public Result supportDatas(@RequestBody CcpInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        service.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "暂存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody CcpInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        service.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.queryDetailListByOrderId(orderId));
        // 设置该任务认领人
        CcpTask task = new CcpTask();
        task.setId(orderId);
        task.setClaimCode(RedisUtils.getUserCode());
        task.setClaimName(RedisUtils.getUserName());
        RecordUtils.updateData(task);
        dao.updateClaim(task);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "委派功能显示隐藏", httpMethod = "GET")
    @GetMapping("/getDelegate")
    public Result getDelegate() throws Exception {
        boolean result = service.getDelegate();
        return new Result(ResultCode.SUCCESS, result);
    }

    @ApiOperation(value = "委派人员和部门", httpMethod = "GET")
    @GetMapping("/delegateUserList")
    public Result delegateUserList() throws Exception {
        return new Result(ResultCode.SUCCESS, service.delegateUserList());
    }

    @ApiOperation(value = "委派的确定按钮", httpMethod = "GET")
    @GetMapping("/delegateConfirmation")
    public Result delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag) throws Exception {
        return new Result(ResultCode.SUCCESS, service.delegateConfirmation(id, userCode, userName, teamCode, teamName, addFlag));
    }

    @ApiOperation(value = "查询质检项", httpMethod = "GET")
    @GetMapping("/selectInsection")
    public Result selectInsection(String routeCode, String processCode) throws Exception {
        return new Result(ResultCode.SUCCESS, service.selectInsection(routeCode, processCode));
    }

    @ApiOperation(value = "取消认领", httpMethod = "GET")
    @GetMapping("/cancelClaim")
    public Result cancelClaim(String id) throws Exception {
        return new Result(ResultCode.SUCCESS, service.cancelClaim(id));
    }

    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.queryByActivtiId(activitiId));
        return new Result(ResultCode.SUCCESS, reMap);
    }
}
