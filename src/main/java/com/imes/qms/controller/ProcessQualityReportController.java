package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.ProcessBadCountsQualityReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsProcessInspectionReportListVo;
import com.imes.qms.service.ProcessQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/processQualityReport")
@Api(tags = "过程检验质量报表相关接口")
public class ProcessQualityReportController {

    @Autowired
    ProcessQualityReportService processQualityReportService;

//    /**
//     * 获取过程检验报表
//     *
//     * @return
//     */
//    @ApiOperation(value = "获取过程检验报表",httpMethod = "GET")
//    @GetMapping("/getInspectionList")
//    public Result getInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
//                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
//                                    String materialCode,String materialName,
//                                    String specification,String materialMarker) {
//        try {
//            Map reMap=new HashMap();
//            Map map = new HashMap();
//            map.put("materialCode",materialCode);
//            map.put("materialName",materialName);
//            map.put("specification",specification);
//            map.put("materialMarker",materialMarker);
//            // 通过
//            map.put("process", ConstantUtil.process);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            map.put("ending",ConstantUtil.ending);
//            Page page = PageHelper.startPage(pageNum, pageSize);
//            List<ProcessQualityReportVO> inBillQualityReportVOs = processQualityReportService.getInspectionList(map);
//            reMap.put("list",inBillQualityReportVOs);
//            reMap.put("total",null==inBillQualityReportVOs?0:inBillQualityReportVOs.size());
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("ProcessQualityReportController.getInspectionList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 获取过程检验报表
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionList")
    public Result getInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String materialCode,String materialName,
                                    String specification,String materialMarker,
                                    String ppNo,String pcNo,String processCode,String processName,
                                    String inspectionStartTime,String inspectionEndTime,
                                    String startTime,String endTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("ppNo",ppNo);
            map.put("pcNo",pcNo);
            map.put("processCode",processCode);
            map.put("processName",processName);
            map.put("specification",specification);
            map.put("materialMarker",materialMarker);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            map.put("startTime",startTime);
            map.put("endTime",endTime);
            // 通过
            map.put("process", ConstantUtil.process);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            List<ProcessQualityReportVO> inBillQualityReportVOs = processQualityReportService.getInspectionList(map);
            reMap.put("list",inBillQualityReportVOs);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取过程检验报表
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionListNew")
    public Result getInspectionListNew(QmsProcessInspectionReportListVo vo) throws Exception {
        vo.setProcess(ConstantUtil.adopt);
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<ProcessQualityReportVO> processQualityReportVOs = processQualityReportService.getInspectionListNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), processQualityReportVOs));
    }

    /**
     * 获取过程检验报表-日期方向
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验报表-日期方向",httpMethod = "GET")
    @GetMapping("/getInspectionByDate")
    public Result getInspectionByDate(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String startTime,String endTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("startTime",startTime);
            map.put("endTime",endTime);
            // 通过
            map.put("process", ConstantUtil.process);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            List<ProcessQualityReportVO> inBillQualityReportVOs = processQualityReportService.getInspectionList(map);
            reMap.put("list",inBillQualityReportVOs);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取过程检验报表-不良项top10统计
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验报表-不良项top10统计",httpMethod = "GET")
    @GetMapping("/getInspectionByBadCounts")
    public Result getInspectionByBadCounts(String startTime,String endTime,String limit) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("startTime",startTime);
            map.put("endTime",endTime);
            map.put("limit",limit);
            // 通过
            map.put("process", ConstantUtil.process);
            List<ProcessBadCountsQualityReportVO> counts = processQualityReportService.getInspectionByBadCounts(map);
            reMap.put("list",counts);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取过程检验报表-不良项top10统计-明细
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验报表-不良项top10统计-明细",httpMethod = "GET")
    @GetMapping("/badCountTopDetails")
    public Result getBadCountDetails(@RequestParam("badCode") String badCode,
                                     String materialCode,String materialName,
                                     String processCode,String processName,
                                     String specification) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            Object counts = processQualityReportService.getBadCountDetails(badCode,materialCode,materialName,processCode,
                        processName,specification);
            reMap.put("list",counts);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }


    /**
     * 获取检验单明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取检验单明细列表",httpMethod = "GET")
    @GetMapping("/inspectionDetails")
    public Result getInspectionDetail(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                      @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                      String ppNo,String inspectionStartTime,String inspectionEndTime,
                                      String pcNo,String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("ppNo",ppNo);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            map.put("pcNo",pcNo);
            map.put("inspector",inspector);
            // 通过
            map.put("process", ConstantUtil.process);
            // 驳回
            map.put("reject",ConstantUtil.has_reject);
            // 结束
            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",processQualityReportService.getInspectionDetail(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取工序不良品明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取工序不良品明细列表",httpMethod = "GET")
    @GetMapping("/badCountDetails")
    public Result getBadCountDetail(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                      @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                      String pcNo,String processCode,String processName,
                                    String inspectionStartTime,String inspectionEndTime,
                                    String startTime,String endTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("pcNo",pcNo);
            // 通过
            map.put("process", ConstantUtil.process);
            map.put("processCode",processCode);
            map.put("processName",processName);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            map.put("startTime",startTime);
            map.put("endTime",endTime);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            // 结束
//            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",processQualityReportService.getBadCountDetail(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取不良品分类明细
     *
     * @return
     */
    @ApiOperation(value = "获取不良品分类明细",httpMethod = "GET")
    @GetMapping("/badCountClassify")
    public Result getBadCountClassify(String inspectionCode,String wfNo,String processCode) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",processQualityReportService.getBadCountClassify(inspectionCode,wfNo,processCode));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
