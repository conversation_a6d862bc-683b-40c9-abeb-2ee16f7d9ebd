package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItemDetail;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.qms.service.InBillDataProtectService;
import com.imes.qms.service.QualityTraceService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/qualityTrace")
@Api(tags = "质量追溯相关接口")
public class QualityTraceController {

    @Autowired
    QualityTraceService qualityTraceService;

    /**
     * 获取质量追溯数据列表
     *
     * @return
     */
    @ApiOperation(value = "获取质量追溯数据列表",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String inspectionType,String inspectionCode,
                            String materialCode,String materialName,
                            String inspectionStartTime,String inspectionEndTime,
                            String customerName,String supplierCode,String conditions) {
        try {
            Map reMap = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("customerName",customerName);
            map.put("supplierCode",supplierCode);
            map.put("inspection", ConstantUtil.has_inspection);
            map.put("reject",ConstantUtil.has_reject);
            map.put("process",ConstantUtil.process);
            map.put("conditions",conditions);
//            map.put("ending",ConstantUtil.ending);
//            map.put("status", ConstantUtil.has_support);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            reMap.put("list",qualityTraceService.queryList(inspectionType,inspectionCode,map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("QualityTraceController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

//    /**
//     * 查询检验项
//     *
//     * @return
//     */
//    @ApiOperation(value = "查询检验项",httpMethod = "GET")
//    @GetMapping("/queryItemList")
//    public Result queryItemList(String id,String inspectionNo,String itemName,String itemCode,String inspectionType) {
//        try {
//            Map reMap=new HashMap();
//            Map map = new HashMap();
//            map.put("parentId",id);
//            map.put("inspectionNo",inspectionNo);
//            map.put("itemName",itemName);
//            map.put("itemCode",itemCode);
//            reMap.put("list",qualityTraceService.queryItemList(inspectionType,map));
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("QualityTraceController.queryItemList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验项",httpMethod = "GET")
    @GetMapping("/queryItemList")
    public Result queryItemList(String id,String inspectionType) {
        try {
            Map<String,Object> reMap=new HashMap();
            reMap = qualityTraceService.queryItemList(inspectionType,id);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("QualityTraceController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 查询供应商
     *
     * @return
     */
    @ApiOperation(value = "查询供应商",httpMethod = "GET")
    @GetMapping("/suppliers")
    public Result getSupplier() {
        try {
            return qualityTraceService.getSupplier();
        } catch (Exception e) {
            log.error("QualityTraceController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
