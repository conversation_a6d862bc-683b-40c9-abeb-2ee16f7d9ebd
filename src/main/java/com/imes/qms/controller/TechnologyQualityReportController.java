package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.FirstFinalQualityReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityReportVO;
import com.imes.domain.entities.qms.vo.TechnologyQualityReportVO;
import com.imes.qms.service.OutBillQualityReportService;
import com.imes.qms.service.TechnologyQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/technologyQualityReport")
@Api(tags = "工艺检验质量报表相关接口")
public class TechnologyQualityReportController {

    @Autowired
    TechnologyQualityReportService technologyQualityReportService;

    /**
     * 获取首末检工艺检验计划
     *
     * @return
     */
    @ApiOperation(value = "获取首末检工艺检验计划", httpMethod = "GET")
    @GetMapping("/firstAndEnds")
    public Result firstAndEnds(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String materialCode,String materialName,
                                    String specification,String materialMarker,
                                    String processCode,String processName,
                                    String inspectionStartTime,String inspectionEndTime,
                                    String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("inspector",inspector);
            map.put("processCode",processCode);
            map.put("processName",processName);
            map.put("specification",specification);
            map.put("materialMarker",materialMarker);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            // 通过
            map.put("process", ConstantUtil.process);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            List<FirstFinalQualityReportVO> firstFinalQualityReportVOS = technologyQualityReportService.getFirstEndInspectionList(map);
            reMap.put("list",firstFinalQualityReportVOS);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取首末检工艺检验计划
     *
     * @return
     */
    @ApiOperation(value = "获取巡检工艺检验计划", httpMethod = "GET")
    @GetMapping("/patrols")
    public Result getPatrolInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                          @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                          String materialCode, String materialName, String processName) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("materialCode", materialCode);
            map.put("materialName", materialName);
            map.put("processName", processName);
            Page page = PageHelper.startPage(pageNum, pageSize);
            List<TechnologyQualityReportVO> technologyQualityReportVOs = technologyQualityReportService.getPatrolInspectionList(map);
            reMap.put("list", technologyQualityReportVOs);
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("TechnologyQualityReportController.getFirstEndInspectionList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取首末检验详情
     *
     * @return
     */
    @ApiOperation(value = "获取首末检验详情", httpMethod = "GET")
    @GetMapping("/firstEndDetails")
    public Result getInspectionDetailList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                          @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                          String materialCode, String materialMarker,
                                          String specification, String processCode,
                                          String inspectionStartTime, String inspectionEndTime,
                                          String inspector,String ppNo,String pcNo) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("materialCode", materialCode);
            map.put("materialMarker", materialMarker);
            map.put("specification", specification);
            map.put("processCode", processCode);
            map.put("startTime", inspectionStartTime);
            map.put("endTime", inspectionEndTime);
            map.put("inspector", inspector);
            map.put("ppNo", ppNo);
            map.put("pcNo", pcNo);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", technologyQualityReportService.getInspectionDetailList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("TechnologyQualityReportController.getInspectionList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }


    }

    /**
     * 获取巡检详情
     *
     * @return
     */
    @ApiOperation(value = "获取巡检详情", httpMethod = "GET")
    @GetMapping("/patrolDetails")
    public Result getPatrolDetails(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                   @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                   String materialCode, String materialMarker,
                                   String specification, String processCode,
                                   String inspectionStartTime, String inspectionEndTime,
                                   String inspector,String ppNo,String pcNo) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("materialCode", materialCode);
            map.put("materialMarker", materialMarker);
            map.put("specification", specification);
            map.put("processCode", processCode);
            map.put("startTime", inspectionStartTime);
            map.put("endTime", inspectionEndTime);
            map.put("inspector", inspector);
            map.put("ppNo", ppNo);
            map.put("pcNo", pcNo);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", technologyQualityReportService.getPatrolDetails(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("TechnologyQualityReportController.getInspectionList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取检验单明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取检验单明细列表",httpMethod = "GET")
    @GetMapping("/inspectionDetails")
    public Result getInspectionDetail(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                      @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                      String ppNo,String inspectionStartTime,String inspectionEndTime,
                                      String pcNo,String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("ppNo",ppNo);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            map.put("pcNo",pcNo);
            map.put("inspector",inspector);
            // 通过
            map.put("process", ConstantUtil.process);
            // 驳回
            map.put("reject",ConstantUtil.has_reject);
            // 结束
            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",technologyQualityReportService.getInspectionDetail(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取工序不良品明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取工序不良品明细列表",httpMethod = "GET")
    @GetMapping("/badCountDetails")
    public Result getBadCountDetail(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                    @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String processCode,String processName,
                                    String inspectionStartTime,String inspectionEndTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            // 通过
            map.put("process", ConstantUtil.process);
            map.put("processCode",processCode);
            map.put("processName",processName);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
//            // 驳回
//            map.put("reject",ConstantUtil.has_reject);
//            // 结束
//            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",technologyQualityReportService.getBadCountDetail(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取不良品分类明细
     *
     * @return
     */
    @ApiOperation(value = "获取不良品分类明细",httpMethod = "GET")
    @GetMapping("/badCountClassify")
    public Result getBadCountClassify(String processCode,String inspectionCode) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",technologyQualityReportService.getBadCountClassify(processCode,inspectionCode));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ProcessQualityReportController.getInspectionDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
