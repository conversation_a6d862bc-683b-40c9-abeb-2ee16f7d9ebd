package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.PatrolInspection;
import com.imes.domain.entities.qms.po.PatrolInspectionDetail;
import com.imes.domain.entities.qms.vo.PatrolInspectionVo;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.PatrolInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/patrolInspection")
@Api(tags = "巡检相关接口")
public class PatrolInspectionController {

    @Autowired
    PatrolInspectionService patrolInspectionService;

    /**
     * 获取巡检列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取巡检列表信息",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String ppNo,String materialName,String processName,String materialCode,
                            String inspectionCode,String inspectionStartTime,String inspectionEndTime,
                            String inspectionStatus,String status,String pcNo,String isQualified) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("ppNo",ppNo);
            map.put("pcNo",pcNo);
            map.put("materialName",materialName);
            map.put("materialCode",materialCode);
            map.put("processName",processName);
            map.put("inspectionCode",inspectionCode);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("inspectionStatus",inspectionStatus);
            map.put("status",status);
            map.put("isQualified",isQualified);
            map.put("ending", ConstantUtil.ending);
            map.put("unSupport",ConstantUtil.un_support);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",patrolInspectionService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "获取生产单号信息",httpMethod = "GET")
    @GetMapping("/getPpNoInfo")
    public Result getPpNoInfo(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                              @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                              String ppNo,String materialName,String processName,
                              String pcNo,String materialCode,String workshopName,
                              String lineName) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("ppNo",ppNo);
            map.put("pcNo",pcNo);
            map.put("materialName",materialName);
            map.put("materialCode",materialCode);
            map.put("processName",processName);
            map.put("lineName",lineName);
            map.put("workshopName",workshopName);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",patrolInspectionService.getPpNoInfo(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据工序code获取station
     *
     * @return
     */
    @ApiOperation(value = "根据工序code获取station",httpMethod = "GET")
    @GetMapping("/getStationByProcessCode")
    public Result getStationByProcessCode(String processCode) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",patrolInspectionService.getStationByProcessCode(processCode));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据主表信息获取检验项信息
     *
     * @return
     */
    @ApiOperation(value = "根据主表信息获取检验项信息",httpMethod = "GET")
    @GetMapping("/getInspectionItemsByPpNoInfo")
    public Result getInspectionItemsByPpNoInfo(PatrolInspection patrolInspection) throws Exception {
        Map reMap=new HashMap();
        patrolInspection.setInspector(RedisUtils.getUserCode());
        reMap.put("list",patrolInspectionService.getInspectionItemsByPpNoInfo(patrolInspection));
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 保存检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "保存检验详细数据",httpMethod = "POST")
    @PostMapping ("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody PatrolInspectionVo data) throws Exception {
        Map reMap=new HashMap();
        patrolInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 获取来料检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取巡检条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",patrolInspectionService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("PatrolInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 提交检验条目按钮
     *
     * @return
     */
    @ApiOperation(value = "提交检验条目按钮",httpMethod = "PUT")
    @PutMapping ("/inspections")
    public Result updateStatus(String id) {
        Map reMap=new HashMap();
        patrolInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 编辑修改检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "编辑修改检验详细数据",httpMethod = "PUT")
    @PutMapping ("/updateInspectionDetailData")
    public Result updateInspectionDetailData(PatrolInspectionDetail data) {
        Map reMap=new HashMap();
        patrolInspectionService.updateInspectionDetailData(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }
}
