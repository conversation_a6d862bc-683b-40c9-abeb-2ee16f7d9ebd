package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.interceptor.MyPermCompateQz;
import com.imes.common.utils.ExcelUtils;
import com.imes.common.utils.ImportUtil;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.InspectionItemDetail;
import com.imes.domain.entities.qms.po.InspectionItems;
import com.imes.domain.entities.qms.po.QmsProtectFile;
import com.imes.domain.entities.qms.vo.ProcessQualityBadCountReportVO;
import com.imes.domain.entities.qms.vo.ProcessQualityReportVO;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionDataProtectImportVo;
import com.imes.domain.entities.query.plugin.imports.template.qms.QmsInspectionItemsImportVo;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.domain.entities.system.base.vo.SysExportTemplateWrap;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.service.FtsInterfaceService;
import com.imes.qms.service.InMaterialDataProtectService;
import com.imes.qms.service.MaterialQualityReportService;
import com.imes.qms.service.ProcessQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ResourceUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inMaterialDataProtect")
@Api(tags = "检验基础数据相关接口")
public class InMaterialDataProtectController {

    @Autowired
    InMaterialDataProtectService inMaterialDataProtectService;

    @Autowired
    MaterialQualityReportService materialQualityReportService;

    @Autowired
    ProcessQualityReportService processQualityReportService;

    @Autowired
    FeignService feignService;

    @Autowired
    FtsInterfaceService ftsInterfaceService;

    @Value("${imes.qms.template.qmsInspectionMaterial-model}")
    private String qmsInspectionMaterialPath;

    @Value("${imes.qms.template.qmsVerificationMaterial-model}")
    private String qmsVerificationMaterialPath;

    @Value("${imes.qms.template.qmsMaterialInspection-model}")
    private String qmsMaterialInspectionPath;

    /**
     * 获取来料检验数据维护列表
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验数据维护列表", httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            @RequestParam(required = false, value = "materialCode") String materialCode,
                            @RequestParam(required = false, value = "materialName") String materialName,
                            @RequestParam(value = "inspectionType") String inspectionType) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            map.put("materialCode", materialCode);
            map.put("materialName", materialName);
            map.put("inspectionType", inspectionType);
            reMap.put("list", inMaterialDataProtectService.queryList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 保存物料
     *
     * @return
     */
    @ApiOperation(value = "保存物料", httpMethod = "POST")
    @PostMapping("/saveMaterialDatas")
    public Result saveMaterialDatas(@RequestBody List<InspectionDataProtect> list) {
        try {
            Map reMap = new HashMap();
            String message = inMaterialDataProtectService.saveMaterialDatas(list);
            if ("ok".equals(message)) {
                return new Result(ResultCode.SUCCESS, reMap);
            } else {
                return new Result(false, 10001, message, reMap);
            }

        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 保存检验比率
     *
     * @return
     */
    @ApiOperation(value = "保存检验比率", httpMethod = "POST")
    @PostMapping("/saveInspectionRatio")
    public Result saveInspectionRatio(@RequestBody List<InspectionDataProtect> ratios) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.saveInspectionRatio(ratios);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 删除物料维护数据
     *
     * @return
     */
    @ApiOperation(value = "删除物料维护数据", httpMethod = "POST")
    @PostMapping("/deleteMaterialProtectData")
    public Result deleteMaterialProtectData(@RequestParam("id") String id) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.deleteMaterialProtectData(id);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.deleteItems出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验项", httpMethod = "GET")
    @GetMapping("/queryItemList")
    public Result queryItemList(@RequestParam(value = "materialCode") String materialCode,
                                @RequestParam(value = "inspectionType") String inspectionType,
                                @RequestParam(required = false, value = "itemName") String itemName) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("materialCode", materialCode);
            map.put("inspectionType", inspectionType);
            map.put("itemName", itemName);
            reMap.put("list", inMaterialDataProtectService.queryItemList(map));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.queryItemList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

//    /**
//     * 获取检验项编码
//     *
//     * @return
//     */
//    @ApiOperation(value = "获取检验项编码",httpMethod = "GET")
//    @GetMapping("/getInspectionCode")
//    public Result getInspectionCode() {
//        try {
//            Map reMap=new HashMap();
//            reMap.put("inspectionCode",inMaterialDataProtectService.getInspectionCode());
//            return new Result(ResultCode.SUCCESS,reMap);
//        } catch (Exception e) {
//            log.error("InBillDataProtectController.queryItemList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 保存检验项
     *
     * @return
     */
    @ApiOperation(value = "保存检验项", httpMethod = "POST")
    @PostMapping("/saveInspectionItem")
    public Result saveInspectionItem(@RequestBody InspectionItems item) {
        try {
            return new Result(ResultCode.SUCCESS, inMaterialDataProtectService.saveInspectionItem(item));
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取临时生成检验项id、检验项code
     *
     * @return
     */
    @ApiOperation(value = "获取临时生成检验项id", httpMethod = "GET")
    @GetMapping("/getTempItems")
    public Result getTempItems() {
        try {
            Map reMap = new HashMap();
            reMap.put("list", inMaterialDataProtectService.getTempItems());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取检验子项id
     *
     * @return
     */
    @ApiOperation(value = "获取检验子项id", httpMethod = "GET")
    @GetMapping("/getTempDetailItemId")
    public Result getTempDetailItemId() {
        try {
            Map<String, Object> reMap = inMaterialDataProtectService.getTempDetailItemId();
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 修改检验项
     *
     * @return
     */
    @ApiOperation(value = "修改检验项", httpMethod = "POST")
    @PostMapping("/updateItems")
    public Result updateItems(@RequestBody InspectionItems item) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.updateItems(item);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.updateItems出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 删除检验项
     *
     * @return
     */
    @ApiOperation(value = "删除检验项", httpMethod = "POST")
    @PostMapping("/deleteItem")
    public Result deleteItem(@RequestBody Map map) {
        try {
            Map reMap = new HashMap();
            String itemCode = map.get("itemCode").toString();
            inMaterialDataProtectService.deleteItem(itemCode);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.deleteItem出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 查询检验项
     *
     * @return
     */
    @ApiOperation(value = "查询检验子项", httpMethod = "GET")
    @GetMapping("/queryItemDetailList")
    public Result queryItemDetailList(@RequestParam(value = "itemCode") String itemCode) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("itemCode", itemCode);
            reMap.put("list", inMaterialDataProtectService.queryItemDetailList(map));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.queryItemDetailList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 保存检验子项
     *
     * @return
     */
    @ApiOperation(value = "保存检验子项", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailItems")
    public Result saveInspectionDetailItems(@RequestBody List<InspectionItemDetail> items) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.saveInspectionDetailItems(items);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.saveStandardDatas出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 修改检验子项
     *
     * @return
     */
    @ApiOperation(value = "修改检验子项", httpMethod = "POST")
    @PostMapping("/updateItemDetails")
    public Result updateItemDetails(@RequestBody List<InspectionItemDetail> items) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.updateItemDetails(items);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.updateItems出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 删除检验子项
     *
     * @return
     */
    @ApiOperation(value = "删除检验子项", httpMethod = "POST")
    @PostMapping("/deleteItemDetails")
    public Result deleteItemDetails(@RequestBody List<String> ids) {
        try {
            Map reMap = new HashMap();
            inMaterialDataProtectService.deleteItemDetails(ids);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("InMaterialDataProtectController.deleteItem出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }


    /**
     * 基础数据维护文件上传接口
     *
     * @param uploadFile
     * @return
     * @throws Exception
     */
    @PostMapping("/uploadProtectFile")
    @ApiOperation(value = " 基础数据维护文件上传接口", httpMethod = "POST")
    public Result uploadProtectFile(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        SysFile sysFile = inMaterialDataProtectService.uploadProtectFile(uploadFile);
        return new Result(ResultCode.SUCCESS, sysFile);
    }

    /**
     * 文件批量上传接口
     *
     * @param uploadFiles
     * @return
     * @throws Exception
     */
    @PostMapping("/multiUpload")
    @ApiOperation(value = " 文件批量上传接口", httpMethod = "POST")
    public Result multiUpload(@RequestParam("file") MultipartFile[] uploadFiles) throws Exception {
        List<SysFile> files = new ArrayList<>();
        for (MultipartFile uploadFile : uploadFiles) {
            SysFile sysFile = inMaterialDataProtectService.uploadProtectFile(uploadFile);
            files.add(sysFile);
        }

        return new Result(ResultCode.SUCCESS, files);
    }


    /**
     * 保存文件与检验项关系
     *
     * @param qmsProtectFile
     * @return
     * @throws Exception
     */
    @GetMapping("/insertProtectFile")
    @ApiOperation(value = " 保存文件与检验项关系", httpMethod = "GET")
    public Result insertProtectFile(QmsProtectFile qmsProtectFile) throws Exception {
        inMaterialDataProtectService.insertProtectFile(qmsProtectFile);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 批量保存文件与检验项关系
     *
     * @param qmsProtectFiles
     * @return
     * @throws Exception
     */
    @GetMapping("/multiSaveFiles")
    @ApiOperation(value = " 批量保存文件与检验项关系", httpMethod = "GET")
    public Result multiSaveFiles(QmsProtectFile[] qmsProtectFiles) throws Exception {
        for (QmsProtectFile qmsProtectFile : qmsProtectFiles) {
            inMaterialDataProtectService.insertProtectFile(qmsProtectFile);
        }
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 查询检验项文件
     *
     * @param qmsProtectFile
     * @return
     * @throws Exception
     */
    @GetMapping("/findProtectFile")
    @ApiOperation(value = " 查询检验项文件", httpMethod = "GET")
    public Result findProcessFile(QmsProtectFile qmsProtectFile) throws Exception {
        Map<String, Object> protectFile = inMaterialDataProtectService.findProtectFile(qmsProtectFile);
        return new Result(ResultCode.SUCCESS, protectFile);
    }

    /**
     * 查询检验项文件
     *
     * @param processCode
     * @param itemCode
     * @return
     */
    @GetMapping("/findTechnologyFile")
    @ApiOperation(value = " 查询工艺检验检验项文件", httpMethod = "GET")
    public Result findTechnologyFile(String processCode, String itemCode) {
        Map<String, Object> protectFile = inMaterialDataProtectService.findTechnologyFile(processCode, itemCode);
        return new Result(ResultCode.SUCCESS, protectFile);
    }

    /**
     * 删除检验项文件
     *
     * @param id
     * @return
     * @throws Exception
     */
    @GetMapping("/deleteProtectFileById")
    @ApiOperation(value = " 删除检验项文件", httpMethod = "GET")
    public Result deleteProtectFileById(@RequestParam("id") String id) throws Exception {
        inMaterialDataProtectService.deleteProtectFileById(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 保存文件与检验子项关系
     *
     * @param qmsProtectFile
     * @return
     * @throws Exception
     */
    @PostMapping("/childFiles")
    @ApiOperation(value = " 保存文件与检验子项关系", httpMethod = "POST")
    public Result childFiles(QmsProtectFile qmsProtectFile, String inspectionType) throws Exception {
        inMaterialDataProtectService.insertChildFile(qmsProtectFile, inspectionType);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 批量保存文件与检验子项关系
     *
     * @param qmsProtectFiles
     * @return
     * @throws Exception
     */
    @PostMapping("/multiSaveChildFiles")
    @ApiOperation(value = " 批量保存文件与检验子项关系", httpMethod = "POST")
    public Result multiChildFiles(@RequestBody QmsProtectFile[] qmsProtectFiles, @RequestParam("inspectionType") String inspectionType) throws Exception {
        for (QmsProtectFile qmsProtectFile : qmsProtectFiles) {
            inMaterialDataProtectService.insertChildFile(qmsProtectFile, inspectionType);
        }
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 查询检验子项文件
     *
     * @param qmsProtectFile
     * @return
     * @throws Exception
     */
    @GetMapping("/childFiles")
    @ApiOperation(value = " 查询检验子项文件", httpMethod = "GET")
    public Result findChildFile(QmsProtectFile qmsProtectFile) throws Exception {
        List<Map<String, Object>> protectFile = inMaterialDataProtectService.findChildFile(qmsProtectFile);
        return new Result(ResultCode.SUCCESS, protectFile);
    }

    /**
     * 删除检验子项文件
     *
     * @param id
     * @return
     * @throws Exception
     */
    @DeleteMapping("/childFiles")
    @ApiOperation(value = " 删除检验子项文件", httpMethod = "DELETE")
    public Result deleteChildFileById(@RequestParam("id") String id) throws Exception {
        inMaterialDataProtectService.deleteProtectFileById(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 根据子项id删除文件
     *
     * @param fileIds
     * @return Result
     * @throws Exception
     */
    @DeleteMapping("/deleteFiles")
    @ApiOperation(value = " 删除检验子项文件", httpMethod = "DELETE")
    public Result deleteByFileIds(@RequestParam("fileIds") List<String> fileIds, @RequestParam("inspectionType") String inspectionType) throws Exception {
        inMaterialDataProtectService.deleteByFileIds(fileIds, inspectionType);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取临时单号
     *
     * @param code
     * @return tempNo
     * @throws Exception
     */
    @GetMapping("/v1/tempNos")
    public Result getTempNo(String code) throws Exception {
        Map result = new HashMap();
        result.put("list", inMaterialDataProtectService.getTempNo(code));
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 多物料模板导出
     *
     * @param request
     * @param response
     * @return Result
     */
    @GetMapping("/materialTemplates")
    @MyPermCompateQz(value = "SYS-MATERIAL-EXPORT")
    @ApiOperation(value = "多物料模板导出", httpMethod = "GET")
    public Result downloadMaterialTemplate(HttpServletRequest request, HttpServletResponse response) {
        InputStream in = null;
        OutputStream out = null;
        try {
            File templatePath = ResourceUtils.getFile(qmsInspectionMaterialPath);
            String fileName = templatePath.getName();
            response.setContentType("application/octet-stream");
            if (ExcelUtils.isLowVersionBrowser(request)) {
                fileName = URLEncoder.encode(fileName, "UTF8");
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + fileName);
            } else {
                response.setHeader("Content-Disposition",
                        "attachment;filename=" + new String((fileName).getBytes("gb2312"), "ISO8859-1"));
            }
            in = new FileInputStream(templatePath);
            out = response.getOutputStream();
            int b;
            while ((b = in.read()) != -1) {
                out.write(b);
            }
            out.flush();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return new Result(ResultCode.FAIL.code(), "找不到模板文件", false);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        }
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 多物料Excel导入
     *
     * @param
     * @return
     */
    @PostMapping("/materialTemplates")
    @MyPermCompateQz(value = "SYS-MATERIAL-IMPORT")
    @ApiOperation(value = "多物料Excel导入", httpMethod = "POST")
    public Result importMaterialData(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 3, 3);
        inMaterialDataProtectService.checkMaterialImport(importList, 3);
        inMaterialDataProtectService.importMaterialData(importList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取检验比率、检验合格率
     *
     * @param
     * @return
     */
    @GetMapping("/ratesAndRatios")
    @ApiOperation(value = " 获取检验比率、检验合格率", httpMethod = "GET")
    public Result getRatesAndRatios(String materialCode, String inspectionType, String supplierCode) throws Exception {
        Map map = new HashMap();
        map.put("materialCode", materialCode);
        map.put("inspectionType", inspectionType);
        map.put("supplierCode", supplierCode);
        Map<String, Object> protectFile = inMaterialDataProtectService.getRatesAndRatios(map);
        return new Result(ResultCode.SUCCESS, protectFile);
    }

    /**
     * 物料-校验基础数据导入模板下载
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/verificationTemplate")
    public void downloadVerificationTemplate(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, qmsVerificationMaterialPath);
    }

    /**
     * 导入物料-校验基础数据Excel
     *
     * @param uploadFile
     * @return
     */
//    @PostMapping("/importVerification")
//    @Transactional(rollbackFor = Exception.class)
//    public Result importVerification(@RequestParam("file") MultipartFile uploadFile) throws Exception {
//        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 6, 3);
//        inMaterialDataProtectService.checkVerificationImport(importList, 3);
//        int dataNum = inMaterialDataProtectService.importVerificationData(importList);
//        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
//    }

    @PostMapping("/importVerification")
    @Transactional(rollbackFor = Exception.class)
    public void importVerification(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ImportUtil.getData(QmsInspectionDataProtectImportVo.class, uploadFile, x -> inMaterialDataProtectService.checkVerificationImport(x));
//        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 6, 3);
//        inMaterialDataProtectService.checkVerificationImport(importList, 3);
//        int dataNum = inMaterialDataProtectService.importVerificationData(importList);
//        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
    }

    /**
     * 物料质检项导入模板下载
     *
     * @param request
     * @param response
     * @return
     */
    @GetMapping("/inspectionTemplate")
    public void downloadInspectionTemplate(HttpServletRequest request, HttpServletResponse response) {
        ExcelUtils.downloadTemplate(request, response, qmsMaterialInspectionPath);
    }


    /**
     * 导入物料质检项Excel
     *
     * @param uploadFile
     * @return
     */
//    @PostMapping("/importMaterialInspection")
//    @Transactional(rollbackFor = Exception.class)
//    public Result importMaterialInspection(@RequestParam("file") MultipartFile uploadFile) throws Exception {
//        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 9, 3);
//        inMaterialDataProtectService.checkMaterialInspection(importList, 3);
//        int dataNum = inMaterialDataProtectService.importMaterialInspection(importList);
//        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
//    }


    @PostMapping("/importMaterialInspection")
    @Transactional(rollbackFor = Exception.class)
    public void importMaterialInspection(@RequestParam("file") MultipartFile uploadFile) throws Exception {
        ImportUtil.getData(QmsInspectionItemsImportVo.class, uploadFile, x -> inMaterialDataProtectService.checkMaterialInspection(x));

//        List<List<Object>> importList = ExcelUtils.getImportData(uploadFile.getInputStream(), uploadFile.getOriginalFilename(), 9, 3);
//        inMaterialDataProtectService.checkMaterialInspection(importList, 3);
//        int dataNum = inMaterialDataProtectService.importMaterialInspection(importList);
//        return new Result(ResultCode.SUCCESS, "成功导入【" + dataNum + "】条数据");
    }




    @PostMapping("/deleteQmsInspectionByMaterialCode")
    @ApiOperation("删除物料时关联删除质量项信息")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteByMaterialCode(@RequestParam("materialCode") String materialCode) {
        inMaterialDataProtectService.deleteByMaterialCode(materialCode);
        return new Result(ResultCode.SUCCESS);
    }

    @PostMapping("/deleteQmsInspectionByMaterialCodeIn")
    @ApiOperation("批量删除物料时关联删除质量项信息")
    @Transactional(rollbackFor = Exception.class)
    public Result deleteQmsInspectionByMaterialCodeIn(@RequestBody List<String> materialCodeList) {
        inMaterialDataProtectService.deleteByMaterialCodeIn(materialCodeList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 是否需要进行检验
     *
     * @param materialCode   物料编码
     * @param inspectionType 检验类型 1:入库检验;2:来料检验;3:出库检验
     * @return Result
     */
    @GetMapping("/needInspection")
    @Transactional(rollbackFor = Exception.class)
    public Result needInspection(@RequestParam("materialCode") String materialCode, @RequestParam("inspectionType") String inspectionType) throws Exception {
        Boolean result = inMaterialDataProtectService.needInspection(materialCode, inspectionType);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 获取委派班组信息
     *
     * @return Result
     */
    @ApiOperation(value = "获取委派班组信息", httpMethod = "GET")
    @GetMapping("/delegateInfo")
    public Result delegateInfo(@RequestParam("teamCode") String teamCode) throws Exception {
        return new Result(ResultCode.SUCCESS, inMaterialDataProtectService.delegateInfo(teamCode));
    }

    /**
     * 获取AQL配置预览
     *
     * @param inspectType  检验方式
     * @param inspectLevel 检验水平
     * @param aqlCode      aql值
     * @return Result
     */
    @ApiOperation(value = "获取AQL配置预览", httpMethod = "GET")
    @GetMapping("/aqlPreview")
    public Result aqlPreview(@RequestParam("inspectType") String inspectType, @RequestParam("inspectLevel") String inspectLevel, @RequestParam("aqlCode") String aqlCode) throws Exception {
        return new Result(ResultCode.SUCCESS, inMaterialDataProtectService.aqlPreview(inspectType, inspectLevel, aqlCode));
    }

    @GetMapping("/processExcel")
    @ApiOperation("导出过程检验报表EXCEL")
    public void processExport(HttpServletRequest request, HttpServletResponse response,
                                String materialCode,String materialName,
                                String specification,String materialMarker,
                                String ppNo,String pcNo,String processCode,String processName,
                                String inspectionStartTime,String inspectionEndTime,
                                String startTime,String endTime) throws Exception {

        Map map = new HashMap();
        map.put("materialCode",materialCode);
        map.put("materialName",materialName);
        map.put("ppNo",ppNo);
        map.put("pcNo",pcNo);
        map.put("processCode",processCode);
        map.put("processName",processName);
        map.put("specification",specification);
        map.put("materialMarker",materialMarker);
        map.put("inspectionStartTime",inspectionStartTime);
        map.put("inspectionEndTime",inspectionEndTime);
        map.put("startTime",startTime);
        map.put("endTime",endTime);
        // 通过
        map.put("process", ConstantUtil.process);
        List<ProcessQualityReportVO> processQualityReportVOs = processQualityReportService.getInspectionList(map);
        List<Map<String,Object>> dataMap = new ArrayList<>();
        for(ProcessQualityReportVO x : processQualityReportVOs){
            map.put("pcNo",x.getPcNo());
            List<ProcessQualityBadCountReportVO> details =  processQualityReportService.getBadCountDetail(map);
            details.forEach(y ->{
                Map<String,Object> item = new HashMap<>();
                item.put("materialCode",x.getMaterialCode());
                item.put("materialName",x.getMaterialName());
                item.put("specification",x.getSpecification());
                item.put("materialMarker",x.getMaterialMarker());
                item.put("ppNo",x.getPpNo());
                item.put("pcNo",x.getPcNo());
                item.put("pcNum",x.getPcNum());
                item.put("goodQty",x.getGoodQty());
                item.put("completePercent",x.getCompletePercent());
                item.put("processCode",y.getProcessCode());
                item.put("processName",y.getProcessName());
                item.put("finisherName",y.getFinisherName());
                item.put("teamName",y.getTeamName());
                item.put("workshopName",y.getWorkshopName());
                item.put("finishTime",y.getFinishTime());
                item.put("wfNo",y.getWfNo());
                item.put("finishNum",y.getFinishNum());
                item.put("finishGoodQty",y.getFinishGoodQty());
                item.put("finishBadQty",y.getFinishBadQty());
                item.put("finisherName",y.getFinisherName());
                item.put("inspectionCode",y.getInspectionCode());
                item.put("inspector",y.getInspector());
                item.put("isQualified",y.getIsQualified());
                dataMap.add(item);
            });
        }
        SysExportTemplateWrap template = feignService.exportTemplateFetch(ConstantUtil.PROCESS_REPORT);
        ExcelUtils.exportExcel(response, request, dataMap, template);
    }
}
