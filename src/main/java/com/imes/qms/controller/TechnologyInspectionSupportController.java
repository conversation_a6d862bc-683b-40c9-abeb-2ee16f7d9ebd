package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.supportMaterialInfoVo;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.TechnologyInspectionSupportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/InspectionSupport")
@Api(tags = "工艺检验提交相关接口")
public class TechnologyInspectionSupportController {

    @Autowired
    TechnologyInspectionSupportService technologyInspectionSupportService;

    /**
     * 获取工艺检验提交列表
     *
     * @return
     */
    @ApiOperation(value = "获取工艺检验提交列表",httpMethod = "GET")
    @GetMapping("/submissions")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String inspectType,String status,String isQualified,
                            String inspectionStartTime,String inspectionEndTime,
                            String materialName,String publishStatus) throws Exception {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("inspectionType",inspectType);
            map.put("isQualified",isQualified);
            map.put("status",status);
            map.put("publishStatus",publishStatus);
            map.put("materialName",materialName);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",technologyInspectionSupportService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 选择物料
     *
     * @return
     */
    @ApiOperation(value = "选择物料",httpMethod = "GET")
    @GetMapping("/materials")
    public Result getMaterialInfo(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                  @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                  String materialName) {
        try {
            Map map = new HashMap();
            Map reMap=new HashMap();
            map.put("materialName",materialName);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",technologyInspectionSupportService.getMaterialInfo(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("TechnologyInspectionSupportController.getMaterialInfo出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 当前提交人
     *
     * @return
     */
    @ApiOperation(value = "当前提交人",httpMethod = "GET")
    @GetMapping("/users")
    public Result getUserCode() {
        try {
            return new Result(ResultCode.SUCCESS,technologyInspectionSupportService.getUserCode());
        } catch (Exception e) {
            log.error("TechnologyInspectionSupportController.getMaterialInfo出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据生产单号获取工艺检验信息
     *
     * @return
     */
    @ApiOperation(value = "根据排产单号获取工艺检验信息",httpMethod = "GET")
    @GetMapping("/inspections")
    public Result getInspectionInfo(@RequestParam(value = "ppNo") String ppNo,@RequestParam(value = "pcNo") String pcNo) {
        try {
            return new Result(ResultCode.SUCCESS,technologyInspectionSupportService.getInspectionInfo(ppNo,pcNo));
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据首末检验id获取检验项详细信息
     *
     * @return
     */
    @ApiOperation(value = "根据首末检验id获取检验项详细信息",httpMethod = "GET")
    @GetMapping("/v1/inspectionDetails")
    public Result getInspectionDetails(@RequestParam(value = "patrolId") String patrolId) {
        try {
            return new Result(ResultCode.SUCCESS,technologyInspectionSupportService.getInspectionDetails(patrolId));
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
//
//    /**
//     * 根据工序号和生产号获取质检信息
//     *
//     * @return
//     */
//    @ApiOperation(value = "根据工序号和生产号获取质检信息和工位",httpMethod = "GET")w
//    @GetMapping("/getQmsRoute")
//    public Result getQmsRoute(String routeCode,String processCode) {
//        try {
//            return new Result(ResultCode.SUCCESS,technologyInspectionSupportService.getQmsRoute(routeCode,processCode));
//        } catch (Exception e) {
//            log.error("MaterialInspectionController.queryList出错",e);
//            return new Result(ResultCode.FAIL,e.getMessage());
//        }
//    }

    /**
     * 提交
     *
     * @return
     */
    @ApiOperation(value = "提交",httpMethod = "POST")
    @PostMapping ("/saveInspectionDatas")
    public Result saveInspectionDatas(@RequestBody TechnologyInspection data) throws Exception {
        Map reMap=new HashMap();
        technologyInspectionSupportService.saveInspectionDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 暂存
     *
     * @return
     */
    @ApiOperation(value = "暂存",httpMethod = "POST")
    @PostMapping ("/temporarySave")
    public Result temporarySave(@RequestBody TechnologyInspection data) throws Exception {
        Map reMap=new HashMap();
        technologyInspectionSupportService.temporarySave(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 删除
     *
     * @return
     */
    @ApiOperation(value = "删除",httpMethod = "DELETE")
    @DeleteMapping("/supports")
    public Result deleteSupport(String id) {
        Map reMap=new HashMap();
        technologyInspectionSupportService.deleteSupport(id);
        return new Result(ResultCode.SUCCESS,reMap);
    }

}
