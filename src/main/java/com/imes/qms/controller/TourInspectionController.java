package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RecordUtils;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.qms.po.TourTask;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewVo;
import com.imes.domain.entities.query.template.qms.GetTourListVo;
import com.imes.qms.dao.TourTaskDao;
import com.imes.qms.service.TourInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/tourInspection")
@Api(tags = "新巡检相关接口")
public class TourInspectionController {

    @Autowired
    TourTaskDao dao;
    @Autowired
    TourInspectionService service;


    /**
     * 获取生产单号信息（标准版）
     *
     * @return
     */
    @ApiOperation(value = "获取生产单号信息（高级查询版本）", httpMethod = "GET")
    @GetMapping("/getList")
    public Result getList(GetTourListVo vo) throws Exception {
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setUnInspection(ConstantUtil.un_inspection);
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<GetTourListVo> list = service.getList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        Map reMap = new HashMap();
        reMap.put("list", service.queryByActivtiId(activitiId));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 反提交按钮
     *
     * @return Result
     */
    @ApiOperation(value = "反提交按钮", httpMethod = "GET")
    @GetMapping("/unsubmission")
    public Result unsubmission(String id) throws Exception {
        service.unsubmission(id);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 获取巡检检验条目详情(移动端)
     *
     * @return
     */
    @ApiOperation(value = "获取巡检检验条目详情(移动端)", httpMethod = "GET")
    @GetMapping("/queryDetailListByMobile")
    public Result queryDetailList(String id) {
        Map reMap = new HashMap();
        List<TourInspectionInfoVo> tourInspectionInfoVos = service.queryDetailList(id);
        List<TourInspectionInfoVo> collect = tourInspectionInfoVos.stream().sorted((u1, u2) -> new Long(u2.getMain().getLatestInspectionTime().getTime()).intValue() - new Long(u1.getMain().getLatestInspectionTime().getTime()).intValue()).collect(Collectors.toList());
        TourInspectionInfoVo tourInspectionInfoVo = collect.get(0);
        reMap.put("vo", tourInspectionInfoVo);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取巡检检验条目详情(pc端)
     *
     * @return
     */
    @ApiOperation(value = "获取巡检检验条目详情(pc端)", httpMethod = "GET")
    @GetMapping("/queryDetailListByPc")
    public Result queryDetailListByPc(String id) {
        Map reMap = new HashMap();
        reMap.put("list", service.queryDetailList(id));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 生成质检任务
     *
     * @return
     */
    @ApiOperation(value = "生成质检任务", httpMethod = "POST")
    @PostMapping("/createTourTask")
    public Result createToutTask(@RequestBody List<TourTask> task) throws Exception {
        service.createToutTask(task);
        return Result.SUCCESS(new PageResult());
    }

    /**
     * 生成质检任务
     *
     * @return
     */
    @ApiOperation(value = "删除质检任务", httpMethod = "POST")
    @PostMapping("/deleteTask")
    public Result deleteTask(@RequestBody List<String> ids) throws Exception {
        service.deleteTask(ids);
        return Result.SUCCESS(new PageResult());
    }

    /**
     * 根据生产单号获取检验项信息（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "根据生产单号获取检验项信息（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItemsByPpNoInfo")
    public Result getInspectionItemsStd(String routeCode, String processCode, String bomCode, String id, int batchNum) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.getInspectionItemsStd(routeCode, processCode, bomCode, id, batchNum));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据排产单号和工序编码获取检验信息
     *
     * @return
     */
    @ApiOperation(value = "根据排产单号和工序编码获取检验信息", httpMethod = "GET")
    @GetMapping("/queryInfoByWfNo")
    public Result queryInfoByWfNo(@RequestParam("pcNo") String pcNo, @RequestParam("processCode") String processCode) {
        Map reMap = new HashMap();
        reMap.put("list", service.queryInfoByWfNo(pcNo, processCode));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * mom-open-api接口
     *
     * @return
     */
    @ApiOperation(value = "mom-open-api接口", httpMethod = "POST")
    @PostMapping("/queryTourInfoByVo")
    public Result queryTourInfoByVo(@RequestBody TourInforVo vo) throws Exception {
        List<TourResultVo> list = service.queryTourInfoByVo(vo);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     * 根据任务id查找到显示结果对象（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "根据任务id查找到显示结果对象（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionById")
    public Result getInspectionById(String id) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.getInspectionById(id));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 暂存检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "暂存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody TourInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        service.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", service.queryDetailListByOrderId(orderId));
        // 设置该任务认领人
        TourTask task = new TourTask();
        task.setId(orderId);
        task.setClaimCode(RedisUtils.getUserCode());
        task.setClaimName(RedisUtils.getUserName());
        RecordUtils.updateData(task);
        dao.updateClaim(task);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 提交检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "提交检验详细数据", httpMethod = "POST")
    @PostMapping("/supportDatas")
    public Result supportDatas(@RequestBody TourInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        service.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 委派功能显示隐藏
     *
     * @return Result
     */
    @ApiOperation(value = "委派功能显示隐藏", httpMethod = "GET")
    @GetMapping("/getDelegate")
    public Result getDelegate() throws Exception {
        boolean result = service.getDelegate();
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 委派人员和部门
     *
     * @return Result
     */
    @ApiOperation(value = "委派人员和部门", httpMethod = "GET")
    @GetMapping("/delegateUserList")
    public Result delegateUserList() throws Exception {
        return new Result(ResultCode.SUCCESS, service.delegateUserList());
    }

    /**
     * 委派的确定按钮
     *
     * @return Result
     */
    @ApiOperation(value = "委派的确定按钮", httpMethod = "GET")
    @GetMapping("/delegateConfirmation")
    public Result delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag) throws Exception {
        return new Result(ResultCode.SUCCESS, service.delegateConfirmation(id, userCode, userName, teamCode, teamName, addFlag));
    }

    /**
     * 取消认领
     *
     * @return Result
     */
    @ApiOperation(value = "取消认领", httpMethod = "GET")
    @GetMapping("/cancelClaim")
    public Result cancelClaim(String id) throws Exception {
        return new Result(ResultCode.SUCCESS, service.cancelClaim(id));
    }
}
