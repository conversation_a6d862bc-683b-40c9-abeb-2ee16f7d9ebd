package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.OutBillInspectionDetail;
import com.imes.domain.entities.qms.po.ReturnBillInspectionDetail;
import com.imes.domain.entities.qms.vo.MaterialInspectionInfoVo;
import com.imes.domain.entities.qms.vo.OutBillInspectionInfoVo;
import com.imes.domain.entities.qms.vo.ReturnBillInspectionInfoVo;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.OutBillInspectionService;
import com.imes.qms.service.ReturnBillInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/returnBillInspection")
@Api(tags = "退货检验相关接口")
public class ReturnBillInspectionController {

    @Autowired
    ReturnBillInspectionService returnBillInspectionService;

    @Autowired
    MaterialInspectionService materialInspectionService;

    /**
     * 获取退库检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取退库检验列表信息",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String arrivalOrder,String buyOrder,String materialCode,
                            String materialName,String inspectionCode,
                            String inspectionStartTime,String inspectionEndTime,
                            String inspectionStatus,String status) {
        try {
            Map reMap=new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("arrivalOrder",arrivalOrder);
            map.put("buyOrder",buyOrder);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("inspectionCode",inspectionCode);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("inspectionStatus",inspectionStatus);
            map.put("status",status);
            map.put("ending",ConstantUtil.ending);
            map.put("unSupport",ConstantUtil.un_support);
            reMap.put("list",returnBillInspectionService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ReturnBillInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 根据物料编码获取检验项
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码获取检验项",httpMethod = "GET")
    @GetMapping("/getInspectionByMaterialCode")
    public Result getInspectionByMaterialCode(String materialCode, String inspectionType, BigDecimal batchNum) {
        Map reMap=new HashMap();
        int num = batchNum.intValue();
        reMap.put("list",materialInspectionService.getInspectionByMaterialCode(materialCode,inspectionType,null,num));
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @SneakyThrows
    @ApiOperation(value = "根据采购单号获取物料信息",httpMethod = "GET")
    @GetMapping("/getMaterialByOrderNo")
    public Result getMaterialByOrderNo(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                       @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                       String arrivalOrder,String materialCode,String materialName,
                                       String inspectionStatus,String buyOrder,String category,
                                       String customerName,String startTime,String endTime) {
        Map reMap=new HashMap();
        Map map = new HashMap();
        map.put("arrivalOrder",arrivalOrder);
        map.put("buyOrder",buyOrder);
        map.put("materialCode",materialCode);
        map.put("materialName",materialName);
        map.put("inspectionStatus",inspectionStatus);
        map.put("category",category);
        map.put("customerName",customerName);
        map.put("startTime",startTime);
        map.put("endTime",endTime);
        map.put("orderType", ConstantUtil.returned);
        map.put("inspectionType", ConstantUtil.outBill);
        map.put("temporarySave",ConstantUtil.temporary_save);
        map.put("reject",ConstantUtil.has_reject);
        map.put("ending",ConstantUtil.ending);
        Page page = PageHelper.startPage(pageNum, pageSize);
        reMap.put("list",returnBillInspectionService.getMaterialByOrderNo(map));
        reMap.put("total",page.getTotal());
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 暂存检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "暂存检验详细数据",httpMethod = "POST")
    @PostMapping ("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody ReturnBillInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        returnBillInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 提交检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "提交检验详细数据",httpMethod = "POST")
    @PostMapping ("/supportDatas")
    public Result supportDatas(@RequestBody ReturnBillInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        returnBillInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 获取来料检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",returnBillInspectionService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ReturnBillInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情",httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",returnBillInspectionService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ReturnBillInspectionController.queryByActivtiId出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",returnBillInspectionService.queryDetailListByOrderId(orderId));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("ReturnBillInspectionController.queryDetailListByOrderId出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     //     * 编辑修改检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "编辑修改检验详细数据",httpMethod = "PUT")
    @PutMapping ("/updateInspectionDetailData")
    public Result updateInspectionDetailData(ReturnBillInspectionDetail data) {
        Map reMap=new HashMap();
        returnBillInspectionService.updateInspectionDetailData(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 提交检验条目
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "提交检验条目",httpMethod = "PUT")
    @PutMapping ("/updateStatus")
    public Result updateStatus(String id) throws Exception {
        Map reMap=new HashMap();
        returnBillInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 更改图片状态
     *
     * @return
     */
    @ApiOperation(value = "更改图片状态",httpMethod = "PUT")
    @PutMapping ("/updatePicStatus")
    public Result updatePicStatus(String id,String picStatus) throws Exception {
        Map reMap=new HashMap();
        returnBillInspectionService.updatePicStatus(id,picStatus);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    @GetMapping("/findAoByFilter")
    public Result findAoByFilter(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                 @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                 @RequestParam(required=false, value = "prop") String prop,
                                 @RequestParam(required=false, value = "order") String order,
                                 @RequestParam Map map){
        Map result = new HashMap();
        Page page = PageHelper.startPage(pageNum, pageSize);
        map.put("orderStatus","10");
        StringBuffer sortStr = new StringBuffer("");
        if (!StringUtils.isNullOrBlank(prop) && !StringUtils.isNullOrBlank(order))
            sortStr.append(StringUtils.humpToLine(prop)+" "+order+", ");

        sortStr.append("update_on desc, ao_code asc ");
        PageHelper.orderBy(sortStr.toString());

        result.put("list", returnBillInspectionService.findAoByFilter(map));
        result.put("total", page.getTotal());

        return new Result(ResultCode.SUCCESS, result);
    }

}
