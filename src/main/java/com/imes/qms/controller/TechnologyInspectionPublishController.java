package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.qms.service.TechnologyInspectionPublishService;
import com.imes.qms.service.TechnologyInspectionSupportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/InspectionPublish")
@Api(tags = "工艺检验发布相关接口")
public class TechnologyInspectionPublishController {

    @Autowired
    TechnologyInspectionPublishService technologyInspectionPublishService;

    /**
     * 获取工艺检验计划
     *
     * @return
     */
    @ApiOperation(value = "获取工艺检验计划",httpMethod = "GET")
    @GetMapping("/getInspectionPlanList")
    public Result getInspectionPlanList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                        String inspectType,String publishStatus,String planCode,
                                        String ppNo,String materialCode,String materialName,
                                        String startPlanStartTime,String startPlanEndTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("inspectType",StringUtils.str2In(inspectType));
            map.put("publishStatus",publishStatus);
            map.put("planCode",planCode);
            map.put("ppNo",ppNo);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("startPlanStartTime",startPlanStartTime);
            map.put("startPlanEndTime",startPlanEndTime);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",technologyInspectionPublishService.getInspectionPlanList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("TechnologyInspectionPublishController.getInspectionPlanList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改发布状态
     *
     * @return
     */
    @ApiOperation(value = "修改发布状态",httpMethod = "POST")
    @PostMapping("/updatePublishStatus")
    public Result updatePublishStatus(@RequestBody TechnologyInspection inspection) throws Exception {
        Map reMap=new HashMap();
        technologyInspectionPublishService.updatePublishStatus(inspection);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 获取检验子项
     *
     * @return
     */
    @ApiOperation(value = "获取检验子项",httpMethod = "GET")
    @GetMapping("/getInspectionItemDetail")
    public Result getInspectionItemDetail(String bomCode,String bomVer,String processCode) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",technologyInspectionPublishService.getInspectionItemDetail(bomCode,bomVer,processCode));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("TechnologyInspectionPublishController.getInspectionItemDetail出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
