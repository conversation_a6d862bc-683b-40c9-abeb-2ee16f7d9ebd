package com.imes.qms.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.imes.common.utils.AssertUtil;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.QmsFillCheckNewData;
import com.imes.domain.entities.qms.vo.QmsWorkInspectBulidVo;
import com.imes.domain.entities.system.base.SysFile;
import com.imes.qms.feignClient.FeignService;
import com.imes.qms.feignClient.PpcClient;
import com.imes.qms.service.FtsInterfaceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.entity.StatPageResult;
import com.imes.domain.entities.qms.po.QmsWorkInspect;
import com.imes.qms.service.QmsWorkInspectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;
import com.imes.domain.entities.query.template.qms.QmsWorkInspectSearchVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 异常提报单-主表-志特(QmsWorkInspect)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-13 09:45:57
 */
@RestController
@RequestMapping("/api/qms/qmsWorkInspect")
@Api(tags = "异常提报单-主表-志特")
public class QmsWorkInspectController {
    /**
     * 服务对象
     */
    @Autowired
    private QmsWorkInspectService qmsWorkInspectService;

    @Autowired
    private FtsInterfaceService ftsInterfaceService;

    @Autowired
    private FeignService feignService;

    @Autowired
    private PpcClient ppcClient;



    /**
     * 通过主键查询单条数据
     *
     * @return 单条数据
     */
    @GetMapping("/queryById")
    @ApiOperation(value = "通过主键查询单条数据", httpMethod = "GET")
    public Result queryById(String id) {
        return new Result(ResultCode.SUCCESS, (qmsWorkInspectService.getById(id)));
    }

    /**
     * 新增数据
     *
     * @param qmsWorkInspect 实体
     * @return 新增结果
     */
    @PostMapping("/insert")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result insert(@RequestBody QmsWorkInspect qmsWorkInspect) throws Exception {
        String result = qmsWorkInspectService.insert(qmsWorkInspect);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 全检调整新增/更新
     *
     * @param qmsWorkInspect 实体
     * @return 新增结果
     */
    @PostMapping("/inspectModifySave")
    @ApiOperation(value = "新增", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result inspectModifySave(@RequestBody QmsWorkInspectSearchVo qmsWorkInspect) throws Exception {
        String result = qmsWorkInspectService.inspectModifySave(qmsWorkInspect);
        if ("".equals(result)) {
            return new Result(ResultCode.FAIL);
        } else {
            return new Result(ResultCode.SUCCESS, result);
        }
    }

    /**
     * 全检调整提交
     *
     * @param idList 实体
     * @return 提交结果
     */
    @PostMapping("/inspectModifyCommit")
    @ApiOperation(value = "提交", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result inspectModifyCommit(@RequestBody List<String> idList) throws Exception {
        String message = qmsWorkInspectService.inspectModifyCommit(idList);
        if (StringUtils.isNullOrBlank(message)) {
            return new Result(ResultCode.SUCCESS);
        }
        return new Result(ResultCode.SUCCESS.code(), message, true);
    }

    /**
     * 编辑数据
     *
     * @param qmsWorkInspect 实体
     * @return 编辑结果
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result update(@RequestBody QmsWorkInspect qmsWorkInspect) throws Exception {
        if (qmsWorkInspectService.update(qmsWorkInspect) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }
    }

    /**
     * 删除数据
     *
     * @return 删除是否成功
     */
    @GetMapping("/batchDelete")
    @ApiOperation(value = "批量删除", httpMethod = "GET")
    @Transactional(rollbackFor = Exception.class)
    public Result batchDelete(String ids) throws Exception {
        List<String> idList = Arrays.asList(ids.split(","));
        if (qmsWorkInspectService.batchDelete(idList) > 0) {
            return new Result(ResultCode.SUCCESS, 1);
        } else {
            return new Result(ResultCode.FAIL);
        }

    }


    @GetMapping("/queryList")
    @ApiOperation(value = "高级查询列表")
    @Transactional(rollbackFor = Exception.class)
    public Result queryList(QmsWorkInspectSearchVo vo) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsWorkInspectSearchVo> list = qmsWorkInspectService.queryList(vo);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/searchWorkOrders")
    @ApiOperation(value = "入库检验-新增-关键字查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchWorkOrders(@RequestParam(defaultValue = "") String keyWord) {
            return Result.SUCCESS(qmsWorkInspectService.searchWorkOrdersByKeyWord(keyWord));
    }

    @GetMapping("/searchBySnCode")
    @ApiOperation(value = "通过SN码查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchBySnCode(@RequestParam(defaultValue = "") String code) {
        return Result.SUCCESS(qmsWorkInspectService.searchBySnCode(code));
    }

    @PostMapping("/buildInspectReport")
    @ApiOperation(value = "入库检验-新增-生成填报单")
    @Transactional(rollbackFor = Exception.class)
    public Result buildInspectReport(@RequestBody List<QmsWorkInspect> qmsWorkInspectList ) {
        qmsWorkInspectService.buildInspectReport(qmsWorkInspectList);
        return Result.SUCCESS();

    }

    @GetMapping("/getStatuNumInfor")
    @ApiOperation(value = "入库检验-状态数量")
    @Transactional(rollbackFor = Exception.class)
    public Result getStatuNumInfor(@RequestParam(defaultValue = "") String keyWord , @RequestParam(defaultValue = "") String factoryLineName) {
            return Result.SUCCESS(qmsWorkInspectService.getStatuNumInfor(keyWord ,factoryLineName));
    }

    @GetMapping("/getScanStatuNumInfor")
    @ApiOperation(value = "入库检验-状态数量")
    @Transactional(rollbackFor = Exception.class)
    public Result getScanStatuNumInfor(@RequestParam(defaultValue = "") String keyWord , @RequestParam(defaultValue = "") String factoryLineName) {
        return Result.SUCCESS(qmsWorkInspectService.getScanStatuNumInfor(keyWord ,factoryLineName));
    }
    @GetMapping("/searchHandleWorkOrders")
    @ApiOperation(value = "入库检验-关键字查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searchHandleWorkOrders(@RequestParam(defaultValue = "") String keyWord , @RequestParam(defaultValue = "") String status  , @RequestParam(defaultValue = "") String factoryLineName , @RequestParam(defaultValue = "1")  Integer pageNum ,  @RequestParam(defaultValue = "10")  Integer pageSize) {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<QmsWorkInspect> list = qmsWorkInspectService.searchHandleWorkOrders(keyWord, status, factoryLineName);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/searchWorkOrdersForView")
    @ApiOperation(value = "入库检验-关键字查询")
    @Transactional(rollbackFor = Exception.class)
    public Result searcWorkOrdersForView(@RequestParam(defaultValue = "") String keyWord , @RequestParam(defaultValue = "") String status  , @RequestParam(defaultValue = "") String factoryLineName ,
                                         @RequestParam(defaultValue = "") String startDate  , @RequestParam(defaultValue = "") String endDate ,
                                         @RequestParam(defaultValue = "1")  Integer pageNum ,  @RequestParam(defaultValue = "10")  Integer pageSize) throws Exception {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<QmsWorkInspect> list = qmsWorkInspectService.searchWorkOrdersForView(keyWord, status, factoryLineName,startDate,endDate);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }

    @GetMapping("/scanHandleWorkOrders")
    @ApiOperation(value = "入库检验-关键字查询(扫码)")
    @Transactional(rollbackFor = Exception.class)
    public Result scanHandleWorkOrders(@RequestParam(defaultValue = "") String keyWord , @RequestParam(defaultValue = "") String status  , @RequestParam(defaultValue = "") String factoryLineName , @RequestParam(defaultValue = "1")  Integer pageNum ,  @RequestParam(defaultValue = "10")  Integer pageSize) {
        Map<String, Object> map = new HashMap<>();
        //如有需要计算合计对map进行赋值
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<QmsWorkInspect> list = qmsWorkInspectService.scanHandleWorkOrders(keyWord, status, factoryLineName);
        return Result.SUCCESS(new StatPageResult(page.getTotal(), list, map));
    }


    @PostMapping("/cancelCheckReport")
    @ApiOperation(value = "入库检验-检验取消")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelCheckReport(@RequestBody Map<String , Object> map) {
        qmsWorkInspectService.cancelCheckReport(map);
        return Result.SUCCESS();
    }

    @PostMapping("/saveCheckReport")
    @ApiOperation(value = "入库检验-保存")
    @Transactional(rollbackFor = Exception.class)
    public Result saveCheckReport(@RequestBody List<String> idList) {
        qmsWorkInspectService.saveCheckReport(idList);
        return Result.SUCCESS();
    }

    @PostMapping("/submitCheckReport")
    @ApiOperation(value = "入库检验-提交")
    @Transactional(rollbackFor = Exception.class)
    public Result submitCheckReport(@RequestBody List<String> idList) {
        qmsWorkInspectService.submitCheckReport(idList);
        return Result.SUCCESS();
    }

    @PostMapping("/buildException")
    @ApiOperation(value = "入库检验-填报异常单据")
    @Transactional(rollbackFor = Exception.class)
    public Result buildException(@RequestBody QmsWorkInspectBulidVo vo) {
        qmsWorkInspectService.buildException(vo);
        return Result.SUCCESS();
    }

    @PostMapping("/cancelSubmit")
    @ApiOperation(value = "入库检验-反审核")
    @Transactional(rollbackFor = Exception.class)
    public Result cancelSubmit(@RequestBody List<QmsWorkInspect> inspects) {
        qmsWorkInspectService.cancelSubmit(inspects);
        return Result.SUCCESS();
    }

    @GetMapping("/getExceptionInfor")
    @ApiOperation(value = "入库检验-查询异常表单")
    @Transactional(rollbackFor = Exception.class)
    public Result getExceptionInfor(@RequestParam String  inspectNo , @RequestParam String lineNo) {
        return Result.SUCCESS( qmsWorkInspectService.getExceptionInfor(inspectNo,lineNo));
    }

    @GetMapping("/getDetialInfor")
    @ApiOperation(value = "入库检验-详情")
    @Transactional(rollbackFor = Exception.class)
    public Result getDetialInfor(@RequestParam String  id) {
        return Result.SUCCESS( qmsWorkInspectService.getDetialInfor(id));
    }

    /**
     * 批量上传图片
     */
    @ApiOperation("批量上传图片")
    @PostMapping("/fileBatchUpload")
    @Transactional(rollbackFor = Exception.class)
    public Result fileBatchUpload(@RequestBody JSONObject o) throws Exception {
        Result result = ftsInterfaceService.batchUpload(o);
        if (result == null || !result.isSuccess()) {
            AssertUtil.throwException("调用文件服务失败，上传文件失败");
        }
        List<SysFile> files = (List<SysFile>) result.getData();
        return new Result(ResultCode.SUCCESS, files);
    }

    @ApiOperation("工控机-入库质检新增-查询")
    @GetMapping("/ics/searchWorkOrders")
    @Transactional(rollbackFor = Exception.class)
    public Result icsSearchWorkOrders(@RequestParam(defaultValue = "") String keyWord,
                                      @RequestParam(defaultValue = "10") Integer pageSize , @RequestParam(defaultValue = "0")  Integer pageNum,
                                      @RequestParam(required = false) String productNo,@RequestParam(required = false) String productShortName,
                                      @RequestParam(required = false) String build,@RequestParam(required = false) String orderNo

    ) throws Exception {
        return ppcClient.icsSearchWorkOrders(keyWord , pageSize , pageNum,productNo,productShortName,build,orderNo);
    }

    @ApiOperation("工控机-入库质检新增-详情查询")
    @GetMapping("/ics/getDetail")
    @Transactional(rollbackFor = Exception.class)
    public Result icsGetDetail(@RequestParam String id) throws Exception {
        return new Result(ResultCode.SUCCESS ,feignService.queryByAztOrderIdList(Lists.newArrayList(id)));
    }



    /**
     * 批量上传图片
     */
    @ApiOperation("全检")
    @PostMapping("/fillCheck")
    @Transactional(rollbackFor = Exception.class)
    public Result fillCheck(@RequestBody List<String> idList) throws Exception {
        qmsWorkInspectService.fillCheck(idList);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 批量上传图片
     */
    @ApiOperation("全检-新增改造")
    @PostMapping("/fillCheckNew")
    @Transactional(rollbackFor = Exception.class)
    public Result fillCheckNew(@RequestBody QmsFillCheckNewData qmsFillCheckNewData) throws Exception {
        qmsWorkInspectService.fillCheckNew(qmsFillCheckNewData);
        return new Result(ResultCode.SUCCESS);
    }



    /**
     * 批量上传图片
     */
    @ApiOperation("SN码生产检验单")
    @GetMapping("/snFillCheck")
    @Transactional(rollbackFor = Exception.class)
    public Result snFillCheck(@RequestParam String planId) throws Exception {
        qmsWorkInspectService.snFillCheck(planId);
        return new Result(ResultCode.SUCCESS);
    }

    @ApiOperation("返回检验单是否进行中")
    @PostMapping("/getInspectOrderStatus")
    @Transactional(rollbackFor = Exception.class)
    public Result getInspectOrderStatus(@RequestBody List<String> orderIdList) throws Exception {
        return new Result(ResultCode.SUCCESS,qmsWorkInspectService.getInspectOrderStatus(orderIdList));
    }

    @ApiOperation("获取异常处置ID信息")
    @GetMapping("/getExceptionIdList")
    @Transactional(rollbackFor = Exception.class)
    public Result getExceptionIdList(@RequestParam String inspectNo) throws Exception {
        return new Result(ResultCode.SUCCESS,qmsWorkInspectService.getExceptionIdList(inspectNo));
    }

    @ApiOperation("查询待计件计算的检验数据")
    @GetMapping("/queryUnCalcEndWorkInspect")
    public Result queryUnCalcEndWorkInspect() throws Exception {
        return new Result(ResultCode.SUCCESS, qmsWorkInspectService.queryUnCalcEndWorkInspect());
    }

    @ApiOperation("更新计件计算结果")
    @PostMapping("/updateInspectByCalc")
    public Result updateInspectByCalc(@RequestBody QmsWorkInspect qmsWorkInspect) throws Exception {
        qmsWorkInspectService.updateInspectByCalc(qmsWorkInspect);
        return new Result(ResultCode.SUCCESS);
    }

    @ApiOperation("批量更新计件计算为重算0")
    @PostMapping("/updateInspectReCalc")
    @Transactional(rollbackFor = Exception.class)
    public Result updateInspectReCalc(@RequestBody List<String> reCalcWfNoList) throws Exception {
        qmsWorkInspectService.updateInspectReCalc(reCalcWfNoList);
        return new Result(ResultCode.SUCCESS);
    }

    @ApiOperation("批量更新计件计算为重算0")
    @PostMapping("/checkIsCalcByQualityNoList")
    @Transactional(rollbackFor = Exception.class)
    public Result checkIsCalcByQualityNoList(@RequestBody List<String> reCalcWfNoList) throws Exception {
        boolean b = qmsWorkInspectService.checkIsCalcByQualityNoList(reCalcWfNoList);
        return new Result(ResultCode.SUCCESS, b);
    }

    /**
     * 全检反结束
     *
     * @param idList 实体
     * @return 提交结果
     */
    @PostMapping("/inspectModifyEndBack")
    @ApiOperation(value = "反结束", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result inspectModifyEndBack(@RequestBody List<String> idList) throws Exception {
        String message = qmsWorkInspectService.inspectModifyEndBack(idList);
        if (StringUtils.isNullOrBlank(message)) {
            return new Result(ResultCode.SUCCESS);
        }
        return new Result(ResultCode.SUCCESS.code(), message, true);
    }

    /**
     * 全检反取消
     *
     * @param idList 实体
     * @return 提交结果
     */
    @PostMapping("/inspectModifyCancelBack")
    @ApiOperation(value = "反取消", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public Result inspectModifyCancelBack(@RequestBody List<String> idList) throws Exception {
        qmsWorkInspectService.inspectModifyCancelBack(idList);
        return new Result(ResultCode.SUCCESS);
    }
}

