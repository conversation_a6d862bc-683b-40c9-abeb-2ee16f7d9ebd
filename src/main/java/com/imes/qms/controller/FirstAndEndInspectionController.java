package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.qms.po.TechnologyInspection;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.GetPlanTasksVo;
import com.imes.qms.dao.TechnologyInspectionSupportDao;
import com.imes.qms.service.FirstAndEndInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

// 解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@Api(tags = "首末检验相关接口")
@RequestMapping(value = "/api/qms/FirstAndEndInspection")
public class FirstAndEndInspectionController {

    @Autowired
    FirstAndEndInspectionService firstAndEndInspectionService;

    @Autowired
    TechnologyInspectionSupportDao technologyInspectionSupportDao;


    /**
     * 获取首末检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取首末检验列表信息", httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(value = "pageNum", defaultValue = "1") int pageNum,
                            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                            String ppNo, String materialName, String processName,
                            String inspectionCode, String inspectionStartTime, String inspectionEndTime,
                            String inspectionStatus, String status, String pcNo, String materialCode) {
        try {
            Map reMap = new HashMap();
            Map map = new HashMap();
            map.put("ppNo", ppNo);
            map.put("pcNo", pcNo);
            map.put("materialName", materialName);
            map.put("materialCode", materialCode);
            map.put("processName", processName);
            map.put("inspectionCode", inspectionCode);
            map.put("startTime", inspectionStartTime);
            map.put("endTime", inspectionEndTime);
            map.put("inspectionStatus", inspectionStatus);
            map.put("status", status);
            map.put("ending", ConstantUtil.ending);
            map.put("unSupport", ConstantUtil.un_support);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", firstAndEndInspectionService.queryList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("FirstAndEndInspectionController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }


    /**
     * 获取检验任务列表
     *
     * @return
     */
    @ApiOperation(value = "获取检验任务列表", httpMethod = "GET")
    @GetMapping("/getPlanTasks")
    public Result getPlanTasks(GetPlanTasksVo vo) throws Exception {
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setPublishStatus(ConstantUtil.firstFinal_unInspection);
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<GetPlanTasksVo> planTasks = firstAndEndInspectionService.getPlanTasks(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), planTasks));
    }

    /**
     * 反提交按钮
     *
     * @return Result
     */
    @ApiOperation(value = "反提交按钮", httpMethod = "GET")
    @GetMapping("/unsubmission")
    public Result unsubmission(String orderId) throws Exception {
        firstAndEndInspectionService.unsubmission(orderId);
        return new Result(ResultCode.SUCCESS);
    }


    /**
     * 根据生产信息获取检验项信息（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "根据生产信息获取检验项信息（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItems")
    public Result getInspectionItemsStd(String routeCode, String processCode, String bomCode, String inspectType, int batchNum, String id) {
        try {
            Map reMap = new HashMap();
//            Map<String,String> map = new HashMap<>();
//            routeCode="RL220119001";
//            processCode="S2";
//            bomCode="BOMKF01";
            reMap.put("list", firstAndEndInspectionService.getInspectionItemsStd(routeCode, processCode, bomCode, inspectType, batchNum, id));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("ProcessInspectionController.getInspectionItemsByPpNoInfo出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 取消认领
     *
     * @return Result
     */
    @ApiOperation(value = "取消认领", httpMethod = "GET")
    @GetMapping("/cancelClaim")
    public Result cancelClaim(String id) throws Exception {
        return new Result(ResultCode.SUCCESS, firstAndEndInspectionService.cancelClaim(id));
    }

    /**
     * 委派功能显示隐藏
     *
     * @return Result
     */
    @ApiOperation(value = "委派功能显示隐藏", httpMethod = "GET")
    @GetMapping("/getDelegate")
    public Result getDelegate() throws Exception {
        boolean result = firstAndEndInspectionService.getDelegate();
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 委派人员和部门
     *
     * @return Result
     */
    @ApiOperation(value = "委派人员和部门", httpMethod = "GET")
    @GetMapping("/delegateUserList")
    public Result delegateUserList() throws Exception {
        return new Result(ResultCode.SUCCESS, firstAndEndInspectionService.delegateUserList());
    }

    /**
     * 委派的确定按钮
     *
     * @return Result
     */
    @ApiOperation(value = "委派的确定按钮", httpMethod = "GET")
    @GetMapping("/delegateConfirmation")
    public Result delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag) throws Exception {
        return new Result(ResultCode.SUCCESS, firstAndEndInspectionService.delegateConfirmation(id, userCode, userName, teamCode, teamName, addFlag));
    }

    /**
     //     * 保存检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "保存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody FirstAndEndInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        firstAndEndInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * mom-open-api接口
     *
     * @return
     */
    @ApiOperation(value = "mom-open-api接口", httpMethod = "POST")
    @PostMapping("/queryFirstAndEndInfoByVo")
    public Result queryFirstAndEndInfoByVo(@RequestBody FirstAndEndInforVo vo) throws Exception {
        List<FirstAndEndResultVo> list = firstAndEndInspectionService.queryFirstAndEndInfoByVo(vo);
        return new Result(ResultCode.SUCCESS, list);
    }

    /**
     * 获取过程检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", firstAndEndInspectionService.queryDetailList(id));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("ProcessInspectionController.queryDetailList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", firstAndEndInspectionService.queryDetailListByOrderId(orderId));
            // 修改 认领人字段
            technologyInspectionSupportDao.updateClaimCodeAndClaimNameById(RedisUtils.getUserName(), RedisUtils.getUserCode(), orderId);
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("ProcessInspectionController.queryDetailListByOrderId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap = new HashMap();
            reMap.put("list", firstAndEndInspectionService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("ProcessInspectionController.queryByActivtiId出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 提交检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "提交检验详细数据", httpMethod = "POST")
    @PostMapping("/supportDatas")
    public Result supportDatas(@RequestBody FirstAndEndInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        firstAndEndInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 驳回检验数据
     *
     * @return
     */
    @ApiOperation(value = "驳回检验数据", httpMethod = "POST")
    @PostMapping("/rejectDatas")
    public Result rejectDatas(@RequestBody TechnologyInspection data) {
        Map reMap = new HashMap();
        firstAndEndInspectionService.rejectDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }
}
