package com.imes.qms.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.domain.entities.query.template.qms.QmsTemplateInspectionDetailQueryVo;
import com.imes.qms.service.impl.QmsTemplateInspectionDetailServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * QMS模板控制器
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Api("QMS方案质检项控制器")
@RestController
@RequestMapping("/api/qms/template/inspection/detail")
public class QmsTemplateInspectionDetailkController {

    @Autowired
    private QmsTemplateInspectionDetailServiceImpl service;

    @ApiOperation("获取质检项列表")
    @GetMapping("/queryList")
    public Result queryList(QmsTemplateInspectionDetailQueryVo vo) {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsTemplateInspectionDetailQueryVo> list = service.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @ApiOperation("删除质检项")
    @DeleteMapping("/del")
    public Result deL(String ids) {
        service.del(ids);
        return Result.SUCCESS();
    }
}
