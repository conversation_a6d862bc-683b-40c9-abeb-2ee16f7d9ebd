package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.QmsInspectionLibrary;
import com.imes.domain.entities.qms.po.TourTask;
import com.imes.domain.entities.qms.vo.ItemCodeAndNameVo;
import com.imes.domain.entities.qms.vo.TourTaskVo;
import com.imes.domain.entities.query.template.qms.GetLibraryVo;
import com.imes.domain.entities.query.template.qms.GetTourListVo;
import com.imes.qms.service.InspectionLibraryService;
import com.imes.qms.service.TourInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inspectionLibrary")
@Api(tags = "检验项库相关接口")
public class InspectionLibraryController {

    @Autowired
    InspectionLibraryService service;


    @ApiOperation(value = "生成检验项库数据",httpMethod = "POST")
    @PostMapping("/createInspection")
    public Result createInspection(@RequestBody QmsInspectionLibrary library) throws Exception {
        service.createInspection(library);
        return Result.SUCCESS(new PageResult());
    }

    @ApiOperation(value = "获取检验项库列表（高级查询版本）",httpMethod = "GET")
    @GetMapping("/getList")
    public Result getList(GetLibraryVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<QmsInspectionLibrary> list = service.getList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(),list));
    }

    @ApiOperation(value = "获取父项编码及名称",httpMethod = "GET")
    @GetMapping("/getItemCodeAndName")
    public Result getItemCodeAndName(String childCode,String childName) throws Exception {
        return new Result(ResultCode.SUCCESS,service.getItemCodeAndName(childCode,childName));
    }

    @ApiOperation(value = "删除质检项",httpMethod = "POST")
    @PostMapping("/deleteInspection")
    public Result deleteInspection(@RequestBody List<String> ids) throws Exception {
        service.deleteInspection(ids);
        return Result.SUCCESS(new PageResult());
    }
}
