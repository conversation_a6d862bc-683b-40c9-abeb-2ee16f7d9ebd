package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.OutBillQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsOutBillInspectionReportVo;
import com.imes.qms.service.OutBillQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/outBillQualityReport")
@Api(tags = "出库检验质量报表相关接口")
public class OutBillQualityReportController {

    @Autowired
    OutBillQualityReportService outBillQualityReportService;

    /**
     * 获取工艺检验计划
     *
     * @return
     */
    @ApiOperation(value = "获取出库检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionList")
    public Result getInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                     String materialCode,String materialName,
                                     String materialMarker,String specification,
                                     String customerName) {
        try {
            Map reMap=new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("materialMarker",materialMarker);
            map.put("specification",specification);
            map.put("customerName",customerName);
            // 通过
            map.put("process", ConstantUtil.process);
            List<OutBillQualityReportVO> outBillQualityReportVOs = outBillQualityReportService.getInspectionList(map);
            reMap.put("list",outBillQualityReportVOs);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("TechnologyInspectionPublishController.getInspectionPlanList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取出库检验报表
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionList1")
    public Result getInspectionListNew(QmsOutBillInspectionReportVo vo) {
        vo.setProcess(ConstantUtil.adopt);
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<OutBillQualityReportVO> outBillQualityReportVOs = outBillQualityReportService.getInspectionListNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), outBillQualityReportVOs));
    }

    /**
     * 获取来料检验详情列表
     *
     * @return
     */
    @ApiOperation(value = "获取出库检验详情列表",httpMethod = "GET")
    @GetMapping("/getInspectionDetailList")
    public Result getInspectionDetailList(
            @RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
            String materialCode,String materialMarker,
            String specification,String customerCode,
            String whName,String sendType,
            String inspectionStartTime,String inspectionEndTime,
            String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialMarker",materialMarker);
            map.put("specification",specification);
            map.put("customerCode",customerCode);
            map.put("whName",whName);
            map.put("sendType",sendType);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("inspector",inspector);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",outBillQualityReportService.getInspectionDetailList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取来料检验详情明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取出库检验详情明细列表",httpMethod = "GET")
    @GetMapping("/detailInfos")
    public Result getDetailInfo(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",outBillQualityReportService.getDetailInfo(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
