package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.InBillInspectionSaveVo;
import com.imes.qms.service.InBillInspectionService;
import com.imes.qms.service.SendOutInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/SendOutInspection")
@Api(tags = "发货检验相关接口")
public class SendOutInspectionController {

    @Autowired
    SendOutInspectionService sendOutInspectionService;

    /**
     * 获取入库检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验列表信息",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @RequestParam(defaultValue = "10", value = "pageSize") int pageSize) {
        try {
            Map reMap=new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",sendOutInspectionService.queryList());
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("SendOutInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据产品名称，生产计划编号和检验方式获取检验项
     *
     * @return
     */
    @ApiOperation(value = "根据产品名称，生产计划编号和检验方式获取检验项",httpMethod = "GET")
    @GetMapping("/getItemsByParams")
    public Result getItemsByParams(@RequestParam(value = "ppNo") String ppNo,
                            @RequestParam(value = "inspectionType") String inspectionType,
                                   @RequestParam(value = "productName") String productName) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",sendOutInspectionService.getItemsByParams(ppNo,inspectionType,productName));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("SendOutInspectionController.getItemsByParams出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 保存检验基础数据
     *
     * @return
     */
    @ApiOperation(value = "保存检验数据",httpMethod = "POST")
    @PostMapping ("/saveInspectionDatas")
    public Result saveInspectionDatas(@RequestBody InBillInspectionSaveVo datas) {
        try {
            Map reMap=new HashMap();
            sendOutInspectionService.saveInspectionDatas(datas);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("SendOutInspectionController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 修改检验数据
     *
     * @return
     */
    @ApiOperation(value = "修改检验数据",httpMethod = "POST")
    @PostMapping ("/updateInspectionDatas")
    public Result updateInspectionDatas(@RequestBody InBillInspectionSaveVo datas) {
        try {
            Map reMap=new HashMap();
            sendOutInspectionService.updateInspectionDatas(datas);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("SendOutInspectionController.saveStandardDatas出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 删除质检数据
     *
     * @return
     */
    @ApiOperation(value = "删除检验数据",httpMethod = "POST")
    @PostMapping ("/deleteStandardList")
    public Result deleteStandardList(@RequestParam("ids") List<String> ids) {
        try {
            Map reMap=new HashMap();
            sendOutInspectionService.deleteStandardList(ids);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("SendOutInspectionController.deleteStandardList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

}
