package com.imes.qms.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.qms.service.QmsLookBoardService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * @<NAME_EMAIL>
 * @Description:
 * @date 2023/11/17 11:29
 **/
@Slf4j
@RestController
@ApiOperation("成套厂质检控制器")
@RequestMapping("/api/qms/board")
public class QmsLookBoardController {

    @Autowired
    QmsLookBoardService lookBoardService;


    /**
     * 成套厂质检概览
     * @param schema
     * @return
     */
    @ApiOperation("成套厂质检概览")
    @GetMapping("/outsideBoard/completeSet/overview")
    public Result overview(@RequestParam("schema") String schema) throws Exception {
        return new Result(ResultCode.SUCCESS, lookBoardService.overview(schema));
    }

    /**
     * 成套厂质检工序异常数量统计
     * @param param
     * @return
     */
    @ApiOperation("成套厂质检工序异常数量统计")
    @PostMapping("/outsideBoard/completeSet/processUnusual")
    public Result processUnusual(@RequestBody Map<String, Object> param) throws Exception {
        return new Result(ResultCode.SUCCESS, lookBoardService.processUnusual(param));
    }



}
