package com.imes.qms.controller;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.domain.entities.qms.po.QmsTemplate;
import com.imes.domain.entities.query.template.qms.QmsTemplateQueryVo;
import com.imes.qms.service.impl.QmsTemplateServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * QMS模板控制器
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Api("QMS模板控制器")
@RestController
@RequestMapping("/api/qms/template")
public class QmsTemplateController {

    @Autowired
    private QmsTemplateServiceImpl service;

    @ApiOperation("保存方案")
    @PostMapping("/apply")
    public Result apply(@RequestBody QmsTemplate template) throws Exception {
        return Result.SUCCESS(service.apply(template));
    }

    @ApiOperation("获取详情")
    @GetMapping("/info")
    public Result info(String id, String inspectionType) throws Exception {
        return Result.SUCCESS(service.info(id, inspectionType));
    }

    @ApiOperation("获取列表")
    @GetMapping("/queryList")
    public Result queryList(QmsTemplateQueryVo vo) {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsTemplateQueryVo> list = service.queryList(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }

    @ApiOperation("删除方案")
    @DeleteMapping("/del")
    public Result deL(String ids) {
        service.del(ids);
        return Result.SUCCESS();
    }
}
