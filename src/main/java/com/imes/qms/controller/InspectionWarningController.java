package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.qms.service.InspectionWarningService;
import com.imes.qms.service.QualityTraceService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/warning")
@Api(tags = "检验警报相关接口")
public class InspectionWarningController {

    @Autowired
    InspectionWarningService inspectionWarningService;

    /**
     * 测试警报接口
     *
     * @return
     */
    @ApiOperation(value = "测试警报接口",httpMethod = "GET")
    @GetMapping("/testWarning")
    public Result testWarning(String code) {
        try {
            Map reMap = new HashMap();
            inspectionWarningService.testWarning(code);
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("QualityTraceController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
