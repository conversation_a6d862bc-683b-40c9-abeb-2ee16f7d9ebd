package com.imes.qms.controller;


import com.imes.common.entity.Result;
import com.imes.qms.service.impl.QmsTemplateInspectionServiceImpl;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * QMS模板控制器
 *
 * <AUTHOR>
 * @date 2024-05-14
 */
@Slf4j
@Api("QMS模板控制器")
@RestController
@RequestMapping("/api/qms/template/inspection")
public class QmsTemplateInspectionController {

    @Autowired
    private QmsTemplateInspectionServiceImpl service;


    @ApiOperation("根据方案获取配置")
    @GetMapping("/info")
    public Result info(String templateId, String inspectionType) {
        return Result.SUCCESS(service.info(templateId, inspectionType));
    }

    @ApiOperation("删除方案")
    @DeleteMapping("/del")
    public Result deL(String ids) {
        service.del(ids);
        return Result.SUCCESS();
    }
}
