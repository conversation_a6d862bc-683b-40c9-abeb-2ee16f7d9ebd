package com.imes.qms.controller;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.utils.RecordUtils;
import com.imes.common.utils.RedisUtils;
import com.imes.domain.entities.qms.po.*;
import com.imes.domain.entities.qms.vo.ProcessInforVo;
import com.imes.domain.entities.qms.vo.ProcessInspectionInfoVo;
import com.imes.domain.entities.qms.vo.ProcessInspectionTaskVo;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewForMobileVo;
import com.imes.domain.entities.query.template.qms.GetPpNoInfoNewVo;
import com.imes.domain.entities.query.template.qms.QmsProcessResultQueryVo;
import com.imes.qms.dao.ProcessTaskDao;
import com.imes.qms.service.ProcessInspectionService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/processInspection")
@Api(tags = "过程检验相关接口")
public class ProcessInspectionController {

    @Autowired
    ProcessInspectionService processInspectionService;

    @Autowired
    ProcessTaskDao processTaskDao;


    /**
     * 获取过程检验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验列表信息", httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String ppNo, String materialName, String processName, String materialCode,
                            String inspectionCode, String inspectionStartTime, String inspectionEndTime,
                            String inspectionStatus, String status, String isQualified, String pcNo) {
        try {
            Map reMap = new HashMap();
            Map<String, Object> map = new HashMap();
            map.put("ppNo", ppNo);
            map.put("pcNo", pcNo);
            map.put("materialName", materialName);
            map.put("materialCode", materialCode);
            map.put("processName", processName);
            map.put("inspectionCode", inspectionCode);
            map.put("startTime", inspectionStartTime);
            map.put("endTime", inspectionEndTime);
            map.put("inspectionStatus", inspectionStatus);
            map.put("ending", ConstantUtil.ending);
            map.put("unSupport", ConstantUtil.un_support);
            map.put("status", status);
            map.put("isQualified", isQualified);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list", processInspectionService.queryList(map));
            reMap.put("total", page.getTotal());
            return new Result(ResultCode.SUCCESS, reMap);
        } catch (Exception e) {
            log.error("ProcessInspectionController.queryList出错", e);
            return new Result(ResultCode.FAIL, e.getMessage());
        }
    }

    /**
     * 获取生产单号信息（标准版）
     *
     * @return
     */
    @ApiOperation(value = "获取生产单号信息（标准版）", httpMethod = "GET")
    @GetMapping("/getPpNoInfo")
    public Result getPpNoInfoStd(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                 @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                 String ppNo, String materialName, String processName,
                                 String pcNo, String materialCode, String workshopName,
                                 String lineName, String inspectionStatus,
                                 String startTime, String endTime) throws Exception {
        Map reMap = new HashMap();
        Map map = new HashMap();
        map.put("ppNo", ppNo);
        map.put("pcNo", pcNo);
        map.put("materialName", materialName);
        map.put("materialCode", materialCode);
        map.put("startTime", startTime);
        map.put("endTime", endTime);
        map.put("processName", processName);
        map.put("lineName", lineName);
        map.put("workshopName", workshopName);
        map.put("inspectionStatus", inspectionStatus);
        map.put("temporarySave", ConstantUtil.temporary_save);
        map.put("reject", ConstantUtil.has_reject);
        map.put("ending", ConstantUtil.ending);
        map.put("unInspection", ConstantUtil.un_inspection);
        Page page = PageHelper.startPage(pageNum, pageSize);
        reMap.put("list", processInspectionService.getPpNoInfoStd(map));
        reMap.put("total", page.getTotal());
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取生产单号信息（标准版）
     *
     * @return
     */
    @ApiOperation(value = "获取生产单号信息（高级查询版本）", httpMethod = "GET")
    @GetMapping("/getPpNoInfoNew")
    public Result getPpNoInfoStdNew(GetPpNoInfoNewVo vo) throws Exception {
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setUnInspection(ConstantUtil.un_inspection);
        //vo.setStatus(vo.getInspectionStatus());
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<GetPpNoInfoNewVo> ppNoInfoStdNew = processInspectionService.getPpNoInfoStdNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), ppNoInfoStdNew));
    }

    /**
     * 获取生产单号信息（标准版）
     *
     * @return
     */
    @ApiOperation(value = "获取生产单号信息（高级查询版本）", httpMethod = "GET")
    @GetMapping("/getPpNoInfoNewForMobile")
    public Result getPpNoInfoNewForMobile(GetPpNoInfoNewForMobileVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<ProcessTask> ppNoInfoStdNew = processInspectionService.getPpNoInfoNewForMobile(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), ppNoInfoStdNew));
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验任务中的所有的供应商", httpMethod = "GET")
    @GetMapping("/getProcessCodeAndNameFromTask")
    public Result getProcessCodeAndNameFromTask() throws Exception {
        List<HashMap<String, String>> materialByOrderNoNew = processInspectionService.getProcessCodeAndNameFromTask();
        return new Result(ResultCode.SUCCESS, materialByOrderNoNew);
    }

    /**
     * 根据生产单号获取检验项信息（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "根据生产单号获取检验项信息（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItemsByPpNoInfo")
    public Result getInspectionItemsStd(String routeCode, String processCode, String bomCode, String id, int batchNum) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.getInspectionItemsStd(routeCode, processCode, bomCode, id, batchNum));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 检验接口（多选）（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "检验接口（标准版）", httpMethod = "GET")
    @GetMapping("/getInspectionItemsForMore")
    public Result getInspectionItemsForMore(String routeCode, String processCode, String bomCode, String id, Integer batchNum) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.getInspectionItemsForMore(routeCode, processCode, bomCode, id, batchNum));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 合并接口（多选）（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "合并接口（标准版）", httpMethod = "POST")
    @PostMapping("/getMergeTasks")
    public Result getMergeTasks(@RequestBody List<ProcessTask> processTasks) throws Exception {
        Map reMap = new HashMap();
        reMap.put("ProcessTask", processInspectionService.getMergeTasks(processTasks));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 取消合并
     *
     * @return Result
     */
    @ApiOperation(value = "取消合并", httpMethod = "POST")
    @PostMapping("/cancelMerge")
    public Result cancelMerge(@RequestBody List<ProcessTask> processTasks) throws Exception {
        Map reMap = new HashMap();
        processInspectionService.cancelMerge(processTasks);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取工艺路线下面的检验班组
     *
     * @return Result
     */
    @ApiOperation(value = "获取工艺路线下面的检验班组", httpMethod = "GET")
    @GetMapping("/getPpcRouteProcessQmsDepartment")
    public Result getPpcRouteProcessQmsDepartment(String routeCode, String processCode) throws Exception {
        Result result = processInspectionService.getPpcRouteProcessQmsDepartment(routeCode, processCode);
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * 委派的确定按钮
     *
     * @return Result
     */
    @ApiOperation(value = "委派的确定按钮", httpMethod = "GET")
    @GetMapping("/delegateConfirmation")
    public Result delegateConfirmation(String id, String userCode, String userName, String teamCode, String teamName, String addFlag) throws Exception {
        return new Result(ResultCode.SUCCESS, processInspectionService.delegateConfirmation(id, userCode, userName, teamCode, teamName, addFlag));
    }

    /**
     * 委派人员和部门
     *
     * @return Result
     */
    @ApiOperation(value = "委派人员和部门", httpMethod = "GET")
    @GetMapping("/delegateUserList")
    public Result delegateUserList() throws Exception {
        return new Result(ResultCode.SUCCESS, processInspectionService.delegateUserList());
    }

    /**
     * 取消认领
     *
     * @return Result
     */
    @ApiOperation(value = "取消认领", httpMethod = "GET")
    @GetMapping("/cancelClaim")
    public Result cancelClaim(String orderId) throws Exception {
        return new Result(ResultCode.SUCCESS, processInspectionService.cancelClaim(orderId));
    }

    /**
     * 委派功能显示隐藏
     *
     * @return Result
     */
    @ApiOperation(value = "委派功能显示隐藏", httpMethod = "GET")
    @GetMapping("/getDelegate")
    public Result getDelegate() throws Exception {
        boolean result = processInspectionService.getDelegate();
        return new Result(ResultCode.SUCCESS, result);
    }

    /**
     * //     * 暂存检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "暂存检验详细数据", httpMethod = "POST")
    @PostMapping("/saveInspectionDetailDatas")
    @Transactional(rollbackFor = Exception.class)
    public Result saveInspectionDetailDatas(@RequestBody ProcessInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        processInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 提交检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "提交检验详细数据", httpMethod = "POST")
    @PostMapping("/supportDatas")
    @Transactional(rollbackFor = Exception.class)
    public Result supportDatas(@RequestBody ProcessInspectionInfoVo data) throws Exception {
        Map reMap = new HashMap();
        processInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 获取过程检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取过程检验条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryDetailList(id));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @ApiOperation(value = "获取过程检验条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListForMobile")
    public Result queryDetailListForMobile(String id) {
        Map reMap = new HashMap();
        List<ProcessInspectionInfoVo> processInspectionInfoVos = processInspectionService.queryDetailList(id);
        List<ProcessInspectionInfoVo> collect = processInspectionInfoVos.stream().sorted((u1, u2) -> new Long(u2.getMain().getLatestInspectionTime().getTime()).intValue() - new Long(u1.getMain().getLatestInspectionTime().getTime()).intValue()).collect(Collectors.toList());
        reMap.put("list", collect.get(0));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据报工单号获取id
     *
     * @return
     */
    @ApiOperation(value = "根据报工单号获取id", httpMethod = "GET")
    @GetMapping("/queryTaskByWfNo")
    public Result queryTaskByWfNo(@RequestParam("wfNo") String wfNo) {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryTaskByWfNo(wfNo));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据报工单号获取检验信息
     *
     * @return
     */
    @ApiOperation(value = "根据报工单号获取检验信息", httpMethod = "POST")
    @PostMapping("/queryInfoByWfNo")
    public Result queryInfoByWfNo(@RequestBody List<String> wfNo) {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryInfoByWfNo(wfNo));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据id查找合并数据之前的原始数据
     *
     * @return
     */
    @ApiOperation(value = "根据id查找合并数据之前的原始数据", httpMethod = "GET")
    @GetMapping("/queryMergeTaskByid")
    public Result queryMergeTaskByid(@RequestParam("id") String id) {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryMergeTaskByid(id));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 更改废弃状态接口
     *
     * @return
     */
    @ApiOperation(value = "更改废弃状态接口", httpMethod = "GET")
    @GetMapping("/toDisuse")
    public Result toDisuse(@RequestParam("wfNos") List<String> wfNos) throws Exception {
        processInspectionService.toDisuse(wfNos);
        return new Result(ResultCode.SUCCESS, new HashMap());
    }

    /**
     * 废弃状态反审核按钮
     *
     * @return
     */
    @ApiOperation(value = "废弃状态反审核按钮", httpMethod = "GET")
    @GetMapping("/obsoleteAntiAuditing")
    public Result obsoleteAntiAuditing(String id) throws Exception {
        processInspectionService.obsoleteAntiAuditing(id);
        return new Result(ResultCode.SUCCESS, new HashMap());
    }

    /**
     * mom-open-api接口
     *
     * @return
     */
    @ApiOperation(value = "mom-open-api接口", httpMethod = "POST")
    @PostMapping("/queryInfoByVo")
    public Result queryInfoByVo(@RequestBody ProcessInforVo vo) throws Exception {
        return new Result(ResultCode.SUCCESS, processInspectionService.queryInfoByVo(vo));
    }

    /**
     * 通过状态反审核按钮
     *
     * @return
     */
    @ApiOperation(value = "通过状态反审核按钮", httpMethod = "GET")
    @GetMapping("/passAntiAuditing")
    @Transactional(rollbackFor = Exception.class)
    public Result passAntiAuditing(String orderId, String inspectionStatus, String status) throws Exception {
        processInspectionService.passAntiAuditing(orderId, inspectionStatus, status);
        return new Result(ResultCode.SUCCESS, new HashMap());
    }

    /**
     * 已驳回状态反审核按钮
     *
     * @return
     */
    @ApiOperation(value = "已驳回状态反审核按钮", httpMethod = "GET")
    @GetMapping("/rejectedAuditing")
    public Result rejectedAuditing(String orderId, String inspectionStatus, String status) throws Exception {
        processInspectionService.rejectedAuditing(orderId, inspectionStatus, status);
        return new Result(ResultCode.SUCCESS, new HashMap());
    }

    /**
     * 已提交状态反审核按钮
     *
     * @return
     */
    @ApiOperation(value = "已提交状态反审核按钮", httpMethod = "GET")
    @GetMapping("/submittedAuditing")
    public Result submittedAuditing(String orderId, String inspectionStatus, String status) throws Exception {
        processInspectionService.submittedAuditing(orderId, inspectionStatus, status);
        return new Result(ResultCode.SUCCESS, new HashMap());
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情", httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) throws Exception {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryDetailListByOrderId(orderId));
        // 设置该任务认领人
        ProcessTask task = new ProcessTask();
        task.setId(orderId);
        task.setClaimCode(RedisUtils.getUserCode());
        task.setClaimName(RedisUtils.getUserName());
        RecordUtils.updateData(task);
        processTaskDao.updateClaim(task);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情", httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        Map reMap = new HashMap();
        reMap.put("list", processInspectionService.queryByActivtiId(activitiId));
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 提交检验条目按钮
     *
     * @return
     */
    @ApiOperation(value = "提交检验条目按钮", httpMethod = "PUT")
    @PutMapping("/inspections")
    public Result updateStatus(String id) throws Exception {
        Map reMap = new HashMap();
        processInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * //     * 编辑修改检验详细数据
     * //     *
     * //     * @return
     * //
     */
    @ApiOperation(value = "编辑修改检验详细数据", httpMethod = "PUT")
    @PutMapping("/updateInspectionDetailData")
    public Result updateInspectionDetailData(ProcessInspectionDetail data) {
        Map reMap = new HashMap();
        processInspectionService.updateInspectionDetailData(data);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    /**
     * 删除检验条目和明细
     *
     * @return
     */
    @ApiOperation(value = "根据报工单号删除工艺过程检验任务数据", httpMethod = "DELETE")
    @DeleteMapping("/deleteByWfNo")
    public Result deleteByWfNo(@RequestParam("workFinishNo") String workFinishNo) throws Exception {
        Map reMap = new HashMap();
        processInspectionService.deleteByWfNo(workFinishNo);
        return new Result(ResultCode.SUCCESS, reMap);
    }

    @GetMapping("/queryListNew")
    public Result queryListNew(QmsProcessResultQueryVo vo) throws Exception {
        vo.setEnding(ConstantUtil.ending);
        vo.setUnSupport(ConstantUtil.un_support);
        Page page = PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        List<QmsProcessResultQueryVo> list = processInspectionService.queryListNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), list));
    }
}
