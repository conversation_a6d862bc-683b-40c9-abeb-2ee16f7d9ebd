package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.vo.InBillQualityReportVO;
import com.imes.domain.entities.query.template.qms.QmsInBillInspectionReportVo;
import com.imes.qms.service.InBillQualityReportService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/inBillQualityReport")
@Api(tags = "入库检验质量报表相关接口")
public class InBillQualityReportController {

    @Autowired
    InBillQualityReportService inBillQualityReportService;

    /**
     * 获取入库检验报表
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionList")
    public Result getInspectionList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                        @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                    String materialCode,String materialName,
                                    String specification,String materialMarker) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("specification",specification);
            map.put("materialMarker",materialMarker);
            // 通过
            map.put("inspectionStatus", ConstantUtil.process);
            map.put("ending", ConstantUtil.ending);
            map.put("unSupport",ConstantUtil.un_support);
            Page page = PageHelper.startPage(pageNum, pageSize);
                List<InBillQualityReportVO> inBillQualityReportVOs = inBillQualityReportService.getInspectionList(map);
            reMap.put("list",inBillQualityReportVOs);
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取入库检验报表
     *
     * @return
     */
    @ApiOperation(value = "获取入库检验报表",httpMethod = "GET")
    @GetMapping("/getInspectionList1")
    public Result getInspectionListNew(QmsInBillInspectionReportVo vo) {
        vo.setProcess(ConstantUtil.adopt);
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<InBillQualityReportVO> inBillQualityReportVOs = inBillQualityReportService.getInspectionListNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), inBillQualityReportVOs));
    }

    /**
     * 获取检验单明细列表
     *
     * @return
     */
    @ApiOperation(value = "获取检验单明细列表",httpMethod = "GET")
    @GetMapping("/inspectionDetails")
    public Result getInspectionDetail(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                      @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                      String ppNo,String inspectionStartTime,String inspectionEndTime,
                                      String pcNo,String inspector) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("ppNo",ppNo);
            map.put("inspectionStartTime",inspectionStartTime);
            map.put("inspectionEndTime",inspectionEndTime);
            map.put("pcNo",pcNo);
            map.put("inspector",inspector);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",inBillQualityReportService.getInspectionDetail(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("InBillQualityReportController.getInspectionList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
}
