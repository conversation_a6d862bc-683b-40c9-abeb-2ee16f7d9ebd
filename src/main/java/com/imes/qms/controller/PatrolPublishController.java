package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.domain.entities.qms.po.PatrolPublish;
import com.imes.domain.entities.system.CoDepartment;
import com.imes.qms.service.PatrolPublishService;
import com.imes.qms.service.QualityTraceService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/patrolPublish")
@Api(tags = "巡检发布相关接口")
public class PatrolPublishController {

    @Autowired
    PatrolPublishService patrolPublishService;

    /**
     * 获取巡检发布列表
     *
     * @return
     */
    @ApiOperation(value = "获取巡检发布列表",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                            @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String status,String planCode,
                            String inspectedDepartName,String inspectionDepartName,
                            String createStartTime,String createEndTime) {
        try {
            Map reMap = new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("status",status);
            map.put("planCode",planCode);
            map.put("inspectedDepartName",inspectedDepartName);
            map.put("inspectionDepartName",inspectionDepartName);
            map.put("startTime",createStartTime);
            map.put("endTime",createEndTime);
            reMap.put("list",patrolPublishService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("PatrolPublishController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 发布
     *
     * @return
     */
    @ApiOperation(value = "发布",httpMethod = "POST")
    @PostMapping("/publish")
    public Result publish(@RequestBody PatrolPublish patrolPublish) throws Exception {
        Map<String,Object> reMap=new HashMap();
        patrolPublishService.publish(patrolPublish);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 发布
     *
     * @return
     */
    @ApiOperation(value = "确认完成",httpMethod = "POST")
    @PostMapping("/finish")
    public Result finish(@RequestBody PatrolPublish patrolPublish) throws Exception {
        Map<String,Object> reMap=new HashMap();
        patrolPublishService.finish(patrolPublish);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 重新发布status:1/停止status:2
     *
     * @return
     */
    @ApiOperation(value = "重新发布/停止",httpMethod = "POST")
    @PostMapping("/rePublish")
    public Result rePublish(@RequestBody PatrolPublish patrolPublish) {
        Map<String,Object> reMap=new HashMap();
        patrolPublishService.rePublish(patrolPublish);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 被检部门
     *
     * @return
     */
    @ApiOperation(value = "被检部门",httpMethod = "GET")
    @GetMapping("/inspectedDapart")
    public Result inspectedDapart() {
        try {
            return new Result(ResultCode.SUCCESS,patrolPublishService.inspectedDapart());
        } catch (Exception e) {
            log.error("PatrolPublishController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 检验组
     *
     * @return
     */
    @ApiOperation(value = "检验组",httpMethod = "GET")
    @GetMapping("/inspectionDapart")
    public Result inspectionDapart(@RequestParam("departCodes") List<String> departCodes) {
        try {

            return new Result(ResultCode.SUCCESS,patrolPublishService.inspectionDapart(departCodes));
        } catch (Exception e) {
            log.error("PatrolPublishController.queryItemList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 委派
     *
     * @return Result
     */
    @ApiOperation(value = "委派",httpMethod = "POST")
    @PostMapping ("/delegate")
    public Result delegate(@RequestBody Map<String,String[]> map) throws Exception {
        String[] ids = map.get("ids");
        String[] userCodes = map.get("userCodes");
        String[] userNames = map.get("userNames");
        patrolPublishService.delegate(ids,userCodes,userNames);
        return new Result(ResultCode.SUCCESS);
    }

}
