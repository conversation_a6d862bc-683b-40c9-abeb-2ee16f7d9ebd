package com.imes.qms.controller;

import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.qms.dao.ProcessBadCountDao;
import com.imes.qms.service.InBillInspectionService;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.OutBillInspectionService;
import com.imes.qms.service.ProcessInspectionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-05-24
 */
@Slf4j
@RestController
@Api(tags = "QMS业务数据控制器")
@RequestMapping("/api/qms/businessDataCollect")
public class QmsBusinessDataController {


    @Autowired
    private ProcessInspectionService processInspectionService;

    @Autowired
    private MaterialInspectionService materialInspectionService;

    @Autowired
    private InBillInspectionService inBillInspectionService;

    @Autowired
    private OutBillInspectionService outBillInspectionService;

    @Autowired
    private ProcessBadCountDao processBadCountDao;


    /**
     * QMS业务数据采集
     */
    @GetMapping
    @ApiOperation("QMS业务数据采集")
    public Result qmsBusinessDataCollect() {

        // 过程检验报告数量
        long processReportNum = processInspectionService.getProcessReportTotal();

        // 过程检验项目数量
        long processNum = processInspectionService.getProcessTotal();

        // 来料检验报告数量
        long materialReportNum = materialInspectionService.getMaterialReportTotal();

        // 来料检验项目数量
        long materialNum = materialInspectionService.getMaterialTotal();

        long inBillReportNum = inBillInspectionService.getInBillReportTotal();

        // 入库检验项目数量
        long inBillNum = inBillInspectionService.getInBillTotal();

        // 发货检验报告数量
        long outBillReportNum = outBillInspectionService.getOutBillReportTotal();

        // 发货检验项目数量
        long outBillNum = outBillInspectionService.getOutBillTotal();

        // 不良项项目数量
        long badNum = processBadCountDao.getBadTotal();

        // 检验项目总数量
        long allNum = processNum + materialNum + inBillNum + inBillNum;

        Map<String, Long> map = new LinkedHashMap<>(32);


        map.put("1", allNum);
        map.put("2", processReportNum);
        map.put("3", processNum);
        map.put("4", materialReportNum);
        map.put("5", materialNum);
        map.put("6", inBillReportNum);
        map.put("7", inBillNum);
        map.put("8", outBillReportNum);
        map.put("9", outBillNum);
        map.put("10", badNum);
        return new Result(ResultCode.SUCCESS, map);
    }
}
