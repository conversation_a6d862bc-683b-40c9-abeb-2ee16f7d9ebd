package com.imes.qms.controller;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.imes.common.entity.PageResult;
import com.imes.common.entity.Result;
import com.imes.common.entity.ResultCode;
import com.imes.common.exception.CommonException;
import com.imes.common.utils.RedisUtils;
import com.imes.common.utils.StringUtils;
import com.imes.domain.entities.qms.po.InspectionDataProtect;
import com.imes.domain.entities.qms.po.MaterialInspectionDetail;
import com.imes.domain.entities.qms.po.MaterialTask;
import com.imes.domain.entities.qms.po.ProcessTask;
import com.imes.domain.entities.qms.vo.*;
import com.imes.domain.entities.query.template.qms.MaterialByOrderNoNewByMobileVo;
import com.imes.domain.entities.query.template.qms.MaterialByOrderNoNewVo;
import com.imes.qms.service.MaterialInspectionService;
import com.imes.qms.service.MaterialTaskService;
import com.imes.qms.utils.ConstantUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

//解决跨域问题
@Slf4j
@CrossOrigin
@RestController
@RequestMapping(value = "/api/qms/materialInspection")
@Api(tags = "来料检验相关接口")
public class MaterialInspectionController {

    @Autowired
    MaterialInspectionService materialInspectionService;

    @Autowired
    MaterialTaskService materialTaskService;

    /**
     * 获取来料验列表信息
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验列表信息",httpMethod = "GET")
    @GetMapping("/queryList")
    public Result queryList(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                     @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                            String buyOrder,String arrivalOrder,String materialCode,
                            String materialName,String inspectionCode,
                            String inspectionStartTime,String inspectionEndTime,
                            String inspectionStatus,String status,
                            String isQualified,String taskInspectionCode) {
        try {
            Map reMap=new HashMap();
            Page page = PageHelper.startPage(pageNum, pageSize);
            Map map = new HashMap();
            map.put("arrivalOrder",arrivalOrder);
            map.put("buyOrder",buyOrder);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("inspectionCode",inspectionCode);
            map.put("startTime",inspectionStartTime);
            map.put("endTime",inspectionEndTime);
            map.put("isQualified",isQualified);
            map.put("inspectionStatus",inspectionStatus);
            map.put("ending",ConstantUtil.ending);
            map.put("unSupport",ConstantUtil.un_support);
            map.put("status",status);
            map.put("taskInspectionCode",taskInspectionCode);
            reMap.put("list",materialInspectionService.queryList(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 获取来料验列表信息
     *
     * @return
     */
//    @ApiOperation(value = "获取来料检验列表信息(高级搜索版本)",httpMethod = "GET")
//    @GetMapping("/queryListNew")
//    public Result queryListNew(QmsMaterialInspectionVo vo) {
//       vo.setEnding(ConstantUtil.ending);
//       vo.setUnSupport(ConstantUtil.un_support);
//       Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
//       List<MaterialInspectionListVo> materialInspectionListVos = materialInspectionService.queryListNew(vo);
//       return Result.SUCCESS(new PageResult(page.getTotal(), materialInspectionListVos));
//    }
    /**
     * 根据物料编码获取检验项
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码获取检验项",httpMethod = "GET")
    @GetMapping("/getInspectionByMaterialCode")
    public Result getInspectionByMaterialCode(String orderId,String materialCode, String inspectionType, String supplierCode, BigDecimal batchNum, String[] ids) throws Exception {
        Map reMap=new HashMap();
        int num = batchNum.intValue();
        reMap.put("list",materialInspectionService.getInspectionByMaterialCodeAndSupplier(materialCode,inspectionType,supplierCode,num,orderId));
        // 修改认领状态
        for (String id : ids) {
            materialTaskService.updateClaim(id);
        }
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "根据采购单号获取物料信息",httpMethod = "GET")
    @GetMapping("/getMaterialByOrderNo1")
    public Result getMaterialByOrderNo(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                       @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                       String arrivalOrder,String materialCode,String materialName,
                                       String inspectionStatus,String buyOrder,String category,
                                       String supplierName,String startTime,String endTime) {
        try {
            Map reMap=new HashMap();
            Map map = new HashMap();
            map.put("arrivalOrder",arrivalOrder);
            map.put("buyOrder",buyOrder);
            map.put("materialCode",materialCode);
            map.put("materialName",materialName);
            map.put("inspectionStatus",inspectionStatus);
            map.put("category",category);
            map.put("supplierName",supplierName);
            map.put("startTime",startTime);
            map.put("endTime",endTime);
            map.put("orderType", ConstantUtil.arrived);
            map.put("inspectionType", ConstantUtil.material);
            map.put("temporarySave",ConstantUtil.temporary_save);
            map.put("reject",ConstantUtil.has_reject);
            map.put("ending",ConstantUtil.ending);
            Page page = PageHelper.startPage(pageNum, pageSize);
            reMap.put("list",materialInspectionService.getMaterialByOrderNo(map));
            reMap.put("total",page.getTotal());
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.getMaterialByOrderNo出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "根据采购单号获取物料信息",httpMethod = "GET")
    @GetMapping("/getMaterialByOrderNo")
    public Result getMaterialByOrderNoNew(MaterialByOrderNoNewVo vo) throws Exception {
        vo.setInspectionType(ConstantUtil.material);
        vo.setTemporarySave(ConstantUtil.temporary_save);
        vo.setReject(ConstantUtil.has_reject);
        vo.setEnding(ConstantUtil.ending);
        vo.setInspection(ConstantUtil.has_inspection);
        vo.setProcess(ConstantUtil.process);
        vo.setClaimCode(RedisUtils.getUserCode());
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<MaterialByOrderNoNewVo> materialByOrderNoNew = materialInspectionService.getMaterialByOrderNoNew(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), materialByOrderNoNew));
    }
    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "根据采购单号获取物料信息",httpMethod = "GET")
    @GetMapping("/getMaterialByOrderNoByMobile")
    public Result getMaterialByOrderNoByMobile(MaterialByOrderNoNewByMobileVo vo) throws Exception {
        Page page = PageHelper.startPage(vo.getPageNum(),vo.getPageSize());
        List<MaterialTaskForMobileVo> materialByOrderNoNew = materialInspectionService.getMaterialByOrderNoByMobile(vo);
        return Result.SUCCESS(new PageResult(page.getTotal(), materialByOrderNoNew));
    }
    /**
     * mom-open-api接口
     *
     * @return
     */
    @ApiOperation(value = "mom-open-api接口",httpMethod = "POST")
    @PostMapping("/queryMaterilalInfoByVo")
    public Result queryMaterilalInfoByVo(@RequestBody MaterialInforVo vo) throws Exception {
        List<MaterialResultVo> list = materialInspectionService.queryMaterilalInfoByVo(vo);
        return new Result(ResultCode.SUCCESS,list);
    }
    /**
     * 根据采购单号获取物料信息
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验任务中的所有的供应商",httpMethod = "GET")
    @GetMapping("/getSupplierCodeAndNameFromTask")
    public Result getSupplierCodeAndNameFromTask(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum, @RequestParam(defaultValue = "15", value = "pageSize") int pageSize,String supplierName) throws Exception {
        Map reMap=new HashMap();
        Page page = PageHelper.startPage(pageNum, pageSize);
        List<HashMap<String,String>> materialByOrderNoNew = materialInspectionService.getSupplierCodeAndNameFromTask(supplierName);
        reMap.put("list",materialByOrderNoNew);
        reMap.put("total",page.getTotal());
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 委派功能显示隐藏
     *
     * @return Result
     */
    @ApiOperation(value = "委派功能显示隐藏",httpMethod = "GET")
    @GetMapping("/getDelegate")
    public Result getDelegate() throws Exception {
        boolean result = materialInspectionService.getDelegate();
        return new Result(ResultCode.SUCCESS,result);
    }
    /**
     * 委派人员和部门
     *
     * @return Result
     */
    @ApiOperation(value = "委派人员和部门",httpMethod = "GET")
    @GetMapping("/delegateUserList")
    public Result delegateUserList() throws Exception {
        return new Result(ResultCode.SUCCESS,materialInspectionService.delegateUserList());
    }
    /**
     * 取消认领
     *
     * @return Result
     */
    @ApiOperation(value = "取消认领",httpMethod = "GET")
    @GetMapping("/cancelClaim")
    public Result cancelClaim(String orderId) throws Exception {
        return new Result(ResultCode.SUCCESS,materialInspectionService.cancelClaim(orderId));
    }
    /**
     * 委派的确定按钮
     *
     * @return Result
     */
    @ApiOperation(value = "委派的确定按钮",httpMethod = "GET")
    @GetMapping("/delegateConfirmation")
    public Result delegateConfirmation(String orderId,String userCode,String userName,String teamCode,String teamName,String addFlag) throws Exception {
        return new Result(ResultCode.SUCCESS,materialInspectionService.delegateConfirmation(orderId,userCode,userName,teamCode,addFlag,teamName));
    }
    /**
     * 反提交按钮
     *
     * @return Result
     */
    @ApiOperation(value = "反提交按钮",httpMethod = "GET")
    @GetMapping("/unsubmission")
    public Result unsubmission(String orderId) throws Exception {
        materialInspectionService.unsubmission(orderId);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     //     * 暂存检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "暂存检验详细数据",httpMethod = "POST")
    @PostMapping ("/saveInspectionDetailDatas")
    public Result saveInspectionDetailDatas(@RequestBody MaterialInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.saveInspectionDetailDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     //     * 提交检验详细数据
     //     *
     //     * @return
     //     */
    @ApiOperation(value = "提交检验详细数据",httpMethod = "POST")
    @PostMapping ("/supportDatas")
    public Result supportDatas(@RequestBody MaterialInspectionInfoVo data) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.supportDatas(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 通过新增按钮新增来料检验任务（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "通过新增按钮新增来料检验任务",httpMethod = "POST")
    @PostMapping ("/addMaterialTask")
    public Result addMaterialTask(@RequestBody List<MaterialTask> task) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.addMaterialTask(task);
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 合并接口（多选）（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "合并接口（标准版）",httpMethod = "POST")
    @PostMapping("/getMergeTasks")
    public Result getMergeTasks(@RequestBody List<MaterialTask> materialTasks) throws Exception {
        Map reMap=new HashMap();
        reMap.put("MaterialTask",materialInspectionService.getMergeTasks(materialTasks));
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 取消合并
     *
     * @return Result
     */
    @ApiOperation(value = "取消合并",httpMethod = "POST")
    @PostMapping("/cancelMerge")
    public Result cancelMerge(@RequestBody List<MaterialTask> materialTasks) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.cancelMerge(materialTasks);
        return new Result(ResultCode.SUCCESS,reMap);
    }
//    /**
//     * 取消合并
//     *
//     * @return Result
//     */
//    @ApiOperation(value = "新增按钮新增的任务取消合并",httpMethod = "POST")
//    @PostMapping("/cancelMergeForAddZero")
//    public Result cancelMergeForAddZero(@RequestBody List<MaterialTask> materialTasks) throws Exception {
//        Map reMap=new HashMap();
//        materialInspectionService.cancelMergeForAddZero(materialTasks);
//        return new Result(ResultCode.SUCCESS,reMap);
//    }

    /**
     * 删除质检任务
     *
     * @return Result
     */
    @ApiOperation(value = "删除质检任务",httpMethod = "POST")
    @PostMapping("/deleteMaterialTask")
    public Result deleteMaterialTask(@RequestBody List<String> orders) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.deleteMaterialTask(orders);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 根据poId反选来料检验单
     *
     * @return
     */
    @ApiOperation(value = "根据poId反选来料检验单",httpMethod = "GET")
    @GetMapping("/reverseMaterialInspection")
    public Result reverseMaterialInspection(@RequestParam("poId") String poId) throws Exception {
        return new Result(ResultCode.SUCCESS, materialInspectionService.reverseMaterialInspection(poId));
    }

    /**
     * 根据buyOrder查询来料检验单
     *
     * @return
     */
    @ApiOperation(value = "根据buyOrder查询来料检验单",httpMethod = "GET")
    @GetMapping("/queryTaskByBuyOrder")
    public Result queryTaskByBuyOrder(@RequestParam("buyOrder") String buyOrder) throws Exception {
        List<MaterialTask> list = materialInspectionService.queryTaskByBuyOrder(buyOrder);
        return new Result(ResultCode.SUCCESS,list);
    }

    /**
     * 根据poId反选来料检验单
     *
     * @return
     */
    @ApiOperation(value = "反选来料检验单更新检验单",httpMethod = "POST")
    @PostMapping("/updateReverseInspection")
    public Result updateReverseInspection(@RequestBody List<UpdateReverseInspectionVo> list) throws Exception {
        materialInspectionService.updateReverseInspection(list);
        return new Result(ResultCode.SUCCESS);
    }

    /**
     * 如果已经反选了采购单并且暂存之后删除掉需要清空一下已经反选的数据
     *
     * @return
     */
    @ApiOperation(value = "删除采购到货单质检单变更",httpMethod = "POST")
    @PostMapping("/deleteAoInspection")
    public Result deleteAoInspection(@RequestBody List<String> list) throws Exception {
        materialInspectionService.deleteAoInspection(list);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 根据poId反选来料检验单
     *
     * @return
     */
    @ApiOperation(value = "回滚反选来料检验单",httpMethod = "POST")
    @PostMapping("/rollBackReverseInspection")
    public Result rollBackReverseInspection(@RequestBody List<String> list) throws Exception {
        materialInspectionService.rollBackReverseInspection(list);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 查看是否有此到货单号的来料检验任务
     *
     * @return
     */
    @ApiOperation(value = "查看是否有此到货单号的来料检验任务",httpMethod = "GET")
    @GetMapping("/isMaterialInspection")
    public Result isMaterialInspection(@RequestParam("arrivalOrder") String arrivalOrder) throws Exception {
        return new Result(ResultCode.SUCCESS,materialInspectionService.isMaterialInspection(arrivalOrder));
    }
    /**
     * 查看是否有此到货单号的来料检验任务
     *
     * @return
     */
    @ApiOperation(value = "查看是否有此到货单号的来料检验任务",httpMethod = "GET")
    @GetMapping("/getTaskBySupperCode")
    public List<MaterialTask> getTaskBySupperCode(@RequestParam("supperCode") String supperCode) {
        return materialInspectionService.getTaskBySupperCode(supperCode);
    }
    /**
     * 根据ID查询主信息
     *
     * @return
     */
    @ApiOperation(value = "根据物料编码查询先检验还是先到货",httpMethod = "GET")
    @GetMapping ("/getIncomingInspectionSequence")
    public Result getIncomingInspectionSequence(@RequestParam("materialCode") String materialCode) throws Exception {
        InspectionDataProtect incomingInspectionSequence = materialInspectionService.getIncomingInspectionSequence(materialCode);
        return new Result(ResultCode.SUCCESS, incomingInspectionSequence);
    }
    /**
     * 根据ID查询主信息
     *
     * @return
     */
    @ApiOperation(value = "查询最早的生成质检任务的时间",httpMethod = "GET")
    @GetMapping ("/getInspectionTime")
    public Result getInspectionTime() throws Exception {
        String time = materialInspectionService.getInspectionTime();
        return new Result(ResultCode.SUCCESS, time);
    }
    /**
     * 查看是否有此到货单号的来料检验任务
     *
     * @return
     */
    @ApiOperation(value = "反审核来料检验",httpMethod = "GET")
    @PostMapping("/auditMaterialInspection")
    public Result auditMaterialInspection(@RequestParam("arrivalOrder") String arrivalOrder) throws Exception {
        materialInspectionService.auditMaterialInspection(arrivalOrder);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 查看是否有此到货单号的来料检验任务
     *
     * @return
     */
    @ApiOperation(value = "删除来料检验任务及其相关数据",httpMethod = "GET")
    @PostMapping("/deleteAuditMaterialInspection")
    public Result deleteAuditMaterialInspection(@RequestParam("arrivalOrder") String arrivalOrder) throws Exception {
        materialInspectionService.deleteAuditMaterialInspection(arrivalOrder);
        return new Result(ResultCode.SUCCESS);
    }
    /**
     * 获取来料检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailList")
    public Result queryDetailList(String id) {
        try {
            Map reMap=new HashMap();
            List<MaterialInspectionInfoVo> list = materialInspectionService.queryDetailList(id);
            boolean isNull = false;
            for (MaterialInspectionInfoVo vo : list) {
                if (null == vo.getMain().getAuditTime() || "".equals(vo.getMain().getAuditTime())){
                    isNull = true;
                }
            }
            if (isNull){
                reMap.put("list",list);
                return new Result(ResultCode.SUCCESS,reMap);
            }else {
                reMap.put("list",list.stream().sorted((u1, u2) -> new Long(u2.getMain().getAuditTime().getTime()).intValue() - new Long(u1.getMain().getAuditTime().getTime()).intValue()).collect(Collectors.toList()));
                return new Result(ResultCode.SUCCESS,reMap);
            }
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }
    /**
     * 根据任务id查找到显示结果对象（标准版）
     *
     * @return Result
     */
    @ApiOperation(value = "根据任务id查找到显示结果对象（标准版）",httpMethod = "GET")
    @GetMapping("/getInspectionById")
    public Result getInspectionById(String orderId) throws Exception {
        Map reMap=new HashMap();
        reMap.put("list",materialInspectionService.getInspectionById(orderId));
        return new Result(ResultCode.SUCCESS,reMap);
    }
    /**
     * 获取来料检验条目详情
     *
     * @return
     */
    @ApiOperation(value = "获取来料检验条目详情(移动端)",httpMethod = "GET")
    @GetMapping("/queryDetailListForMobile")
    public Result queryDetailListForMobile(String id) {
        try {
            Map reMap=new HashMap();
            List<MaterialInspectionInfoVo> list = materialInspectionService.queryDetailList(id);
            List<MaterialInspectionInfoVo> collect = list.stream().sorted((u1, u2) -> new Long(u2.getMain().getCreateOn().getTime()).intValue() - new Long(u1.getMain().getCreateOn().getTime()).intValue()).collect(Collectors.toList());
            reMap.put("list",collect.get(0));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 获取合并检验条目信息
     *
     * @return
     */
    @ApiOperation(value = "获取合并检验条目信息",httpMethod = "GET")
    @GetMapping("/mergeOrderIds")
    public Result queryMergeOrderIds(String id) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",materialInspectionService.queryMergeOrderIds(id));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据activitiId获取来料检验详情
     *
     * @return
     */
    @ApiOperation(value = "根据activitiId获取来料检验详情",httpMethod = "GET")
    @GetMapping("/queryByActivtiId")
    public Result queryByActivtiId(String activitiId) {
        try {
            Map reMap=new HashMap();
            reMap.put("list",materialInspectionService.queryByActivtiId(activitiId));
            return new Result(ResultCode.SUCCESS,reMap);
        } catch (Exception e) {
            log.error("MaterialInspectionController.queryDetailList出错",e);
            return new Result(ResultCode.FAIL,e.getMessage());
        }
    }

    /**
     * 根据暂存条目id获取检验项条目详情
     *
     * @return Result
     */
    @ApiOperation(value = "根据暂存条目id获取检验项条目详情",httpMethod = "GET")
    @GetMapping("/queryDetailListByOrderId")
    public Result queryDetailListByOrderId(String orderId) throws Exception {
        Map reMap=new HashMap();
        reMap.put("list",materialInspectionService.queryDetailListByOrderId(orderId));
        //更改认领人及班组
        materialTaskService.updateClaim(orderId);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 编辑修改检验详细数据
     *
     * @return
     */
    @ApiOperation(value = "编辑修改检验详细数据",httpMethod = "PUT")
    @PutMapping ("/updateInspectionDetailData")
    public Result updateInspectionDetailData(MaterialInspectionDetail data) {
        Map reMap=new HashMap();
        materialInspectionService.updateInspectionDetailData(data);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 提交检验条目
     *
     * @return
     */
    @ApiOperation(value = "提交检验条目",httpMethod = "PUT")
    @PutMapping ("/updateStatus")
    public Result updateStatus(String id) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.updateStatus(id);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 取消合并检验
     *
     * @return
     */
    @ApiOperation(value = "取消合并检验",httpMethod = "DELETE")
    @DeleteMapping ("/cancelMergeInspection")
    public Result cancelMergeInspection(@RequestParam("ids") List<String> ids) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.cancelMergeInspection(ids);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    /**
     * 更改图片状态
     *
     * @return
     */
    @ApiOperation(value = "更改图片状态",httpMethod = "PUT")
    @PutMapping ("/updatePicStatus")
    public Result updatePicStatus(String id,String picStatus) throws Exception {
        Map reMap=new HashMap();
        materialInspectionService.updatePicStatus(id,picStatus);
        return new Result(ResultCode.SUCCESS,reMap);
    }

    @GetMapping("/findAoByFilter")
    public Result findAoByFilter(@RequestParam(defaultValue = "1", value = "pageNum") int pageNum,
                                 @RequestParam(defaultValue = "10", value = "pageSize") int pageSize,
                                 @RequestParam(required=false, value = "prop") String prop,
                                 @RequestParam(required=false, value = "order") String order,
                                 @RequestParam Map map){
        Map result = new HashMap();
        Page page = PageHelper.startPage(pageNum, pageSize);
        map.put("orderStatus","10");
        StringBuffer sortStr = new StringBuffer("");
        if (!StringUtils.isNullOrBlank(prop) && !StringUtils.isNullOrBlank(order))
            sortStr.append(StringUtils.humpToLine(prop)+" "+order+", ");

        sortStr.append("update_on desc, ao_code asc ");
        PageHelper.orderBy(sortStr.toString());

        result.put("list", materialInspectionService.findAoByFilter(map));
        result.put("total", page.getTotal());

        return new Result(ResultCode.SUCCESS, result);
    }

}
