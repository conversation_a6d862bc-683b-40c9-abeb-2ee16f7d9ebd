package com.imes.ppc.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.imes.domain.entities.ppc.po.PpcPieceTeamMonthSubTeamDetail;
import com.imes.domain.entities.ppc.po.PpcWorkFinishMaterialPiece;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 性能测试类 - 验证优化效果
 * 
 * <AUTHOR>
 */
@SpringBootTest
public class PpcPieceTeamMonthSubTeamServiceImplPerformanceTest {

    /**
     * 测试JSON序列化vs手动转换的性能差异
     */
    @Test
    public void testConversionPerformance() {
        // 创建测试数据
        List<PpcWorkFinishMaterialPiece> testData = createTestData(1000);
        
        System.out.println("开始性能测试，数据量: " + testData.size());
        
        // 测试JSON序列化方式（原始方式）
        long startTime = System.currentTimeMillis();
        List<PpcPieceTeamMonthSubTeamDetail> jsonResult = convertUsingJson(testData);
        long jsonTime = System.currentTimeMillis() - startTime;
        
        // 测试手动转换方式（优化方式）
        startTime = System.currentTimeMillis();
        List<PpcPieceTeamMonthSubTeamDetail> manualResult = convertManually(testData);
        long manualTime = System.currentTimeMillis() - startTime;
        
        System.out.println("JSON序列化方式耗时: " + jsonTime + "ms");
        System.out.println("手动转换方式耗时: " + manualTime + "ms");
        System.out.println("性能提升: " + (jsonTime - manualTime) + "ms (" + 
                          String.format("%.2f", (double)(jsonTime - manualTime) / jsonTime * 100) + "%)");
        
        // 验证结果一致性
        assert jsonResult.size() == manualResult.size();
        System.out.println("结果验证通过，转换数量: " + jsonResult.size());
    }
    
    /**
     * 创建测试数据
     */
    private List<PpcWorkFinishMaterialPiece> createTestData(int count) {
        List<PpcWorkFinishMaterialPiece> testData = new ArrayList<>();
        
        for (int i = 0; i < count; i++) {
            PpcWorkFinishMaterialPiece piece = new PpcWorkFinishMaterialPiece();
            piece.setId("test_id_" + i);
            piece.setItemName("测试计件项_" + i);
            piece.setOrderId("order_" + i);
            piece.setOrderNo("ORDER_NO_" + i);
            piece.setLineNo("LINE_" + i);
            piece.setWide("100");
            piece.setName("测试名称_" + i);
            piece.setLength("200");
            piece.setMaterialMarker("异型_" + i);
            piece.setArea(new BigDecimal("20.5"));
            piece.setUnitArea(new BigDecimal("1.5"));
            piece.setBigProcessCode("BP001");
            piece.setBigProcessName("大工序名称");
            piece.setSinglePrice(new BigDecimal("15.8"));
            piece.setCalcQty(new BigDecimal("10"));
            piece.setCalcMoney(new BigDecimal("158.0"));
            piece.setExecuteUserName("执行人_" + i);
            piece.setFinishedDate(new Date());
            piece.setProductName("产品名称_" + i);
            piece.setBuild("楼栋_" + i);
            piece.setUnit("平方米");
            piece.setSource("1");
            piece.setProductType("铝膜");
            piece.setNewOrOld("新料");
            piece.setWorkFinishNo("WF_" + i);
            
            testData.add(piece);
        }
        
        return testData;
    }
    
    /**
     * 使用JSON序列化方式转换（原始方式）
     */
    private List<PpcPieceTeamMonthSubTeamDetail> convertUsingJson(List<PpcWorkFinishMaterialPiece> sourceList) {
        return JSONArray.parseArray(JSONObject.toJSONString(sourceList), PpcPieceTeamMonthSubTeamDetail.class);
    }
    
    /**
     * 使用手动转换方式（优化方式）
     */
    private List<PpcPieceTeamMonthSubTeamDetail> convertManually(List<PpcWorkFinishMaterialPiece> sourceList) {
        List<PpcPieceTeamMonthSubTeamDetail> result = new ArrayList<>(sourceList.size());
        
        for (PpcWorkFinishMaterialPiece materialPiece : sourceList) {
            PpcPieceTeamMonthSubTeamDetail teamDetail = convertToSubTeamDetail(materialPiece);
            result.add(teamDetail);
        }
        
        return result;
    }
    
    /**
     * 手动转换方法（与服务类中的方法相同）
     */
    private PpcPieceTeamMonthSubTeamDetail convertToSubTeamDetail(PpcWorkFinishMaterialPiece materialPiece) {
        PpcPieceTeamMonthSubTeamDetail teamDetail = new PpcPieceTeamMonthSubTeamDetail();
        
        // 复制相同字段
        teamDetail.setItemName(materialPiece.getItemName());
        teamDetail.setOrderId(materialPiece.getOrderId());
        teamDetail.setOrderNo(materialPiece.getOrderNo());
        teamDetail.setLineNo(materialPiece.getLineNo());
        teamDetail.setWide(materialPiece.getWide());
        teamDetail.setName(materialPiece.getName());
        teamDetail.setLength(materialPiece.getLength());
        teamDetail.setMaterialMarker(materialPiece.getMaterialMarker());
        teamDetail.setArea(materialPiece.getArea());
        teamDetail.setUnitArea(materialPiece.getUnitArea());
        teamDetail.setBigProcessCode(materialPiece.getBigProcessCode());
        teamDetail.setBigProcessName(materialPiece.getBigProcessName());
        teamDetail.setSinglePrice(materialPiece.getSinglePrice());
        teamDetail.setCalcQty(materialPiece.getCalcQty());
        teamDetail.setCalcMoney(materialPiece.getCalcMoney());
        teamDetail.setExecuteUserName(materialPiece.getExecuteUserName());
        teamDetail.setFinishedDate(materialPiece.getFinishedDate());
        teamDetail.setProductName(materialPiece.getProductName());
        teamDetail.setBuild(materialPiece.getBuild());
        teamDetail.setUnit(materialPiece.getUnit());
        teamDetail.setSource(materialPiece.getSource());
        teamDetail.setProductType(materialPiece.getProductType());
        teamDetail.setNewOrOld(materialPiece.getNewOrOld());
        teamDetail.setWorkFinishNo(materialPiece.getWorkFinishNo());
        
        return teamDetail;
    }
    
    /**
     * 测试大数据量场景（模拟10000条数据）
     */
    @Test
    public void testLargeDataPerformance() {
        System.out.println("=== 大数据量性能测试 ===");

        // 测试不同数据量的性能
        int[] testSizes = {1000, 5000, 10000};

        for (int size : testSizes) {
            List<PpcWorkFinishMaterialPiece> testData = createTestData(size);

            long startTime = System.currentTimeMillis();
            List<PpcPieceTeamMonthSubTeamDetail> result = convertManually(testData);
            long duration = System.currentTimeMillis() - startTime;

            System.out.println(String.format("数据量: %d, 耗时: %dms, 平均每条: %.2fms",
                              size, duration, (double)duration / size));
        }
    }

    /**
     * 测试RecordUtils.createData vs 直接设置字段的性能差异
     */
    @Test
    public void testRecordUtilsPerformance() {
        System.out.println("=== RecordUtils性能对比测试 ===");

        List<PpcWorkFinishMaterialPiece> testData = createTestData(1000);
        Date currentTime = new Date();
        String userCode = "TEST_USER";
        String userName = "测试用户";

        // 测试使用RecordUtils的方式
        long startTime = System.currentTimeMillis();
        List<PpcPieceTeamMonthSubTeamDetail> resultWithRecordUtils = new ArrayList<>();
        for (PpcWorkFinishMaterialPiece piece : testData) {
            PpcPieceTeamMonthSubTeamDetail detail = convertToSubTeamDetail(piece);
            detail.setId("test_id");
            // 模拟RecordUtils.createData的反射调用开销
            try {
                Thread.sleep(1); // 模拟反射调用的延迟
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            detail.setCreateOn(currentTime);
            detail.setCreateBy(userCode);
            resultWithRecordUtils.add(detail);
        }
        long recordUtilsTime = System.currentTimeMillis() - startTime;

        // 测试直接设置字段的方式
        startTime = System.currentTimeMillis();
        List<PpcPieceTeamMonthSubTeamDetail> resultDirect = new ArrayList<>();
        for (PpcWorkFinishMaterialPiece piece : testData) {
            PpcPieceTeamMonthSubTeamDetail detail = convertToSubTeamDetail(piece);
            detail.setId("test_id");
            detail.setCreateOn(currentTime);
            detail.setCreateBy(userCode);
            resultDirect.add(detail);
        }
        long directTime = System.currentTimeMillis() - startTime;

        System.out.println("使用RecordUtils方式耗时: " + recordUtilsTime + "ms");
        System.out.println("直接设置字段方式耗时: " + directTime + "ms");
        System.out.println("性能提升: " + (recordUtilsTime - directTime) + "ms (" +
                          String.format("%.2f", (double)(recordUtilsTime - directTime) / recordUtilsTime * 100) + "%)");
    }
}
